import { defineConfig, devices } from '@playwright/test'
import { workerFixtures } from './tests/ui-tests/fixtures/ui-fixtures'
import { ApiWorkerFixtures } from './tests/api-tests/fixtures/api-fixtures'
import { commonWorkerFixtures } from './tests/commons/common-fixtures'
import dotenv from 'dotenv'
import path from 'path'

function loadEnv() {
  const envFile = process.env.PROD === '1' ? '.env.prod' : '.env.stage'
  const envPath = path.resolve(__dirname, envFile)
  dotenv.config({ path: envPath })
  console.log(`Loaded ${envFile} configuration`)
}

loadEnv()
// Read from default ".env" file.
// dotenv.config();

const dynamicHeaders = process.env.DYNAMIC_HEADERS || ''
const shouldDisableWebSecurity = true //dynamicHeaders.includes('cocoon');

const torchConfig = {
  torchUrl: process.env.TORCH_URL || 'http://torch-service.torch.svc.cluster.local',
  suiteId: process.env.TORCH_SUITE_ID || '6761720fae258e016d07ec6d',
}

// Function to parse and extract dynamic headers from the environment variable
const getDynamicHeaders = () => {
  const headers: { [key: string]: string } = {}

  // Read the EXTRA_HEADERS environment variable
  const extraHeaders = process.env.DYNAMIC_HEADERS

  if (extraHeaders) {
    // Split the headers using the semicolon (;) separator if there are multiple headers
    const headerPairs = extraHeaders.split(';')

    // Iterate over each header pair (key-value)
    headerPairs.forEach((pair) => {
      // Split each pair by ':' to separate the key and value
      const [key, value] = pair.split(':')

      if (key && value) {
        // Store the header key and value in the headers object
        headers[key.trim()] = value.trim()
      }
    })
  }

  return headers
}

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig<commonWorkerFixtures & workerFixtures & ApiWorkerFixtures>({
  globalSetup: process.env.MEETING_ID_TEST === '1' ? './tests/misc-tests/specs/join-meetings/setup.ts' : './global-setup.ts',
  testDir: 'tests',

  testIgnore: (() => {
    const ignoreList = []
    if (process.env.CI) {
      ignoreList.push('*ignore.spec.ts' as never)
    }

    if (process.env.PROD !== '1') {
      ignoreList.push('*prod.spec.ts' as never)
    }

    return ignoreList
  })(),

  timeout: 60000,

  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: process.env.CI === '1',
  /* Retry on CI only */
  retries: process.env.PLAYWRIGHT_RETRIES ? parseInt(process.env.PLAYWRIGHT_RETRIES) : (process.env.CI === '1' ? 2 : 0),
  /* Opt out of parallel tests on CI. */

  workers: process.env.PLAYWRIGHT_WORKERS ? parseInt(process.env.PLAYWRIGHT_WORKERS) : (process.env.CI === '1' ? 6 : 2),

  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  // reporter: [['html'], ['json', { outputFile: 'test-results/play-report.json' }]],

  reporter: process.env.CI === '1'
    ? [
      ['@allen-career-institute/torch-playwright-reporter', torchConfig],
      ['html'],
      ['json', { outputFile: 'test-results/play-report.json' }],
    ]
    : [['html'], ['json', { outputFile: 'test-results/play-report.json' }]],

  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.baseURL ? process.env.baseURL
      : process.env.PROD === '1' ? 'https://allen.in' : 'https://web.allen-stage.in',

    trace: 'retain-on-failure', // Change it to on-first-retry after stabilizing the test suite
    screenshot: 'only-on-failure',

    // Run browser in headless mode.
    headless: true,

    // Emulate browser timezone - set IST as default timezone for easier understanding of test results
    timezoneId: 'IST',

    actionTimeout: 10000,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'Allen E2E',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--use-fake-device-for-media-stream',
            '--use-fake-ui-for-media-stream',
            ...(shouldDisableWebSecurity ? ['--disable-web-security'] : []),
          ],
        },
        extraHTTPHeaders: getDynamicHeaders(),
        contextOptions: {
          permissions: ['microphone', 'camera', 'clipboard-read', 'clipboard-write'],
        },
        channel: 'chrome',
      },
    },
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },

    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },

    /* Test against mobile viewports. */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    {
      name: 'Chrome mWeb',
      grep: /@mobile/,
      use: {
        ...devices['Pixel 5'],
        browserName: 'chromium',      // Required by Playwright for channel to work
        channel: 'chrome',            // Uses real Google Chrome
        isMobile: true,
        launchOptions: {
          args: [
            '--use-fake-device-for-media-stream',
            '--use-fake-ui-for-media-stream',
            ...(shouldDisableWebSecurity ? ['--disable-web-security'] : []),
          ],
        },
        extraHTTPHeaders: getDynamicHeaders(),
        contextOptions: {
          permissions: ['microphone', 'camera', 'clipboard-read', 'clipboard-write'],
        },
      },
    },

    /* Test against branded browsers. */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* Run your local dev server before starting the tests */
  // webServer: {
  //   command: 'npm run start',
  //   url: 'http://127.0.0.1:3000',
  //   reuseExistingServer: !process.env.CI,
  // },
})
