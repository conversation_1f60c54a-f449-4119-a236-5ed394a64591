import os
import mysql.connector
from dotenv import load_dotenv
import logging
from typing import List, Dict, Any, Optional
import requests
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UserEnrollmentManager:
    def __init__(self, env: str = 'stage'):
        self.env = env
        # Load environment variables based on env
        # Get the parent directory path
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        env_file = os.path.join(parent_dir, '.env.stage' if env == 'stage' else '.env.prod')
        
        logger.info(f"Loading environment file from: {env_file}")
        load_dotenv(env_file)
        
        # Database configuration
        self.db_config = {
            'host': '***********',
            'user': 'automation_user',
            'password': 'automation@password123',
            'database': 'allen_automation_test_db'
        }
        
        # API configuration
        self.base_url = 'https://bff.allen-stage.in' if env=="stage" else "https://api.allen-live.in"
        
        # Get admin access token from environment
        admin_token = os.getenv('ADMIN_ACCESS_TOKEN')
        if not admin_token:
            logger.error(f"ADMIN_ACCESS_TOKEN not found in {env_file}")
            raise ValueError(f"ADMIN_ACCESS_TOKEN not found in {env_file}")
            
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {admin_token}"
        }
        
        # Batch ID from environment
        self.cleanup_batch_id = os.getenv('DEL_JEE_BATCH_ID')
        if not self.cleanup_batch_id:
            logger.error(f"DEL_JEE_BATCH_ID not found in {env_file}")
            logger.error(f"Current environment variables: {dict(os.environ)}")
            raise ValueError(f"DEL_JEE_BATCH_ID not found in {env_file}")

        # Course and Phase IDs
        self.course_id = os.getenv('JEE_BATCH_COURSE_ID')
        self.phase_id = os.getenv('JEE_BATCH_PHASE_ID')
        
        if not self.course_id or not self.phase_id:
            logger.error(f"Missing required environment variables: JEE_BATCH_COURSE_ID={self.course_id}, JEE_BATCH_PHASE_ID={self.phase_id}")
            raise ValueError("Missing required environment variables")

    def get_db_connection(self):
        """Create and return a database connection"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            return connection
        except mysql.connector.Error as err:
            logger.error(f"Error connecting to database: {err}")
            raise

    def get_all_users(self) -> List[Dict[str, Any]]:
        """Fetch all users from the stage_student_accounts table"""
        connection = self.get_db_connection()
        cursor = connection.cursor(dictionary=True)
        
        try:
            query = """
                SELECT id, phone, name, email
                FROM stage_student_accounts
            """
            cursor.execute(query)
            users = cursor.fetchall()
            logger.info(f"Found {len(users)} users to process")
            return users
        except mysql.connector.Error as err:
            logger.error(f"Error fetching users: {err}")
            raise
        finally:
            cursor.close()
            connection.close()

    def delete_user(self, user_id: str) -> bool:
        """Delete a user"""
        try:
            url = f"{self.base_url}/api/v1/users"
            logger.info(f"Attempting to delete user {user_id}")
            response = requests.delete(url, headers=self.headers)
            response.raise_for_status()
            logger.info(f"Successfully deleted user {user_id}")
            return True
        except requests.exceptions.RequestException as err:
            logger.error(f"Error deleting user {user_id}: {err}")
            if hasattr(err.response, 'text'):
                logger.error(f"Response text: {err.response.text}")
            return False

    def create_test_student(self, phone: str, name: str, email: str) -> Optional[Dict]:
        """Create a test student account"""
        try:
            first_name, last_name = name.split(' ', 1)
            url = f"{self.base_url}/api/v1/users/add-test"
            payload = {
                "email": email,
                "first_name": first_name,
                "last_name": last_name,
                "phone_number": phone
            }
            
            logger.info(f"Attempting to create test student with payload: {payload}")
            response = requests.post(url, json=payload, headers=self.headers)
            response.raise_for_status()
            
            student_data = response.json().get('data', {}).get('data', {})
            logger.info(f"Successfully created test student with ID: {student_data.get('id')}")
            return student_data
        except requests.exceptions.RequestException as err:
            logger.error(f"Error creating test student: {err}")
            if hasattr(err.response, 'text'):
                logger.error(f"Response text: {err.response.text}")
            return None

    def update_db_with_new_id(self, phone: str, new_id: str) -> bool:
        """Update the database with the new student ID"""
        connection = self.get_db_connection()
        try:
            cursor = connection.cursor()
            query = """
                UPDATE stage_student_accounts 
                SET id = %s 
                WHERE phone = %s
            """
            cursor.execute(query, (new_id, phone))
            connection.commit()
            logger.info(f"Successfully updated database with new ID {new_id} for phone {phone}")
            return True
        except mysql.connector.Error as err:
            logger.error(f"Error updating database: {err}")
            connection.rollback()
            return False
        finally:
            cursor.close()
            connection.close()

    def enroll_user_to_batch(self, user_id: str, name: str, phone: str, email: str) -> bool:
        """Enroll a user into the cleanup batch"""
        try:
            url = f"{self.base_url}/api/v1/users/enroll-test"
            params = {
                "studentId": user_id,
                "batchId": self.cleanup_batch_id,
                "courseId": self.course_id,
                "phaseId": self.phase_id
            }
            
            logger.info(f"Attempting to enroll user {user_id} with params: {params}")
            response = requests.post(url, params=params, headers=self.headers)
            response.raise_for_status()
            
            logger.info(f"Successfully enrolled user {user_id} to cleanup batch")
            return True
        except requests.exceptions.RequestException as err:
            logger.error(f"Error enrolling user {user_id}: {err}")
            if hasattr(err.response, 'text'):
                logger.error(f"Response text: {err.response.text}")
            
            # Mark the user as expired in the database
            connection = self.get_db_connection()
            try:
                cursor = connection.cursor()
                query = """
                    UPDATE stage_student_accounts 
                    SET expired = true 
                    WHERE id = %s
                """
                cursor.execute(query, (user_id,))
                connection.commit()
                logger.info(f"Marked user {user_id} as expired in database")
            except mysql.connector.Error as db_err:
                logger.error(f"Error marking user as expired: {db_err}")
                connection.rollback()
            finally:
                cursor.close()
                connection.close()
            
            return False

    def process_users(self):
        """Main process to enroll all users to cleanup batch"""
        try:
            users = self.get_all_users()
            success_count = 0
            failure_count = 0
            
            for user in users:
                if self.enroll_user_to_batch(user['id'], user['name'], user['phone'], user['email']):
                    success_count += 1
                else:
                    failure_count += 1
            
            logger.info(f"Process completed. Success: {success_count}, Failures: {failure_count}")
            
        except Exception as err:
            logger.error(f"Error in main process: {err}")
            raise

def main():
    # Get environment from command line argument or default to stage
    env = os.getenv('ENV', 'stage')
    logger.info(f"Running script in {env} environment")
    
    try:
        manager = UserEnrollmentManager(env)
        manager.process_users()
    except Exception as err:
        logger.error(f"Script failed: {err}")
        exit(1)

if __name__ == "__main__":
    main() 