import { SecretsManager } from '@aws-sdk/client-secrets-manager';
import { fromSSO } from '@aws-sdk/credential-provider-sso';
import { STSClient, AssumeRoleCommand } from '@aws-sdk/client-sts';

type Environment = 'prod' | 'stage';
type SecretValue = string;
interface Secrets {
    [key: string]: SecretValue;
}

interface SecretConfig {
    secretArn: string;
}

interface EnvSecrets {
    stage: {
        MEETING_ACCESS_TOKEN: string;
        ADMIN_ACCESS_TOKEN: string;
        ADMIN_REFRESH_TOKEN: string;
    };
    prod: {
        ADMIN_ACCESS_TOKEN: string;
    };
}

type SecretKeys = keyof (EnvSecrets['stage'] & EnvSecrets['prod']);

class SecretsService {
    private static instance: SecretsService;
    private secretsManager: SecretsManager | null = null;
    private secretCache: Map<Environment, { value: Secrets; timestamp: number }>;
    private readonly CACHE_TTL = 3600000; // 1 hour
    private readonly REGION = 'ap-south-1';

    private readonly JENKINS_ROLE_ARN = 'arn:aws:iam::646289650353:role/jenkins_assume_role';
    private readonly GITHUB_ROLE_ARN = 'arn:aws:iam::646289650353:role/github-action-secret-role';

    private readonly secretConfigs: Record<Environment, SecretConfig> = {
        stage: {
            secretArn: 'arn:aws:secretsmanager:ap-south-1:646289650353:secret:allen-web-automation/stage/access-tokens-yud89p'
        },
        prod: {
            secretArn: 'arn:aws:secretsmanager:ap-south-1:646289650353:secret:allen-web-automation/prod/access-tokens-MhnQbR'
        }
    };

    private constructor() {
        this.secretCache = new Map();
    }

    public static getInstance(): SecretsService {
        if (!SecretsService.instance) {
            SecretsService.instance = new SecretsService();
        }
        return SecretsService.instance;
    }

    private isCI(): boolean {
        return process.env.CI === '1' || process.env.GIT_HUB === '1';
    }

    private isGitHub(): boolean {
        return process.env.GIT_HUB === '1';
    }

    private getRoleArn(): string {
        return this.isGitHub() ? this.GITHUB_ROLE_ARN : this.JENKINS_ROLE_ARN;
    }

    private async initializeCredentials(env: Environment): Promise<void> {
        const mode = this.isCI() ? 'CI/CD' : 'local';
        this.log(`Initializing credentials for ${env} in ${mode} mode`);

        if (this.isCI()) {
            const stsClient = new STSClient({ region: this.REGION });
            const response = await stsClient.send(new AssumeRoleCommand({
                RoleArn: this.getRoleArn(),
                RoleSessionName: `playwright-test-${env}-${Date.now()}`,
                DurationSeconds: 3600
            }));

            if (!response.Credentials) {
                throw new Error('No credentials returned from assume role');
            }

            this.secretsManager = new SecretsManager({
                region: this.REGION,
                credentials: {
                    accessKeyId: response.Credentials.AccessKeyId!,
                    secretAccessKey: response.Credentials.SecretAccessKey!,
                    sessionToken: response.Credentials.SessionToken
                }
            });
        } else {
            this.secretsManager = new SecretsManager({
                region: this.REGION,
                credentials: fromSSO()
            });
        }
    }

    public async initialize(env: Environment): Promise<void> {
        await this.initializeCredentials(env);
        await this.setSecretsToEnv(env);
    }

    private isCacheValid(env: Environment): boolean {
        const cached = this.secretCache.get(env);
        return !!cached && Date.now() - cached.timestamp < this.CACHE_TTL;
    }

    private async fetchSecrets(env: Environment): Promise<Secrets> {
        if (!this.secretsManager) {
            throw new Error('SecretsManager not initialized. Call initialize first.');
        }

        if (this.isCacheValid(env)) {
            this.log(`Using cached secrets for ${env}`);
            return this.secretCache.get(env)!.value;
        }

        try {
            const secretArn = this.secretConfigs[env].secretArn;
            this.log(`Fetching secrets from ARN: ${secretArn}`);

            const data = await this.secretsManager.getSecretValue({ SecretId: secretArn });

            if (!data.SecretString) {
                throw new Error(`No secret string found for ARN: ${secretArn}`);
            }

            const secrets: Secrets = JSON.parse(data.SecretString);

            this.secretCache.set(env, {
                value: secrets,
                timestamp: Date.now()
            });

            this.log(`Fetched secrets with keys: ${Object.keys(secrets).join(', ')}`);
            return secrets;
        } catch (error) {
            this.handleError(error, env);
            throw error;
        }
    }

    public async setSecretsToEnv(env: Environment): Promise<void> {
        try {
            this.log(`Setting secrets for ${env}`);

            // Set environment variables based on environment type
            process.env.ADMIN_ACCESS_TOKEN = process.env.ADMIN_ACCESS_TOKEN || 
                await this.getSecret('ADMIN_ACCESS_TOKEN', env);

            if (env === 'stage') {
                process.env.MEETING_ACCESS_TOKEN = process.env.MEETING_ACCESS_TOKEN || 
                    await this.getSecret('MEETING_ACCESS_TOKEN', env);
                process.env.ADMIN_REFRESH_TOKEN = process.env.ADMIN_REFRESH_TOKEN || 
                    await this.getSecret('ADMIN_REFRESH_TOKEN', env);
            }

            console.log(`✅ Successfully set ${env} environment secrets`);
        } catch (error) {
            console.error(`❌ Failed to set secrets for ${env}:`, error);
            throw error;
        }
    }

    private async getSecret(key: SecretKeys, env: Environment): Promise<string> {
        const secrets = await this.fetchSecrets(env);
        if (!(key in secrets)) {
            throw new Error(`Secret key '${key}' not found in ${env}`);
        }
        return secrets[key];
    }

    private log(message: string) {
        if (process.env.DEBUG === 'true') {
            console.log(`🔍 [SecretsService]: ${message}`);
        }
    }

    private handleError(error: unknown, env: Environment) {
        if (error instanceof Error) {
            console.error('❌ Secret fetch error:', {
                message: error.message,
                name: error.name,
                env
            });
        } else {
            console.error('❌ Unknown error during secret fetch:', error);
        }
    }
}

export const secretsService = SecretsService.getInstance();

const ENV_SECRETS: Record<Environment, string[]> = {
    stage: ['MEETING_ACCESS_TOKEN', 'ADMIN_ACCESS_TOKEN', 'ADMIN_REFRESH_TOKEN'],
    prod: ['ADMIN_ACCESS_TOKEN']
} as const;

function areRequiredSecretsSet(environment: Environment): boolean {
    return ENV_SECRETS[environment].every(key => !!process.env[key]);
}

export async function initializeSecrets(environment: Environment) {
    try {
        console.log(`🔐 Checking secrets for ${environment}...`);
        
        if (areRequiredSecretsSet(environment)) {
            console.log('✅ Required environment variables are already set, skipping AWS Secrets Manager');
            return;
        }

        console.log(`🔐 Fetching secrets from AWS for ${environment}...`);
        await secretsService.initialize(environment);
    } catch (error) {
        console.error('❌ Failed to initialize secrets:', error);
        process.exit(1);
    }
}