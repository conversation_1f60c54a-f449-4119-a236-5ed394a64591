#!/usr/bin/env python3

import mysql.connector
import requests
import os
from dotenv import load_dotenv
import logging
from typing import Dict, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class StudentIDUpdater:
    def __init__(self, env: str = 'stage'):
        self.env = env
        self.base_url = 'https://bff.allen-stage.in' if env=="stage" else "https://api.allen-live.in"
        self.db_config = {
            'host': '***********',
            'user': 'automation_user',
            'password': 'automation@password123',
            'database': 'allen_automation_test_db'
        }
        self.headers = {
            'x-client-type': 'web',
            'x-device-id': '0e752b19-2866-46c6-afbc-3d6f20397903',
            'Content-Type': 'application/json'
        }

    def get_db_connection(self):
        """Create and return a database connection"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            return connection
        except mysql.connector.Error as err:
            logger.error(f"Error connecting to database: {err}")
            raise

    def get_student_accounts(self, connection) -> list:
        """Fetch student accounts from database"""
        cursor = connection.cursor(dictionary=True)
        table_name = 'stage_student_accounts' if self.env == 'stage' else 'prod_student_accounts'
        
        try:
            cursor.execute(f"""
                SELECT id, phone, name, email
                FROM {table_name} 
            """)
            return cursor.fetchall()
        except mysql.connector.Error as err:
            logger.error(f"Error fetching student accounts: {err}")
            raise
        finally:
            cursor.close()

    def create_test_student(self, phone: str, name: str, email: str) -> Optional[Dict]:
        """Create a test student account"""
        try:
            first_name, last_name = name.split(' ', 1)
            response = requests.post(
                f"{self.base_url}/api/v1/users/add-test",
                json={
                    "email": email,
                    "first_name": first_name,
                    "last_name": last_name,
                    "phone_number": phone
                },
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json().get('data', {}).get('data', {})
            else:
                logger.error(f"Failed to create test student for phone {phone}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating test student for phone {phone}: {str(e)}")
            return None

    def reset_password(self, phone: str) -> Optional[str]:
        """Reset password for the account"""
        try:
            # Step 1: Initial login attempt
            login_response = requests.post(
                f"{self.base_url}/api/v1/auth/username",
                json={
                    "username": phone,
                    "password": "********",  # Initial password format (DDMMYYYY)
                    "persona_type": "STUDENT"
                },
                headers=self.headers
            )
            
            if login_response.status_code != 200:
                logger.error(f"Initial login failed for phone {phone}: {login_response.status_code}")
                return None
                
            login_data = login_response.json()
            transaction_id = login_data.get('data', {}).get('transaction_id')
            
            if not transaction_id:
                logger.error(f"No transaction_id in login response for phone {phone}")
                return None
                
            # Step 2: Reset password
            reset_response = requests.post(
                f"{self.base_url}/api/v1/user/password/reset",
                json={
                    "transaction_id": transaction_id,
                    "password": phone  # Using phone as new password
                },
                headers=self.headers
            )
            
            if reset_response.status_code != 200:
                logger.error(f"Password reset failed for phone {phone}: {reset_response.status_code}")
                return None
                
            return transaction_id
            
        except Exception as e:
            logger.error(f"Error resetting password for phone {phone}: {str(e)}")
            return None

    def get_user_details(self, phone: str, name: str, email: str) -> Optional[Dict]:
        """Call username API to get user details"""
        try:
            if self.env == 'stage':
                # Step 1: Send OTP
                send_otp_response = requests.post(
                    f"{self.base_url}/api/v1/auth/sendOtp",
                    json={
                        "country_code": "91",
                        "phone_number": phone,
                        "persona_type": "STUDENT"
                    },
                    headers=self.headers
                )
                
                if send_otp_response.status_code != 200:
                    logger.info(f"Send OTP failed for phone {phone}, creating new account")
                    # Create new account
                    student_data = self.create_test_student(phone, name, email)
                    if not student_data:
                        return None
                        
                    # Reset password
                    if not self.reset_password(phone):
                        return None
                        
                    # Try send OTP again
                    send_otp_response = requests.post(
                        f"{self.base_url}/api/v1/auth/sendOtp",
                        json={
                            "country_code": "91",
                            "phone_number": phone,
                            "persona_type": "STUDENT"
                        },
                        headers=self.headers
                    )
                
                transaction_id = send_otp_response.json().get('data', {}).get('transaction_id')
                if not transaction_id:
                    logger.error(f"No transaction_id in send OTP response for phone {phone}")
                    return None
                
                # Step 2: Verify OTP
                verify_otp_response = requests.post(
                    f"{self.base_url}/api/v1/auth/verifyOtp",
                    json={
                        "otp": 1111,
                        "transaction_id": transaction_id,
                    },
                    headers=self.headers
                )
                
                if verify_otp_response.status_code == 200:
                    return verify_otp_response.json().get('data', {}).get('user_data', {})
                else:
                    logger.error(f"Verify OTP failed for phone {phone}: {verify_otp_response.status_code}")
                    return None
            else:
                # For prod, use username API
                response = requests.post(
                    f"{self.base_url}/api/v1/auth/username",
                    json={
                        "username": phone,
                        "password": phone,  # Using phone as password for test accounts
                        "persona_type": "STUDENT"
                    },
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    return response.json().get('data', {}).get('user_data', {})
                else:
                    logger.error(f"API call failed for phone {phone}: {response.status_code}")
                    return None
                
        except Exception as e:
            logger.error(f"Error getting user details for phone {phone}: {str(e)}")
            return None

    def update_student_id(self, connection, phone: str, new_id: str):
        """Update student ID in database"""
        cursor = connection.cursor()
        table_name = 'stage_student_accounts' if self.env == 'stage' else 'prod_student_accounts'
        
        try:
            cursor.execute(f"""
                UPDATE {table_name}
                SET id = %s
                WHERE phone = %s
            """, (new_id, phone))
            connection.commit()
            logger.info(f"Updated ID for phone {phone}: id={new_id}")
        except mysql.connector.Error as err:
            connection.rollback()
            logger.error(f"Error updating student ID for phone {phone}: {err}")
            raise
        finally:
            cursor.close()

    def process_student_accounts(self):
        """Main process to update student IDs"""
        connection = self.get_db_connection()
        try:
            students = self.get_student_accounts(connection)
            logger.info(f"Found {len(students)} student accounts to process")
            
            for student in students:
                user_details = self.get_user_details(student['phone'], student['name'], student['email'])
                if user_details:
                    new_id = user_details.get('id')
                    
                    if new_id:
                        self.update_student_id(connection, student['phone'], new_id)
                    else:
                        logger.warning(f"Missing ID for phone {student['phone']}")
                else:
                    logger.warning(f"Could not get user details for phone {student['phone']}")
                    
        except Exception as e:
            logger.error(f"Error processing student accounts: {str(e)}")
        finally:
            connection.close()

def main():
    # Allow specifying environment via command line argument
    env = os.getenv('ENV', 'stage')
    updater = StudentIDUpdater(env)
    updater.process_student_accounts()

if __name__ == "__main__":
    main() 