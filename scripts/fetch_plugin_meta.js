const fs = require('fs');
const path = require('path');

try {
  // Read the plugin-reporter-meta.json file
  const reportMetaPath = path.join(process.cwd(), 'plugin-reporter-meta.json');
  const reportMeta = JSON.parse(fs.readFileSync(reportMetaPath, 'utf8'));
  if (reportMeta && reportMeta.id) {
    console.log(`${reportMeta.id}`);
  } else {
    throw new Error('Report ID not found in plugin-reporter-meta.json');
  }
} catch (error) {
  console.error('Error reading report metadata:', error);
  process.exit(1);
}