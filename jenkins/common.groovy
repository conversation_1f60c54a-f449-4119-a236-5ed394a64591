def call(Map config) {
    pipeline {
        agent {
            kubernetes {
                inheritFrom 'k8s-agent-with-injection'
                yaml libraryResource('qa-pod-template.yaml')
            }
        }

        options {
            buildDiscarder(logRotator(numToKeepStr: '500', artifactNumToKeepStr: '500'))
        }

        parameters {
            string(
                defaultValue: '',
                description: 'slack channel id for individual service',
                name: 'SERVICE_SLACK_CHANNEL'
            )
            string(
                defaultValue:'NA',
                description: 'Service Name',
                name: 'SERVICE_NAME'
            )
            string(
                defaultValue:'',
                description:'Enter Dynamic headers in key:value format, separated by ; e.g. key1:value1;key2:value2',
                name: 'DYNAMIC_HEADERS'
            )
            string(
                defaultValue:'1',
                description:'Enter the number of times you want to repeat the tests',
                name: 'EACH_TEST_REPEAT_COUNT'
            )
        }

        environment {
            ENVIRONMENT = "${config.environment}"
            SLACK_CHANNEL = "${config.slackChannel}"
            CI = "${config.ci}"
            PROD = "${config.isProd}"
            TEST_TYPE = "${config.testType}"
            API_TEST_PATH = "${config.apiTestPath}"
            UI_TEST_PATH = "${config.uiTestPath}"
            TORCH_URL = 'http://torch-service.torch.svc.cluster.local'
            NPM_TOKEN = credentials('NPM_TOKEN_NEW')
            DYNAMIC_HEADERS = "${params.DYNAMIC_HEADERS ?: ''}"
            GENERATE_CURL_COMMANDS = "${config.generateCurlCommands ?: 'false'}"
            EACH_TEST_REPEAT_COUNT = "${params.EACH_TEST_REPEAT_COUNT ?: '1'}"
        }

        stages {
            stage('Echo Parameters') {
                steps {
                    echo "Selected environment: ${env.ENVIRONMENT}"
                    echo "Service Slack channel: ${params.SERVICE_SLACK_CHANNEL ?: 'Not provided'}"
                }
            }
            
            stage('Install Dependencies') {
                steps {
                    sh '''
                        npm config set //registry.npmjs.org/:_authToken=${NPM_TOKEN}
                        npm ci || (npm install && npm ci)
                        # Check if Chrome is installed
                        if ! which google-chrome > /dev/null; then
                            echo "Chrome not found. Installing Chrome..."
                            npx playwright install chrome
                            npx playwright install-deps chrome
                        else
                            echo "Chrome is already installed: $(google-chrome --version)"
                        fi                    
                    '''
                }
            }

            stage('api-tests') {
                environment {
                    TORCH_SUITE_ID = '6773bd675553744abb97f46e'
                }
                when {
                    expression { 
                        env.JOB_NAME.contains('allen-api-automation') || 
                        env.JOB_NAME.contains('sec-team-allen-web-dast') ||
                        env.TEST_TYPE == 'API'
                    }
                }
                steps {
                    script {
                        try {
                            def (projectName, buildNumber) = config.utils.getUpstreamInfo()
                            env.UPSTREAM_PROJECT_NAME = "${projectName}"
                            env.UPSTREAM_BUILD_NUMBER = "${buildNumber}"
                            def testPath = env.API_TEST_PATH ?: 'tests/api-tests/'                            
                            echo "Executing API tests with --repeat-each=${env.EACH_TEST_REPEAT_COUNT}"
                            sh "npx playwright test ${testPath} --repeat-each=${env.EACH_TEST_REPEAT_COUNT}"
                        } catch (Exception e) {
                            currentBuild.result = 'FAILURE'
                            echo "Error: ${e}"
                        }
                    }
                }
            }

            stage('e2e-tests') {
                environment {
                    TORCH_SUITE_ID = '6761720fae258e016d07ec6d'
                }
                when {
                    expression { 
                        env.JOB_NAME.contains('allen-web-automation') || 
                        env.JOB_NAME.contains('allen-automation-prod-meetings') ||
                        env.JOB_NAME.contains('qa-team-allen-web') ||
                        env.TEST_TYPE == 'WEB'
                    }
                }
                steps {
                    script {
                        try {
                            def (projectName, buildNumber) = config.utils.getUpstreamInfo()
                            env.UPSTREAM_PROJECT_NAME = "${projectName}"
                            env.UPSTREAM_BUILD_NUMBER = "${buildNumber}"
                            def testPath = env.UI_TEST_PATH ?: 'tests/ui-tests/'                            
                            echo "Executing E2E tests with --repeat-each=${env.EACH_TEST_REPEAT_COUNT}"
                            sh "npx playwright test ${testPath} --repeat-each=${env.EACH_TEST_REPEAT_COUNT}"
                        } catch (Exception e) {
                            currentBuild.result = 'FAILURE'
                            echo "Error: ${e}"
                        }
                    }
                }
            }

            stage('Parse Test Results') {
                steps {
                    script {
                        sh '''
                            pwd
                            ls
                            ls test-results
                        '''
                        def jsonFile = readJSON file: 'test-results/play-report.json'

                        def passedTests = jsonFile.stats.expected ?: 0
                        def failedTests = jsonFile.stats.unexpected ?: 0
                        def flakyTests = jsonFile.stats.flaky ?: 0
                        def skippedTests = jsonFile.stats.skipped ?: 0
                        def totalTests = passedTests + failedTests + flakyTests + skippedTests

                        echo "Total Tests: ${totalTests}, Passed: ${passedTests}, Failed: ${failedTests}, Flaky: ${flakyTests}, Skipped: ${skippedTests}"

                        env.TOTAL_TESTS = totalTests
                        env.PASSED_TESTS = passedTests
                        env.FAILED_TESTS = failedTests
                        env.FLAKY_TESTS = flakyTests
                        env.SKIPPED_TESTS = skippedTests

                        def failedDetails = []
                        def flakyDetails = []

                        jsonFile.suites.each { topSuite ->
                            if (topSuite.specs) {
                                def results = config.utils.processSpecs(topSuite, topSuite.specs)
                                failedDetails.addAll(results.failed)
                                flakyDetails.addAll(results.flaky)
                            }
                            topSuite.suites.each { innerSuite ->
                                if (innerSuite.specs) {
                                    def results = config.utils.processSpecs(innerSuite, innerSuite.specs)
                                    failedDetails.addAll(results.failed)
                                    flakyDetails.addAll(results.flaky)
                                }
                            }
                        }

                        def failures = ""
                        
                        if (!failedDetails.isEmpty()) {
                            failures += "*:rotating_light: Failed Tests:*\n"
                            failedDetails.eachWithIndex { test, index ->
                                failures += "*${index + 1}.* ${test.module.split('/')[-1]} - ${test.name}\n"
                            }
                        }

                        if (!flakyDetails.isEmpty()) {
                            failures += "\n\n*:warning: Flaky Tests:*\n"
                            flakyDetails.eachWithIndex { test, index ->
                                failures += "*${index + 1}.* ${test.module.split('/')[-1]} - ${test.name}\n"
                            }
                        }

                        env.failures = failures
                    }
                }
            }

            stage('Upload Curl to App Sec Server') {
                when {
                    expression { env.GENERATE_CURL_COMMANDS == 'true' }
                }
                steps {
                    script {
                        if (fileExists('test-results/curls.txt')) {
                            try {
                                sh '''
                                    curl -X POST \
                                    -F "file=@test-results/curls.txt" \
                                    http://*********:5000/upload
                                '''
                                echo "Successfully uploaded curls.txt to app sec server"
                            } catch (Exception e) {
                                def errorMessage = "Failed to upload curls.txt: ${e.message}"
                                echo errorMessage
                                slackSend(channel: 'C086157JNP3', message: errorMessage, tokenCredentialId: 'SLACK_TOKEN')
                            }
                        } else {
                            echo "test-results/curls.txt does not exist, skipping upload"
                        }
                    }
                }
            }
            
        }

        post {
            always {
                archiveArtifacts artifacts: 'playwright-report/**/*.*, test-results/play-report.json', fingerprint: true
            }
            success {
                script {
                    def statusMessage = config.utils.getSlackStatusMessage('success')
                    def failureMessage = env.failures + " "
                    config.utils.sendSlackNotification("#36a64f", statusMessage, failureMessage)
                }
            }
            failure {
                script {
                    def emailStatusMessage = ""
                    def statusMessage =config.utils.getSlackStatusMessage('failed')
                    def emailSubject = config.utils.getEmailSubject("Failed")
                    def failureMessage = env.failures
                    failureMessage += "\n\n\n*📝 Note:* Pipeline is not blocked. If these failures are related to your recent changes, please investigate. You can find open issues here - <https://acikota.atlassian.net/issues/?filter=10666|Jira Board>. Reach out to @test-automation-oncall for any help/queries."
                    config.utils.sendSlackNotification("#ff0000", statusMessage, failureMessage)
                    echo "enviornemnt in failure ${env.ENVIRONMENT}"
                    if((env.ENVIRONMENT == 'prod')) {
                        echo "Inside failure send email"
                        emailStatusMessage = "<b>${emailSubject}</b>"
                        def emailTo = "<EMAIL>"
                        // def emailFrom = "Test Bot <<EMAIL>>"
                        emailext(
                            to: emailTo,
                            // from: emailFrom,
                            subject: emailSubject,
                            body:config.utils.buildEmailMessage("#ff0000", emailStatusMessage, failureMessage), 
                            mimeType: 'text/html'
                        ) 
                    }
                }
            }
        }
    }
}
return this