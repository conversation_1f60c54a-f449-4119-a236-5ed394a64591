def processSpecs(suite, specs) {
    def localFailedDetails = []
    def localFlakyDetails = []
    specs.each { spec ->
        spec.tests.each { test ->
            if (test.status == 'unexpected') {
                localFailedDetails.add([
                    name: spec.title,
                    module: suite.file,
                ])
            }
            else if (test.status == 'flaky') {
                localFlakyDetails.add([
                    name: spec.title,
                    module: suite.file,
                ])
            }
        }
    }
    return [failed: localFailedDetails, flaky: localFlakyDetails]
}

def sendSlackNotification(String statusColor, String statusText, String failureMessage) {
    def (upstreamProjectName, upstreamBuildNumber) = getUpstreamInfo()
    def triggeredBy = currentBuild.getBuildCauses().any { it.shortDescription.contains("Started by timer") } ? "Timer" : (env.BUILD_USER ?: "Unknown User")
    def totalTimeInMillis = currentBuild.duration

    def totalSeconds = (totalTimeInMillis / 1000).intValue()
    def minutes = (totalSeconds / 60).intValue()
    def seconds = (totalSeconds % 60).intValue()

    def formattedTime = "\t\t\t\t\t:clock3: ${minutes}m ${seconds}s"
    def upstreamDetails = ""
    if (upstreamProjectName) {
        upstreamDetails = "\n*Upstream Job:* ${upstreamProjectName} *Build:* ${upstreamBuildNumber}"
    }

    // Extract Cocoon ID if present in DYNAMIC_HEADERS
    def cocoonId = "N/A"
    if (env.DYNAMIC_HEADERS?.contains("cocoon")) {
        // Format expected: baggage:cocoon=co-496063
        def cocoonPattern = /.*cocoon=([^;]+).*/
        def matcher = env.DYNAMIC_HEADERS =~ cocoonPattern
        if (matcher.matches()) {
            cocoonId = matcher[0][1]
        }
    }

    def attachment = [
        color: "${statusColor}",
        blocks: [
            [
                type: "section",
                text: [
                    type: "mrkdwn",
                    text: "${statusText}${formattedTime}${upstreamDetails}"
                ]
            ],
            [
                type: "divider"
            ],
            [
                type: "section",
                fields: [
                    [
                        type: "mrkdwn",
                        text: env.DYNAMIC_HEADERS?.contains("cocoon") ? "🔖 *Cocoon:* ${cocoonId}" : "🔖 *Application:* ${params.SERVICE_NAME}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "🌐 *Environment:* ${env.ENVIRONMENT?.toUpperCase()}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "📊 *Total Tests:* ${env.TOTAL_TESTS}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "✅ ${env.PASSED_TESTS} | ❌ ${env.FAILED_TESTS} | ⚠️ ${env.FLAKY_TESTS} | ⏭️ ${env.SKIPPED_TESTS}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "👤 *Triggered By:* ${triggeredBy}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "📋 *Report Link:* <${env.BUILD_URL}artifact/playwright-report/index.html|Open in Jenkins>"
                    ]
                ]
            ],
            [
                type: "divider"
            ],
            [
                type: "section",
                text: [
                    type: "mrkdwn",
                    text: "${failureMessage}"
                ]
            ]
        ]
    ]

    echo "${attachment.toString()}"
    def attachmentsArray = [attachment]

    slackSend(channel: env.SLACK_CHANNEL, attachments: attachmentsArray, tokenCredentialId: 'SLACK_TOKEN')

    if (params.SERVICE_SLACK_CHANNEL?.trim() && params.SERVICE_SLACK_CHANNEL?.trim() != env.SLACK_CHANNEL?.trim()) {
        slackSend(channel: params.SERVICE_SLACK_CHANNEL, attachments: attachmentsArray, tokenCredentialId: 'SLACK_TOKEN')
    }
}

def getUpstreamInfo() {
    def upstreamProject = []
    def upstreamBuild = []
    try {
        def causes = currentBuild.getBuildCauses()
        def causeResponse = readJSON(text: causes.toString())
        causeResponse.each { cause ->
            def project = cause.upstreamProject ?: ""
            def build = cause.upstreamBuild ?: ""
            upstreamProject.add(project)
            upstreamBuild.add(build)
        }
    } catch (Exception e) {
        echo "An error occurred while retrieving build causes: ${e.message}"
    }

    def upstreamProjectName = (upstreamProject?.findAll { it?.trim() })?.size() > 0 ? upstreamProject.join(', ') : ""
    def upstreamBuildNumber = upstreamBuild ? upstreamBuild.join(', ') : ""
    return [upstreamProjectName, upstreamBuildNumber]
}

def getEmailSubject(String testStatus) {
    def envName = env.ENVIRONMENT?.toUpperCase()
    def jobName = env.JOB_NAME
    def executionType = ""
    switch (true) {
        case jobName.contains('allen-api-automation'):
            executionType = 'Test Execution (E2E API)'
            break
        case jobName.contains('allen-automation-prod-meetings'):
            executionType = 'Prod meeting joining test'
            break
        default:
            executionType = 'Test Execution (E2E Web Sanity)'
            break
    }
    return params.SERVICE_NAME == 'NA' ? 
        "[${envName}] ${executionType}: ${testStatus}" : 
        "[${envName}] ${executionType}: ${testStatus} | ${params.SERVICE_NAME}"
}

def getSlackStatusMessage(String status) {
    def icon = status == 'success' ? '✅' : ':rotating_light:'
    def statusText = status == 'success' ? 'Completed successfully' : 'Failed'
    
    if (env.JOB_NAME.contains('allen-api-automation') || env.JOB_NAME.contains('appsec') || env.TEST_TYPE == 'API') {
        return "${icon} *Test Execution (E2E API):* ${statusText}"
    } 
    else if (env.JOB_NAME.contains('allen-automation-prod-meetings')) {
        return "${icon} *Test Execution (Meeting Joining test):* ${statusText}"
    }
    else {
        return "${icon} *Test Execution (E2E Web Sanity):* ${statusText}"
    }
}

def buildEmailMessage(String statusColor, String statusText, String failureMessage) {
    // def (upstreamProjectName, upstreamBuildNumber) = getUpstreamInfo()
    def triggeredBy = currentBuild.getBuildCauses().any { it.shortDescription.contains("Started by timer") } ? "Timer" : (env.BUILD_USER ?: "Unknown User")
    def upstreamDetails = ""
    if (env.UPSTREAM_PROJECT_NAME) {
        upstreamDetails = "<b>Upstream Job:</b> ${env.UPSTREAM_PROJECT_NAME} <b>Build:</b> ${env.UPSTREAM_BUILD_NUMBER}"
    }

   def emailContent = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Test Execution Report</title>
        </head>
        <body>
            <table class="email" 
                width="100%"
                border= 0;
                cellspacing=0;
                cellpadding="20" 
                style="border-bottom-width: 10px;
                border-bottom-style: solid;
                border-bottom-color: #ff665e">
                <tr>
                    <td class="header" 
                        style="background-color: #ff665e;">  
                        <table border="0" 
                            style="color: #fff; 
                            width: 600px; 
                            margin: 0 auto; 
                            font-family: Arial, Helvetica, sans-serif;" 
                            cellspacing="0"  width="600">
                            <tr>
                                <td colspan="2">
                                    <h1>${statusText}</h1>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="content" 
                        style="background-color: #eee;">
                        <table border="0" 
                            style="color: #444; 
                            width: 600px; 
                            margin: 0 auto; 
                            border-bottom-width: 1px;
                            border-bottom-style: solid;
                            border-bottom-color: #ddd;
                            font-family: Arial, Helvetica, sans-serif;
                            line-height: 1.4;" 
                              cellpadding="15" cellspacing="1"  width="600">
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Upstream Job:</strong></td>
                                <td>${env.UPSTREAM_PROJECT_NAME ? env.UPSTREAM_PROJECT_NAME : 'NA'}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Upstream Build:</strong></td>
                                <td>${env.UPSTREAM_BUILD_NUMBER ? env.UPSTREAM_BUILD_NUMBER : 'NA'}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Application:</strong></td>
                                <td>${params.SERVICE_NAME}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Environment:</strong></td>
                                <td>${env.ENVIRONMENT?.toUpperCase()}</td>
                            </tr>
                             <tr  style="background-color:#fff">
                                <td width="30%"><strong>Total Tests:</strong></td>
                                <td>${env.TOTAL_TESTS}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Test Stats:</strong></td>
                                <td>
                                    <span>✅ </span>
                                    <span>${env.PASSED_TESTS}</span> |
                                    <span>❌ </span>
                                    <span>${env.FAILED_TESTS}</span> |
                                    <span>⚠️</span>
                                    <span>${env.FLAKY_TESTS}</span> |
                                    <span>⏭️ </span>
                                    <span>${env.SKIPPED_TESTS}</span>
                                </td>
                            </tr>
                             <tr  style="background-color:#fff">
                                <td width="30%"><strong>Triggered By:</strong></td>
                                <td>${triggeredBy}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Report Link:</strong></td>
                                <td><a href="${env.BUILD_URL}artifact/playwright-report/index.html">Open in Jenkins</a></td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Failed / Flaky Tests:</strong></td>
                                <td>
                                    <ul>
                                        ${failureMessage.split('\n')
                                            .findAll { line -> 
                                                line.trim() &&
                                                !line.contains('*:rotating_light: Failed Tests:*') &&
                                                !line.contains('*:warning: Flaky Tests:*') &&
                                                !line.contains('*📝 Note:*') &&
                                                !line.contains('Pipeline is not blocked')
                                            }
                                            .collect { line -> 
                                                "<li>${line.replaceAll(/^\*\d+\.\*\s*/, '')}</li>"
                                            }.join('\n')}
                                    </ul> 
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <div class="footer">
                This is an auto-generated email by Jenkins.
            </div>
        </body>
        </html> 
    """

    return emailContent
}

return this