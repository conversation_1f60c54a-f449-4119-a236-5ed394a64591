@Library('Allen_Shared_Libraries') _
pipeline {
  agent {
    kubernetes {
      inheritFrom 'k8s-agent-with-injection'
      yaml libraryResource('qa-pod-template.yaml')
    }
  }

  options{
    buildDiscarder(logRotator(numToKeepStr: '500', artifactNumToKeepStr: '500'))
  }

  triggers {
     cron('30 2,14 * * *') // This schedule triggers the pipeline to run at 8:00 AM and 8:00 PM IST daily
  }

  parameters {
      // Environment
      choice(
          choices: ['stage', 'prod'],
          description: 'Environment to run the test',
          name: 'ENVIRONMENT'
      )
      // Password parameter
      string(
          defaultValue: '',
          description: 'slack channel id for individual service',
          name: 'SERVICE_SLACK_CHANNEL'
      )
      string(
        defaultValue:'NA',
        description: 'Service Name',
        name: 'SERVICE_NAME'
      )
      string(
        defaultValue:'',
        description:'Enter Dynamic headers in key:value format, separated by ; e.g. key1:value1;key2:value2',
        name: 'DYNAMIC_HEADERS'
      )
      string(
        defaultValue:'',
        description:'Enter Git repo and commit id key:value format. For multiple repos, separate by ; e.g. REPO_NAME:COMMIT_ID;REPO_NAME:COMMIT_ID',
        name: 'GIT_INFO'
      )
  }

  environment {
    // Define Slack channels based on job type
    SLACK_CHANNEL = """${
        params.DYNAMIC_HEADERS?.contains('cocoon') ? 'C088UN337SR' : 
        params.ENVIRONMENT == 'prod' ? 'C082366JJ9J' : 'C0760MXSHK2'
    }"""    
    //SLACK_CHANNEL = 'C086157JNP3' //#torch-plugin-test-jenkins
    CI = '1'
    PROD = "${params.ENVIRONMENT == 'prod' ? 1 : 0}"
    TORCH_URL = 'http://torch-service.torch.svc.cluster.local'
    NPM_TOKEN = credentials('NPM_TOKEN_NEW')
    GITHUB_PAT = credentials('git_pat')
    DYNAMIC_HEADERS = "${params.DYNAMIC_HEADERS}"
  }

  stages {
    stage('Echo Parameters') {
        steps {
            echo "Selected environment: ${params.ENVIRONMENT}"
            echo "Service Slack channel: ${params.SERVICE_SLACK_CHANNEL ?: 'Not provided'}"
            echo "TEST_RUN_ID = \"${env.TORCH_TEST_RUN_ID}\""
        }
    }
    
    stage('Install Dependencies') {
      steps {
        sh '''
          npm config set //registry.npmjs.org/:_authToken=${NPM_TOKEN}
          # First try npm ci, if it fails, fall back to npm install followed by npm ci
          npm ci || (npm install && npm ci)
          npx playwright install chrome
        '''
      }
    }

    // API Tests stage - only runs for API automation
    stage('api-tests') {
      environment {
        TORCH_SUITE_ID = '6773bd675553744abb97f46e'
      }
      when {
        expression { env.JOB_NAME.contains('allen-api-automation') }
      }
      steps {
        script {
          try {
            def (projectName, buildNumber) = getUpstreamInfo()
            env.UPSTREAM_PROJECT_NAME = "${projectName}"
            env.UPSTREAM_BUILD_NUMBER = "${buildNumber}"
            sh 'npx playwright test tests/api-tests/'
            echo "TEST_RUN_ID after API tests = \"${env.TORCH_TEST_RUN_ID}\""
          } catch (Exception e) {
            currentBuild.result = 'FAILURE'
            echo "Error: ${e}"
          }
        }
      }
    }

    // E2E Tests stage - only runs for web automation
    stage('e2e-tests') {
      environment {
        TORCH_SUITE_ID = '6761720fae258e016d07ec6d'
      }
      when {
        expression { env.JOB_NAME.contains('allen-web-automation') }
      }
      steps {
        script {
          try {
            def (projectName, buildNumber) = getUpstreamInfo()
            env.UPSTREAM_PROJECT_NAME = "${projectName}"
            env.UPSTREAM_BUILD_NUMBER = "${buildNumber}"
            sh 'npx playwright test tests/ui-tests/'
            echo "TEST_RUN_ID after E2E tests = \"${env.TORCH_TEST_RUN_ID}\""
          } catch (Exception e) {
            currentBuild.result = 'FAILURE'
            echo "Error: ${e}"
          }
        }
      }
    }

    stage('Extract Plugin Report Id') {
      steps {
        script {
          try{
            def reportId = sh(script: 'node scripts/fetch_plugin_meta.js', returnStdout: true).trim()
            env.PLUGIN_REPORT_ID = reportId
            echo "Jenkins Stage Report ID: ${env.PLUGIN_REPORT_ID}"
          }
          catch(Exception e){
            echo "Error: ${e}"
          }
        }
      }
    }

    stage('Parse Test Results') {
      steps {
        script {
          // Read the JSON file into a variable
          sh '''
            pwd
            ls
            ls test-results
          '''
          def jsonFile = readJSON file: 'test-results/play-report.json'

          // Extract values from the JSON structure
          def passedTests = jsonFile.stats.expected ?: 0
          def failedTests = jsonFile.stats.unexpected ?: 0
          def flakyTests = jsonFile.stats.flaky ?: 0
          def skippedTests = jsonFile.stats.skipped ?: 0
          def totalTests = passedTests + failedTests + flakyTests + skippedTests

          echo "Total Tests: ${totalTests}, Passed: ${passedTests}, Failed: ${failedTests}, Flaky: ${flakyTests}, Skipped: ${skippedTests}"

          env.TOTAL_TESTS = totalTests
          env.PASSED_TESTS = passedTests
          env.FAILED_TESTS = failedTests
          env.FLAKY_TESTS = flakyTests
          env.SKIPPED_TESTS = skippedTests

          // Initialize lists to collect failed and flaky test details
          def failedDetails = []
          def flakyDetails = []

          // Process the JSON structure with nested suites
          jsonFile.suites.each { topSuite ->
              // Process specs at the top level if they exist
              if (topSuite.specs) {
                  def results = processSpecs(topSuite, topSuite.specs)
                  failedDetails.addAll(results.failed)
                  flakyDetails.addAll(results.flaky)
              }
              // Process nested suites
              topSuite.suites.each { innerSuite ->
                  if (innerSuite.specs) {
                      def results = processSpecs(innerSuite, innerSuite.specs)
                      failedDetails.addAll(results.failed)
                      flakyDetails.addAll(results.flaky)
                  }
              }
          }
          def failures = ""
          
          if (!failedDetails.isEmpty()) {
              failures += "*:rotating_light: Failed Tests:*\n"

              def topFailedDetails = []
              failedDetails.eachWithIndex { test, index ->
                topFailedDetails << "*${index + 1}.* ${test.module.split('/')[-1]} - ${test.name}"
              }

              failures += topFailedDetails.join("\n")
          }

          if (!flakyDetails.isEmpty()) {
              failures += "\n\n\n*:warning: Flaky Tests:*\n"

              def topFlakyDetails = []
              flakyDetails.eachWithIndex { test, index ->
                topFlakyDetails << "*${index + 1}.* ${test.module.split('/')[-1]} - ${test.name}"
              }

              failures += topFlakyDetails.join("\n")
          }
          env.failures = failures
        }
      }
    }
  }

  post {
    always {
      archiveArtifacts artifacts: 'playwright-report/**/*.*, test-results/play-report.json', fingerprint: true
    }
    success {
      script {
        def statusMessage = "${env.JOB_NAME.contains('allen-api-automation') ? '✅ *Test Execution (E2E API):* Completed successfully' : '✅ *Test Execution (E2E Web Sanity):* Completed successfully'}"
        def failureMessage = env.failures + " "
        sendSlackNotification("#36a64f", statusMessage, failureMessage)
        if (params.GIT_INFO?.trim()) {
          updateGitHubStatus("success")
        }
      }
    }
    failure {
      script {
        def emailStatusMessage = ""
        def statusMessage = getSlackStatusMessage('failed')
        def emailSubject = getEmailSubject("Failed")
        def failureMessage = env.failures
        def noteMessage = "*📝 Note:* Pipeline is not blocked. If these failures are related to your recent changes, please investigate. You can find open issues here - <https://acikota.atlassian.net/issues/?filter=10666|Jira Board>. Reach out to @test-automation-oncall for any help/queries."
        failureMessage = truncateText(failureMessage ?: "", noteMessage)
        sendSlackNotification("#ff0000", statusMessage, failureMessage)
        if (params.GIT_INFO?.trim()) {
          updateGitHubStatus("failure")
        }
        echo "enviornemnt in failure ${env.ENVIRONMENT}"
        if((env.ENVIRONMENT == 'prod')) {
          echo "Inside failure send email"
          emailStatusMessage = "<b>${emailSubject}</b>"
          def emailTo = "<EMAIL>"
          // def emailFrom = "Test Bot <<EMAIL>>"
          emailext(
            to: emailTo,
            // from: emailFrom,
            subject: emailSubject,
            body:buildEmailMessage("#ff0000", emailStatusMessage, failureMessage), 
            mimeType: 'text/html'
          ) 
        }
        
      }
    }
  }
}

def processSpecs(suite, specs) {
    def localFailedDetails = []
    def localFlakyDetails = []
    specs.each { spec ->
        spec.tests.each { test ->
            // Collect failed and flaky test details
            // find if test is failed if status is unexpected
            if (test.status == 'unexpected') {
                localFailedDetails.add([
                    name: spec.title,
                    module: suite.file,
                ])
            }
            // find if test is flaky if status is flaky
            else if (test.status == 'flaky') {
                localFlakyDetails.add([
                    name: spec.title,
                    module: suite.file,
                ])
            }
        }
    }
    return [failed: localFailedDetails, flaky: localFlakyDetails]
}

def sendSlackNotification(String statusColor, String statusText, String failureMessage) {
    def (upstreamProjectName, upstreamBuildNumber) = getUpstreamInfo()
    def triggeredBy = currentBuild.getBuildCauses().any { it.shortDescription.contains("Started by timer") } ? "Timer" : (env.BUILD_USER ?: "Unknown User")
    def totalTimeInMillis = currentBuild.duration

    def totalSeconds = (totalTimeInMillis / 1000).intValue()
    def minutes = (totalSeconds / 60).intValue()
    def seconds = (totalSeconds % 60).intValue()

    def formattedTime = "\t\t\t\t\t:clock3: ${minutes}m ${seconds}s"
    def upstreamDetails = ""
    if (upstreamProjectName) {
        upstreamDetails = "\n*Upstream Job:* ${upstreamProjectName} *Build:* ${upstreamBuildNumber}"
    }

    // Extract Cocoon ID if present in DYNAMIC_HEADERS
    def cocoonId = "N/A"
    if (env.DYNAMIC_HEADERS?.contains("cocoon")) {
        // Format expected: baggage:cocoon=co-496063
        def cocoonPattern = /.*cocoon=([^;]+).*/
        def matcher = env.DYNAMIC_HEADERS =~ cocoonPattern
        if (matcher.matches()) {
            cocoonId = matcher[0][1]
        }
    }

    def attachment = [
        color: "${statusColor}",
        blocks: [
            [
                type: "section",
                text: [
                    type: "mrkdwn",
                    text: "${statusText}${formattedTime}${upstreamDetails}"
                ]
            ],
            [
                type: "divider"
            ],
            [
                type: "section",
                fields: [
                    [
                        type: "mrkdwn",
                        text: env.DYNAMIC_HEADERS?.contains("cocoon") ? "🔖 *Cocoon:* ${cocoonId}" : "🔖 *Application:* ${params.SERVICE_NAME}"

                    ],
                    [
                        type: "mrkdwn",
                        text: "🌐 *Environment:* ${env.ENVIRONMENT?.toUpperCase()}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "📊 *Total Tests:* ${env.TOTAL_TESTS}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "✅ ${env.PASSED_TESTS} | ❌ ${env.FAILED_TESTS} | ⚠️ ${env.FLAKY_TESTS} | ⏭️ ${env.SKIPPED_TESTS}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "👤 *Triggered By:* ${triggeredBy}"
                    ],
                    [
                        type: "mrkdwn",
                        text: "📋 *Report Link:* <https://idp.allen-live.in/torch-service/test-run/${env.PLUGIN_REPORT_ID}|View in TORCH>"
                    ]
                ]
            ],
            [
                type: "divider"
            ],
            [
                type: "section",
                text: [
                    type: "mrkdwn",
                    text: "${failureMessage}"
                ]
            ]
        ]
    ]

    echo "${attachment.toString()}"

    def attachmentsArray = [attachment]

    slackSend(channel: env.SLACK_CHANNEL, attachments: attachmentsArray, tokenCredentialId: 'SLACK_TOKEN')

     // Send to SERVICE_SLACK_CHANNEL only if it's provided and different from primary channel
    if (params.SERVICE_SLACK_CHANNEL?.trim() && params.SERVICE_SLACK_CHANNEL?.trim() != env.SLACK_CHANNEL?.trim()){
        slackSend(channel: params.SERVICE_SLACK_CHANNEL, attachments: attachmentsArray, tokenCredentialId: 'SLACK_TOKEN')
    }
}
def getUpstreamInfo() {
    def upstreamProject = []
    def upstreamBuild = []
    try {
        def causes = currentBuild.getBuildCauses()
        // Parse JSON response
        def causeResponse = readJSON(text: causes.toString())
        causeResponse.each { cause ->
            def project = cause.upstreamProject ?: ""
            def build = cause.upstreamBuild ?: ""
            upstreamProject.add(project)
            upstreamBuild.add(build)
        }
    } catch (Exception e) {
        echo "An error occurred while retrieving build causes: ${e.message}"
    }

    def upstreamProjectName = (upstreamProject?.findAll { it?.trim() })?.size() > 0 ? upstreamProject.join(', ') : ""
    def upstreamBuildNumber = upstreamBuild ? upstreamBuild.join(', ') : ""
    return [upstreamProjectName, upstreamBuildNumber]
}

def truncateText(String text, String note, int maxLength = 2900) {
    int noteLength = note.length()
    int allowedTextLength = maxLength - noteLength
    
    if (text.length() > allowedTextLength) {
        return text.take(allowedTextLength) + "...\n\n\n" + note
    }
    return text + "\n\n\n" + note
}
def getSlackStatusMessage(String status) {
    def icon = status == 'success' ? '✅' : ':rotating_light:'
    def statusText = status == 'success' ? 'Completed successfully' : 'Failed'
    
    if (env.JOB_NAME.contains('allen-api-automation')) {
        return "${icon} *Test Execution (E2E API):* ${statusText}"
    } 
    else if (env.JOB_NAME.contains('allen-automation-prod-meetings')) {
        return "${icon} *Test Execution (Meeting Joining test):* ${statusText}"
    }
    else {
        return "${icon} *Test Execution (E2E Web Sanity):* ${statusText}"
    }
}

def getEmailSubject(String testStatus) {
    def envName = env.ENVIRONMENT?.toUpperCase()
    def jobName = env.JOB_NAME
    def executionType = ""
    switch (true) {
        case jobName.contains('allen-api-automation'):
            executionType = 'Test Execution (E2E API)'
            break
        case jobName.contains('allen-automation-prod-meetings'):
            executionType = 'Prod meeting joining test'
            break
        default:
            executionType = 'Test Execution (E2E Web Sanity)'
            break
    }
    return params.SERVICE_NAME == 'NA' ? 
        "[${envName}] ${executionType}: ${testStatus}" : 
        "[${envName}] ${executionType}: ${testStatus} | ${params.SERVICE_NAME}"
}

def buildEmailMessage(String statusColor, String statusText, String failureMessage) {
    // def (upstreamProjectName, upstreamBuildNumber) = getUpstreamInfo()
    def triggeredBy = currentBuild.getBuildCauses().any { it.shortDescription.contains("Started by timer") } ? "Timer" : (env.BUILD_USER ?: "Unknown User")
    def upstreamDetails = ""
    if (env.UPSTREAM_PROJECT_NAME) {
        upstreamDetails = "<b>Upstream Job:</b> ${env.UPSTREAM_PROJECT_NAME} <b>Build:</b> ${env.UPSTREAM_BUILD_NUMBER}"
    }

   def emailContent = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Test Execution Report</title>
        </head>
        <body>
            <table class="email" 
                width="100%"
                border= 0;
                cellspacing=0;
                cellpadding="20" 
                style="border-bottom-width: 10px;
                border-bottom-style: solid;
                border-bottom-color: #ff665e">
                <tr>
                    <td class="header" 
                        style="background-color: #ff665e;">  
                        <table border="0" 
                            style="color: #fff; 
                            width: 600px; 
                            margin: 0 auto; 
                            font-family: Arial, Helvetica, sans-serif;" 
                            cellspacing="0"  width="600">
                            <tr>
                                <td colspan="2">
                                    <h1>${statusText}</h1>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="content" 
                        style="background-color: #eee;">
                        <table border="0" 
                            style="color: #444; 
                            width: 600px; 
                            margin: 0 auto; 
                            border-bottom-width: 1px;
                            border-bottom-style: solid;
                            border-bottom-color: #ddd;
                            font-family: Arial, Helvetica, sans-serif;
                            line-height: 1.4;" 
                              cellpadding="15" cellspacing="1"  width="600">
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Upstream Job:</strong></td>
                                <td>${env.UPSTREAM_PROJECT_NAME ? env.UPSTREAM_PROJECT_NAME : 'NA'}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Upstream Build:</strong></td>
                                <td>${env.UPSTREAM_BUILD_NUMBER ? env.UPSTREAM_BUILD_NUMBER : 'NA'}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Application:</strong></td>
                                <td>${params.SERVICE_NAME}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Environment:</strong></td>
                                <td>${env.ENVIRONMENT?.toUpperCase()}</td>
                            </tr>
                             <tr  style="background-color:#fff">
                                <td width="30%"><strong>Total Tests:</strong></td>
                                <td>${env.TOTAL_TESTS}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Test Stats:</strong></td>
                                <td>
                                    <span>✅ </span>
                                    <span>${env.PASSED_TESTS}</span> |
                                    <span>❌ </span>
                                    <span>${env.FAILED_TESTS}</span> |
                                    <span>⚠️</span>
                                    <span>${env.FLAKY_TESTS}</span> |
                                    <span>⏭️ </span>
                                    <span>${env.SKIPPED_TESTS}</span>
                                </td>
                            </tr>
                             <tr  style="background-color:#fff">
                                <td width="30%"><strong>Triggered By:</strong></td>
                                <td>${triggeredBy}</td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Report Link:</strong></td>
                                <td><a href="${env.BUILD_URL}artifact/playwright-report/index.html">Open in Jenkins</a></td>
                            </tr>
                            <tr  style="background-color:#fff">
                                <td width="30%"><strong>Failed / Flaky Tests:</strong></td>
                                <td>
                                    <ul>
                                        ${failureMessage.split('\n')
                                            .findAll { line -> 
                                                line.trim() &&
                                                !line.contains('*:rotating_light: Failed Tests:*') &&
                                                !line.contains('*:warning: Flaky Tests:*') &&
                                                !line.contains('*📝 Note:*') &&
                                                !line.contains('Pipeline is not blocked')
                                            }
                                            .collect { line -> 
                                                "<li>${line.replaceAll(/^\*\d+\.\*\s*/, '')}</li>"
                                            }.join('\n')}
                                    </ul> 
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <div class="footer">
                This is an auto-generated email by Jenkins.
            </div>
        </body>
        </html> 
    """

    return emailContent
}

def updateGitHubStatus(String state) {
  echo "Updating GitHub status for ${params.GIT_INFO}"
  def errors = []

  def gitInfoEntries = params.GIT_INFO?.split(";") ?: []

  gitInfoEntries.each { entry ->
    def gitInfoParts = entry.split(":").collect { it.trim() }

    if (gitInfoParts.size() != 2) {
      def errorMsg = "Invalid GIT_INFO format for entry: ${entry}. Expected REPO_NAME:COMMIT_ID"
      echo errorMsg
      errors.add(errorMsg)
      return
    }

    def (repoName, commitId) = gitInfoParts
    def context = "ci/cocoon-tested"
    def description = state == 'success' ? "Tests Passed" : "Tests Failed"

    withCredentials([string(credentialsId: 'git_pat', variable: 'GITHUB_PAT')]) {
      try {
        sh """
          curl --location --globoff 'https://api.github.com/repos/Allen-Career-Institute/${repoName}/statuses/${commitId}' \\
            --header 'Accept: application/vnd.github+json' \\
            --header "Authorization: Bearer \$GITHUB_PAT" \\
            --header 'X-GitHub-Api-Version: 2022-11-28' \\
            --header 'Content-Type: application/json' \\
            --data '{
              "state": "${state}",
              "target_url": "${BUILD_URL}artifact/playwright-report/index.html",
              "description": "${description}",
              "context": "${context}"
            }'
        """

        echo "GitHub status update sent: ${state} for ${repoName}:${commitId}"
      } catch (Exception e) {
        def errorMsg = "Failed to update GitHub status for ${repoName}:${commitId}: ${e.message}"
        echo errorMsg
        errors.add(errorMsg)
      }
    }
  }

  if (!errors.isEmpty()) {
    def errorMessage = "*GitHub Status Update Errors:*\n" + errors.join("\n")
    slackSend(channel: env.SLACK_CHANNEL, message: errorMessage, tokenCredentialId: 'SLACK_TOKEN')
  }
}