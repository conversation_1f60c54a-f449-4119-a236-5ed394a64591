@Library('Allen_Shared_Libraries') _

node {
    checkout scm

    def common = load "${WORKSPACE}/jenkins/common.groovy"
    def utils = load "${WORKSPACE}/jenkins/utils.groovy"

    properties([
        pipelineTriggers([
            cron('30 0 * * 0')  // Run every Sunday at 6:00 AM IST
        ])
    ])

    common([
        environment: 'stage',
        slackChannel: 'C0760MXSHK2',
        isProd: '0',
        ci: '1',
        testType: 'API',
        apiTestPath: 'tests/api-tests/',
        uiTestPath: 'tests/ui-tests/',
        utils: utils,
        generateCurlCommands: 'true'
    ])
}