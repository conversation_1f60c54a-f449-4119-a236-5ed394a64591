@Library('Allen_Shared_Libraries') _

node {
    checkout scm

    def common = load "${WORKSPACE}/jenkins/common.groovy"
    def utils = load "${WORKSPACE}/jenkins/utils.groovy"

    properties([
        pipelineTriggers([
            cron('0 0 * * *')  // Run everyday at 5.30 AM IST in CI/CD
        ])
    ])

    common([
        environment: 'stage',
        slackChannel: 'C086157JNP3',
        isProd: '0',
        ci: '1',
        apiTestPath: 'tests/api-tests/',
        uiTestPath: 'tests/async-tests/specs',
        utils: utils,
        testType: 'WEB'
    ])
}