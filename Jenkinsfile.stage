@Library('Allen_Shared_Libraries') _

node {
    checkout scm

    def common = load "${WORKSPACE}/jenkins/common.groovy"
    def utils = load "${WORKSPACE}/jenkins/utils.groovy"

    properties([
        pipelineTriggers([
            // cron('30 1-23/3 * * *')  // Run every 3 hours for stage
        ])
    ])

    common([
        environment: 'stage',
        slackChannel: 'C0760MXSHK2',
        isProd: '0',
        ci: '1',
        apiTestPath: 'tests/api-tests/',
        uiTestPath: 'tests/ui-tests/',
        utils: utils 
    ])
}