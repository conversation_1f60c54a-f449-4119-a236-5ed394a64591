import { expect, slowExpect, test } from '../../../ui-tests/fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const currentEnv = EnvUtils.getInstance();
test.describe('Ecomm UI Tests', {
    tag: ['@ecomm', '@ui']
}, () => {
    if (currentEnv.isStage()) {
        test('Verify course change flow with zero rupees for offline user', async ({ adminPage }) => {
            test.setTimeout(132000);// 2.2 mins are required since some times IC pages will take time to load
            const studentFormId = '1000483877'
            const studentName = 'SAKSHAM'
            let currentStream;
            let afterStreamChangeValue;

            /* Login to ic and navigate to student management */
            await test.step(`Login to ic and navigate to student management`, async () => {
                await adminPage.pos.teacherHomePage.navigateToInternalUser();
                await adminPage.pos.internalUserHomePage.navigateToStudentManagementPage();
            });

            /* Verify filter section and create course change request in student management */
            await test.step(`Verify filter section and create course change request in student management`, async () => {
                await adminPage.pos.internalUserHomePage.verifyAndFilterForStudent(studentFormId);
                await adminPage.pos.internalUserHomePage.verifyStudentAndNavigateTocoursePage();
                currentStream = await adminPage.pos.internalUserHomePage.presentStreamValue.allInnerTexts();
                console.log("current stream " + currentStream);
                await adminPage.pos.internalUserHomePage.verifyAndCreateCourseChangeRequest();
            });

            /* Verify and approve the course change request in approver management */
            await test.step(`Verify and approve the course change request in approver management`, async () => {
                await adminPage.pos.internalUserHomePage.navigateToCourseChangeApproverPage();
                await adminPage.pos.courseChangeApprover.validateAndApproveCourseChange(studentFormId, studentName);
            });

            /* Verify course changed for the student in student management */
            await test.step(`Verify course changed for the student in student management`, async () => {
                await expect(adminPage.pos.internalUserHomePage.sidebarResourseManagementButton, "Verify sidebar resource management button is visible").toBeVisible();
                await adminPage.pos.internalUserHomePage.sidebarResourseManagementButton.click();
                await adminPage.pos.internalUserHomePage.navigateToStudentManagementPage();
                await adminPage.pos.internalUserHomePage.verifyAndFilterForStudent(studentFormId);
                await adminPage.pos.internalUserHomePage.verifyStudentAndNavigateTocoursePage();
                afterStreamChangeValue = await adminPage.pos.internalUserHomePage.presentStreamValue.allInnerTexts();
                console.log("after stream change " + afterStreamChangeValue);
                expect(currentStream, `Verified ${currentStream} is changed to ${afterStreamChangeValue}`).not.toEqual(afterStreamChangeValue);
            });
        });
    }
});