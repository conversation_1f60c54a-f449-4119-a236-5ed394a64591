import { expect, slowExpect, test } from '../../../ui-tests/fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';
import { format } from 'path';
import { PlaywrightUtils, StringUtils } from '../../../commons/common-functions';

test.describe('URM UI Tests', {
    tag: ['@URM', '@ui']
}, () => {
    /** 
     * @info Test Naming convention updated and assertion message
     */
    const currentEnv = EnvUtils.getInstance()
    if (currentEnv.isStage()) {
        /* Since phase/batch deletion are happening as soft delete currently and need to run this flow on demand basis of sanity */
        test('Verify phase management', async ({ adminPage }) => {
            const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
            const currentDate = StringUtils.getCurrentTimeWithOffset("DD-MM-YYYY", 0, false, timezone);
            const endDate = StringUtils.getCurrentTimeWithOffset("DD-MM-", 0, false, timezone) + '2026';
            const randomFourDigit = StringUtils.generateRandomFourDigits();
            const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : "";
            const streamName = process.env.JEE_STREAM_NAME ? process.env.JEE_STREAM_NAME : "";
            /*navigate to phase managent page */
            await test.step(`Navigate to phase management and validate page`, async () => {
                await adminPage.pos.teacherHomePage.navigateToInternalUser();
                await adminPage.pos.internalUserHomePage.navigateToPhaseManagementPage();
                await slowExpect(adminPage).toHaveURL(/.*phase*/);
                await slowExpect(adminPage.pos.phaseManagementPage.apiLoader, "Verify page loading").not.toBeVisible();
                await adminPage.pos.phaseManagementPage.validatePhaseManagementPage();
            });

            /* Verify and create new phase */
            await test.step(`Verify and create new phase`, async () => {
                await adminPage.pos.phaseManagementPage.verifyAndCreatePhase(randomFourDigit, randomFourDigit, currentDate, endDate, endDate, currentDate, endDate);
                await slowExpect(adminPage.pos.phaseManagementPage.apiLoader, "Verify page loading").not.toBeVisible();
            });

            /* Verify filter function */
            await test.step(`Verify filter function`, async () => {
                await adminPage.pos.phaseManagementPage.validateFilterFunctionality(centerName, streamName);
                await slowExpect(adminPage.pos.phaseManagementPage.apiLoader, "Verify page loading").not.toBeVisible();
            });

            /* Verify and delete created phase */
            await test.step(`Verify and delete created phase`, async () => {
                await slowExpect(adminPage.pos.phaseManagementPage.createdPhaseNumber(randomFourDigit), "Verify created phase number is visible").toBeVisible();
                await adminPage.pos.phaseManagementPage.createdPhaseNumber(randomFourDigit).click();
                await slowExpect(adminPage).toHaveURL(/.*detail*/);
                await adminPage.pos.phaseManagementPage.validateAndDelteCreatedPhase();
            });

        });
    }

});
