import { test, expect, slowExpect, customExpect } from '../../../ui-tests/fixtures/ui-fixtures'
import { StringUtils, PlaywrightUtils, getDateStringAfterDays, getDateStringAfterYears, getDateStringAfterMonths } from '../../../commons/common-functions';
import { EnvUtils } from '../../../commons/env-utilities';


test.describe('Course Creation and Checkout Flow', {
    tag: ['@course', '@checkout']
}, () => {
    test('Verify crouse creation/listing/widget creation and validate from student side', async ({ adminPage, studentPage, testData }) => {
        test.setTimeout(60000);

        const courseName = `Test Course ${Date.now()}`;
        const phaseName = `TestPhase${Date.now()}`;

        const randomUrl = '/random-url-' + Date.now();
        const adpl_center_id = 'facility_j1IwGFcbbdl3LSALAsGIX';
        const url_id = 'https://allen.in/';
        const tomorrowDate = getDateStringAfterDays(1);
        const syllabuscompletionDate = getDateStringAfterMonths(8);
        const endDateofPhase = getDateStringAfterMonths(9);
        const todayFormatted = getDateStringAfterDays(0);
        const twoWeeksLaterFormatted = getDateStringAfterDays(14);
        const listingName = `Test Listing ${Date.now()}`;  //course-listing-name
        const widgetTitle = `Test Widget Card ${StringUtils.generateRandomString(3)}`;
        const offerCode = `CODE${StringUtils.generateRandomString(3)}`;

        await test.step('Create new course with required details', async () => {
            await adminPage.pos.teacherHomePage.navigateToInternalUser();
            await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage(); 
            console.log("Creating new course", courseName);
            await adminPage.pos.courseAndSyllabusPage.createNewCourse(courseName, adpl_center_id, url_id);
            await expect(adminPage.pos.courseAndSyllabusPage.successMessage, "Verify success message is visible").toBeVisible();
        });

        await test.step('Create and link phase to course', async () => {
            await adminPage.pos.courseAndSyllabusPage.createNewPhase(courseName, phaseName, tomorrowDate, syllabuscompletionDate, endDateofPhase, todayFormatted, twoWeeksLaterFormatted);
            await slowExpect(adminPage).toHaveURL(/.*phase/);
        });

        await test.step('Add syllabus to course', async () => {
            await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage();
            await adminPage.waitForLoadState('networkidle');
            await adminPage.waitForTimeout(1500);
            await slowExpect(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
            await adminPage.pos.courseAndSyllabusPage.addSyllabusToCourse(courseName);
        });

        await test.step('Create course listing and submit for approval', async () => {
            await adminPage.pos.internalUserHomePage.navigateToCourseListingTab();

            // Create course listing using phase purchase dates
            await adminPage.pos.CourseListingManagement.createCourseListing({
                platform: 'noniOS',
                name: listingName,
                center: adpl_center_id,
                academicSession: '04_2025__03_2026',
                class: 'CLASS_11',
                stream: 'STREAM_JEE_MAIN_ADVANCED',
                course: courseName,
                phase: phaseName,
                duration: '3',
                timeUnit: 'Months',
                tc: process.env.TNC_URL ? process.env.TNC_URL : "",
                hideOnWebsite: 'No',
                mrp: '80000',
                mop: '60000',
                startDate: todayFormatted,  // Using phase purchase start date
                endDate: twoWeeksLaterFormatted,      // Using phase purchase end date
                otherDetails: 'foundation_online'
            });

            await adminPage.pos.CourseListingManagement.submitForApproval(listingName);
        });

        await test.step('Approve course listing', async () => {
            const streamName = 'STREAM_JEE_MAIN_ADVANCED'
            const academicSessionName = '04_2025__03_2026';
            const centerName = adpl_center_id;
            const className = 'CLASS_11';
            await adminPage.pos.internalUserHomePage.navigateToListingApproverPage();
            await adminPage.pos.courseListingApproval.ListingApprovalFilter(streamName, academicSessionName, centerName, className, courseName, phaseName);
            await adminPage.pos.courseListingApproval.approveListing(listingName);
        });

        await test.step('Create widget, generate modified course-details page URL and checkout URL', async () => {
            const pageConstId = '-yWFDwGUqsiLUl-bRfZNV'; //For course details page
            const programMode = 'LIVE';
            const discountedPrice = '500';
            const label = 'Enroll Now';
            await adminPage.pos.internalUserHomePage.navigateToUrlManagement();
            await adminPage.pos.CreateUrlEditsPage.createCheckoutUrl(randomUrl, pageConstId);
            // Add metadata
            await adminPage.pos.CreateUrlEditsPage.addMetadata('class', 'CLASS_11');
            await adminPage.pos.CreateUrlEditsPage.addButton.click();
            await adminPage.pos.CreateUrlEditsPage.addMetadata('mode', 'MODE_LIVE');
            await adminPage.pos.CreateUrlEditsPage.addButton.click();
            await adminPage.pos.CreateUrlEditsPage.addMetadata('stream', 'STREAM_JEE_MAIN_ADVANCED');
            await adminPage.pos.CreateUrlEditsPage.addButton.click();
            await adminPage.pos.CreateUrlEditsPage.addMetadata('listing_type', 'REGULAR_COURSE');
            await adminPage.pos.CreateUrlEditsPage.addButton.click();
            await adminPage.pos.CreateUrlEditsPage.addMetadata('master_course', 'MASTER_COURSE_UNSPECIFIED');
            await expect(adminPage.pos.CreateUrlEditsPage.submitButton, "Verify submit button is visible").toBeVisible();
            await adminPage.pos.CreateUrlEditsPage.submitButton.click();
            await adminPage.waitForLoadState('networkidle');
            await slowExpect(adminPage.pos.CreateUrlEditsPage.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
            await slowExpect(adminPage.pos.CreateUrlEditsPage.toastMessageText, "Verify toast message text is not visible").toBeHidden();
            await adminPage.pos.CreateUrlEditsPage.enableCriteria(randomUrl);
            await adminPage.pos.CreateUrlEditsPage.GoingtoPageWidgets('/jee/online-coaching-class-11');
            await adminPage.pos.CreateUrlEditsPage.WidgetCardCreation(randomUrl, widgetTitle, programMode, discountedPrice, label);
            await expect(adminPage.pos.CreateUrlEditsPage.addFilterDataButton, "Verify add filter data button is visible").toBeVisible();
            await adminPage.pos.CreateUrlEditsPage.addFilterDataButton.click();
            await adminPage.pos.CreateUrlEditsPage.addFilterData('Class', 'Class 11');
            await expect(adminPage.pos.CreateUrlEditsPage.addFilterDimensionButton, "Verify add filter dimension button is visible").toBeVisible();
            await adminPage.pos.CreateUrlEditsPage.addFilterDimensionButton.click();
            await adminPage.pos.CreateUrlEditsPage.addFilterData('Session', '2025-26');
            await expect(adminPage.pos.CreateUrlEditsPage.nextButton, "Verify next button is visible").toBeVisible();
            await adminPage.pos.CreateUrlEditsPage.nextButton.click();
            await adminPage.pos.CreateUrlEditsPage.submitForApproval();
            await slowExpect(adminPage.pos.CreateUrlEditsPage.toastMessageText, "Verify toast message text is visible").toBeVisible();
        });

        await test.step('Navigate to Offers/Discounts page and create offer', async () => {
            const minAmount = '45000';
            const maxAmount = '60000';
            const maximumCap = '5000';
            const beginDate = new Date().toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/\//g, '-');
            const expiryDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'}).replace(/\//g, '-');
    
            await adminPage.pos.internalUserHomePage.navigateToOffersDiscountsPage();
            await adminPage.pos.offersAndDiscountsPage.createOffer(testData.student.phone, offerCode, maximumCap, minAmount, maxAmount, beginDate, expiryDate);
      
        });

        await test.step(`Login with mobile number`, async () => {
            await studentPage.login();
            await slowExpect(studentPage.pos.homePage.exploreMenu, "Verify explore menu is visible").toBeVisible();
        });

        await test.step(`Navigate to the course page`, async () => {
            await studentPage.goto('/jee/online-coaching-class-11');
            await studentPage.waitForLoadState('networkidle');
            await expect(studentPage.pos.coursePage.testingWidgetCard(widgetTitle)).toBeVisible();
            await studentPage.pos.coursePage.testingWidgetCard(widgetTitle).click();
            await slowExpect(studentPage).toHaveURL(`https://web.allen-stage.in${randomUrl}`);
        });

        await test.step(`Verify course page and navigate to checkout page`, async () => {
            await expect(studentPage.pos.courseDetailsPage.courseStartDateButton, "verify course start date button is visible").toBeVisible();
            await studentPage.pos.courseDetailsPage.courseStartDateButton.click();
            await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "verify enroll now button is enabled").toBeEnabled();
            await studentPage.pos.courseDetailsPage.enrollNowButton.click();
            await slowExpect(studentPage).toHaveURL(/.*checkout*/);
        });
        await test.step('Verify offer code is visible and applied', async () => {
           await expect(studentPage.pos.checkoutPage.referralCouponCodeApplied(offerCode), "verify offer code is applied").toBeVisible();
           await expect(studentPage.pos.checkoutPage.appliedText, "verify apply offer button is visible").toBeVisible();
        });

        await test.step('Delete syllabus', async () => {
            await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage();
            await adminPage.waitForLoadState('networkidle');
            await adminPage.waitForTimeout(1500);
            await slowExpect(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
            await adminPage.pos.courseAndSyllabusPage.deleteSyllabus(courseName);
        });

    });
});



