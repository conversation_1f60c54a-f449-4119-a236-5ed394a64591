import fs from 'fs';

export const TEST_DATA_FILE_PATH = 'meeting-id-test-data.csv';

export function writeTestData(meetingIds: string[]): void {
  fs.writeFileSync(TEST_DATA_FILE_PATH, meetingIds.join('\n'));
}

export function readTestData(): string[] {
  if (!fs.existsSync(TEST_DATA_FILE_PATH)) {
    return [];
  }

  const fileContent = fs.readFileSync(TEST_DATA_FILE_PATH, 'utf-8');
  const lines = fileContent.split('\n');

  // Filter out empty lines and trim whitespace
  return lines
    .map(line => line.trim())
    .filter(line => line.length > 0);
} 