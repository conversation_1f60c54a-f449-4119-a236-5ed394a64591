import { readTestData } from './test-data-utils';
import { expect, slowExpect, test } from '../../../ui-tests/fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const testDataList = readTestData();
const currentEnv = EnvUtils.getInstance()

if(currentEnv.isStage()){
test.describe('Dynamic Test Suite', () => {
  if (testDataList.length === 0) {
    test('No Meeting ID Test', async ({ adminPage }) => {
      test.skip(true, 'No meeting IDs available to test');
    });
  } else {
    for (const meetingId of testDataList) {
      test(`Verify admin can join and leave meeting ID: ${meetingId}`, async ({ adminPage }) => {
        await adminPage.pos.teacherHomePage.navigateToInternalUser();
        await adminPage.goto(`${adminPage.pos.teacherEnterClassPage.url}?meetingId=${meetingId}`);
        await adminPage.waitForLoadState('networkidle');
        await adminPage.pos.teacherEnterClassPage.enterClassButton.click();
        await adminPage.waitForLoadState('networkidle');
        await adminPage.waitForTimeout(5000);
        await slowExpect(adminPage.pos.teacherLiveClassPage.teacherOnTheWayText, "verify teacher on the way text is visible").toBeVisible();
        await adminPage.pos.teacherLiveClassPage.leaveClassButton.click();
        await expect(adminPage.getByText("Are you sure you want to leave the class now?"), "verify leave class text is visible").toBeVisible();
      });
    }
  }
});
}
