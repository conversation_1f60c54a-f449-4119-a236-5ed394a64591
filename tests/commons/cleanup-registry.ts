import logger from '../../logger'
import { cleanUpTestData, testData } from '../ui-tests/fixtures/load-test-data'
import API from './api-helpers/api-helper'

class CleanupRegistry {
  private static instance: CleanupRegistry
  private cleanupTasks: Map<
    string,
    {
      testData: testData
      api: API
      env: 'prod' | 'stage'
      stream: string
      type: 'UI' | 'API'
    }
  > = new Map()

  private constructor() { }

  static getInstance(): CleanupRegistry {
    if (!this.instance) {
      this.instance = new CleanupRegistry()
    }
    return this.instance
  }

  register(
    id: string,
    testData: testData,
    api: API,
    env: 'prod' | 'stage',
    stream: string,
    type: 'UI' | 'API' = 'API',
  ) {
    console.log(`\n`);
    console.log(`worker used: ${id}`);
    console.log(`\n`);
    // console.log(`Registering ${type} cleanup task for ${id}`)
    this.cleanupTasks.set(id, { testData, api, env, stream, type })
  }

  unregister(id: string) {
    // console.log(`Unregistering cleanup task for ${id}`)
    this.cleanupTasks.delete(id)
  }

  async runAllCleanups() {
    console.log(`Starting cleanup for ${this.cleanupTasks.size} tasks...`)

    if (this.cleanupTasks.size === 0) {
      console.log('No cleanup tasks to run')
      return
    }

    const errors: Error[] = []

    // Process tasks in series to avoid overwhelming the system
    for (const [id, task] of this.cleanupTasks) {
      try {
        console.log(`Cleaning up ${task.type} task ${id}...`)
        await this.retryCleanup(task, id)
        this.cleanupTasks.delete(id)
      } catch (error) {
        console.error(`Failed to cleanup ${task.type} task ${id}:`, error)
        errors.push(error as Error)
      }
    }
    if (errors.length > 0) {
      throw new Error(`${errors.length} cleanup tasks failed`)
    }

    console.log('All cleanup tasks completed')
  }

  private async retryCleanup(task: any, taskId: string, maxRetries = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await cleanUpTestData(task.testData, task.api, task.env, task.stream)
        console.log(`Successfully cleaned up task ${taskId}`)
        return
      } catch (error) {
        if (attempt === maxRetries) throw error
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000)
        console.log(`Attempt ${attempt} failed, retrying in ${delay}ms...`)
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }
  }
}

export const cleanupRegistry = CleanupRegistry.getInstance()
