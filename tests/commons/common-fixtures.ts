import { test as base } from '@playwright/test';
import { EnvUtils } from './env-utilities';

export type commonWorkerFixtures = {
  adminURL: string;
  bffURL: string;
};

const isProd = EnvUtils.getInstance().isProd();

export const commonTest = base.extend<{}, commonWorkerFixtures>({
  adminURL: [process.env.adminURL || (isProd ? 'https://astra.allen.in' : 'https://console.allen-stage.in'), { scope: 'worker' }],
  bffURL: [process.env.bffURL || (isProd ? 'https://api.allen-live.in' : 'https://bff.allen-stage.in'), { scope: 'worker' }],
});