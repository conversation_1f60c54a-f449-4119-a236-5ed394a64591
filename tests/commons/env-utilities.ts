export class EnvUtils {
    private static instance: EnvUtils;
    private readonly env: string;

    private constructor() {
        this.env = process.env.PROD === "1" ? "prod" : "stage";
    }

    public static getInstance(): EnvUtils {
        if (!EnvUtils.instance) {
            EnvUtils.instance = new EnvUtils();
        }
        return EnvUtils.instance;
    }

    public isProd(): boolean {
        return this.env === 'prod';
    }

    public isStage(): boolean {
        return this.env === 'stage';
    }

    public getEnv(): string {
        return this.env;
    }
}