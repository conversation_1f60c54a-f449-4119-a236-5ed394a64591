import { APIRequestContext } from '@playwright/test'
import API from './api-helper'
import { StringUtils } from '../common-functions'
const fs = require('fs')
import * as path from 'path'
import FormData from 'form-data'

export default class ResourceManagement {
  private api: API

  constructor(api: API) {
    this.api = api
  }

  setToken(token: string) {
    this.api.setToken(token)
  }

  async createTestStudent(studentEmail: any, studentPhone: any, studentFirstName: any, studentLastName: any) {
    return await this.api.post('/internal-bff/api/v1/users/add-test', {
      email: studentEmail,
      first_name: studentFirstN<PERSON>,
      last_name: studentLastName,
      phone_number: studentPhone,
    })
  }

  //Delete Test Student
  async deleteTestAccount(studentId: string) {
    return await this.api.post(`/internal-bff/api/v1/users/delete-test?userId=${studentId}`)
  }
  //Enroll Test Student
  async enrollTestStudent(studentId, courseId, phaseId, batchId) {
    return await this.api.post(
      `/internal-bff/api/v1/users/enroll-test?studentId=${studentId}&courseId=${courseId}&phaseId=${phaseId}&batchId=${batchId}`,
    )
  }

  //Enroll to Special Batch
  async enrollToSpecialBatch(batchId: string, userId: string) {
    try {
      const tenantId = process.env.TENANT_ID ? process.env.TENANT_ID : ''
      const data = {
        batch_id: batchId,
        tenant_id: tenantId,
        user_ids: [userId],
      }
      const response = await this.api.post('/internal-bff/api/v1/resource/special-batch/enroll', data)
      const responseData = await response.json()
      return responseData
    } catch (error) {
      console.error('Error enrolling in special batch:', error.message)
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
      }
      throw error
    }
  }

  //Add syllabus to batch
  async addSyllabusToBatch(batchId: string) {
    var syllabusData = process.env.SYLLABUS_CONFIG_SB ? JSON.parse(process.env.SYLLABUS_CONFIG_SB) : {}
    syllabusData.resource_id = batchId
    console.log('syllabus batchId:', batchId)
    const response = await (await this.api.post('/internal-bff/api/v1/resource/course-syllabus/create', syllabusData)).json()
    if (response.status !== 200) {
      throw new Error(`Failed to add syllabus: ${JSON.stringify(response)}`)
    }
    return response.data
  }

  //Creates a Facility
  async getRoom(facilityData, type) {
    const uniqueFacilityCode = StringUtils.generateXLetterCode(4)

    var data = {
      code: uniqueFacilityCode,
      name: facilityData.name || `AUTO-${uniqueFacilityCode}`,
      type: facilityData.type || 'FACILITY_TYPE_ROOM',
      prefix_code: facilityData.prefix_code || '',
      parent_id: facilityData.parent_id,
    }

    const res = await (await this.api.post('/internal-bff/api/v1/resource/facility', data)).json()

    if (res.status !== 200) {
      throw new Error(`Failed to create room: ${JSON.stringify(res)}`)
    }
    return {
      id: res.data.id,
      name: res.data.name,
    }
  }

  //Deletes a Facility
  async deleteFacility(facilityId) {
    return await this.api.delete(`/internal-bff/api/v1/resource/facility/delete/${facilityId}`)
  }

  //Maps the Teacher to Batch for Doubts
  async uploadDoubtTemplate(filename: string, batchId: string, courseId: string, facilityId: string) {
    try {
      // Construct the full path to the file in test-data directory
      const filePath = path.join(__dirname, '..', '..', '..', 'test-data', filename)
      // Read the file as a buffer
      const fileBuffer = await fs.promises.readFile(filePath)
      // Create multipart form data
      const multipartData = {
        file: {
          name: filename,
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          buffer: fileBuffer,
        },
        course_id: courseId,
        batch_id: batchId,
        center: facilityId,
      }
      // Upload the file
      const response = await this.api.post('/internal-bff/api/v1/resource/doubt-teacher-mapping/upload', multipartData)
      const responseData = await response.json()
      if (responseData.status !== 200) {
        throw new Error(`Upload failed: ${JSON.stringify(responseData)}`)
      }
      // console.log('File uploaded successfully', responseData)
      return responseData
    } catch (error) {
      console.error('File upload failed', error)
      throw error
    }
  }

  //Create Special Batch
  async createSpecialBatch() {
    const uniqueBatchCode = `TEST-SP-${StringUtils.generateXLetterCode(4)}`
    const tenantId = process.env.TENANT_ID ? process.env.TENANT_ID : ''
    const requestData = {
      type: 'BATCH_SPECIAL',
      academic_session: '04_2024__03_2025',
      goal: 'Science Olympiads',
      code: uniqueBatchCode,
      capacity: 1000,
      tenant_id: tenantId,
    }

    const response = await this.api.post('/internal-bff/api/v1/resource/batch', requestData)
    const responseData = await response.json()
    return responseData.data
  }

  //Delete Special Batch
  async deleteSpecialBatch(batchId: string) {
    try {
      const response = await this.api.delete(`/internal-bff/api/v1/resource/batch/${batchId}/delete`)
      const responseData = await response.json()
      console.log(`Special batch ${batchId} deleted successfully`)
      return responseData
    } catch (error) {
      console.error(`Error deleting special batch ${batchId}:`, error.message)
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
      }
      throw error
    }
  }

  async moveStudentsBatch(oldBatchId: string, newBatchId: string | undefined, studentId: string) {
    try {
      const requestData = {
        mapping_requests: [
          {
            new_batch_id: newBatchId,
            student_id: studentId,
          },
        ],
        batch_id: oldBatchId,
      }

      const response = await this.api.post('/internal-bff/api/v1/resource/students-batch-movement', requestData)
      const responseData = await response.json()
      return responseData
    } catch (error) {
      console.error('Error moving student to new batch:', error.message)
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
      }
      throw error
    }
  }

  //inactive content
  async setContentInactiveStatus(contentId, contentName, type, sub_type) {
    const taxonamyId = process.env.TAXONAMY_ID_LIVE ? process.env.TAXONAMY_ID_LIVE : ''
    const centerId = process.env.CENTER_ID ? process.env.CENTER_ID : ''
    var data = {
      learning_material: {
        name: contentName,
        type: type,
        taxonomies: [
          {
            taxonomy_id: taxonamyId,
            center_id: '',
            node_id: '128',
          },
        ],
        language: 'ENGLISH',
        content_stakeholders: {
          created_by: '-',
        },
        active: false,
        client_id: 'Console',
        tenant_id: 'ALLEN',
        session: '04_2024__03_2025',
        center_id: centerId,
        master_course: '',
        sub_type: sub_type,
        learning_category: 'INSTRUCTED',
        hashtags: ['-'],
        duration_minutes: 0,
      },
    }
    return await this.api.patch(`/internal-bff/api/v1/learningMaterials/${contentId}`, data)
  }

  async startFlashcardsSession(batchId: string, nodeId: string, taxonomyId: string) {
    const requestData = {
      taxonomies: [
        {
          node_id: nodeId,
          taxonomy_id: taxonomyId,
        },
      ],
      session_id: '',
      subject: 'Flashcards',
      batch_id: batchId,
    }

    const response = await this.api.post('/internal-bff/api/v1/flashcards/start-session', requestData)
    const responseData = await response.json()
    // console.log('responseData', responseData)
    return responseData.data.show_onboarding
  }

  async getStudentUserId(studentPhone: string, pageSize: number = 20, pageNo: number = 1) {
    try {
      const queryString = `search_by=phone_number&search=${studentPhone}&sort_order=ASC&page_size=${pageSize}&page_no=${pageNo}&phone_number=${studentPhone}`

      const headers = {
        'x-client-type': 'web',
        'x-device-id': '216603f7-c131-49c6-8378-62add4b9f513',
        accept: 'application/json',
      }

      const response = await this.api.get(`/internal-bff/api/v1/student-dashboard/:id/:section?${queryString}`, headers)

      const responseData = await response.json()
      return responseData
    } catch (error) {
      // console.error('Error fetching student dashboard:', error.message)
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
      }
      throw error
    }
  }

  async detachUrl(pageId: string, url: string, userId: string) {
    try {
      const requestData = {
        url: url,
        page_id: pageId,
        created_by_user_details: {
          user_id: userId,
          persona_type: "TEACHER"
        },
        updated_by_user_details: {
          user_id: userId,
          persona_type: "TEACHER"
        },
        meta: {},
        tags: {},
        updated_at: "",
        default: true
      }

      const response = await this.api.post('/internal-bff/api/v1/urls/detach', requestData)
      const responseData = await response.json()
      return responseData
    } catch (error) {
      // console.error('Error detaching URL:', error.message)
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
      }
      throw error
    }
  }

  //Delete Page
  async deletePage(pageId: string) {
    return await this.api.delete(`/internal-bff/api/v1/pages/${pageId}`)
  }

  //Delete Widget
  async deleteWidget(widgetId: string) {
    return await this.api.delete(`/internal-bff/api/v1/widgets/${widgetId}`)
  }


  async removeContentAttach(contentIds: string[], nodeId: string) {
    try {
      const tenantId = process.env.TENANT_ID ? process.env.TENANT_ID : ''
      const taxonomyId = process.env.TAXONAMY_ID_LIVE ? process.env.TAXONAMY_ID_LIVE : ''
      const phaseId = process.env.JEE_BATCH_PHASE_ID ? process.env.JEE_BATCH_PHASE_ID : ''

      // console.log('NODE_ID:', nodeId);

      const data = {
        tenant_id: tenantId,
        entity_type: "PHASE",
        entity_id: phaseId,
        content_details: contentIds.map(materialId => ({
          material_id: materialId,
          node_id: nodeId,
          taxonomy_id: taxonomyId
        })),
        reason: "Error in material"
      }

      const response = await this.api.post('/api/v1/content-attach/remove', data)
      const responseData = await response.json()

      if (responseData.status !== 200) {
        // console.error(`Failed to remove content: ${JSON.stringify(responseData)}`)
        throw new Error(`Content removal failed: ${responseData.message || 'Unknown error'}`)
      }

      // console.log(`Successfully removed content IDs: ${contentIds.join(', ')}`)
      return responseData
    } catch (error) {
      // console.error('Error removing content:', error)
      throw error
    }
  }

  async filterLearningMaterials(name: string) {
    try {
      const requestData = {
        name: name,
        page_size: 25
      }

      const response = await this.api.post('/internal-bff/api/v1/learningMaterials/filter/v2/0', requestData)
      const responseData = await response.json()

      if (responseData.status !== 200) {
        // console.error(`Failed to filter learning materials: ${JSON.stringify(responseData)}`)
        throw new Error(`Learning materials filter failed: ${responseData.message || 'Unknown error'}`)
      }
      return responseData.data.Materials[0].taxonomies[0][0].node_id
    } catch (error) {
      // console.error('Error filtering learning materials:', error.message)
      if (error.response) {
        // console.error('Response status:', error.response.status)
        // console.error('Response data:', error.response.data)
      }
      throw error
    }
  }


  // Send Notice
  async sendNotice(title: string, description: string, category: string, expiry, center: string, course: string, batch: string, phase: string, campus: string, stream: string, class_value: string, sender: string, sender_type: string, schedule?: { expression: string }) {
    const url = '/internal-bff/api/v1/notice-board/notice/send';

    // Prepare the data object
    var data = {
      "notice_id": "",
      "title": title,
      "description": description,
      "category": category,
      "media": [],
      "expiry": expiry,
      "recipient": "RECIPIENT_STUDENT",
      "student_attribute": {
        "center": [
          center
        ],
        "course": [
          course
        ],
        "batch": [
          batch
        ],
        "phase": [
          phase
        ],
        "campus": [
          campus
        ],
        "stream": [
          stream
        ],
        "class": [
          class_value
        ],
        "academic_session": "04_2024__03_2025"
      },
      "priority": "Medium",
      "sender_info": {
        "sender_type": sender_type,
        "sender": sender
      },
      "schedule": schedule,
    };

    // Calculate the content length
    const contentLength = Buffer.byteLength(JSON.stringify(data));

    const headers = {
      'content-length': contentLength.toString(),
      'content-type': 'application/json',
      'x-client-type': 'web',
      'x-device-id': '216603f7-c131-49c6-8378-62add4b9f513',
      accept: 'application/json',
    };

    const response = await this.api.post(url, data, headers);
    const responseData = await response.json();
    return responseData;
  }
  async getWidgetsList(offset: number = 1, limit: number = 10) {
    try {

      const response = await this.api.get(`/internal-bff/api/v1/widgets?offset=${offset}&limit=${limit}`)
      const responseData = await response.json()

      if (responseData.status !== 200) {
        // console.error(`Failed to get widgets list: ${JSON.stringify(responseData)}`)
        throw new Error(`Get widgets list failed: ${responseData.message || 'Unknown error'}`)
      }

      // Return simplified widget data
      return responseData.data.widgets.rows.map(widget => ({
        id: widget.const_widget_id,
        name: widget.name,
        type: widget.widget_type,
        status: widget.status,
        entityType: widget.entity_type,
        version: widget.version,
        data: widget.widget_data
      }))
    } catch (error) {
      console.error('Error getting widgets list:', error.message)
      throw error
    }
  }

  async getPagesList(offset: number = 1, limit: number = 10) {
    try {
      const response = await this.api.get(`/internal-bff/api/v1/pages?offset=${offset}&limit=${limit}`);
      const responseData = await response.json();

      if (responseData.status !== 200) {
        console.error(`Failed to get pages list: ${JSON.stringify(responseData)}`);
        // throw new Error(`Get pages list failed: ${responseData.message || 'Unknown error'}`);
      }

      // Return simplified page data with id and name
      return responseData.data.pages.rows.map(page => ({
        id: page.page_id,
        name: page.name,
        widgets: [
          ...(page.page_link_data.widget_entities.header_widgets?.widgets || []),
          ...(page.page_link_data.widget_entities.normal_widgets?.widgets || []),
          ...(page.page_link_data.widget_entities.footer_widgets?.widgets || []),
          ...(page.page_link_data.widget_entities.onload_widgets?.widgets || []),
          ...(page.page_link_data.widget_entities.floating_widgets?.widgets || [])
        ]
      }));
    } catch (error) {
      console.error('Error getting pages list:', error.message);
      throw error;
    }
  }

  async getUrlsList(limit: number = 10, offset: number = 1) {
    try {
      const response = await this.api.get(`/internal-bff/api/v1/urls?limit=${limit}&offset=${offset}`)
      const responseData = await response.json()

      if (responseData.status !== 200) {
        // console.error(`Failed to get URLs list: ${JSON.stringify(responseData)}`)
        throw new Error(`Get URLs list failed: ${responseData.message || 'Unknown error'}`)
      }

      // Return the rows array from url_mappings
      return responseData.data.url_mappings.rows.map(row => ({
        name: row.url.substring(1), // Remove leading '/' from url
        id: row.url_id,
        pageId: row.page_id
      }))
    } catch (error) {
      console.error('Error getting URLs list:', error.message)
      throw error
    }
  }
  // Fetch Admin Tests
  async filterAdminTests(testName1: string, testName2: string, pageSize: number = 10, pageNo: number = 1) {
    const url = `/internal-bff/api/v1/tests/filterAdminTests?page_size=${pageSize}&page_no=${pageNo}&category=CLASSROOM&stream=STREAM_JEE_MAIN_ADVANCED&status=PUBLISHED&center=Automation`
    const headers = {
      'accept': 'application/json',
      'accept-language': 'en-GB,en;q=0.9',
      'x-client-type': 'web',
    }

    const response = await this.api.get(url, headers)
    const responseData = await response.json()
    let testValue1: string | undefined
    let testValue2: string | undefined

    // Loop through the test info and find matching test IDs
    responseData.data.test_info.forEach((test: any) => {
      if (test.display_name === testName1) {
        testValue1 = test.test_id
        // console.log(`Found match for ${testName1}: ${testValue1}`)
      }
      if (test.display_name === testName2) {
        testValue2 = test.test_id
        // console.log(`Found match for ${testName2}: ${testValue2}`)
      }
    })

    return { testValue1, testValue2 }
  }

}
