import { APIRequestContext } from '@playwright/test'
import API from './api-helper'
import { retryOperation } from './api-common-utils'
import { StringUtils } from '../common-functions'

export default class CourseManagement {
  private api: API

  constructor(api: API) {
    this.api = api
  }

  setToken(token: string) {
    this.api.setToken(token)
  }

  //Creates a Batch
  async getBatch(batchData: Record<string, any>, suiteType: string) {
    //const prefix = suiteType == 'UI' ? 'TEST' : 'ATEST'
    const uniqueBatchCode = `TEST-${StringUtils.generateXLetterCode(4)}-${StringUtils.generateXLetterCode(3)}`

    const response = await this.api.post('/internal-bff/api/v1/resource/batch', {
      description: batchData.description || `Automation Batch ${uniqueBatchCode}`,
      course_id: batchData.course_id,
      phase_id: batchData.phase_id,
      facility_id: batchData.facility_id,
      type: batchData.type || 'BATCH_REGULAR',
      capacity: batchData.capacity || 1111,
      timing: batchData.timing || 'BATCH_TIMING_AFTERNOON',
      gender: batchData.gender || 'BATCH_GENDER_ALL',
      days_of_week: batchData.days_of_week || [
        'WEEK_DAY_MONDAY',
        'WEEK_DAY_TUESDAY',
        'WEEK_DAY_WEDNESDAY',
        'WEEK_DAY_THURSDAY',
        'WEEK_DAY_FRIDAY',
        'WEEK_DAY_SATURDAY',
        'WEEK_DAY_SUNDAY',
      ],
      code: uniqueBatchCode,
      start_time: batchData.start_time || '',
      end_time: batchData.end_time || '',
      room_code: batchData.room_code,
    })

    const res = await response.json()
    if (res.status !== 200) {
      throw new Error(`Failed to create a batch ${JSON.stringify(res)}`)
    }
    return {
      id: res?.data?.id ?? '',
      name: res?.data?.name ?? '',
      course: res?.data?.course_id ?? '',
      phase: res?.data?.phase_id ?? '',
    }
  }

  //Deletes a Batch
  async deleteBatch(batchId: string) {
    return await this.api.delete(`/internal-bff/api/v1/resource/batch/${batchId}/delete`)
  }
}
