import { APIRequestContext } from '@playwright/test'
import API from './api-helper'
import User from './user'

export default class Auth {
  private api: API

  constructor(api: API) {
    this.api = api
  }

  async setPassword(studentPhone: string) {
    const user = new User(this.api)
    const identity = await user.getPhoneIdentities(studentPhone)

    //Send OTP with Identity
    const sendOtpResponse = await (
      await this.sendOtp(studentPhone, {
        identity_type: 'PHONE',
        identity_id: identity.identity_id,
        identity_value: identity.identity_value,
        persona_type: 'STUDENT',
        otp_type: 'RESET_PASSWORD',
      })
    ).json()

    const transactionId = sendOtpResponse.data.transaction_id

    const verifyOtpResponse = await (
      await this.verifyOtp(studentPhone, {
        otp: 1111,
        transaction_id: transactionId,
        otp_type: 'RESET_PASSWORD',
      })
    ).json()

    const resetPassword = await (
      await this.api.post(
        '/user/password/reset',
        {
          transaction_id: transactionId,
          password: studentPhone,
        },
        {
          'x-client-type': 'web',
          'x-device-id': '0e752b19-2866-46c6-afbc-3d6f20397903',
        },
      )
    ).json()
  }

  //Send OTP Api , unless a Request Body is specified , it will
  async sendOtp(studentPhone: string, reqBody: object) {
    return await this.api.post(
      '/auth/sendOtp',
      JSON.stringify(reqBody) == '{}'
        ? {
          country_code: '91',
          phone_number: studentPhone,
          persona_type: 'STUDENT',
        }
        : reqBody,
      {
        'x-client-type': 'web',
        'x-device-id': '0e752b19-2866-46c6-afbc-3d6f20397903',
      },
    )
  }

  async verifyOtp(transactionId: string, reqBody: object) {
    return await this.api.post(
      `/auth/verifyOtp`,
      JSON.stringify(reqBody) == '{}'
        ? {
          otp: 1111,
          transaction_id: transactionId,
        }
        : reqBody,
      {
        'x-client-type': 'web',
        'x-device-id': '0e752b19-2866-46c6-afbc-3d6f20397903',
      },
    )
  }

  async resetPasswordOnAcctCreation(phoneNumber: string, newPassword: string) {
    // Step 1: Initial login attempt with username/phone
    const loginResponse = await this.api.post(
      '/auth/username',
      {
        username: phoneNumber,
        password: '01012024', // Initial password format (DDMMYYYY), this remains same
        persona_type: 'STUDENT',
      },
      {
        'x-client-type': 'web',
        'x-device-id': '46beac21-a2b3-4670-b16a-72985c2c06bd',
      },
    )
    const loginJson = await loginResponse.json()
    // console.log(loginJson)
    // Step 2: Reset password using transaction_id
    const resetResponse = await this.api.post(
      '/user/password/reset',
      {
        transaction_id: loginJson?.data.transaction_id, // Get transaction_id from login response
        password: newPassword,
      },
      {
        'x-client-type': 'web',
        'x-device-id': '46beac21-a2b3-4670-b16a-72985c2c06bd',
      },
    )
    if (resetResponse.status() !== 200) {
      throw new Error(`Reset password with username failed - status code: ${resetResponse.status()}`);
    }

    return await resetResponse.json()
  }

  async login(studentPhone: string) {
    const sendOtpResponse = await this.sendOtp(studentPhone, {})
    console.log(await sendOtpResponse.json())
    const transactionId = (await sendOtpResponse.json()).data.transaction_id
    const verifyOtpResponse = await this.verifyOtp(transactionId, {})
    return verifyOtpResponse
  }

  async loginWithUsername(studentPhone: string, password: string) {
    //Can use this in PROD as well
    const response = await this.api.post(
      '/internal-bff/api/v1/auth/username',
      {
        username: studentPhone,
        password: !password ? studentPhone : password,
        persona_type: 'STUDENT',
      },
      {
        'x-client-type': 'web',
        'x-device-id': '0e752b19-2866-46c6-afbc-3d6f20397903',
      },
    )
    const responseJson = await response.json()
    // Capture the required tokens and user details as specified in the requirements
    const studentToken = response.headers()['x-access-token']
    const userDetailsId = responseJson.data.user_data.id
    // Validate the response
    if (response.status() !== 200) {
      throw new Error(`Login with Username failed - status code: ${response.status()}`)
    }
    return {
      studentToken,
      userDetailsId,
      userData: responseJson.data.user_data,
    }
  }
}
