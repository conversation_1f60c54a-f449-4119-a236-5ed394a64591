import { APIRequestContext } from '@playwright/test';

export default class API {
    private request: APIRequestContext;
    private token: string | null = null;
    private baseUrl: string;

    constructor(request: APIRequestContext, baseUrl: string) {
        this.request = request;
        this.baseUrl = baseUrl;
    }

    setToken(token: string) {
        this.token = token;
    }

    private async apiCall(
        method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
        endpoint: string,
        data?: any,
        customHeaders?: Record<string, string>
    ) {
        const headers: Record<string, string> = {};

        if (this.token) {
            headers['authorization'] = `Bearer ${this.token}`;
        }

        const options: any = {
            method: method,
            headers: headers
        };

        if (data) {
            if (data.file && data.file.buffer) {
                // For multipart/form-data
                options.multipart = data;
            } else {
                // For regular JSON data
                headers['content-type'] = 'application/json';
                options.data = data;
            }
        }

        // Add custom headers last to allow overrides
        Object.assign(options.headers, customHeaders);

        const response = await this.request.fetch(this.baseUrl + endpoint, options);
        return response;
    }

    async post(endpoint: string, data?: any, customHeaders?: Record<string, string>) {
        const res = await this.apiCall('POST', endpoint, data, customHeaders);
        return res;
    }

    async get(endpoint: string, customHeaders?: Record<string, string>) {
        const res = await this.apiCall('GET', endpoint, undefined, customHeaders);
        return res;
    }

    async put(endpoint: string, data: any, customHeaders?: Record<string, string>) {
        const res = await this.apiCall('PUT', endpoint, data, customHeaders);
        return res;
    }

    async patch(endpoint: string, data: any, customHeaders?: Record<string, string>) {
        const res = await this.apiCall('PATCH', endpoint, data, customHeaders);
        return res;
    }

    async delete(endpoint: string, customHeaders?: Record<string, string>) {
        const res = await this.apiCall('DELETE', endpoint, undefined, customHeaders);
        return res;
    }
}