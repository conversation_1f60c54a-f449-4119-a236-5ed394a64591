import { APIRequestContext } from '@playwright/test'
import API from './api-helper'

export default class User {
  private api: API;

  constructor(api: API) {
    this.api = api;
  }

  async studentInfo() {
    return await this.api.get('/api/v1/user/studentInfo', {
      "x-client-type": "web", "x-device-id": "0e752b19-2866-46c6-afbc-3d6f20397903"
    })
  }

  async getPhoneIdentities(studentPhone: string) {
    const res = await (
      await this.api.get(`/user/identities/${studentPhone}?communicable=true`, {
        "x-client-type": "web", "x-device-id": "0e752b19-2866-46c6-afbc-3d6f20397903"
      })
    ).json()
    const identity = res.data.identities.find(
      (id) => id.identity_type === 'PHONE',
    )

    return identity
  }
}
