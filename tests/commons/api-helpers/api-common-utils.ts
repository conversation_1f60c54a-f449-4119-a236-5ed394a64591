export async function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
//Performs Retry for any operation along with customized maximum attempts and delay between retries.
export async function retryOperation<T>(
  operationName: string = '',
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000,
): Promise<T> {
  let attempt = 0
  while (true) {
    try {
      attempt++
      const result = await operation()
      // console.log(`${operation.name} - attempt ${attempt} succeeded`);
      return result
    } catch (error) {
      console.error(`${operationName} - attempt ${attempt} failed due to ${error.message}`)
      if (attempt >= maxAttempts) {
        throw new Error(`${operationName} failed after ${maxAttempts} attempts`)
      }
      await sleep(delayMs)
      continue
    }
  }
}
