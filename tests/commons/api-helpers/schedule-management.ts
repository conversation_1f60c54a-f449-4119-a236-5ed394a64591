import { APIRequestContext } from '@playwright/test';
import API from './api-helper';
import { sleep } from './api-common-utils';

export type MeetingInfo = {
  "meetingId": string,
  "className": string,
  "startTimeStamp": number,
  "endTimeStamp": number,
  "batchId": string,
}

export default class ScheduleManagement {
  private api: API;

  constructor(api: API) {
    this.api = api;
  }
  async createSchedule(classMode: string, courseId: string, batchId: string, batchName: string, facilityId: string, startTimestamp: number, endTimestamp: number, teacherId: string, teacherName: string, className: string) {
    const taxonomyId = process.env.TAXONAMY_ID_LIVE ? process.env.TAXONAMY_ID_LIVE : ""

    return await this.api.post('internal-bff/v1/planning-and-scheduling/schedules', {
      "tenant_id": process.env.TENANT_ID,
      "data": {
        "type": "SCHEDULE_TYPE_CLASS",
        "schedules": [
          {
            "external_identifier": "0",
            "title": className,
            "start_time": `${startTimestamp}`,
            "end_time": `${endTimestamp}`,
            "participants": [
              {
                "id": `${teacherId}`,
                "type": "PARTICIPANT_TYPE_TEACHER",
                "role": "PARTICIPANT_ROLE_ORGANIZER"
              },
              {
                "id": `${batchId}`,
                "type": "PARTICIPANT_TYPE_BATCH",
                "role": "PARTICIPANT_ROLE_VIEWER"
              }
            ],
            "type": "SCHEDULE_TYPE_CLASS",
            "facility_id": "",
            "visibility": "SCHEDULE_VISIBILITY_PUBLIC",
            "class_schedule_metadata": {
              "class_type": "LIVE_LECTURE",
              "mode": classMode,
              "scheduled_subjects": [
                {
                  "id": "2",
                  "subject_id": "2",
                  "taxonomy_id": taxonomyId,
                  "nodes": [
                    {
                      "id": "86",
                      "type": "TOPIC"
                    }
                  ]
                }
              ],
              "pre_class_materials": []
            }
          }
        ]
      }
    });
  }

  async updateScheduleStatus(fromStatus: string[], toStatus: string, batchId: string, startTimestamp: number, endTimestamp: number) {
    try {
      return await this.api.put('internal-bff/v1/planning-and-scheduling/schedules/status', {
        "class_schedule": {
          "request": [
            {
              "from_status": `${fromStatus}`,
              "to_status": `${toStatus}`,
              "schedule_time": {
                "from": `${startTimestamp}`,
                "to": `${endTimestamp}`
              },
              "filter": {
                "participants_ids": {
                  "op": "IN",
                  "values": [`${batchId}`]
                }
              }
            }
          ]
        },
        "type": "SCHEDULE_TYPE_CLASS",
        "tenant_id": process.env.TENANT_ID
      });
    } catch (error) {
      if (toStatus === "SCHEDULE_STATUS_PUBLISHED") {
        console.error("Failed to publish the meeting", error);
      }
    }
  }

  async getSchedules(classType: string, batchId: string, startTimestamp: number, endTimestamp: number) {
    let returnValue;
    if (classType == "LIVE") {
      returnValue = await this.api.get(`/resource/class-schedules?batch_id=${batchId}&scheduled_start_time=${startTimestamp}&scheduled_end_time=${endTimestamp}&class_type=LIVE_LECTURE`);
    }
    else if (classType == "MENTORSHIP") {
      returnValue = await this.api.get(`/resource/class-schedules?batch_id=${batchId}&scheduled_start_time=${startTimestamp}&scheduled_end_time=${endTimestamp}&class_type=MENTORSHIP_SESSION`);
    }
    return returnValue
  }

  private async createMeetingHelper(classMode: string, classType: string, courseId: string, batchId: string, batchName: string, facilityId: string, teacherId: string, teacherName: string, startTimestamp: number, duration: number, className: string, cancelConflict: boolean = true) {
    const startDate = new Date(startTimestamp * 1000);
    const currentDate = new Date(startTimestamp * 1000);
    currentDate.setMinutes(startDate.getMinutes() + duration);
    const endTimestamp = Math.floor(currentDate.getTime() / 1000);

    if (cancelConflict) {
      await this.cancelAllMeeting(batchId, startTimestamp, endTimestamp)
    }

    let createScheduleResponse;
    if (classType == "LIVE") {
      createScheduleResponse = await this.createSchedule(classMode, courseId, batchId, batchName, facilityId, startTimestamp, endTimestamp, teacherId, teacherName, className)
    }
    else if (classType == "MENTORSHIP") {
      createScheduleResponse = await this.createMentorshipSchedule(classMode, courseId, batchId, batchName, facilityId,  12, startTimestamp, endTimestamp, teacherId, teacherName, className)
    }

    const createScheduleJson = await createScheduleResponse.json()
    const scheduleId = createScheduleJson.schedules[0].id

    const updateScheduleResponse = await this.publishMeeting(["SCHEDULE_STATUS_DRAFT"], "SCHEDULE_STATUS_PUBLISHED", scheduleId, startTimestamp, endTimestamp)
    const updateStatus = updateScheduleResponse.status()

    const MAX_RETRIES = 10;
    const RETRY_DELAY = 2000;

    let meetingId = null;
    let attemptCount = 0;

    // Loop until we get the meeting ID or reach max retries
    while ((!meetingId || meetingId == "") && attemptCount < MAX_RETRIES) {
      try {
        if (attemptCount > 0) {
          await sleep(RETRY_DELAY);
        }

        let getScheduleResponse;
        if (classType == "LIVE") {
          getScheduleResponse = await this.getMeetingId("LIVE_LECTURE", scheduleId, batchId, startTimestamp, endTimestamp);
        }
        else if (classType == "MENTORSHIP") {
          getScheduleResponse = await this.getMeetingId("MENTORSHIP_SESSION", scheduleId, batchId, startTimestamp, endTimestamp);
        }

        const getScheduleJson = await getScheduleResponse.json();
        meetingId = getScheduleJson.data.schedules[0].meeting_id;
      }
      catch (error) {
        console.log(`Attempt ${attemptCount + 1} failed with error:`, error.message);
      }

      attemptCount++;
    }

    if (!meetingId || meetingId === "") {
      throw new Error(`Failed to retrieve meeting ID after ${MAX_RETRIES} attempts`);
    }

    console.log(`Meeting ID: ${meetingId}`);

    return { "meetingId": meetingId, "startTimeStamp": startTimestamp, "endTimeStamp": endTimestamp, "batchId": batchId, "className": className };
  }

  async createMeeting(classMode: string, classType: string, courseId: string, batchId: string, batchName: string, facilityId: string, teacherId: string, teacherName: string, timeOffset: number, duration: number, className: string, cancelConflict: boolean = true) {
    const currentDate = new Date(Date.now());
    const startTimestamp = Math.floor((currentDate.getTime() + (timeOffset * 1000)) / 1000);

    return this.createMeetingHelper(classMode, classType, courseId, batchId, batchName, facilityId, teacherId, teacherName, startTimestamp, duration, className, cancelConflict);
  }

  async createPastDateMeeting(classMode: string, classType: string, courseId: string, batchId: string, batchName: string, facilityId: string, teacherId: string, teacherName: string, timeOffset: number, duration: number, className: string, cancelConflict: boolean = true) {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const startTimestamp = Math.floor((yesterday.getTime() + (timeOffset * 60000)) / 1000);

    return this.createMeetingHelper(classMode, classType, courseId, batchId, batchName, facilityId, teacherId, teacherName, startTimestamp, duration, className, cancelConflict);
  }

  async getAllSchedules(classType: string, batchId: string, startTimestamp: number, endTimestamp: number) {
    const getSchedulesResponse = await this.getSchedules(classType, batchId, startTimestamp, endTimestamp)
    const getScheduleJson = await getSchedulesResponse.json()
    return getScheduleJson.data.schedules
  }

  async cancelAllMeeting(batchId: string, startTimestamp, endTimestamp) {
    await this.updateScheduleStatus(["SCHEDULE_STATUS_PUBLISHED"], "SCHEDULE_STATUS_DELETED", batchId, startTimestamp, endTimestamp)
    await this.updateScheduleStatus(["SCHEDULE_STATUS_CLASH"], "SCHEDULE_STATUS_DELETED", batchId, startTimestamp, endTimestamp)
    await this.updateScheduleStatus(["SCHEDULE_STATUS_DRAFT"], "SCHEDULE_STATUS_DELETED", batchId, startTimestamp, endTimestamp)
  }

  async createMentorshipSchedule(classMode: string, courseId: string, batchId: string, batchName: string, facilityId: string, startOfDayTimestamp: number, startTimestamp: number, endTimestamp: number, teacherId: string, teacherName: string, className: string) {
    const taxonomyId = process.env.TAXONAMY_ID_LIVE ? process.env.TAXONAMY_ID_LIVE : ""

    return await this.api.post('internal-bff/v1/planning-and-scheduling/schedules', {
      "tenant_id": process.env.TENANT_ID,
      "data": {
        "type": "SCHEDULE_TYPE_CLASS",
        "schedules": [
          {
            "external_identifier": "0",
            "title": className,
            "start_time": startTimestamp,
            "end_time": endTimestamp,
            "participants": [
              {
                "id": teacherId,
                "type": "PARTICIPANT_TYPE_TEACHER",
                "role": "PARTICIPANT_ROLE_ORGANIZER"
              },
              {
                "id": batchId,
                "type": "PARTICIPANT_TYPE_BATCH",
                "role": "PARTICIPANT_ROLE_VIEWER"
              }
            ],
            "type": "SCHEDULE_TYPE_CLASS",
            "facility_id": "",
            "visibility": "SCHEDULE_VISIBILITY_PUBLIC",
            "class_schedule_metadata": {
              "class_type": "MENTORSHIP_SESSION",
              "mode": classMode,
              "scheduled_subjects": [
                {
                  "id": "2",
                  "subject_id": "2",
                  "taxonomy_id": taxonomyId,
                  "nodes": [
                    {
                      "id": "86",
                      "type": "TOPIC"
                    },
                    {
                      "id": "4",
                      "type": "TOPIC"
                    }
                  ]
                }
              ],
              "pre_class_materials": [
                {
                  "id": "",
                  "resource_id": "",
                  "type": ""
                }
              ]
            }
          }
        ]
      }
    });
  }

  async getMeetingId(classType: string, scheduleId: string, batchId: string, startTimeStamp: number, endTimeStamp: number) {
    return await this.api.post('urm-bff/api/v1/planning-and-scheduling/schedules', {
      "tenant_id": process.env.TENANT_ID,
      "filter": {
        "type": {
          "op": "EQ",
          "value": classType
        },
        "schedules": {
          "op": "IN",
          "values": [
            scheduleId
          ]
        },
        "batches": {
          "op": "IN",
          "values": [
            batchId
          ]
        },
        "schedule_time": {
          "from": startTimeStamp,
          "to": endTimeStamp,
        }
      },
      "pagination": {
        "page_size": 10,
        "page_number": 1,
        "sort": {
          "by": "createdAt",
          "order": "DESC"
        }
      }
    });
  }

  async createNextDateMeeting(classMode: string, classType: string, courseId: string, batchId: string, batchName: string, facilityId: string, teacherId: string, teacherName: string, timeOffset: number, duration: number, className: string, cancelConflict: boolean = true) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const startTimestamp = Math.floor((tomorrow.getTime() + (timeOffset * 60000)) / 1000);

    return this.createMeetingHelper(classMode, classType, courseId, batchId, batchName, facilityId, teacherId, teacherName, startTimestamp, duration, className, cancelConflict);
  }

  async publishMeeting(fromStatus: string[], toStatus: string, scheduleId: string, startTimestamp: number, endTimestamp: number) {
    try {
      return await this.api.put('internal-bff/v1/planning-and-scheduling/schedules/status', {
        "class_schedule": {
          "request": [
            {
              "from_status": `${fromStatus}`,
              "to_status": `${toStatus}`,
              "schedule_time": {
                "from": `${startTimestamp}`,
                "to": `${endTimestamp}`
              },
              "filter": {
                "schedules": {
                  "op": "IN",
                  "values": [`${scheduleId}`]
                }
              }
            }
          ]
        },
        "type": "SCHEDULE_TYPE_CLASS",
        "tenant_id": process.env.TENANT_ID
      });
    } catch (error) {
      if (toStatus === "SCHEDULE_STATUS_PUBLISHED") {
        console.error("Failed to publish the meeting", error);
      }
    }
  }
}
