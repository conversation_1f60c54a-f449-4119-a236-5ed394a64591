import API from './api-helper'

export default class DoubtsHelper {
  private api: API

  constructor(api: API) {
    this.api = api
  }

  setToken(token: string) {
    this.api.setToken(token)
  }

  async doubtAskedOrNot(batchId: string, courseId: string) {
    const headers = {
      'x-selected-batch-list': batchId,
      'x-selected-course-id': courseId
    };

    const data = {
      current_page: 1,
      limit: 20,
      filters: [
        {
          filter_name: "STATUS",
          filter_value: "OPEN"
        }
      ],
      sort_by: {
        label: "UPDATED_AT",
        value: null
      }
    };

    const response = await this.api.post('/api/v1/v6/doubts/seekers/doubts', data, headers);
    const responseData = await response.json();

    if (responseData.data.doubts_unavailibality_code === 'NO_DOUBTS_ASKED_YET') {
      return true;
    }
  }
}
