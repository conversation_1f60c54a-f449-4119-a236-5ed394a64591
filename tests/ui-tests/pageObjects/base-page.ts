import { expect, type Locator, type Page } from '@playwright/test';
import { StringUtils } from '../../commons/common-functions';

export class BasePage {
  readonly page: Page;
  readonly url: string
  readonly isMobile: boolean;

  constructor(page: Page, url: string, isMobile: boolean) {
    this.page = page;
    this.url = url;
    this.isMobile = isMobile;
  }

  async goto() {
    await this.page.goto(this.url);
  }

  // Wait for popup and returns new tab
  // Usage:
  // const newTab = await this.waitForPopup(async () => {
  //   await this.page.getByRole("link", { name: "OpenNewTab" }).click();
  // });
  async waitForPopup(action: () => Promise<void>): Promise<Page> {
    const newTabPromise = this.page.waitForEvent('popup');
    await action();
    const newTab = await newTabPromise;
    return newTab;
  }

  async verifyPage() {
    const escapedBaseUrl = StringUtils.escapeRegExp(this.url);
    const baseUrlPattern = new RegExp(`^${escapedBaseUrl}(\\?|#|$)`, 'i');
    await expect(this.page).toHaveURL(baseUrlPattern);
  }

}
