
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const homeworkInsightPageUrl = '/homework-insight?'
export class HomeworkInsightPage extends WebPage {
    readonly homeworkInsightTitle: Locator;
    readonly yourPerformanceText: Locator;
    readonly attemptedView: Locator;
    readonly seeAnswersButton: Locator;
    readonly backButton: Locator;
    readonly reviewAnswersBackButton: Locator;
    readonly reviewAnswersHeading: Locator;
    readonly gotItButton: Locator;
    readonly incorrectTab: Locator;
    readonly unattemptedTab: Locator;
    readonly solvedTab: Locator;
    readonly question1: Locator;
    readonly viewSolutionButton: Locator;
    readonly viewSolutionsButton: Locator;
    readonly nextButton: Locator;
    readonly previousButton: Locator;
    readonly pendingHomeworkExplore: Locator;
    readonly upcomingDeadlineHeading: Locator;
    readonly tomorrowButton: Locator;
    readonly dayAfterButton: Locator;
    readonly in2DaysButton: Locator;
    readonly othersButton: Locator;
    readonly noPendingHomeworkText: Locator;
    readonly backlogHeader: Locator;
    readonly seeOverDueLink: Locator;
    readonly backlogExplore: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, homeworkInsightPageUrl, isMobile);
        this.homeworkInsightTitle = page.getByText('ExploreHomeworkCompletedInsight');
        this.yourPerformanceText = page.getByText('Your Performance');
        this.attemptedView = page.getByTestId('accordion-header');
        this.seeAnswersButton = page.getByTestId('footer-').getByTestId('dls-button').getByText('See answers');
        this.backButton = page.getByTestId('section-header').nth(1);
        this.reviewAnswersBackButton = page.getByTestId('back-arrow').getByRole('img');
        this.reviewAnswersHeading = page.getByText('Review Answers');
        this.gotItButton = page.getByRole('button', { name: 'Got it' });
        this.incorrectTab = page.getByText('Incorrect(');
        this.unattemptedTab = page.getByText('Unattempted(');
        this.solvedTab = page.getByText('Solved(');
        this.question1 = page.getByText('Question 1');
        this.viewSolutionButton = page.getByText('View solution').first();
        this.viewSolutionsButton = page.getByRole('button', { name: 'View Solution' }).first();
        this.nextButton = page.getByRole('button', { name: 'Next' });
        this.previousButton = page.getByRole('button', { name: 'Previous' });
        this.pendingHomeworkExplore = page.getByText('ExploreHomeworkPending');
        this.upcomingDeadlineHeading = page.getByRole('heading', { name: 'Upcoming deadlines' });
        this.tomorrowButton = page.getByText('Tomorrow');
        this.dayAfterButton = page.getByText('Day After');
        this.in2DaysButton = page.getByText('In 2 days');
        this.othersButton = page.getByText('Others');
        this.noPendingHomeworkText = page.getByRole('heading', { name: 'No Pending Homework' });
        this.backlogHeader = page.getByRole('heading', { name: 'Backlog' });
        this.seeOverDueLink = page.getByText('See your overdue HWs here');
        this.backlogExplore = page.getByText('ExploreHomeworkPendingBacklog');
    }

    async verifyHomeWorkSummaryPageAndFinalSubmit() {
        await slowExpect(this.homeworkInsightTitle, 'verify homework Insight Title is visible').toBeVisible();
        await expect(this.yourPerformanceText, 'verify your Performance Text is visible').toBeVisible();
        await expect(this.attemptedView, 'verify attempted View is visible').toBeVisible();
        await expect(this.seeAnswersButton, 'verify see Answers Button is visible').toBeVisible();
    }

    async verifyPendingHomeworkExpolre() {
        await slowExpect(this.pendingHomeworkExplore, "verify pending explore is visible").toBeVisible();
        await expect(this.upcomingDeadlineHeading, "verify upcoming Dead lines Heading is visible").toBeVisible();
        await expect(this.tomorrowButton, "verify tomorrow deadline button is visible").toBeVisible();
        await expect(this.dayAfterButton, "verify dayAfter deadline button is visible").toBeVisible();
        await expect(this.in2DaysButton, "verify in2Days deadline button is visible").toBeVisible();
        await this.in2DaysButton.click();
        await expect(this.noPendingHomeworkText, "verify no Pending Homework Text is visible").toBeVisible();
    }

    async verifybacklogHomework() {
        await expect(this.backlogHeader, "verify backlog homework Header is visible").toBeVisible();
        await expect(this.seeOverDueLink, "verify see Over Due homework Link is visible").toBeVisible();
        await this.seeOverDueLink.click();
        await slowExpect(this.page, "verify after clicking see over due homework navigated to backlog homework url").toHaveURL(/.*homework-backlog/);
        await slowExpect(this.backlogExplore, "verify backlog homework explore is visible").toBeVisible();

    }

    async verifyReviewAnswers() {
        await slowExpect(this.reviewAnswersHeading, 'verify review Answers Heading is visible').toBeVisible();
        await expect(this.incorrectTab, 'verify incorrect answers Tab is visible').toBeVisible();
        await expect(this.question1, 'verify question 1 is visible').toBeVisible();
        await expect(this.unattemptedTab, 'verify unattempted questions Tab is visible').toBeVisible();
        await expect(this.solvedTab, 'verify solved Answers tab is visible').toBeVisible();
        await this.unattemptedTab.click();
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForLoadState('domcontentloaded');
        await expect(this.viewSolutionButton, 'verify view Explination Button is visible').toBeVisible();
        await this.viewSolutionButton.click();
        await expect(this.nextButton, 'verify next Button is visible').toBeVisible();
        await expect(this.previousButton, 'verify previous Button is visible').toBeVisible();

        if (await this.gotItButton.isVisible({timeout: 2000})) {  //Only visible for new users
            await this.gotItButton.click();
        }

        await expect(this.backButton, 'verify back Button is visible').toBeVisible();
        await this.backButton.click();
        await slowExpect(this.reviewAnswersHeading, 'verify review Answers Heading is visible').toBeVisible();
        await slowExpect(this.reviewAnswersBackButton, 'verify back button is visible').toBeVisible();
        await this.reviewAnswersBackButton.click();
    }
}
