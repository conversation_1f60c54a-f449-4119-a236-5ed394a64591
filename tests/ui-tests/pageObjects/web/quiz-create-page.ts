import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const QuizCreatePageUrl = '/quiz/create'

export class QuizCreatePage extends WebPage {

  readonly selectUpToTopicsHeader: Locator;
  readonly toppicButton: Locator;
  readonly nextCTAButton: Locator;
  readonly stepItUpText: Locator;
  readonly easyText: Locator;
  readonly mediumText: Locator;
  readonly hardText: Locator;
  readonly noOfQuestions: Locator;
  readonly startQuizButton: Locator;
  readonly nextQuestionButton: Locator;
  readonly previousQuestionButton: Locator;
  readonly forceSubmitQuizButton: Locator;
  readonly quizWillBeSubmitText: Locator;
  readonly resumeButton: Locator;
  readonly submitQuizButton: Locator;
  readonly questionOneTitle: Locator;
  readonly questionTwoTitle: Locator;
  readonly marksText: Locator;
  readonly questionCard: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, QuizCreatePageUrl, isMobile);
    this.selectUpToTopicsHeader = page.getByText('Select up to 4 chapters');
    this.toppicButton = page.locator("(//*[text()='Select up to 4 chapters']/ancestor::*[@data-testid='dynamic_page_root']//*[@data-testid='accordion-header'])[1]//*[contains(@class,'ct-checkbox-container mt-1 pr-8 tick relative ')]");
    this.nextCTAButton = page.getByText('Next', { exact: true });
    this.stepItUpText = page.getByText('Step it up!', { exact: true });
    this.easyText = page.getByText('Easy', { exact: true });
    this.mediumText = page.getByText('Medium', { exact: true });
    this.hardText = page.getByText('Hard', { exact: true });
    this.noOfQuestions = page.getByText('No. of questions', { exact: true });
    this.startQuizButton = page.getByText('Start Quiz', { exact: true });
    this.nextQuestionButton = page.locator("//*[@id='arrow-circle-right']/ancestor::*[contains(@class,'cursor-pointer')]/*");
    this.previousQuestionButton = page.locator("(//*[@id='arrow-circle-left']/ancestor::*[contains(@class,'cursor-pointer')]/*");
    this.forceSubmitQuizButton = page.locator('.absolute').first();
    this.quizWillBeSubmitText = page.getByText('Your quiz will be submitted');
    this.resumeButton = page.getByRole('button', { name: 'Resume' });
    this.submitQuizButton = page.getByRole('button', { name: 'Submit Quiz' });
    this.questionOneTitle = page.getByText('Q 1', { exact: true });
    this.questionTwoTitle = page.locator("//*[@class='flex items-center font-bold']").getByText('Q 2');
    this.marksText = page.getByText('Marks', { exact: true });
    this.questionCard = page.locator("//*[contains(@class,'card-question')]");

  }

  async createCustomQuiz() {
    await slowExpect(this.selectUpToTopicsHeader, 'verify select upto 4 chapters header is visible').toBeVisible();
    await expect(this.toppicButton, 'verify topic name is visible').toBeVisible();
    await this.toppicButton.click();
    await expect(this.nextCTAButton, 'verify Next CTA button is visible').toBeVisible();
    await expect(this.nextCTAButton, 'verify Next CTA button is enabled').toBeEnabled();
    await this.nextCTAButton.click();
    await expect(this.stepItUpText, 'verify step It Up Text is visible').toBeVisible();
    await expect(this.easyText, 'verify easy Text is visible').toBeVisible();
    await expect(this.mediumText, 'verify medium Text is visible').toBeVisible();
    await expect(this.hardText, 'verify hard Text is visible').toBeVisible();
    await expect(this.noOfQuestions, 'verify number of questions text is visible').toBeVisible();
    await expect(this.startQuizButton, "verify start Quiz Button is visible").toBeVisible();
    await this.startQuizButton.click();
  }

  async startQuizAndForceSubmit() {
    await slowExpect(this.questionOneTitle, 'verify question 1 title is visible').toBeVisible();
    await this.page.waitForTimeout(1000);
    await expect(this.questionCard.first(), "verify question card is visible").toBeVisible();
    await expect(this.forceSubmitQuizButton, 'verify force submit cross is visible').toBeVisible();
    await this.forceSubmitQuizButton.click();
    await expect(this.quizWillBeSubmitText, 'verify Your quiz will be submitted text is visible').toBeVisible();
    await expect(this.resumeButton, 'verify resume button is visible').toBeVisible();
    await expect(this.submitQuizButton, 'verify submit quiz button is visible').toBeVisible();
    await expect(this.submitQuizButton, 'verify submit quiz button is enable').toBeEnabled();
    await this.submitQuizButton.click();
    await slowExpect(this.marksText, 'verify Marks Text is visible').toBeVisible();
  }

}