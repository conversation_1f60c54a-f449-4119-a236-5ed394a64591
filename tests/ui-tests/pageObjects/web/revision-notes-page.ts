import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect, customExpect } from '../../fixtures/ui-fixtures';
const revisionNotesPageUrl = '/revision-note';

export class RevisionNotesPage extends WebPage {
  readonly revisionNotesTitle: Locator;
  readonly subjectCard: (subjectName: string) => Locator;
  readonly selectTopic: (topic: string) => Locator;
  readonly selectChapter: Locator;
  readonly currentlyReadingText: Locator;
  readonly revisionNotesContent: Locator;
  readonly nextTopicArrow: Locator;
  readonly finishedText: Locator;
  readonly imageExpand: Locator;
  readonly closeModal: Locator;
  readonly tableExpand: Locator;
  readonly highlightText: Locator;
  readonly yourHighlightPage: Locator;
  readonly nothingHighlightedText: Locator;
  readonly demovideoButton: Locator;
  readonly okButton: Locator;
  readonly highlightnotestext: Locator;
  readonly image: Locator;
  readonly starButton: Locator;
  readonly doubtButton: Locator;
  readonly questionText: Locator;
  readonly specializedNotesHeader: Locator;
  readonly keyPointers: Locator;
  readonly commonConfusions: Locator;
  readonly diagramsAndGraphs: Locator;
  readonly keyPointersHeader: Locator;
  readonly nextTopicDiv: Locator;
  readonly keyPointersNoteBody: Locator;
  readonly questionidSearch: Locator;
  readonly popUpTextCloseButton: Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, revisionNotesPageUrl, isMobile);

    this.revisionNotesTitle = page.getByText('Revision Notes');
    this.subjectCard = (subjectName: string) => page.locator('div').filter({ hasText: new RegExp(`^${subjectName}$`)});
    this.selectTopic = (topic) => page.getByText(topic);
    this.selectChapter = page.getByText('Your Highlights').locator('xpath=../../../following-sibling::div').first().locator('div').first();
    this.currentlyReadingText = page.getByText('Currently Reading');
    this.revisionNotesContent = page.locator('div.bg-secondary.overflow-y-auto').first();
    this.nextTopicArrow = page.getByText('NEXT TOPIC').locator('xpath=../following-sibling::div//*[@role="img"]');
    this.finishedText = page.getByText('Finished');
    this.imageExpand = page.locator('.img-rn-highlight').first().locator('xpath=following-sibling::span');
    this.closeModal = page.locator('[aria-label="Close modal"]');
    this.tableExpand = page.locator('table').first().locator('xpath=../following-sibling::*') .locator('xpath=.//*[@role="img"]');
    this.highlightText = page.getByText('Your Highlights');
    this.yourHighlightPage = page.locator("//h1[contains(@class, 'text-default-title') and contains(@class, 'font-bold')]");
    this.nothingHighlightedText = page.getByText('Nothing is highlighted!');
    this.demovideoButton = page.getByTestId('dls-button');
    this.okButton = page.getByRole('button', { name: 'Ok' });
    this.highlightnotestext = page.locator(`//h5[1]`);
    this.image = page.locator(`(//div[@data-highlightable='true' and .//img])[1]`);
    this.starButton = page.locator(`//div[span[text()='Star']]`);
    this.doubtButton = page.locator(`//div[span[text()='Doubt']]`);
    this.questionText = page.locator(`//p[normalize-space(text())="Example Questions"]/following::div[@id="question"][1]`);
    this.questionidSearch = page.locator(`//div[@id='question']`);
    this.specializedNotesHeader = page.getByText('SPECIALISED NOTES');
    this.keyPointers = page.getByText('Key Pointers');
    this.commonConfusions = page.getByText('Common Confusions');
    this.diagramsAndGraphs = page.getByText('Diagrams & Graphs');
    this.keyPointersHeader = page.getByRole('heading', { name: 'Key Pointers' });
    this.keyPointersNoteBody = page.locator(`//div[@id='revision-note-body']`);
    this.nextTopicDiv = page.getByText('NEXT TOPIC');
    this.popUpTextCloseButton = page.getByTestId('dls-button').nth(1);

  }

  async selectSubjectAndTopic() {
    await expect(this.subjectCard('Chemistry'), 'Verify chemistry subject card is visible').toBeVisible();
    await expect(this.subjectCard('Maths'), 'Verify chemistry subject card is visible').toBeVisible();
    await expect(this.subjectCard('Physics'), 'Verify chemistry subject card is visible').toBeVisible();
    await this.subjectCard('Chemistry').click();
    await expect(this.selectTopic('Atomic Structure')).toBeVisible();
    await this.selectTopic('Atomic Structure').click();
    await expect(this.selectChapter, 'Verify chapter name is visible').toBeVisible();
    await this.selectChapter.click();
  }

  async verifyRevisionNotesContent() {
    await expect(this.currentlyReadingText, 'Verify currently reading text is visible').toBeVisible();
    await this.page.waitForLoadState('domcontentloaded');
    await expect(this.revisionNotesContent, 'Verify revision notes content is visible').toBeVisible();
  }

  async switchTopics() {
    await slowExpect(this.nextTopicArrow, 'Verify next topic arrow is visible').toBeVisible();
    await this.nextTopicArrow.click();
    await expect(this.finishedText, 'Verify finished text is visible').toBeVisible();
    await this.page.waitForLoadState('domcontentloaded');
    try {
      await this.popUpTextCloseButton.waitFor({ timeout: 5000 });
      await this.popUpTextCloseButton.click();
    } catch {
      console.log("Popup did not appear within 5 seconds");
    }
    await expect(this.selectChapter, 'Verify chapter name is visible').toBeVisible();
    await this.selectChapter.click();
    await expect(this.currentlyReadingText, 'Verify currently reading text is visible').toBeVisible();
  }

  async verifyImageAndTableInteraction() {
    await expect(this.imageExpand).toBeVisible();
    await this.imageExpand.click();
    await this.page.waitForLoadState('domcontentloaded');
    await expect(this.closeModal, "Verify close button is visible").toBeVisible();
    await this.closeModal.click();
    await slowExpect(this.tableExpand).toBeVisible();
    await this.tableExpand.click();
    await this.page.waitForLoadState('domcontentloaded');
    await expect(this.closeModal, "Verify close button is visible").toBeVisible();
    await this.closeModal.click();
  }
  async verifyHighlightPage() {
    await expect(this.highlightText, 'Verify your highlights text is visible').toBeVisible();
    await this.highlightText.click();
    await expect(this.yourHighlightPage, 'Verify your highlights page is visible').toBeVisible();
    await expect(this.nothingHighlightedText, 'Verify nothing highlighted text is visible').toBeVisible();
    await expect(this.demovideoButton, 'Verify demo video button is visible').toBeVisible();
    await this.demovideoButton.click();
    await expect(this.okButton, 'Verify ok button is visible').toBeVisible();
    await this.okButton.click();
  }
}

