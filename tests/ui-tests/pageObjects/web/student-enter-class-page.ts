
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { customExpect } from '../../fixtures/ui-fixtures';

const StudentEnterClassPageUrl = '/join'
export class StudentEnterClassPage extends WebPage {
  readonly enterClassButton: Locator;
  readonly classCardName: (className: string) => Locator;
  readonly popUpDiv: Locator;
  readonly popUpCrossIcon: Locator;
  readonly goToHomePageButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, StudentEnterClassPageUrl, isMobile);
    this.classCardName = (className: string) => page.getByText(`${className}`).first();
    this.enterClassButton = page.getByText('Enter Class');
    this.popUpDiv = page.getByText('There is still time for class to startYou can join our classes 15 minutes');
    this.popUpCrossIcon = page.getByTestId('modal-cross-icon').locator('path');
    this.goToHomePageButton = page.getByTestId('modal-element').getByTestId('cta-button');
  }

  async navigateToLiveClass(className) {
    await customExpect(15000)(this.classCardName(className), 'live class card display name is visible').toBeVisible();
    await this.enterClassButton.click();
  }

  async studentJoiningTheClass(meetingId) {
    await this.page.goto(`${this.url}?meetingId=${meetingId}`);
    await this.page.waitForLoadState('networkidle');
    await this.enterClassButton.click();
    await this.page.waitForTimeout(5000); //wait is requried due to gif animation of entering into the class
    await this.page.waitForLoadState('networkidle');
  }

  async studentNavigatingToPreClassPage(meetingId) {
    await this.page.goto(`${this.url}?meetingId=${meetingId}`);
    await this.page.waitForLoadState('networkidle');
  }

  async verifyEarlyJoinPopUp() {
    await expect(this.popUpDiv, 'Verify early join pop up is visible').toBeVisible();
    await expect(this.popUpCrossIcon, 'Verify pop up cross icon is visible').toBeVisible();
    await expect(this.goToHomePageButton, 'Verify go to home page button is visible').toBeVisible();
  }

}