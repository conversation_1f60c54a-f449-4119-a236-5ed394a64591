import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const OnlineTestSeriesPageUrl = '/online-test-series';

export class OnlineTestSeriesPage extends WebPage {
  readonly onlineTestSeriesHeading: Locator;
  readonly discoverThePerfectTestHeading: Locator;
  readonly streamCardViewLink: (streamName: string) => Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, OnlineTestSeriesPageUrl, isMobile);
    this.onlineTestSeriesHeading = page.getByRole('heading', { name: 'Online Test Series', exact: true });
    this.discoverThePerfectTestHeading = page.getByRole('heading', { name: 'Discover the perfect Online' });
    this.streamCardViewLink = (streamName: string) => page.getByRole('link', { name: `${streamName}` + ' View' });
  }

  async verifyOltsPageAndClickOnStream(streamName) {
    await slowExpect(this.onlineTestSeriesHeading, 'online test series heading is visible').toBeVisible();
    await expect(this.discoverThePerfectTestHeading, 'discover the perfect online test heading is visible').toBeVisible();
    await expect(this.streamCardViewLink(streamName), 'course name card is visible').toBeVisible();
    await this.streamCardViewLink(streamName).click();
  }

}