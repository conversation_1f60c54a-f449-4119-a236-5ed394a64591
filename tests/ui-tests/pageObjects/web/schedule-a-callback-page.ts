import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const ScheduleACallbackUrl = '/schedule-a-call-back';

export class ScheduleACallbackHomePage extends WebPage {

  readonly talkToUsTitle: Locator;
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly mobileNumberInput: Locator;
  readonly emailIdInput: Locator;
  readonly selectClass: Locator;
  readonly selectGoal: Locator;
  readonly selectMode: Locator;
  readonly selectState: Locator;
  readonly tncCheckbox: Locator;
  readonly authoriseCheckbox: Locator;
  readonly submitButton: Locator;
  readonly class6thOption: Locator;
  readonly goalNeetOption: Locator;
  readonly onlineProgramsOption: Locator;
  readonly andhraPradeshOption: Locator;
  readonly nameErrorMessage: Locator;
  readonly mobileNumberErrorMessage: Locator;
  readonly emailErrorMessage: Locator;
  readonly checkBoxErrorMessage: Locator;
  readonly teamWillContactText: Locator;
  readonly goToAllenButton: Locator;
  readonly termsCheckbox: Locator;



  constructor(page: Page, isMobile: boolean) {
    super(page, ScheduleACallbackUrl, isMobile);
    this.talkToUsTitle = page.getByText('Schedule a Callback', { exact: true });
    this.firstNameInput = page.getByPlaceholder('Enter first name');
    this.lastNameInput = page.getByPlaceholder('Enter last name');
    this.emailIdInput = page.getByPlaceholder('Enter email ID');
    this.mobileNumberInput = page.getByPlaceholder('Enter 10 digit mobile number');
    this.selectClass = page.getByText('Select class');
    this.selectGoal = page.getByText('Select your goal');
    this.selectMode = page.getByText('Select Mode');
    this.selectState = page.getByText(/Select State|Choose State/);
    this.tncCheckbox = page.locator('div').filter({ hasText: /^I agree to Terms & conditions$/ }).locator('span');
    this.authoriseCheckbox = page.getByTestId('dynamic_page_root').locator('span').nth(1);
    this.submitButton = page.getByRole('button', { name: 'Submit' });
    this.class6thOption = page.getByText('6th', { exact: true });
    this.goalNeetOption = page.getByTestId('dynamic_page_root').getByText('NEET', { exact: true });
    this.onlineProgramsOption = page.getByTestId('dynamic_page_root').getByText('Online Programs', { exact: true });
    this.andhraPradeshOption = page.getByTestId('dynamic_page_root').getByText('Andhra Pradesh', { exact: true });
    this.nameErrorMessage = page.getByText('Please enter a name that is at least 3 characters long and uses only alphabets.');
    this.mobileNumberErrorMessage = page.getByText('Please enter a valid 10-digit number.');
    this.emailErrorMessage = page.getByText('Please enter a valid email address.');
    this.checkBoxErrorMessage = page.getByText('Please tick all the boxes to proceed');
    this.teamWillContactText = page.getByText('Our team will contact you shortly');
    this.goToAllenButton = page.getByRole('button', { name: 'Go to ALLEN' });
    this.termsCheckbox = page.locator('div').filter({ hasText: /^I agree to theterms & conditions$/ }).locator('span');
  }


}