import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities'


const courseDetailsPageUrl = '/course-details';
export class CourseDetailsPage extends WebPage {
  readonly courseNameText: Locator;
  readonly courseLanguageTitle: Locator;
  readonly courseStandardTitle: Locator;
  readonly courseDurationTitle: Locator;
  readonly programOfferings: Locator;
  readonly selectProgramLanguageText: Locator;
  readonly annualFeeText: Locator;
  readonly courseFeeFinalPrice: Locator;
  readonly enrollNowButton: Locator;
  readonly courseStartDateButton: Locator;
  readonly languagePrefrenceValue: Locator;
  readonly languageContainer: Locator;
  readonly languageChangeButton: Locator;
  readonly courseStandardValue: Locator;
  readonly courseDurationValue: Locator;
  readonly courseLanguageValue: Locator;
  readonly liveProgramsTitle: Locator;
  readonly neetUltimateTitle: Locator;
  readonly nurtureOnlineProgram: Locator;
  readonly neetUltimateProgramCard: Locator;
  readonly courseFeeText: Locator;
  readonly closePopupButton: Locator;
  readonly selectBatchAndEnrollButton: Locator;
  readonly loginButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, courseDetailsPageUrl, isMobile);
    this.courseNameText = page.getByTestId('productInfo-title');
    this.courseLanguageTitle = page.locator("(//*[text()='Language'])[1]");
    this.courseStandardTitle = page.getByText('Standard', { exact: true });
    this.courseDurationTitle = page.getByText('Duration', { exact: true });
    this.programOfferings = page.getByText(/Program Offerings|Course Offerings/);
    this.selectProgramLanguageText = page.getByText(/Select program language|Select course language/);
    this.annualFeeText = isMobile ? page.getByText('Annual Fee').nth(1) : page.getByRole('heading', { name: 'Annual Fee' });
    this.courseFeeFinalPrice = isMobile ? page.locator('p').filter({ hasText: '₹' }) :page.getByTestId('productInfo-paymentPrice');
    this.enrollNowButton = isMobile ? page.getByRole('button', { name: 'Continue' }) : page.getByTestId('enroll-btn');
    this.courseStartDateButton = page.getByTestId('preference-label-2-0');
    this.languagePrefrenceValue = page.locator("//*[@id='listingOptions_undefined']/*");
    this.languageContainer = page.locator("//*[@id='listingOptions_undefined']/*");
    this.languageChangeButton = page.getByTestId('dls-button').getByText('Change');
    this.courseStandardValue = page.getByTestId('course-standard-1').first();
    this.courseDurationValue = page.getByTestId('course-duration-1').first();
    this.courseLanguageValue = page.getByTestId('course-language-1').locator('visible=true');
    this.liveProgramsTitle = page.getByTestId('title').getByText(/Live Online Programs|Live Programs|Live Online Courses/);
    this.neetUltimateTitle = page.getByTestId('title').getByText('NEET Ultimate');
    this.nurtureOnlineProgram = page.getByTestId(/Nurture Online Program|Nurture Online Course/).first().locator('visible=true');
    this.neetUltimateProgramCard = page.getByTestId('NEET Ultimate : Target 2025').locator('visible=true');
    this.closePopupButton = page.getByTestId('dynamicPopupCloseBtn');
    this.courseFeeText = isMobile ? page.getByText('Course Fee').nth(1) : page.getByRole('heading', { name: 'Course Fee' });
    this.selectBatchAndEnrollButton = page.getByRole('button', { name: 'Select batch and enroll' });
    this.loginButton = page.getByTestId('mwebLoginCtaButton');
  }

  async verifyCourseDetails() {
    await slowExpect(this.courseNameText, " verify course name text is visible").toBeVisible();
    await slowExpect(this.courseNameText, " verify course name text is not to be null").not.toBeNull();

    if (this.isMobile) {
      await expect(this.selectBatchAndEnrollButton, "Verify select batch and enroll button is visible").toBeVisible()
      await this.selectBatchAndEnrollButton.click();
    }
  }

  async selectBatchAndEnroll() {
    await expect(this.selectProgramLanguageText, "verify select program text is visible").toBeVisible();

    const feeText = (await this.courseFeeText.isVisible()) ? this.courseFeeText : this.annualFeeText;
    await expect(feeText, "verify course/annual fee text is visible").toBeVisible();

    await expect(this.courseFeeFinalPrice, "verify final fee price is visible").toBeVisible();
    const courseValueText = await this.courseFeeFinalPrice.textContent();
    const courseValue = courseValueText!.split(' ')[1];
    expect(parseInt(courseValue)).toBeGreaterThan(0);

    await expect(this.enrollNowButton, "verify enroll now button is disabled").toBeDisabled();
    await expect(this.courseStartDateButton, "verify course start date button is visible").toBeVisible();
    await this.courseStartDateButton.click();
  }
  
}