
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import {EnvUtils} from '../../../commons/env-utilities'


const CoursePageUrl = '/courses';

class CourseCardWidget {
  readonly yourTestTitle: Locator;
  readonly courseCardTitleText: (courseCardTitle: string) => Locator;
  readonly courseDurationText: (courseCardTitle: string, classDuration: string) => Locator;
  readonly knowMoreButton: (knowMore: string) => Locator;
  readonly courseCardLink: Locator;
  readonly courseKnowMoreButton: Locator;

  constructor(page: Page) {
    this.courseCardTitleText = (courseCardTitle: string) => page.getByTestId(`${courseCardTitle}`);
    this.courseDurationText = (courseCardTitle: string, courseDuration: string) => page.locator(`//*[@data-testid='${courseCardTitle}']/ancestor::*[contains(@class,'shadow-courseListing')]/descendant::*[@data-testid='${courseDuration}']`);
    this.knowMoreButton = (courseCardTitle: string) => page.locator(`//*[@data-testid='${courseCardTitle}']/ancestor::*[contains(@class,'shadow-courseListing')]/descendant::*[@data-testid='ctaButtonLabel']`);
    this.courseCardLink = page.locator('a[href^="/course-details"]');
    this.courseKnowMoreButton = page.locator('a[href^="/course-details"] div[data-testid="ctaButton"] div[data-testid="ctaButtonLabel"]');
  }

  async validateCourseCardDetailsAndClickOnCard(courseCardTitle, courseDuration) {
    await expect(this.courseCardTitleText(courseCardTitle), 'course card title is visible').toBeVisible();
    await expect(this.courseDurationText(courseCardTitle, courseDuration), 'course card duration is visible').toBeVisible();
    await expect(this.knowMoreButton(courseCardTitle), 'Know more button is visible').toBeVisible();
    await this.courseCardTitleText(courseCardTitle).click();

  }
}
export class CoursePage extends WebPage {
  readonly AllProgramsText: Locator;
  readonly OneYearProgramsText: Locator;
  readonly JEEUltimateCourse: Locator;
  readonly LiveProgram: Locator;
  readonly EnrollNowButton: Locator;
  readonly KnowMoreButton: Locator;
  readonly StartingDateRadioButton: Locator;
  readonly JeeTestingCourseText: Locator;
  readonly programHeadingText: (programHeading: string) => Locator;
  readonly programDurationText: (programDuration: string) => Locator;
  readonly courseCardWidget: CourseCardWidget;
  readonly alreadyPurchasedCourseText: Locator;
  readonly courseCardForNEET: Locator;
  readonly knowMoreButtonForNEETCourse: Locator;
  readonly testingWidgetCard: (widgetTitle: string) => Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, CoursePageUrl, isMobile);
    this.AllProgramsText = page.getByText('All Programs');
    this.OneYearProgramsText = page.getByText('1 year programs');
    this.JEEUltimateCourse = page.getByTestId('1 Year | 2024-2025');
    this.LiveProgram = page.getByText('Live Program');
    this.EnrollNowButton = page.getByTestId('enroll-btn');
    this.KnowMoreButton = page.getByTestId('ctaButtonLabel').nth(1);
    this.StartingDateRadioButton = page.getByTestId('preference-2-edit').locator('label').first();
    this.JeeTestingCourseText = page.getByText('JEE Testing QA');
    this.programHeadingText = (programHeading: string) => page.getByText(`${programHeading}`);
    this.programDurationText = (programDuration: string) => page.getByText(`${programDuration}`);
    this.courseCardWidget = new CourseCardWidget(page);
    this.alreadyPurchasedCourseText = page.getByText('Looks like you have already purchased a course').locator('visible=true');
    this.courseCardForNEET = EnvUtils.getInstance().isProd() 
      ? page.getByRole('link', { name: /NEET Nurture Online Course/i }).first()
      : page.locator("xpath=//div[contains(@class, 'lg:grid-cols-2')]/*[1]");
    this.knowMoreButtonForNEETCourse = this.courseCardForNEET.locator('[data-testid="ctaButtonLabel"]');
    this.testingWidgetCard = (widgetTitle: string) => page.getByRole('link', { name: widgetTitle });
  }

  async verifyProgramHeadingAndCardDetails(programHeading, programDuration, courseCardTitle, courseDuration) {
    await slowExpect(this.programHeadingText(programHeading), "verify program heading text is visible").toBeVisible();
    await expect(this.programDurationText(programDuration), "verify program duration text is visible").toBeVisible();
    await this.courseCardWidget.validateCourseCardDetailsAndClickOnCard(courseCardTitle, courseDuration);
  }

  async verifyCourseCardDetails() {
    await slowExpect(this.courseCardWidget.courseCardLink.first(), "verify course card is visible").toBeVisible();
    expect(await this.courseCardWidget.courseCardLink.first().textContent(), "verify course card have card name").not.toBeNull();
    await expect(this.courseCardForNEET, "Verify NEET course card is visible").toBeVisible();
    await expect(this.courseCardForNEET.first(), "verify course card Know more button is visible").toBeVisible();
    await this.courseCardForNEET.first().click();
  }

}
