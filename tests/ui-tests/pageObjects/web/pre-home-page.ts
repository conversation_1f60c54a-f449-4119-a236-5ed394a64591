import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const PreHomePageUrl = '/'

class LoginWidget {
  readonly page: Page;
  readonly allenImage: Locator;
  readonly loginFormTitle: Locator;
  readonly mobileNumberInput: Locator;
  readonly sendOtpButton: Locator;
  readonly loginWithUserNameButton: Locator;

  readonly otpFormTitle: Locator;
  readonly otpFormSubtitle: Locator;
  readonly otpInputField0: Locator;
  readonly otpInputField1: Locator;
  readonly otpInputField2: Locator;
  readonly otpInputField3: Locator;
  readonly retryOTPButton: Locator;
  readonly verifyOTPButton: Locator;

  readonly usernameLoginFormTitle: Locator;
  readonly usernameInput: Locator;
  readonly passwordInput: Locator;
  readonly forgotPasswordButton: Locator;
  readonly loginButton: Locator;
  readonly forgotPasswordTitle: Locator;
  readonly nextButton: Locator;
  readonly forgotPasswordSubtitle: Locator;
  readonly mobileNumberRadioButton: Locator;
  readonly enterOtpTitle: Locator;
  readonly resetPasswordTitle: Locator;
  readonly enterNewPasswordField: Locator;
  readonly confirmNewPasswordField: Locator;
  readonly savePasswordButton: Locator;
  readonly loaderSection: Locator;
  readonly goBackButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    this.page = page
    this.allenImage = page.getByRole('img', { name: 'ALLEN' });
    this.loginFormTitle = page.getByTestId('loginFormTitle').nth(1);
    this.mobileNumberInput = page.getByRole('textbox', { name: 'Enter 10-digit mobile number' });
    this.sendOtpButton = page.getByRole('button', { name: 'Send OTP' });

    this.otpFormTitle = page.getByTestId('OTPFormTitle').nth(1);
    this.otpFormSubtitle = page.getByTestId('OTPFormSubTitle').nth(1);
    this.otpInputField0 = isMobile ? page.getByTestId('OTPInputField-0').first() : page.getByTestId('OTPInputField-0').nth(1);
    this.otpInputField1 = isMobile ? page.getByTestId('OTPInputField-1').first() : page.getByTestId('OTPInputField-1').nth(1);
    this.otpInputField2 = isMobile ? page.getByTestId('OTPInputField-2').first() : page.getByTestId('OTPInputField-2').nth(1);
    this.otpInputField3 = isMobile ? page.getByTestId('OTPInputField-3').first() : page.getByTestId('OTPInputField-3').nth(1);
    this.retryOTPButton = page.getByTestId('RetryOTPButton').nth(1);
    this.verifyOTPButton = page.getByRole('button', { name: 'Verify' });

    this.loginWithUserNameButton = page.getByRole('button', { name: 'Continue with Email ID' });
    this.usernameLoginFormTitle = isMobile ? page.getByRole('heading', { name: 'Login with email ID' }) : page.getByTestId('usernameLoginFormTitle').nth(1);
    this.usernameInput = page.getByRole('textbox', { name: 'Email' });
    this.passwordInput = page.getByTestId('password').locator('visible=true');
    this.forgotPasswordButton = page.getByRole('button', { name: 'Forgot password?' });
    this.loginButton = isMobile ? page.getByTestId('usernameLoginFormButtonWeb') : page.locator("//*[@data-testid='usernameLoginFormButtonMweb']");
    this.forgotPasswordTitle = isMobile ? page.getByTestId('forgotFormTitle').first() : page.getByTestId('forgotFormTitle').nth(1);
    this.nextButton = page.getByRole('button', { name: 'Next' });
    this.forgotPasswordSubtitle = isMobile ? page.getByTestId('forgotFormSubtitle').first().getByText('Select your registered contact information to receive an OTP for resetting your password.') : page.getByTestId('forgotFormSubtitle').nth(1).getByText('Select your registered contact information to receive an OTP for resetting your password.');
    this.mobileNumberRadioButton = isMobile ? page.getByTestId('radio').first() : page.getByTestId('radio').nth(2);
    this.enterOtpTitle = page.getByTestId('OTPFormTitle').nth(1).getByText('Enter OTP');
    this.resetPasswordTitle = isMobile ? page.getByTestId('resetFormTitle').first() :  page.getByTestId('resetFormTitle').nth(1);
    this.enterNewPasswordField = page.getByRole('textbox', { name: 'Enter new password' });
    this.confirmNewPasswordField = page.getByRole('textbox', { name: 'Confirm password' });
    this.savePasswordButton = page.getByRole('button', { name: 'Save' });
    this.loaderSection = page.getByTestId('section-loader');
    this.goBackButton = page.getByTestId('dls-button').first();
  }

  async loginWithUsername(username, password) {
    await this.loginWithUserNameButton.click();
    await this.usernameInput.fill(username);
    await this.passwordInput.fill(password);
    await expect(this.loginButton, "Verify login button should be enabled").toBeEnabled();
    await this.page.waitForTimeout(1000);
    // const requestPromise = this.page.waitForRequest('**/auth/*'); // commented to check the code in jenkin run
    await this.loginButton.click();
    // await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
    await slowExpect(this.loaderSection, "Verify loader section should not visible").toBeHidden();
    // await requestPromise // commented to check the code in jenkin run
  }

  async forgotPasswordFlow(email, password, otp) {
    await expect(this.loginWithUserNameButton, "verify login with email is visible").toBeVisible();
    await this.loginWithUserNameButton.click();
    await expect(this.usernameLoginFormTitle, "verify email login form title is visible").toBeVisible();
    await expect(this.forgotPasswordButton, "verify forgot password button is visible").toBeVisible();
    await this.forgotPasswordButton.click();
    await expect(this.forgotPasswordTitle, "verify forgot password title is visible").toBeVisible();
    await expect(this.usernameInput, "verify username input field is visible").toBeVisible();
    await this.usernameInput.fill(email);
    await expect(this.nextButton, "verify next button is visible").toBeVisible();
    await this.nextButton.click();
    await expect(this.forgotPasswordSubtitle, "verify forgot password subtitle text is visible").toBeVisible();
    await expect(this.mobileNumberRadioButton, "verify mobile number radio button is visible").toBeVisible();
    await this.mobileNumberRadioButton.click();
    await expect(this.sendOtpButton, "verify send otp button is visible").toBeVisible();
    await expect(this.sendOtpButton, "verify send otp button is enabled").toBeEnabled();
    await this.sendOtpButton.click();
    await expect(this.enterOtpTitle, "verify enter otp title is enabled").toBeEnabled();
    await this.otpInputField0.click();
    const otp_arr = otp.split('');
    await this.otpInputField0.fill(otp_arr[0]);
    await this.otpInputField1.fill(otp_arr[1]);
    await this.otpInputField2.fill(otp_arr[2]);
    await this.otpInputField3.fill(otp_arr[3]);
    await expect(this.verifyOTPButton, "verify otp button is enabled").toBeEnabled();
    await this.verifyOTPButton.click();
    await expect(this.resetPasswordTitle, "verify reset password title is visible").toBeVisible();
    await expect(this.enterNewPasswordField, "verify enter new password input field is visible").toBeVisible();
    await this.enterNewPasswordField.click();
    await this.enterNewPasswordField.fill(password);
    await expect(this.confirmNewPasswordField, "verify confirm new password input field is visible").toBeVisible();
    await this.confirmNewPasswordField.click();
    await this.confirmNewPasswordField.fill(password);
    await expect(this.savePasswordButton, "verify save password button field is visible").toBeVisible();
    await this.savePasswordButton.click();
  }

  async loginWithMobileNumber(mobileNumber, otp) {
    await this.mobileNumberInput.click();
    await this.mobileNumberInput.fill(mobileNumber);
    await this.sendOtpButton.click();
    await this.otpInputField0.click();
    const otp_arr = otp.split('');
    await this.otpInputField0.fill(otp_arr[0]);
    await this.otpInputField1.fill(otp_arr[1]);
    await this.otpInputField2.fill(otp_arr[2]);
    await this.otpInputField3.fill(otp_arr[3]);
    // const requestPromise = this.page.waitForRequest('**/auth/*'); // commented to check the code in jenkin run
    await this.verifyOTPButton.click();
    // await requestPromise // commented to check the code in jenkin run
  }
}

export class PreHomePage extends WebPage {
  readonly allenImage: Locator;
  readonly examsMenu: Locator;
  readonly programsMenu: Locator;
  readonly scholarshipsMenu: Locator;
  readonly loginButton: Locator;
  readonly talkToUsButton: Locator;
  readonly displayName: Locator;
  readonly userTitle: Locator;
  readonly loginWidget: LoginWidget;
  readonly jeeButton: Locator;
  readonly neetButton: Locator;
  readonly class11Link: Locator;
  readonly invalidLoginText: Locator;
  readonly iSymbolIcon: Locator;
  readonly usernameInfoText: Locator;
  readonly userInfoDetails: Locator;
  readonly gotItButton: Locator;
  readonly editNumberButton: Locator;
  readonly enterValidNumberText: Locator;
  readonly selectYourStreamTitle: Locator;
  readonly selectYourClassTitle: Locator;
  readonly class11Th: Locator;
  readonly class12Th: Locator;
  readonly exploreCourses: Locator;
  readonly exploreMenu: Locator;
  readonly sectionLoader: Locator;
  readonly exploreOnlineCoursesButton: Locator;
  readonly closePopupButton: Locator;
  readonly mWebAvatar: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, PreHomePageUrl, isMobile);
    this.allenImage = page.getByRole('img', { name: 'ALLEN' });
    this.examsMenu = page.getByTestId('Exams');
    this.programsMenu = page.getByTestId('Programs');
    this.scholarshipsMenu = page.getByTestId('Scholarships');
    this.loginButton = page.getByTestId(/loginCtaButton|mwebLoginCtaButton/);
    this.talkToUsButton = page.getByRole('button', { name: 'Talk to us' });
    this.displayName = page.getByTestId('user-name').first();
    this.userTitle = page.getByTestId('user-title').first();
    this.loginWidget = new LoginWidget(page, isMobile);
    this.jeeButton = page.getByTestId('onboardingChipDisplayName').getByText('JEE');
    this.neetButton = EnvUtils.getInstance().isProd() ? page.getByTestId('dialog').getByRole('button', { name: 'NEET' }) : page.getByRole('button', { name: 'NEET', exact: true });
    this.class11Link = page.getByTestId('Starting Class 11');
    this.invalidLoginText = page.getByText('Invalid login credentials. Please try again.').locator('visible=true');
    this.iSymbolIcon = page.getByRole('button').first();
    this.usernameInfoText = page.getByText('Username information');
    this.userInfoDetails = page.getByText('For classroom students, the');
    this.gotItButton = page.getByRole('button', { name: 'Got it' });
    this.editNumberButton = page.getByTestId('OTPChangeButton').locator('visible=true');
    this.enterValidNumberText = page.getByText('Please enter a valid 10-digit number.').locator('visible=true');
    this.selectYourStreamTitle = page.getByText('Select your stream').locator('visible=true');
    this.selectYourClassTitle = page.getByText(/Select your class|Select your grade/).locator('visible=true');
    this.class11Th = page.getByRole('button', { name: '11th' });
    this.class12Th = page.getByRole('button', { name: '12th', exact: true });
    this.exploreCourses = page.getByRole('button', { name: 'Explore Courses' });
    this.exploreMenu = page.getByRole('heading', { name: 'Explore', exact: true });
    this.sectionLoader = page.getByTestId('section-loader');
    this.exploreOnlineCoursesButton = page.getByRole('button', { name: 'Explore Online Courses' })
    this.closePopupButton = page.getByTestId('dynamicPopupCloseBtn');
    this.mWebAvatar = page.getByTestId('user-mweb-avatar').locator('div').first();

  }

  async isLoggedIn() {
    const element = this.loginButton.or(this.exploreMenu).or(this.userTitle).or(this.displayName).or(this.mWebAvatar).locator('visible=true').first();
    const loggedInElement = this.exploreMenu.or(this.userTitle).or(this.displayName).or(this.mWebAvatar).locator('visible=true').first();
    await expect(element).toBeVisible();
    await slowExpect(this.sectionLoader, "Verify section loader should not visible").toBeHidden();
    return await loggedInElement.isVisible();
  }

  async loginWithUsername(username, password) {
    await this.loginButton.click();
    await this.loginWidget.loginWithUsername(username, password);
    // await this.page.waitForLoadState('networkidle'); // commented to check the code in jenkin run 
  }

  async loginWithMobileNumber(mobileNumber, otp?: string) {
    await this.loginButton.click();
    await this.loginWidget.loginWithMobileNumber(mobileNumber, `${otp ?? '1111'}`);
    // await this.page.waitForLoadState('networkidle'); // commented to check the code in jenkin run 
  }
  async clickOnAllen() {
    await this.allenImage.click();
  }

  async forgotPasswordFlow(email, password, otp?: string) {
    await slowExpect(this.loginButton, "verify login button is visible").toBeVisible();
    await this.loginButton.click();
    await this.loginWidget.forgotPasswordFlow(email, password, `${otp ?? '1111'}`);
    await slowExpect(this.loginWidget.loginWithUserNameButton, "verify login with email is visible").toBeVisible();
    await this.loginWidget.loginWithUsername(email, password);
  }

}
