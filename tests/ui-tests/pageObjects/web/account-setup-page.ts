
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const AccountSetupPageUrl = '/POSTPURCHASE'
export class AccountSetupPage extends WebPage {
  readonly HeaderDetails: Locator;
  readonly FillEmergencyContact: Locator;
  readonly ContinueButton: Locator;
  readonly FillAddressDetailsText: Locator;
  readonly Address: Locator;
  readonly Locality: Locator;
  readonly City: Locator;
  readonly State: Locator;
  readonly SameAsBuildingAddress: Locator;
  readonly UploadAcademicFilesText: Locator;
  readonly LatestMarksheetText: Locator;
  readonly UploadMarksheetButton: Locator;
  readonly SuccessIcon: Locator;
  readonly SubmitButton: Locator;
  readonly AccountSetupButton: Locator;
  readonly marksheetUploadErrorText: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, AccountSetupPageUrl, isMobile);
    this.HeaderDetails = page.getByText('Need this for sending academic purposes');
    this.FillEmergencyContact = page.getByText('Fill emergency contact');
    this.ContinueButton = page.getByRole('button', { name: 'Continue' });
    this.FillAddressDetailsText = page.getByText('Fill address details');
    this.Address = page.getByPlaceholder('house no, buliding no, street').first();
    this.Locality = page.getByPlaceholder('Ex: Kormangala').first();
    this.City = page.getByPlaceholder('Ex. Bangaluru').first();
    this.State = page.getByPlaceholder('Ex. Karnataka').first();
    this.UploadAcademicFilesText = page.getByText('Upload Academic files');
    this.LatestMarksheetText = page.getByText('Latest Marksheet');
    this.UploadMarksheetButton = page.locator('#Latest\\ Marksheet-file-upload');
    this.SuccessIcon = page.getByRole('img', { name: 'success-icon' });
    this.SubmitButton = page.getByRole('button', { name: 'Submit' });
    this.AccountSetupButton = page.getByRole('img', { name: 'Go', exact: true });
    this.marksheetUploadErrorText = page.getByText("File size exceeding more than 5MB");
  }

  async verifyAndUploadMarksheet(filepath: string) {
    await expect(this.UploadAcademicFilesText, 'verifying "Upload Academic Files" text should be visible').toBeVisible();
    await expect(this.LatestMarksheetText, 'verifying "Latest Marksheet" input box should be visible').toBeVisible();
    await this.UploadMarksheetButton.setInputFiles(filepath)
    await slowExpect(this.SuccessIcon, 'verifying "Success icon" should be visible').toBeVisible();
    await slowExpect(this.SubmitButton, 'verifying "Submit" button should be visible').toBeEnabled();
    await this.SubmitButton.click();
  }

}
