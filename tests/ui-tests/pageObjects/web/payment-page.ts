
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { customExpect } from '../../fixtures/ui-fixtures';

const PaymentPageUrl = 'https://sandbox.assets.juspay.in/payment-page/order/ordeh_81e929637219490c96de0903d4c4c2a0'
const waitTime = 15000;

export class PaymentPage extends WebPage {
  readonly CardDetails: Locator;
  readonly Netbanking: Locator;
  readonly ICICINetbanking: Locator;
  readonly ProceedToPay: Locator;
  readonly SuccessButton: Locator;
  readonly FailureButton: Locator;
  readonly WelcomeToICICIBankText: Locator;
  readonly PaymentSuccessful: Locator;
  readonly OrderDetails: Locator;
  readonly YourPurchaseIsCompleteText: Locator;
  readonly SetupYourAccount: Locator;
  readonly AccountSetupButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, PaymentPageUrl, isMobile);
    this.CardDetails = page.getByText('Enter Credit / Debit card details');
    this.Netbanking = page.locator("//*[@testid='nvb_net_banking']").locator('visible=true');
    this.ICICINetbanking = isMobile ? page.getByText('ICICI') : page.getByText('ICICI Netbanking');
    this.ProceedToPay = page.getByRole('button', { name: 'Pay' });
    this.SuccessButton = page.getByRole('button', { name: 'Success' });
    this.FailureButton = page.getByRole('button', { name: 'Failure' });
    this.WelcomeToICICIBankText = page.getByText('Welcome to ICICI bank Bank');
    this.PaymentSuccessful = page.getByText('PAYMENT SUCCESSFUL');
    this.OrderDetails = page.getByText('Order details');
    this.YourPurchaseIsCompleteText = page.getByText('Your purchase is complete,');
    this.SetupYourAccount = page.getByText('Setup your account');
    this.AccountSetupButton = page.getByRole('img', { name: 'Go', exact: true });
  }

  async paymentSuccess() {
    if (!this.isMobile) {
      await customExpect(waitTime)(this.CardDetails, 'verifying "card Details" should be visible').toBeVisible();
      await this.Netbanking.click();
    }

    await customExpect(waitTime)(this.ICICINetbanking, 'verifying "ICICINetbanking" text should be visible').toBeVisible();
    await this.ICICINetbanking.click();

    if (!this.isMobile) {
      await customExpect(waitTime)(this.ProceedToPay, 'verifying "ProceedToPay" text should be visible').toBeVisible();
      await this.ProceedToPay.click();
    }

    await customExpect(waitTime)(this.SuccessButton, 'verifying "Success" button should be visible',).toBeVisible();
    await this.SuccessButton.click();
  }
  async verifySuccessPaymentPageDetails() {
    await customExpect(waitTime)(this.PaymentSuccessful, 'verifying "PAYMENT SUCCESSFUL" text should be visible').toBeVisible();
    await expect(this.OrderDetails, 'verifying "Order details" text should be visible').toBeVisible();
    await expect(this.YourPurchaseIsCompleteText, 'verifying "Your purchase is complete" text should be visible').toBeVisible();
    await expect(this.SetupYourAccount, 'verifying "Setup Your Account" text should be visible').toBeVisible();
  }

}