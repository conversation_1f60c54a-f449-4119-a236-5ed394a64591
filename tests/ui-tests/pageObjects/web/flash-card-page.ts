import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { customExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';
const FlashCardPageUrl = '/flash-card';

const currentEnv = EnvUtils.getInstance()
export class FlashCardPage extends WebPage {

    readonly pickSubjectSelectTopicTitleText: Locator;
    readonly selectSubjectsTitleText: Locator;
    readonly selectRequiredTopicTitleText: Locator;
    readonly subjectText: (subjectName: string) => Locator;
    readonly topicNameText: (topicName: string) => Locator;
    readonly startButton: Locator;
    readonly introducingText: Locator;
    readonly flashCardsText: Locator;
    readonly getStartedButton: Locator;
    readonly tapToFlipCardText: Locator;
    readonly tapToFlipCardButton: Locator;

    readonly swipeRightIfYouKnowItText: Locator;
    readonly swipeLeftIfYouDontKnowItText: Locator;
    readonly topicHeading: (topicName: string) => Locator;
    readonly closeFlashCardIcon: Locator;

    readonly stillLearningButton: Locator;
    readonly iKnowThisButton: Locator;
    readonly showAHintButton: Locator;
    readonly questionText: Locator;
    readonly hintButton: Locator;
    readonly anwserText: Locator;
    readonly goingSoSoonTitleText: Locator;
    readonly youCanContinueRevisingText: Locator;
    readonly knewAnwsersForCard: Locator;
    readonly stillLearningForCard: Locator;
    readonly stillLearningText: Locator;
    readonly keepPracticingButton: Locator;
    readonly closeButton: Locator;
    readonly thatsAllText: Locator;
    readonly youHaveFinishedText: Locator;
    readonly completedCardDoneText: Locator;
    readonly completedText: Locator;
    readonly practiceAgainButton: Locator;
    readonly afterSwipeCardCount: (swipeCount: string) => Locator;
    readonly thatWasQuickTitleText: Locator;
    readonly youHaveSpentLessSecondsText: Locator;
    readonly loadingMoreCardsButton: Locator;
    readonly getCardValue: Locator;
    readonly loadingDecksCardsButton: Locator;
    readonly flashcardsToBeReviewedCount: Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, FlashCardPageUrl, isMobile);

        this.pickSubjectSelectTopicTitleText = page.getByText('Pick a subject & select the');
        this.selectSubjectsTitleText = page.getByText('Select Subjects');
        this.selectRequiredTopicTitleText = page.getByText('Select required topics');
        this.subjectText = (subjectName: string) => page.getByText(`${subjectName}`, { exact: true }).first();
        this.topicNameText = (topicName: string) => page.locator('div').filter({ hasText: `${topicName}` }).first();
        this.startButton = page.getByRole('button', { name: 'Start' });
        this.topicHeading = (topicName: string) => page.getByRole('heading', { name: `${topicName}` });
        this.closeFlashCardIcon = page.getByRole('img', { name: 'Close' });
        this.questionText = page.locator(`//*[@id="question"]//*`).first();
        this.introducingText = page.getByText('Introducing');
        this.flashCardsText = page.getByText('flashcards');
        this.getStartedButton = page.locator('//*[text()="Get started"]');
        this.tapToFlipCardText = page.getByText('tap to flip card for answer');
        this.tapToFlipCardButton = page.locator('#fingertap path').nth(3);
        this.swipeRightIfYouKnowItText = page.getByText('swipe right if you know it');
        this.swipeLeftIfYouDontKnowItText = page.getByText('swipe left if you don\'t know');
        this.hintButton = page.getByText('HINT');
        this.anwserText = page.locator(`//*[text()="ANSWER"]`);
        this.knewAnwsersForCard = page.locator(`//*[text()="Knew answers for"]/following-sibling::*`);
        this.stillLearningForCard = page.locator(`//*[text()="Still learning"]/following-sibling::*`);
        this.stillLearningText = page.getByText('Still learning');
        this.goingSoSoonTitleText = page.getByRole('heading', { name: 'Going so soon?' });
        this.youCanContinueRevisingText = page.getByText('You can continue revising');
        this.keepPracticingButton = page.getByRole('button', { name: 'Keep Practicing' });
        this.closeButton = page.getByRole('button', { name: 'Close' });
        this.thatsAllText = page.getByRole('heading', { name: 'That was all!' });
        this.youHaveFinishedText = page.getByText('Yay! You have finished all');
        this.completedCardDoneText = page.locator(`//*[contains(text(),'Done')]`);
        this.completedText = page.getByText('Decks finished');
        this.practiceAgainButton = page.getByRole('button', { name: 'Practice Again' });
        this.afterSwipeCardCount = (swipeCount: string) => page.locator(`//*[contains(@class,'text-gray')]//*[contains(text(),'${swipeCount}')]`);
        this.iKnowThisButton = page.getByRole('button', { name: 'I know this' });
        this.stillLearningButton = page.getByRole('button', { name: 'Still learning' });
        this.showAHintButton = page.getByText('SHOW A HINT');
        this.thatWasQuickTitleText = page.getByRole('heading', { name: 'That was quick!' });
        this.youHaveSpentLessSecondsText = page.getByText('You have spent less than 60 seconds per flashcard in this session.');
        this.loadingMoreCardsButton = page.getByText('Last deck remaining');
        this.getCardValue = page.locator(`//*[@class="font-medium text-xs no-underline text-default-caption"]`);
        this.loadingDecksCardsButton = page.getByText('Loading next deck of cards');
        this.flashcardsToBeReviewedCount = page.locator("//div[p and following-sibling::p[contains(text(), 'Flashcards to be reviewed')]]/p[1]");    
        

    }

    async validateSelectSubjectAndTopic() {
        await expect(this.pickSubjectSelectTopicTitleText, "Verify pick the subjects & select topics title text is visible").toBeVisible();
        await expect(this.selectSubjectsTitleText, "Verify select subjects title text is visible").toBeVisible();
        await expect(this.selectRequiredTopicTitleText, "Verify select required topic title text is visible").toBeVisible();
    }

    async checkAndClickSubjectUnderSelectSubject(subjectName) {
        await slowExpect(this.subjectText(subjectName).first(), "Verify subject text is visible").toBeVisible();
        await this.subjectText(subjectName).first().click();
    }

    async checkAndClickTopicUnderSelectTopic(topicName) {
        await expect(this.startButton, "Verify start button is disabled").toBeDisabled();
        await this.subjectText(topicName).scrollIntoViewIfNeeded({ timeout: 10000 });
        await slowExpect(this.subjectText(topicName).first(), "Verify subject text is visible").toBeVisible();
        await this.subjectText(topicName).first().click();
        await expect(this.startButton, "Verify start button is visible").toBeEnabled();
    }

    async clickOnStartButton(baseURL) {
        await slowExpect(this.startButton, "Verify start button is visible").toBeVisible();
        await this.startButton.click();
        await this.page.waitForTimeout(1000); //required to load the page
    }
    async clickOnGetStartButton() {
        if (await this.introducingText.isVisible({ timeout: 10000 })) {
            await slowExpect(this.introducingText, "Verify introducing text is visible").toBeVisible();
            await expect(this.flashCardsText, "Verify flashcards text is visible").toBeVisible();
            await expect(this.getStartedButton, "Verify get started button is visible").toBeVisible();
            await this.getStartedButton.click();
            await expect(this.tapToFlipCardText, "Verify tap to flip card text is visible").toBeVisible();
            await expect(this.tapToFlipCardButton, "Verify tap to flip card  button is visible").toBeVisible();
            await this.tapToFlipCardButton.click({ force: true });
            await this.page.waitForTimeout(1000); //required to load the page
            await slowExpect(this.swipeRightIfYouKnowItText, "Verify swipe to right if you don't know text is visible").toBeVisible();
            await expect(this.iKnowThisButton, "Verify i know this button is enabled").toBeEnabled();
            await expect(this.iKnowThisButton, "Verify i know this button is visible").toBeVisible();
            await this.iKnowThisButton.click();

            await slowExpect(this.swipeLeftIfYouDontKnowItText, "Verify swipe to left if you know text is visible").toBeVisible();
            await expect(this.stillLearningButton, "Verify still learningbutton is enabled").toBeEnabled();
            await expect(this.stillLearningButton, "Verify still learningbutton is visible").toBeVisible();
            await this.stillLearningButton.click();
        }
    }

    async clickOnCloseFlashCardIcon() {
        await slowExpect(this.closeFlashCardIcon, "Verify close flash card icon is visible").toBeVisible();
        await this.closeFlashCardIcon.click();
    }


    async verifyFlashCardPage(topicName, flashCardTotalCount, getTotalCount) {
        await slowExpect(this.topicHeading(topicName).first(), "Verify topic heading text is visible").toBeVisible();
        await expect(this.getCardValue, "Verify total count cards is visible").toBeVisible();
        // const getTotalCount = this.getCardSwipeCount();
        await expect(getTotalCount, "Verify the total cards is visible").toBe(flashCardTotalCount);
        await expect(this.closeFlashCardIcon, "Verify close flash card icon is visible").toBeVisible();
        await expect(this.stillLearningButton, "Verify still learning button is visible").toBeVisible();
        await expect(this.iKnowThisButton, "Verify i know this button is visible").toBeVisible();
        await expect(this.iKnowThisButton, "Verify i know this button is enabled").toBeEnabled();
        await slowExpect(this.showAHintButton, "Verify show a hint button is visible").toBeVisible();
        await this.showAHintButton.click();
        await expect(this.hintButton, "Verifyhint button is visible").toBeVisible();
        await this.hintButton.click();
        await expect(this.questionText, "Verify question is visible").toBeVisible();
        await this.questionText.click({ force: true });
        await expect(this.anwserText, "Verify anwer text is visible").toBeVisible();
        await this.anwserText.click({ force: true });
    }

    async clickOnSwipeIKnowThisButtonTimes(n) {
        for (let i = 0; i < n; i++) {
            await expect(this.iKnowThisButton, "Verify i know this button is visible").toBeVisible();
            await this.iKnowThisButton.click();
            // Optional: Add a delay if needed
            await new Promise(resolve => setTimeout(resolve, 500));
        }

    }

    async clickOnSwipeStillLearningButtonTimes(n) {

        for (let i = 0; i < n; i++) {
            await expect(this.stillLearningButton, "Verify still learning button is visible").toBeVisible();
            await this.stillLearningButton.click();
            // Optional: Add a delay if needed
            await new Promise(resolve => setTimeout(resolve, 500));
        }

    }
    async validateSwipeCards(knewCard, stillLearning) {
        await expect(this.goingSoSoonTitleText, "Verify going so soon title text is visible").toBeVisible();
        await expect(this.youCanContinueRevisingText, "Verify you can continue revising this text is visible").toBeVisible();
        await expect(this.closeButton, "Verifyclose button is visible").toBeVisible();
        await expect(this.keepPracticingButton, "Verify keep practicing button is visible").toBeVisible();

        await expect(this.knewAnwsersForCard, "Verify keep practicing button is visible").toBeVisible();
        await expect(this.stillLearningForCard, "Verify keep practicing button is visible").toBeVisible();


        const knewAnwsersCrad = await this.knewAnwsersForCard;
        const knewAnwsersCount = await knewAnwsersCrad.textContent() ?? "";

        const knewAnwCard = knewAnwsersCount.split(" ")[0].trim();

        const stillLearningCrad = await this.stillLearningForCard;
        const stillLearningCount = await stillLearningCrad.textContent() ?? "";
        const stillLearnCard = stillLearningCount.split(" ")[0].trim();

        await expect(knewAnwCard, "Verify knew answer swipe card count  is correct").toBe(knewCard);
        await expect(stillLearnCard, "Verify still learning swipe card count is correct").toBe(stillLearning);

    }

    async clickOnCloseFlashCardButton() {
        await slowExpect(this.closeButton, "Verify close flash card button is visible").toBeVisible();
        await this.closeButton.click();
    }

    async validateCompletedFlashCards() {
        await expect(this.thatsAllText, "Verify that was all title text is visible").toBeVisible();
        await expect(this.youHaveFinishedText, "Verify you have finished all cards available text is visible").toBeVisible();
        await expect(this.completedCardDoneText, "Verify done text is visible").toBeVisible();
        await expect(this.completedText, "Verify completed text is visible").toBeVisible();
        await expect(this.practiceAgainButton, "Verify practice again button is visible").toBeVisible();
        await expect(this.closeButton, "Verify close button is visible").toBeVisible();
        await this.closeButton.click();
    }

    async validateThatWasQuick() {
        await slowExpect(this.thatWasQuickTitleText, "Verify that was quick title text is visible").toBeVisible();
        await expect(this.youHaveSpentLessSecondsText, "Verify you have spent less seconds text is visible").toBeVisible();
        if (currentEnv.isStage()) {
            await expect(this.loadingMoreCardsButton, "Verify last deck remaining text is visible").toBeVisible();
        }
        await expect(this.loadingDecksCardsButton, "Verify last deck cards button is visible").toBeVisible();
        await customExpect(15000)(this.iKnowThisButton, "Verify i know this button is visible ").toBeVisible();
    }

    async getCardSwipeCount() {

        const getCardCount = await this.getCardValue;
        const cardValue = await getCardCount.textContent() ?? "";
        const Cardcount = parseInt(cardValue.split("of ")[1].split(" ")[0]); // Extracts "50"
        return Cardcount;
    }
    
    async getCardCount(action) {
        const cardCountText = await this.page.locator('text=/\\d+ of \\d+ cards/').textContent();
        const match = cardCountText?.match(/(\d+) of (\d+) cards/);
      
        if (!match) {
          throw new Error("Card count text not found or in unexpected format");
        }
      
        let totalCards = parseInt(match[2]);
        let totalCards1 = totalCards-1;
        for (let current = 1; current <= totalCards1; current++) {
            if(action == 'stillLearning'){
                await slowExpect(this.stillLearningButton, "Verify still learning text is visible").toBeVisible();
                await this.stillLearningButton.click();
            }else{
                await slowExpect(this.iKnowThisButton, "Verify i know this text is visible").toBeVisible();
                await this.iKnowThisButton.click();
            }
      
          if (current < totalCards) {
            const nextText = `${current + 1} of ${totalCards} cards`;
            await this.page.locator(`text=${nextText}`).waitFor({ timeout: 1000 });
          }
        }
    }

}

