
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const ExamPageUrl = '/test'
export class ExamPage extends WebPage {
  readonly questionNumberText: Locator;
  readonly firstOptionToBeSelected: Locator;
  readonly secondOptionToBeSelected: Locator;
  readonly saveAndNext: Locator;
  readonly markAsReviewButton: Locator;
  readonly clearResponseButton: Locator;
  readonly backButton: Locator;
  readonly rightArrowButton: Locator;
  readonly submitButton: Locator;
  readonly examTimerCountDown: Locator;
  readonly oneMinLeftForExamTostText: Locator;
  readonly notVisitedQuestionIcon: (notVisited: string) => Locator;
  readonly notAnsweredQuestionIcon: (notAnswered: string) => Locator;
  readonly answeredQuestionIcon: (answered: string) => Locator;
  readonly markedForReviewQuestionIcon: (markedForReview: string) => Locator;
  readonly answeredButMarkedForReviewIcon: (answeredAndMarkedForReview: string) => Locator;
  readonly currentQuestionNumber: (currentQuestion: string) => Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, ExamPageUrl, isMobile);
    this.questionNumberText = page.getByTestId('question_number');
    this.firstOptionToBeSelected = page.locator("//*[@id='0']");
    this.secondOptionToBeSelected = page.locator("//*[@id='1']");
    this.saveAndNext = page.getByRole('button', { name: 'Save & Next' })
    this.markAsReviewButton = page.getByRole('button', { name: 'Mark for Review & Next' });
    this.clearResponseButton = page.getByRole('button', { name: 'Clear Response' });
    this.backButton = page.getByRole('button', { name: 'Back' });
    this.rightArrowButton = page.locator('.fixed > .lg\\:flex').or(page.getByTestId('test-drawer'));
    this.submitButton = page.getByText('Submit Exam');
    this.examTimerCountDown = page.locator("//*[@alt='ALLEN logo']/ancestor::*[contains(@class,'background h-screen overflow')]/descendant::*[contains(@class, 'font-semibold text-2xs')]");
    this.oneMinLeftForExamTostText = page.getByText('Last 1 minute. Your exam will be auto submitted');
    this.notVisitedQuestionIcon = (notVisited: string) => page.locator(`(//*[text()='Question Palette']/ancestor::*[contains(@class,'fixed bottom-16')]/descendant::*[contains(@class,'flex flex-wrap gap')])[1]//*[text()='${notVisited}']/ancestor::*[@class='relative cursor-pointer']//*[@alt='not visited']`);
    this.answeredQuestionIcon = (answered: string) => page.locator(`(//*[text()='Question Palette']/ancestor::*[contains(@class,'fixed bottom-16')]/descendant::*[contains(@class,'flex flex-wrap gap')])[1]//*[text()='${answered}']/ancestor::*[@class='relative cursor-pointer']//*[@alt='answered']`);
    this.notAnsweredQuestionIcon = (notAnswered: string) => page.locator(`(//*[text()='Question Palette']/ancestor::*[contains(@class,'fixed bottom-16')]/descendant::*[contains(@class,'flex flex-wrap gap')])[1]//*[text()='${notAnswered}']/ancestor::*[@class='relative cursor-pointer']//*[@alt='not answered']`);
    this.answeredButMarkedForReviewIcon = (answeredAndMarkedForReview: string) => page.locator(`(//*[text()='Question Palette']/ancestor::*[contains(@class,'fixed bottom-16')]/descendant::*[contains(@class,'flex flex-wrap gap')])[1]//*[text()='${answeredAndMarkedForReview}']/ancestor::*[@class='relative cursor-pointer']//*[@alt='answered and marked for review']`);
    this.markedForReviewQuestionIcon = (markedForReview: string) => page.locator(`(//*[text()='Question Palette']/ancestor::*[contains(@class,'fixed bottom-16')]/descendant::*[contains(@class,'flex flex-wrap gap')])[1]//*[text()='${markedForReview}']/ancestor::*[@class='relative cursor-pointer']//*[@alt='marked for review']`);
    this.currentQuestionNumber = (currentQuestion: string) => page.getByText(`Question No ${currentQuestion}`);
  }

  async selectTheOptionForAnswer() {
    await expect(this.questionNumberText, 'question number text is visible').toBeVisible();
    await this.firstOptionToBeSelected.click();
    await this.saveAndNext.click();
  }

  async markAsReviewTheAnswer() {
    await this.markAsReviewButton.click();
  }

  async selectAnswerAndMarkAsReview() {
    await this.firstOptionToBeSelected.click();
    await this.markAsReviewButton.click();
    await this.saveAndNext.click();
  }

  async clearResponseAndAgainAnswerTheQuestion(questionNumber) {
    await slowExpect(this.currentQuestionNumber(questionNumber), "current question number is visible").toBeVisible();
    await this.firstOptionToBeSelected.click();
    await this.clearResponseButton.click();
    await this.secondOptionToBeSelected.click();
    await expect(this.secondOptionToBeSelected, "verify option is selected").toBeChecked();
    await this.saveAndNext.click();
  }

  async verifyTheBackButtonFunctionality(questionNumber) {
    await slowExpect(this.backButton, "back button is visible").toBeVisible();
    await this.backButton.click();
    await slowExpect(this.currentQuestionNumber(questionNumber), "current question number is visible").toBeVisible();
  }

  async validatingTestTimerCountDown() {
    await slowExpect(this.examTimerCountDown).toBeVisible();
    const countdownTimeText = await this.examTimerCountDown.textContent();
    const countdownTime = countdownTimeText!.split(' ')[0]; //Spliting is using to seperate the text and countdown time
    let timeInNumber = countdownTime.split(':')[0] + countdownTime.split(':')[1]; //spliting and taking only numbers to convert into int from string to compare the values
    await this.page.waitForTimeout(6000);
    const afterTenSecounds = await this.examTimerCountDown.textContent();
    const timeAfterTenSeconds = afterTenSecounds!.split(' ')[0];
    let timeValueAfterTenSeconds = timeAfterTenSeconds.split(':')[0] + timeAfterTenSeconds.split(':')[1];
    expect(parseInt(timeInNumber)).toBeGreaterThan(parseInt(timeValueAfterTenSeconds));

  }

  async validatingQuestionIsAnswered(questionNumber) {
    await slowExpect(this.currentQuestionNumber(questionNumber), "current question number is visible").toBeVisible();
    await expect(this.notVisitedQuestionIcon(questionNumber), "Question is not visited is visible").toBeVisible();
    await expect(this.firstOptionToBeSelected, "first option of the question is visible").toBeVisible();
    await this.firstOptionToBeSelected.click();
    await expect(this.saveAndNext, "save and next button is visible").toBeVisible();
    await this.saveAndNext.click();
    await expect(this.answeredQuestionIcon(questionNumber), "Question marked as answer is visible").toBeVisible();
  }

  async validatingQuestionIsNotAnswered(questionNumber) {
    await slowExpect(this.currentQuestionNumber(questionNumber), "current question number is visible").toBeVisible();
    await expect(this.notVisitedQuestionIcon(questionNumber), "Question is not visited is visible").toBeVisible();
    await expect(this.saveAndNext, "save and next button is visible").toBeVisible();
    await this.saveAndNext.click();
    await expect(this.notAnsweredQuestionIcon(questionNumber), "Question not answered is visible").toBeVisible();
  }

  async validatingQuestionIsAnsweredAndMarkedForReview(questionNumber) {
    await slowExpect(this.currentQuestionNumber(questionNumber), "current question number is visible").toBeVisible();
    await expect(this.notVisitedQuestionIcon(questionNumber), "Question is not visited is visible").toBeVisible();
    await expect(this.firstOptionToBeSelected, "first option of the question is visible").toBeVisible();
    await this.firstOptionToBeSelected.click();
    await expect(this.markAsReviewButton, "marked as review button is visible").toBeVisible();
    await this.markAsReviewButton.click();
    await expect(this.answeredButMarkedForReviewIcon(questionNumber), "Question is mark as review after answered is visible").toBeVisible();
  }

  async validatingQuestionIsNotAnsweredAndMarkedForReview(questionNumber) {
    await slowExpect(this.currentQuestionNumber(questionNumber), "current question number is visible").toBeVisible();
    await expect(this.notVisitedQuestionIcon(questionNumber), "Question is not visited is visible").toBeVisible();
    await expect(this.markAsReviewButton, "marked as review button is visible").toBeVisible();
    await this.markAsReviewButton.click();
    await expect(this.markedForReviewQuestionIcon(questionNumber), "Question is mark as review without answered is visible").toBeVisible();
  }


}