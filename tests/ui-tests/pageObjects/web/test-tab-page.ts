import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const testTabPageUrl = '/test-tab-page'

export class TestTabPage extends WebPage {

    readonly yourPastTestsTitle: Locator;
    readonly viewAllPastTestsButton: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, testTabPageUrl, isMobile);
        this.yourPastTestsTitle = page.getByRole('heading', { name: 'Your past tests' });
        this.viewAllPastTestsButton = page.getByTestId('test-discovery').getByText('View All');

    }

    async navigateToPastTestsResults() {
        await slowExpect(this.yourPastTestsTitle, 'Verify your past tests title is visible').toBeVisible();
        await expect(this.viewAllPastTestsButton, "Verify view all button for past tests section is visible").toBeVisible();
        await this.viewAllPastTestsButton.click();
        await this.page.waitForLoadState('networkidle');
    }

}