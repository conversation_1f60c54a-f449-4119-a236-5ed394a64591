import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const CalendarPageUrl = '/calendar'

export class CalendarPage extends WebPage {

  readonly calendarText: Locator;
  readonly currentMonthText: (currentMonthName: string) => Locator;
  readonly weekText: Locator;
  readonly dateText: (dateNumber: string) => Locator;
  readonly previousWeekButton: Locator;
  readonly nextWeekButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, CalendarPageUrl, isMobile);
    this.calendarText = page.getByText('Calendar');
    this.currentMonthText = (currentMonthName: string) =>
      page.getByRole('heading', { name: `${currentMonthName}`, });
    this.weekText = page.getByText('Week:');
    this.dateText = (dateNumber: string) => page.getByText(`${dateNumber}`, { exact: true });
    this.previousWeekButton = page.locator("//*[contains(@class,' rounded-full border ml-3 mr-1 ')]");
    this.nextWeekButton = page.locator("//*[contains(@class,'rounded-full ml-1 bg-secondary cursor-pointer')]");

  }

}