import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const WebMigratioPagenUrl = '/'

export class WebMigrationPage extends WebPage {

    readonly meetOurChampionsText: (titleName: string) => Locator;
    readonly contentNotFoundText: Locator;
    readonly imageClick: Locator;
    readonly class11Button: Locator;
    readonly liveNeetCard: Locator;
    readonly knowMoreCtaButton: Locator;
    readonly viewAllProgramsCtaButton: Locator;
    readonly taxesApplicableText: Locator;
    readonly justLaunchedText: Locator;
    readonly filterCta: Locator;
    readonly classFilter: Locator;
    readonly closeFilterIcon: Locator;

    readonly englishLanguage: Locator;
    readonly hindiLanguage: Locator;
    readonly clearFilterButton: Locator;
    readonly showProgramsButton: Locator;
    readonly titleText: Locator;
    readonly tableTitleText: Locator;
    readonly cbseExamSyllabusTable: Locator;
    readonly syllabusSeoPage: Locator;
    readonly textCarouselTitle: Locator;
    readonly textCarouselTesting: Locator;
    readonly homeNavBar: Locator;
    readonly exploreNavBar: Locator;
    readonly allenText: Locator;
    readonly allenPopUpText: Locator;
    readonly closeIcon: Locator;
    readonly onlineCourseNavBar: Locator;
    readonly neetNavigationLink: Locator;
    readonly jeeNavigationLink: Locator;

    readonly pncfNavigationLink: Locator;
    readonly widgetLinkstitle: Locator;
    readonly widgetLinksIcon: Locator;
    readonly quizHeaderText: Locator;
    readonly questionsText: Locator;
    readonly nextQuestionsLink: Locator;

    readonly question1Ans: Locator;
    readonly question2Ans: Locator;
    readonly question3Ans: Locator;
    readonly question4Ans: Locator;
    readonly question5Ans: Locator;
    readonly scoresHeading: Locator;
    readonly rightGuess: Locator;
    readonly inCorrectGuess: Locator;
    readonly dialog: Locator;
    readonly popUp: Locator;



    constructor(page: Page, isMobile: boolean) {
        super(page, WebMigratioPagenUrl, isMobile);

        this.meetOurChampionsText = (titleName: string) => page.getByRole('heading', { name: `${titleName}` });
        this.contentNotFoundText = page.getByRole('heading', { name: 'Content not found' });
        this.imageClick = page.getByTestId('dynamic_page_root').getByRole('link');
        this.class11Button = page.getByRole('button', { name: 'Class 11' });
        this.liveNeetCard = page.getByRole('link', { name: 'LIVE NEET Nurture Online' });
        this.knowMoreCtaButton = page.getByTestId('cardButton').getByTestId('dls-button');
        this.viewAllProgramsCtaButton = page.getByRole('button', { name: 'View all programs' });
        this.taxesApplicableText = page.locator(`//*[text()=" + Taxes applicable"]`);
        this.justLaunchedText = page.getByText('Just Launched');
        this.filterCta = page.getByRole('button', { name: 'Filters' });
        this.classFilter = page.locator('#filter_Class').getByRole('img');
        this.englishLanguage = page.getByRole('button', { name: 'English' });
        this.hindiLanguage = page.getByRole('button', { name: 'Hindi' });
        this.closeFilterIcon = page.locator('div').filter({ hasText: /^Filters$/ }).locator('path').nth(1);
        this.showProgramsButton = page.getByRole('button', { name: 'Show 3 Programs' })
        this.clearFilterButton = page.getByRole('button', { name: 'Clear Filters' });
        this.titleText = page.getByText('CBSE - Central Board of');
        this.tableTitleText = page.getByRole('heading', { name: 'Table of Contents:' });
        this.cbseExamSyllabusTable = page.locator('span').filter({ hasText: /^CBSE Exam Syllabus$/ });
        this.syllabusSeoPage = page.getByRole('heading', { name: 'CBSE Exam Syllabus' }).getByRole('strong')
        this.textCarouselTitle = page.getByRole('heading', { name: 'Text Carousel' });
        this.textCarouselTesting = page.getByText('Testing Texts');

        this.allenText = page.getByRole('heading', { name: 'Allen Career Institute names' });
        this.allenPopUpText = page.getByTestId('dialog').getByText('Allen Career Institute names');
        this.closeIcon = page.getByTestId('dynamicPopupCloseBtn');

        this.homeNavBar = page.getByRole('link', { name: 'Home' }).first();
        this.exploreNavBar = page.locator(`//button[contains(.,"Explore")]`);
        this.onlineCourseNavBar = page.locator('//div[text()="Online Courses"]');
        this.neetNavigationLink = page.getByRole('link', { name: 'NEET' });
        this.jeeNavigationLink = page.getByRole('link', { name: 'JEE' });
        this.pncfNavigationLink = page.getByRole('link', { name: 'PNCF' });
        this.widgetLinkstitle = page.getByText('Trending Topics');
        this.widgetLinksIcon = page.getByTestId('shownImage');
        this.quizHeaderText = page.getByRole('heading', { name: 'Test your Knowledge' });
        this.questionsText = page.getByRole('heading', { name: 'question 1 of' });
        this.nextQuestionsLink = page.getByTestId('dynamic_page_root').getByTestId('dls-button');
        this.question1Ans = page.getByRole('button', { name: 'a. Vitamin A' })
        this.question2Ans = page.getByRole('button', { name: 'Keratin' })
        this.question3Ans = page.getByRole('button', { name: 'ii. Cricket' })
        this.question4Ans = page.getByRole('button', { name: 'A. 9 years' })
        this.question5Ans = page.getByRole('button', { name: 'I. Marie Curie' })
        this.scoresHeading = page.getByRole('heading', { name: 'Scores' })
        this.rightGuess = page.getByText('Right guesses')
        this.inCorrectGuess = page.getByText('Incorrect guesses')
        this.dialog = page.getByTestId('dialog');
        this.popUp = this.allenPopUpText.locator('..').locator('..');



    }

    /* verifying to url navigation with out enrolled/login */
    async verifyWithOutLogin(titleName) {
        await expect(this.meetOurChampionsText(titleName), "Verify meet our champions title text is not visible").toBeHidden();
    }

    /* verifying to url navigation with enrolled/login */
    async verifyWithLoginAndEnrolled(titleName) {
        await expect(this.meetOurChampionsText(titleName), "Verify added title text is visible").toBeVisible();
    }

    /* verifying to url navigation after deleting url/page/widget  */
    async verifyPageUrlAfterDeletion() {
        await expect(this.contentNotFoundText, "Verify content not found text is visible").toBeVisible();
    }

    async verifyImageClick() {
        await expect(this.imageClick, "Verify image is visible").toBeVisible();
        this.imageClick.click();
    }

    async verifyFilterCreatedOnPlpPage() {
        await expect(this.class11Button, "Verify class 11 button is visible").toBeVisible();
        await expect(this.liveNeetCard, "Verify live neet card is visible").toBeVisible();
        await expect(this.knowMoreCtaButton, "Verify know more cta button is visible").toBeVisible();
        await expect(this.viewAllProgramsCtaButton, "Verify view all programs cta button is visible").toBeVisible();
        await expect(this.taxesApplicableText, "Verify taxes applicable text is visible").toBeVisible();
    }

    async clickOnKnowMoreCtaButton() {
        await expect(this.knowMoreCtaButton, "Verify know more cta button is visible").toBeVisible();
        this.knowMoreCtaButton.click();
    }

    async clickOnVieAllProgramsCtaButton() {
        await expect(this.viewAllProgramsCtaButton, "Verify know more cta button is visible").toBeVisible();
        this.viewAllProgramsCtaButton.click();
    }

    async verifyMultiFilterCreatedOnPlpPage() {
        await expect(this.justLaunchedText, "Verify just launched text is visible").toBeVisible();
        await expect(this.filterCta, "Verify filter cta button is visible").toBeVisible();
        await expect(this.classFilter, "Verify class filter is visible").toBeVisible();
        await expect(this.englishLanguage, "Verify english language multi select is visible").toBeVisible();
        this.englishLanguage.click();
        await expect(this.hindiLanguage, "Verify hindi language multi select is visible").toBeVisible();
        this.hindiLanguage.click();
    }


    async clickOnFilterCtaButton() {
        await expect(this.filterCta, "Verify filter cta button is visible").toBeVisible();
        this.filterCta.click();
        await expect(this.showProgramsButton, "Verify filter cta button is visible").toBeVisible();
        await expect(this.closeFilterIcon, "Verify filter close icon is visible").toBeVisible();
        await expect(this.clearFilterButton, "Verify filter cta button is visible").toBeVisible();
        this.clearFilterButton.click();

    }

    async verifySeoPage() {
        await expect(this.titleText, "Verify title text is visible").toBeVisible();
        await expect(this.tableTitleText, "Verify table of contents title is visible").toBeVisible();
        await expect(this.cbseExamSyllabusTable, "Verify cbse exam syllabus is present in table of content is visible").toBeVisible();
        this.cbseExamSyllabusTable.click();
        await expect(this.syllabusSeoPage, "Verify cbse syllabus in seo page is visible").toBeVisible();
    }

    async clickOutsidePopUp() {
        const dialogBox = await this.dialog.boundingBox();
        const popUp = await this.popUp.boundingBox();

        if (dialogBox && popUp) {
            // Try clicking 10px from the top-left corner of dialog
            let clickX = dialogBox.x + 10;
            let clickY = dialogBox.y + 10;

            const isInsideText =
              clickX >= popUp.x &&
              clickX <= popUp.x + popUp.width &&
              clickY >= popUp.y &&
              clickY <= popUp.y + popUp.height;

            if (isInsideText) {
                // Click below the text element inside the dialog
                clickX = popUp.x + 10;
                clickY = popUp.y + popUp.height + 10;

                // Safety check to ensure the point is still inside the dialog
                if (
                  clickX > dialogBox.x + dialogBox.width - 5 ||
                  clickY > dialogBox.y + dialogBox.height - 5
                ) {
                    clickX = dialogBox.x + 5;
                    clickY = dialogBox.y + dialogBox.height - 10;
                }
            }

            await this.page.mouse.click(clickX, clickY);
        }
    }

    async verifyTextCarousel() {
        await expect(this.textCarouselTitle, "Verify text carousel heading title text is visible").toBeVisible();
        await expect(this.textCarouselTesting, "Verify text carousel testing is visible").toBeVisible();
        await expect(this.allenText, "Verify allen institute text carousel is visible").toBeVisible();
        this.allenText.click();
        await expect(this.allenPopUpText, "Verify pop up is visible").toBeVisible();
        await this.clickOutsidePopUp();
    }

    async verifyNavBarTabAndNavigation() {
        await slowExpect(this.homeNavBar, "Verify home nav bar is visible").toBeVisible();
        await expect(this.exploreNavBar, "Verify explore nav bar is visible").toBeVisible();
        this.exploreNavBar.click();
        await expect(this.onlineCourseNavBar, "Verify online courses is visible").toBeVisible();
        this.onlineCourseNavBar.click();
    }


    async verifyWidgetLinks() {
        await expect(this.widgetLinkstitle, "Verify widget links title is visible").toBeVisible();
        await expect(this.widgetLinksIcon, "Verify widget links icon is visible").toBeVisible();
    }

    async verifyQuizSection() {
        await expect(this.quizHeaderText, "Verify quiz header title is visible").toBeVisible();
        await expect(this.questionsText, "Verify questions is visible").toBeVisible();
        await expect(this.nextQuestionsLink, "Verify next questions link is disabled").toBeDisabled();
    }

    async verifyQuizScore() {
        await expect(this.question1Ans, "Verify question one anwser is visible").toBeVisible();
        this.question1Ans.click();

        await expect(this.nextQuestionsLink, "Verify next questions link is visible").toBeVisible();
        this.nextQuestionsLink.click();

        await expect(this.question2Ans, "Verify question two answer is visible").toBeVisible();
        this.question2Ans.click();
        await expect(this.nextQuestionsLink, "Verify next questions link is visible").toBeVisible();
        this.nextQuestionsLink.click();

        await expect(this.question3Ans, "Verify question three answer is visible").toBeVisible();
        this.question3Ans.click();
        await expect(this.nextQuestionsLink, "Verify next questions link is visible").toBeVisible();
        this.nextQuestionsLink.click();

        await expect(this.question4Ans, "Verify question four answer is visible").toBeVisible();
        this.question4Ans.click();
        await expect(this.nextQuestionsLink, "Verify next questions link is visible").toBeVisible();
        this.nextQuestionsLink.click();
        await expect(this.question5Ans, "Verify question five answer is visible").toBeVisible();
        this.question5Ans.click();
        await expect(this.nextQuestionsLink, "Verify next questions link is visible").toBeVisible();
        this.nextQuestionsLink.click();

        await expect(this.scoresHeading, "Verify scores heading is visible").toBeVisible();
        await expect(this.rightGuess, "Verify right guess is visible").toBeVisible();
        await expect(this.inCorrectGuess, "Verify incorrect guess is visible visible").toBeVisible();
    }


}  