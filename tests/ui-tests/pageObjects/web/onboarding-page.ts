
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const OnboardingPageUrl = '/onboarding'

export class OnboardingPage extends WebPage {
  readonly allenLogo: Locator;
  readonly jeeButton: Locator;
  readonly grade6Button: Locator;
  readonly goBackButton: Locator;
  readonly letsStartYourJEEText: Locator;
  readonly letsStartYourNEETText: Locator;
  readonly loginButton: Locator;
  readonly talkToUsButton: Locator;
  readonly movingTo11th: Locator;
  readonly onboardingTextInput: Locator;
  readonly enterFullNameOfStudentText: Locator;
  readonly studentNameSubmitButton: Locator;
  readonly userName: Locator;
  readonly userTitle: Locator;
  readonly movingTo12th: Locator;
  readonly moving12thPlus: Locator;
  readonly examPreparingText: Locator;
  readonly onboardingBackButton: Locator;
  readonly jeeButtonLink: Locator;
  readonly neetButtonLink: Locator;
  readonly onboardingImage: Locator;
  readonly onboardingFormCard: Locator;
  readonly onboardingChipGroup: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, OnboardingPageUrl, isMobile);
    this.allenLogo = page.locator("//*[@alt='Allen logo']").locator('visible=true');
    this.jeeButton = page.getByText('JEE', { exact: true }).locator('visible=true');
    this.grade6Button = page.getByText('Class 6-10', { exact: true });
    this.goBackButton = page.getByTestId('onboardingBackButton')
    this.letsStartYourJEEText = page.getByText('Lets start your JEE');
    this.letsStartYourNEETText = page.getByText('Lets start your NEET');
    this.movingTo11th = page.getByText('11th', { exact: true })
    this.movingTo12th = page.getByText('12th', { exact: true })
    this.moving12thPlus = page.getByText('12th Plus', { exact: true })
    this.onboardingTextInput = page.getByTestId('onboardingTextInput');
    this.enterFullNameOfStudentText = page.getByText('Enter full name of student');
    this.studentNameSubmitButton = isMobile ? page.getByRole('button', { name: 'Submit' }) : page.getByTestId('student-name-submit-button');
    this.userName = page.getByTestId('user-name');
    this.userTitle = page.getByTestId('user-title');
    this.examPreparingText = page.getByText('What exam you are preparing for?');
    this.onboardingBackButton = isMobile ? page.getByRole('button', { name: 'Go back' }) : page.getByTestId(/KeyboardBackspaceOutlinedIcon|onboardingBackButton/);
    this.jeeButtonLink = page.getByRole('button', { name: 'JEE' });
    this.neetButtonLink = page.getByTestId('onboardingChipDisplayName').getByText('NEET');
    this.onboardingImage = page.locator("//*[@alt='onboarding']").locator('visible=true');
    this.onboardingFormCard = page.getByTestId('onboardingFormCard');
    this.onboardingChipGroup = page.getByTestId('onboardingChipGroup');
  }

  async onBoardToNEET() {
    await expect(this.allenLogo, "verify allen logo is visible").toBeVisible();
    await expect(this.examPreparingText, "Exam preparing text is visible").toBeVisible();
    await expect(this.jeeButton, "JEE button is visible").toBeVisible();
    await expect(this.neetButtonLink, "NEET button is visible").toBeVisible();
    await expect(this.grade6Button, "Grade 6-10 button is visible").toBeVisible();
    // await this.page.waitForTimeout(1000);
    // await this.page.locator("button[data-testid='onboardingChip'] >> text='NEET'").click();
    await expect(this.neetButtonLink, "NEET button is enabled").toBeEnabled();
    // await this.neetButtonLink.hover();
    await this.page.locator("button[data-testid='onboardingChip'] >> text='NEET'").click();
    // await this.neetButtonLink.hover();
    // await this.neetButtonLink.click({ force: true });
    await expect(this.letsStartYourNEETText, "Lets start your NEET text is visible").toBeVisible();
    await expect(this.movingTo11th, "Moving To 11th button is visible").toBeVisible();
    await expect(this.movingTo12th, "Moving To 12th button is visible").toBeVisible();
    await expect(this.moving12thPlus, "12th plus button is visible").toBeVisible();
    await this.movingTo11th.click();
  }

}
