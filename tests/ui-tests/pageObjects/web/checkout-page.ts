
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const CheckoutPageUrl = '/checkout'

export class CheckoutPage extends WebPage {
  readonly StudentDetails: Locator;
  readonly StandardText: Locator;
  readonly Class12: Locator;
  readonly FullName: Locator;
  readonly PhoneNumber: Locator;
  readonly EmailID: Locator;
  readonly AdditionalDetails: Locator;
  readonly Pincode: Locator;
  readonly ParentName: Locator;
  readonly FullNameInputBox: Locator;
  readonly PhoneNumberInputBox: Locator;
  readonly EmailIDInputBox: Locator;
  readonly ContinueButton: Locator;
  readonly YourPreferences: Locator;
  readonly ContinueToPayButton: Locator;
  readonly parentNameInputBox: Locator;
  readonly cityName: Locator;
  readonly invalidUserNameText: Locator;
  readonly invalidPinCodeText: Locator;
  readonly invalidParentNameText: Locator;
  readonly basicDetailsTitle: Locator;
  readonly personalDetailsTitle: Locator;
  readonly userPhoneNumber: Locator;
  readonly fullNameValue: Locator;
  readonly emailIdValue: Locator;
  readonly parentNameValue: Locator;
  readonly pincodeValue: Locator;
  readonly personalDetailsEditButton: Locator;
  readonly yourPreferenceEditButton: Locator;
  readonly selectedLanguageInCourseDetails: Locator;
  readonly selectedStartDateValue: Locator;
  readonly standardValue: Locator;
  readonly durationValue: Locator;
  readonly multipleCitesText: Locator;
  readonly selectCity: Locator;
  readonly palgharCity: Locator;
  readonly selectPaymentMethod: Locator;
  readonly paymentModeFull: Locator;
  readonly paymentModeLoan: Locator;
  readonly justPayOption: Locator;
  readonly DateOfBirth: Locator;
  readonly Gender: Locator;
  readonly GenderMale: Locator;
  readonly DateOfBirthInputBox: Locator;
  readonly emailErrorText: Locator;
  readonly filledText: Locator;
  readonly ParentsNumbers: Locator;
  readonly ParentsNumberInputBox: Locator;
  readonly billingAddressTtile: Locator;
  readonly PinCode: Locator;
  readonly addressLineOne: Locator;
  readonly addressLineTwo: Locator;
  readonly sameAsBuildingAddress: Locator;
  readonly sameAsBuildingAddressCheckbox: Locator;
  readonly invalidValueText: Locator;
  readonly oneYearText: Locator;
  readonly parentDetailsContinueButton: Locator;
  readonly addressDetailsContinueButton: Locator;
  readonly paymentContinueButton: Locator;
  readonly couponsForYouText: Locator;
  readonly allCouponsButton: Locator;
  readonly allCouponsText: Locator;
  readonly referralCouponCodeText: (referralCode: string) => Locator;
  readonly referralCouponCodeApplied: (referralCode: string) => Locator;
  readonly appliedText: Locator;
  readonly coursePrice: Locator;



  constructor(page: Page, isMobile: boolean) {
    super(page, CheckoutPageUrl, isMobile);
    this.StudentDetails = page.getByText('Student Details');
    this.StandardText = page.getByText('Standard');
    this.Class12 = page.getByText('Class 12');
    this.FullName = page.getByText('Full Name');
    this.PhoneNumber = page.getByText('Phone Number');
    this.EmailID = page.getByText('Email ID');
    this.AdditionalDetails = page.getByText('Additional Details');
    this.YourPreferences = page.getByText('Your Preferences').nth(1);
    this.Pincode = page.getByTestId('pincode');
    this.ParentName = page.getByTestId('parent_name');
    this.FullNameInputBox = page.getByPlaceholder('Enter full name');
    this.PhoneNumberInputBox = page.getByTestId('user_phone');
    this.EmailIDInputBox = page.getByPlaceholder('Enter e-mail id');
    this.ContinueButton = page.getByTestId(/dls-button|personal-details-continue-button/).getByText('Continue');
    this.ContinueToPayButton = page.getByRole('button', { name: 'Continue to pay' });
    this.cityName = page.getByTestId('city');
    this.invalidUserNameText = page.getByText('Invalid name');
    this.invalidPinCodeText = page.getByText('Invalid pincode');
    this.parentNameInputBox = page.getByTestId('parent_name');
    this.invalidParentNameText = page.getByText('Invalid parent name');
    this.basicDetailsTitle = page.getByText('Basic Details');
    this.personalDetailsTitle = page.getByText('Personal Details').locator('visible=true');
    this.userPhoneNumber = page.locator("//*[text()='Phone Number']/following-sibling::*");
    this.fullNameValue = page.locator("//*[text()='Full Name']/following-sibling::*");
    this.emailIdValue = page.locator("//*[text()='Email ID']/following-sibling::*");
    this.parentNameValue = page.locator("//*[contains(text(),'Parent')]/following-sibling::*");
    this.pincodeValue = page.locator("//*[text()='Pincode']/following-sibling::*");
    this.personalDetailsEditButton = page.locator("//*[text()='Personal Details']/following-sibling::*[@data-testid='dls-button']/*[text()='Edit']").locator('visible=true');
    this.yourPreferenceEditButton = page.locator("//*[text()='Your Preferences']/following-sibling::*[@data-testid='dls-button']/*[text()='Edit']").locator('visible=true');
    this.selectedLanguageInCourseDetails = page.locator("//*[text()='Language']/following-sibling::*");
    this.selectedStartDateValue = page.locator("//*[text()='Starting date']/following-sibling::*");
    this.standardValue = page.locator("//*[text()='Standard']/following-sibling::*");
    this.durationValue = page.locator("//*[text()='Duration']/following-sibling::*");
    this.multipleCitesText = page.getByText('*Multiple cities for this pincode. Please select your city.');
    this.selectCity = page.getByText('Select city').locator('visible=true');
    this.palgharCity = page.getByText('PALGHAR').locator('visible=true');
    this.selectPaymentMethod = page.getByText('You can select any one as per');
    this.paymentModeFull = page.getByTestId('payment-option-step-Pay In Full');
    this.paymentModeLoan = page.getByTestId('payment-option-step-Pay Via Loan');
    this.justPayOption = isMobile ? page.getByTestId('payment-sub-option-step-mobileJUSPAY') : page.getByTestId(/payment-sub-option-step-desktop-JUSPAY|payment-sub-option-step-JUSPAY/).first();
    this.DateOfBirth = page.getByText('Date of birth');
    this.Gender = page.getByText('Gender');
    this.GenderMale = page.getByRole('button', { name: 'Male', exact: true });
    this.DateOfBirthInputBox = page.getByPlaceholder('Select your DOB');
    this.emailErrorText = page.getByText('error in creating EmailIdentity - Email exists');
    this.filledText = page.getByText('FILLED');
    this.ParentsNumbers = page.getByText('Parent’s Mobile Number');
    this.ParentsNumberInputBox = page.getByPlaceholder("Enter your parent's mobile number");
    this.billingAddressTtile = page.getByText('BILLING ADDRESS', { exact: true });
    this.PinCode = page.getByPlaceholder('Ex :800023').first();
    this.addressLineOne = page.getByPlaceholder('Flat/house no, Block,').first();
    this.addressLineTwo = page.getByPlaceholder('Street name, locality').first();
    this.sameAsBuildingAddress = page.getByText('My billing address is same as');
    this.sameAsBuildingAddressCheckbox = page.getByTestId('billing_shipping_address_checkbox');
    this.invalidValueText = page.getByText('Invalid value').first();
    this.oneYearText = page.getByText('1 Year');
    this.parentDetailsContinueButton = page.getByTestId(/dls-button|parent-details-continue-button/).getByText('Continue');
    this.addressDetailsContinueButton = page.getByTestId(/dls-button|address-details-continue-button/).getByText('Continue');
    this.paymentContinueButton = page.getByTestId(/dls-button|payment-details-continue-button/).getByText('Continue');
    this.couponsForYouText = page.getByText('Coupons for you');
    this.allCouponsButton = page.getByTestId('dls-button');
    this.allCouponsText = page.getByTestId('coupon-widget').getByText('All coupons')
    this.referralCouponCodeText = (referralCode: string) => page.getByTestId('coupon-widget').getByText(`${referralCode}`);
    this.referralCouponCodeApplied = (referralCode: string) => page.getByText(`${referralCode}`).first();
    this.appliedText = page.getByText('Applied');
    this.coursePrice = isMobile ? page.getByTestId('priceBreakdownTitle').locator('xpath=../following-sibling::div[1]').locator('xpath=./p[last()]') : page.getByTestId('final-price');
  }

  async verifyingCoursePageDetails() {
    await expect(this.StudentDetails, 'verifying "Student Details" text should be visible').toBeVisible();
    await expect(this.StandardText, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.FullName, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.PhoneNumber, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.EmailID, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.AdditionalDetails, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.Pincode, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.EmailIDInputBox, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.ParentName, 'verifying "Standard" text should be visible').toBeVisible();
    await expect(this.ContinueButton, 'verifying "Continue" button should be visible').toBeVisible();
  }

  async verifyReferralCodeCoupon(referralCode) {
    await expect(this.couponsForYouText, 'Verifying coupons for you text is visible').toBeVisible();
    await expect(this.allCouponsButton, 'Verifying all coupons button is visible').toBeVisible();
    await this.allCouponsButton.click();
    await slowExpect(this.allCouponsText, 'Verifying all coupons text is visible').toBeVisible();
    await expect(this.referralCouponCodeText(referralCode), 'Verifying referral coupon code is visible').toBeVisible();
  }

}
