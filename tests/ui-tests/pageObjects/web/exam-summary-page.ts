
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';
const ExamSummaryPageUrl = '/test'
export class ExamSummaryPage extends WebPage {
  readonly examSummaryTitle: Locator;
  readonly yesSubmitButton: Locator;
  readonly examSubmittedSuccessfullyToastMessage: Locator;
  readonly finalSubmitTest: Locator;
  readonly finalSubmitTestPopUp: Locator;
  readonly viewResultButton: Locator;
  readonly resultsSummaryMarks: Locator;
  readonly viewSoultionButton: Locator;
  readonly accuracySection: Locator;
  readonly resultSummaryAttempt: Locator;
  readonly subjectWiseMarks: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, ExamSummaryPageUrl, isMobile);
    this.examSummaryTitle = page.getByText('Exam Summary');
    this.yesSubmitButton = page.getByRole('button', { name: 'Yes, Submit' });
    this.examSubmittedSuccessfullyToastMessage = page.getByTestId('toast_message_text');
    this.finalSubmitTest = EnvUtils.getInstance().isProd() ? page.getByTestId('submit-test') : page.getByTestId('submit-exam');
    this.finalSubmitTestPopUp = page.getByTestId('submit-test');
    this.viewResultButton = page.getByTestId('cta-button').getByText('View Result');
    this.viewSoultionButton = page.getByTestId('result-summary').getByTestId('dls-button');
    this.resultsSummaryMarks = page.getByTestId('result-summary').getByText('MARKS');
    this.accuracySection = page.getByText('% ACCURACY');
    this.resultSummaryAttempt = page.getByTestId('result-summary').getByText('ATTEMPT');
    this.subjectWiseMarks = page.getByRole('heading', { name: 'Subject wise marks' });

  }

  async verifySuccessToasMessageAfterTestSubmission() {
    await expect(this.examSummaryTitle, 'exam summary title is visible').toBeVisible();
    await this.yesSubmitButton.click();
    await expect(this.finalSubmitTestPopUp, "verify final submit button is visible").toBeVisible();
    await this.finalSubmitTestPopUp.click();
    await slowExpect(this.examSubmittedSuccessfullyToastMessage, 'exam submitted tost is visible').toBeVisible();
  }

  async verifyViewResultAfterReAttemptTestSubmission() {
    await expect(this.examSummaryTitle, 'exam summary title is visible').toBeVisible();
    await this.yesSubmitButton.click();
    await slowExpect(this.viewResultButton, 'view result button is visible').toBeVisible();
    await this.viewResultButton.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.resultsSummaryMarks, 'view result summary marks section is visible').toBeVisible();
    await expect(this.viewSoultionButton, 'view view solution button is visible').toBeVisible();
    await expect(this.accuracySection, 'view accuracy section is visible').toBeVisible();
    await expect(this.resultSummaryAttempt, 'view result summary attempt section is visible').toBeVisible();
    await expect(this.subjectWiseMarks, 'view subject wise marks section is visible').toBeVisible();
  }
}
