import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const ProfilePageUrl = '/profile'

export class ProfilePage extends WebPage {

  readonly noOrderText: Locator;
  readonly exploreCoursesButton: Locator;
  readonly yourProfileText: Locator;
  readonly personalDetailsText: Locator;
  readonly orderDetailsText: Locator;
  readonly noticeBoardText: Locator;
  readonly helpAndSupportText: Locator;
  readonly manageAddressText: Locator;
  readonly settingsText: Locator;
  readonly privacyAndPolicyText: Locator;
  readonly termsAndConditionsText: Locator;
  readonly studentText: (student: string) => Locator;
  readonly enrolledStudentText: Locator;
  readonly contactDetailsSubTitle: Locator;
  readonly parentDetailsSubTitle: Locator;
  readonly studentDetailsSubTitle: Locator;
  readonly batchDetailsSubTitle: Locator;
  readonly needHelpText: Locator;
  readonly belowQueryText: Locator;
  readonly additionalQueryText: Locator;
  readonly accountSettingsSubTitleText: Locator;
  readonly logoutButton: Locator;
  readonly deleteAccountButton: Locator;
  readonly deleteAccountConfirmationText: Locator;
  readonly deletedButton: Locator;
  readonly orderDetailsStatus: Locator;
  readonly orderDetailstatusButton: Locator;
  readonly orderIdValue: Locator;
  readonly viewInvoicesButton: Locator;
  readonly downloadInvoice: Locator;
  readonly orderIdText: (orderId: string) => Locator;
  readonly displayStreamText: (grade: string) => Locator;
  readonly changeStreamButton: Locator;
  readonly streamText: (stream: string) => Locator;
  readonly classText: (classText: string) => Locator;
  readonly saveButton: Locator;
  readonly contactDetailsEditButton: Locator;
  readonly studentDetailsEditButton: Locator;
  readonly editContactDetailsText: Locator;
  readonly phoneNumberText: Locator;
  readonly emailIdText: Locator;
  readonly numberEdit: Locator;
  readonly verifyNumberText: Locator;
  readonly verifyAndSaveButton: Locator;
  readonly updateYourProfileText: Locator;
  readonly otpInputField0: Locator;
  readonly otpInputField1: Locator;
  readonly otpInputField2: Locator;
  readonly otpInputField3: Locator;
  readonly addEmaiIdInput: Locator;
  readonly emailIdValue: (emailIdValue: string) => Locator;
  readonly mobileNumberValue: (mobileNumberValue: string) => Locator;
  readonly dobValue: (dobValue: string) => Locator;
  readonly dobInput: Locator;
  readonly bloodGroupDropdown: Locator;
  readonly aPositiveBloodGroop: Locator;
  readonly maleGender: Locator;
  readonly accountBasicsText: Locator;
  readonly ordersAndPaymentsText: Locator;
  readonly courseAndEnrolments: Locator;
  readonly liveClassText: Locator;
  readonly doubtsText: Locator;
  readonly homeworkText: Locator;
  readonly onlineTestSeriesText: Locator;
  readonly refundsText: Locator;
  readonly addAddressButton: Locator;
  readonly noAddressFoundText: Locator;
  readonly pincodeInput: Locator;
  readonly addressInput: Locator;
  readonly streetInput: Locator;
  readonly addedAddress: Locator;
  readonly removeAddress: Locator;
  readonly editAddress: Locator;
  readonly areYouSureText: Locator;
  readonly deleteAddressPermanentlyText: Locator;
  readonly removeButton: Locator;
  readonly allenBotButton: Locator;
  readonly allenDigitalBotText: Locator;
  readonly botMessageInput: Locator;
  readonly botSendMessageButton: Locator;
  readonly createTicketText: Locator;
  readonly viewAllMyPreviousTicketsText: Locator;
  readonly minimizeDigitalBot: Locator;
  readonly deleteAccountMessageOfEnrolledUser: Locator;
  readonly cancelButton: Locator;
  readonly accountLogout: Locator;
  readonly manageAddressLink: Locator;
  readonly helpAndSupportLink: Locator;
  readonly successfulTostMessage: Locator;
  readonly logoutConfirmText: Locator;
  readonly fromAdminText: Locator;
  readonly broadcastName: (broadcastName: string) => Locator;
  readonly broadcastCategory: (broadcastCategory: string) => Locator;
  readonly noticeText: Locator;
  readonly attachmentOfBroadcast: Locator;
  readonly loadingNotification: Locator;
  readonly backToNoticeBoardButton: Locator;
  readonly backToNoticeArrow: Locator;
  readonly userNameMenu: Locator;



  constructor(page: Page, isMobile: boolean) {
    super(page, ProfilePageUrl, isMobile);
    this.noOrderText = page.getByText('No orders yet');
    this.exploreCoursesButton = page.getByTestId('noOrders').getByTestId('dls-button');
    this.yourProfileText = page.getByText('Your profile');

    this.personalDetailsText = page.getByText('Personal details');
    this.orderDetailsText = page.getByText('Order details');
    this.noticeBoardText = page.getByText('Noticeboard');
    this.helpAndSupportText = page.getByText('Help & support');
    this.manageAddressText = page.getByText('Manage address');
    this.termsAndConditionsText = isMobile ? page.getByRole('link', { name: 'TERMS & CONDITION' }) : page.getByText('TERMS & CONDITION');
    this.settingsText = page.getByText('Settings');
    this.privacyAndPolicyText = isMobile ? page.getByRole('link', { name: 'PRIVACY POLICY' }) : page.getByText('PRIVACY POLICY');
    this.studentText = (student: string) => page.getByText(`${student}`, { exact: true });
    this.enrolledStudentText = page.getByText('ENROLLED STUDENT');
    this.contactDetailsSubTitle = page.getByText('Contact details');
    this.parentDetailsSubTitle = page.getByText('Parent details');
    this.studentDetailsSubTitle = page.getByText('Student details');
    this.batchDetailsSubTitle = page.getByText('Batch Details');
    this.needHelpText = page.getByText('Need help');
    this.belowQueryText = page.getByText('Related to any of the below query:');
    this.additionalQueryText = page.getByText('For any additional queries,');
    this.accountSettingsSubTitleText = page.getByText('Account Settings');
    this.logoutButton = page.getByText('Log out of all devices');
    this.deleteAccountButton = page.getByText('Delete account');
    this.deleteAccountConfirmationText = page.getByText('Are you sure you want to delete your account permanently?');
    this.deletedButton = page.getByRole("button", { name: "Delete", exact: true });
    this.orderDetailsStatus = page.getByText('Purchase complete');
    this.orderDetailstatusButton = page.getByTestId('orderDetailsStatus');
    this.orderIdValue = page.locator("//*[text()='Order ID : ']/following-sibling::*[@data-testid='orderId']");
    this.viewInvoicesButton = page.getByRole('button', { name: 'View invoices' });
    this.downloadInvoice = page.getByText("Download");
    this.orderIdText = (orderId: string) => page.getByText(`${orderId}`, { exact: true });
    this.displayStreamText = (grade: string) => page.getByRole('paragraph').filter({ hasText: `Class ${grade} | NEET` });
    this.changeStreamButton = page.getByRole('button', { name: 'Change' });
    this.streamText = (stream: string) => page.getByTestId('chipComponent').getByText(`${stream}`);
    this.classText = (classText: string) => page.getByRole('button', { name: classText, exact: true });
    this.saveButton = page.getByRole('button', { name: 'Save' });
    this.contactDetailsEditButton = page.locator("//*[text()='Contact details']/following-sibling::*");
    this.studentDetailsEditButton = page.locator("//*[text()='Student details']/following-sibling::*");
    this.editContactDetailsText = page.getByText('Edit Contact Details', { exact: true });
    this.phoneNumberText = page.getByText('Phone Number').nth(1);
    this.emailIdText = page.getByText('Email').nth(1);
    this.numberEdit = page.locator("//*[@id='edit-number']");
    this.verifyNumberText = page.getByText('Verify Phone Number');
    this.verifyAndSaveButton = page.getByRole('button', { name: 'Verify & Save' });
    this.updateYourProfileText = page.getByText('Update Your Profile');
    this.otpInputField0 = page.getByTestId('OTPInputField-0');
    this.otpInputField1 = page.getByTestId('OTPInputField-1');
    this.otpInputField2 = page.getByTestId('OTPInputField-2');
    this.otpInputField3 = page.getByTestId('OTPInputField-3');
    this.addEmaiIdInput = page.locator("//*[@id='edit-email']");
    this.emailIdValue = (emailIdValue: string) => page.getByText(`${emailIdValue}`);
    this.mobileNumberValue = (mobileNumberValue: string) => page.getByText(`${mobileNumberValue}`);
    this.dobInput = page.getByPlaceholder('dd-mm-yyyy');
    this.bloodGroupDropdown = page.getByText('Select Blood Group');
    this.aPositiveBloodGroop = page.getByText('A +ve');
    this.maleGender = page.getByRole('button', { name: 'Male', exact: true });
    this.dobValue = (dobValue: string) => page.getByText(`${dobValue}`);
    this.accountBasicsText = page.getByText('Account Basics');
    this.courseAndEnrolments = page.getByText('Course & Enrolments');
    this.ordersAndPaymentsText = page.getByText('Orders & Payments');
    this.liveClassText = page.getByText('Live class');
    this.doubtsText = page.getByText('Doubts');
    this.homeworkText = page.getByText('Homework');
    this.refundsText = page.getByText('Refunds');
    this.onlineTestSeriesText = page.getByText('Online Test Series');
    this.pincodeInput = page.getByPlaceholder('Ex:');
    this.noAddressFoundText = page.getByText('No address found');
    this.addAddressButton = page.getByRole('button', { name: 'Add address' });
    this.addressInput = page.getByPlaceholder('house no, building no');
    this.streetInput = page.getByPlaceholder('Street, area');
    this.addedAddress = page.getByTestId('address-container');
    this.removeAddress = page.getByTestId('remove-address-button');
    this.editAddress = page.getByTestId('edit-address-button');
    this.areYouSureText = page.getByText('Are you sure');
    this.deleteAddressPermanentlyText = page.getByText('By deleting the address you will lose it permanently');
    this.removeButton = page.getByRole('button', { name: 'Remove' });
    this.allenBotButton = page.getByLabel('Hello, have a question? Let’s');
    this.allenDigitalBotText = page.getByText('Allen Digital Bot');
    this.botMessageInput = page.frameLocator('iframe[title="Chat with an Agent"]').getByPlaceholder('Type your message...');
    this.createTicketText = page.frameLocator('iframe[title="Chat with an Agent"]').getByRole('option', { name: 'Create Ticket' });
    this.viewAllMyPreviousTicketsText = page.frameLocator('iframe[title="Chat with an Agent"]').getByRole('option', { name: 'View all my previous tickets' });
    this.botSendMessageButton = page.frameLocator('iframe[title="Chat with an Agent"]').getByRole('button', { name: 'Send message' });
    this.minimizeDigitalBot = page.getByLabel('Minimize the chat window');
    this.deleteAccountMessageOfEnrolledUser = page.getByText('You have an active course on your account. Please contact support if you want to delete your profile');
    this.cancelButton = page.getByRole('button', { name: 'Cancel' });
    this.accountLogout = page.getByRole('button', { name: 'Logout' });
    this.manageAddressLink = page.getByRole('link', { name: 'Manage address' });
    this.helpAndSupportLink = page.getByRole('link', { name: 'Help & support' });
    this.successfulTostMessage = page.getByText('Successfully Updated');
    this.logoutConfirmText = page.getByText('Are you sure you want to');
    this.broadcastName = (broadcastName: string) => page.getByText(`${broadcastName}`);
    this.broadcastCategory = (broadcastCategory: string) => page.getByText(`${broadcastCategory}`);
    this.fromAdminText = page.getByText('From : Admin');
    this.noticeText = page.getByText('Notice', { exact: true });
    this.attachmentOfBroadcast = page.getByRole('link', { name: 'Attachment_1.png' });
    this.loadingNotification = page.getByText('Loading');
    this.backToNoticeBoardButton = page.locator('div').filter({ hasText: /^Notice$/ }).locator('div').nth(1).or(page.locator('div').filter({ hasText: /^Notice$/ }).getByRole('img'));
    this.backToNoticeArrow = page.locator('div').filter({ hasText: /^Notice$/ }).getByRole('img');
    this.userNameMenu = isMobile ? page.getByTestId('user-mweb-avatar').locator('div').first().locator('visible=true') : page.getByTestId('avatar').locator('div').first().locator('visible=true');
  }


  async verifyProfilePageTitleFields() {
    await slowExpect(this.yourProfileText, 'verify your profile text is visible').toBeVisible();
    await expect(this.personalDetailsText, 'verify personal details text is visible').toBeVisible();
    await expect(this.noticeBoardText, 'verify notice board text is visible').toBeVisible();
    await expect(this.orderDetailsText, 'verify order details text is visible').toBeVisible();
    await expect(this.helpAndSupportText, 'verify help and support text is visible').toBeVisible();
    await expect(this.settingsText, 'verify settings text is visible').toBeVisible();
    await expect(this.privacyAndPolicyText, 'verify privacy and policy text is visible').toBeVisible();
    await expect(this.termsAndConditionsText, 'verify terms and conditions text is visible').toBeVisible();
  }

  async verifySubFieldsUnderPersonalDetails(studentName, studentPhoneNumber) {
    await slowExpect(this.studentText(studentName), 'verify student name is visible').toBeVisible();
    await expect(this.contactDetailsSubTitle, 'verify contact details text is visible').toBeVisible();
    await expect(this.studentText(studentPhoneNumber).first(), 'verify student number is visible').toBeVisible();
    await expect(this.parentDetailsSubTitle, 'verify parent details text is visible').toBeVisible();
    await expect(this.studentDetailsSubTitle, 'verify Student details text is visible').toBeVisible();
    await expect(this.batchDetailsSubTitle, 'verify batch details text is visible').toBeVisible();
  }

  async verifySubFieldsUnderHelpAndSupport() {
    await slowExpect(this.helpAndSupportLink, 'verify help & support link is visible').toBeVisible();
    await this.helpAndSupportLink.click();
    await expect(this.needHelpText, 'verify need help text is visible').toBeVisible();
    await expect(this.belowQueryText, 'verify below query text is visible').toBeVisible();
    await expect(this.additionalQueryText, 'verify additional query text is visible').toBeVisible();
  }

  async verifySubFieldsUnderSettings() {
    await slowExpect(this.settingsText, 'verify settings text is visible').toBeVisible();
    await this.settingsText.click();
    await expect(this.accountSettingsSubTitleText, 'verify account settings text is visible').toBeVisible();
    await expect(this.logoutButton, 'verify logout button is visible').toBeVisible();
    await expect(this.deleteAccountButton, 'verify delete account button is visible').toBeVisible();
  }

  async verifyChangeStream(stream, classText) {
    await slowExpect(this.displayStreamText('11'), "current stream text is visible").toBeVisible();
    let current_stream = (await this.displayStreamText('11').innerText()).replace(/Change/g, '').trim();
    expect(current_stream, "verify current stream is displaying").not.toBeNull();
    await expect(this.changeStreamButton, 'verify change stream button is visible').toBeVisible();
    await this.changeStreamButton.click();
    await expect(this.streamText(stream), 'verify stream option is visible').toBeVisible();
    await this.streamText(stream).click();
    await expect(this.classText(classText), 'verify class option is visible').toBeVisible();
    await this.classText(classText).click();
    await expect(this.saveButton, 'verify save stream button is visible').toBeVisible();
    await this.saveButton.click();
    await this.page.waitForTimeout(2000);
    let updated_stream = (await this.displayStreamText('12').innerText()).replace(/Change/g, '').trim();
    expect(updated_stream, "verify stream is displaying post change").not.toBeNull();
    expect(current_stream, "verify stream is updated").not.toEqual(updated_stream);
  }

  async verifyEditContactDetailInPersonalDetails(new_number, otp, emai_id) {
    await slowExpect(this.contactDetailsEditButton, 'verify contact details edit button is visible').toBeVisible();
    await this.contactDetailsEditButton.click();
    await slowExpect(this.phoneNumberText, 'verify phone number text is visible').toBeVisible();
    await this.phoneNumberText.click();
    await slowExpect(this.numberEdit, 'verify edit number input is visible').toBeVisible();
    await this.numberEdit.click();
    await this.numberEdit.clear();
    await this.numberEdit.fill(new_number);
    await slowExpect(this.saveButton, 'verify save button is visible').toBeVisible();
    await this.saveButton.click();
    await slowExpect(this.verifyNumberText, 'verify phone number text is visible').toBeVisible();
    await this.otpInputField0.click();
    const otp_arr = otp.split('');
    await this.otpInputField0.fill(otp_arr[0]);
    await this.otpInputField1.fill(otp_arr[1]);
    await this.otpInputField2.fill(otp_arr[2]);
    await this.otpInputField3.fill(otp_arr[3]);
    await expect(this.verifyAndSaveButton, 'verify & save text is visible').toBeVisible();
    await expect(this.verifyAndSaveButton, 'verify & save text is enabled').toBeEnabled();
    await this.page.waitForTimeout(1500) // wait is required to load the ui
    await this.verifyAndSaveButton.click();
    await slowExpect(this.successfulTostMessage, 'verify successfully updated tost message to be visible').toBeVisible();
    await slowExpect(this.successfulTostMessage, 'verify successfully updated tost message not to be visible').not.toBeVisible();
    await slowExpect(this.mobileNumberValue(new_number), 'verify updated mobile number is visible').toBeVisible();
    await this.contactDetailsEditButton.click();
    await expect(this.emailIdText, 'verify email id input is visible').toBeVisible();
    await this.emailIdText.click();
    await expect(this.addEmaiIdInput, 'verify add email id input is visible').toBeVisible();
    await this.addEmaiIdInput.fill(emai_id);
    await expect(this.saveButton, 'verify save button is visible').toBeVisible();
    await this.saveButton.click();
    await this.otpInputField0.click();
    const otp_arr1 = otp.split('');
    await this.otpInputField0.fill(otp_arr1[0]);
    await this.otpInputField1.fill(otp_arr1[1]);
    await this.otpInputField2.fill(otp_arr1[2]);
    await this.otpInputField3.fill(otp_arr1[3]);
    await expect(this.verifyAndSaveButton, 'verify & save text is visible').toBeVisible();
    await expect(this.verifyAndSaveButton, 'verify & save text is enabled').toBeEnabled();
    await this.page.waitForTimeout(1500) // wait is required to load the ui
    await this.verifyAndSaveButton.click();
    // await this.page.waitForLoadState('networkidle') // wait is required to load the ui
    await customExpect(15000)(this.successfulTostMessage, 'verify successfully updated tost message to be visible').toBeVisible();
    await slowExpect(this.successfulTostMessage, 'verify successfully updated tost message not to be visible').not.toBeVisible();
    await slowExpect(this.emailIdValue(emai_id), 'verify updated email id is visible').toBeVisible();
  }

  async verifyEditStudentDetailInPersonalDetails(dobText) {
    await slowExpect(this.studentDetailsEditButton, 'verify student details edit button is visible').toBeVisible();
    await this.studentDetailsEditButton.click();
    await expect(this.updateYourProfileText, 'verify update your profile text is visible').toBeVisible();
    await expect(this.dobInput, 'verify update your profile text is visible').toBeVisible();
    await this.dobInput.fill(dobText);
    await this.updateYourProfileText.click();
    await expect(this.bloodGroupDropdown, "verify blood group input is visible").toBeVisible();
    await this.bloodGroupDropdown.click();
    await expect(this.aPositiveBloodGroop, "verify a positive blood group is visible").toBeVisible();
    await this.aPositiveBloodGroop.click();
    await expect(this.maleGender, "verify gender male is visible").toBeVisible();
    await this.maleGender.click();
    await expect(this.saveButton, 'verify save button is visible').toBeVisible();
    await this.saveButton.click();
    await slowExpect(this.dobValue(dobText), "verify updated dob value is visible").toBeVisible();
  }

  async verifyHelpAndSupportTitles() {
    await slowExpect(this.accountBasicsText, 'verify account Basics text is visible').toBeVisible();
    await expect(this.ordersAndPaymentsText, 'verify orders And Payments Text is visible').toBeVisible();
    await expect(this.courseAndEnrolments, 'verify course And Enrolments text is visible').toBeVisible();
    await expect(this.liveClassText, 'verify Live class text is visible').toBeVisible();
    await expect(this.doubtsText, 'verify doubts text is visible').toBeVisible();
    await expect(this.homeworkText, 'verify homework text is visible').toBeVisible();
    await expect(this.refundsText, 'verify refunds text is visible').toBeVisible();
    await expect(this.onlineTestSeriesText, 'verify online Test Series Text is visible').toBeVisible();
  }
  async verifyAddAndDeleteAddress(pincode, address, street) {
    await slowExpect(this.successfulTostMessage, 'verify successfully updated tost message not to be visible').not.toBeVisible();
    await slowExpect(this.noAddressFoundText, 'verify no Address Found Text is visible').toBeVisible();
    await expect(this.addAddressButton, 'verify add Address button is visible').toBeVisible();
    await this.addAddressButton.click();
    await slowExpect(this.pincodeInput, 'verify pincode input is visible').toBeVisible();
    await this.pincodeInput.fill(pincode);
    await expect(this.addressInput, 'verify address input is visible').toBeVisible();
    await this.addressInput.fill(address);
    await expect(this.streetInput, 'verify street input is visible').toBeVisible();
    await this.streetInput.fill(street);
    await expect(this.saveButton, 'verify save stream button is visible').toBeVisible();
    await this.saveButton.click();
    await slowExpect(this.addedAddress.nth(1), "verify added address is visible").toBeVisible();
    await this.addedAddress.nth(1).click();
    await expect(this.removeAddress.nth(1), 'verify remove address button is visible').toBeVisible();
    await expect(this.editAddress.nth(1), 'verify edit address button is visible').toBeVisible();
    await this.removeAddress.nth(1).click();
    await slowExpect(this.areYouSureText, "verify are You Sure Text is visible").toBeVisible();
    await expect(this.deleteAddressPermanentlyText, "verify delete address permanently Text is visible").toBeVisible();
    await expect(this.removeButton, "verify remove button is visible").toBeVisible();
    await this.removeButton.click();
    await slowExpect(this.noAddressFoundText, 'verify address is deleted').toBeVisible();
  }

  async verifyAllenDigitalBot() {
    await customExpect(15000)(this.allenBotButton, "Verify allen bot button is visible").toBeVisible();
    await this.allenBotButton.click();
    await customExpect(15000)(this.createTicketText, "verify create ticket option is visible").toBeVisible();
    await expect(this.viewAllMyPreviousTicketsText, "verify view all my previous tickets option is visible").toBeVisible();
    await expect(this.botMessageInput, "verify message input field is visible").toBeVisible();
    await this.botMessageInput.fill("what is the syllabus");
    await this.botSendMessageButton.click();

    // let minimizeDigitalBot;
    // try {
    //   const frameLocator = await this.page
    //     .locator('iframe[title="Chat with an Agent"]')
    //     .contentFrame();

    //   minimizeDigitalBot = await frameLocator?.getByRole('button', {
    //     name: 'Minimize the chat window',
    //   });
    // } catch (e) {
    //   // If the button inside the iframe is not found
    //   minimizeDigitalBot = this.page.getByLabel('Minimize the chat window');
    // }
    // minimizeDigitalBot.click();
    await expect(this.minimizeDigitalBot, "verify minimize digital bot option is visible").toBeVisible();
    await this.minimizeDigitalBot.click();

  }

  async verifyBroadcastInNoticeboard(nameOfBroadcast, categoryOfBroadcast) {
    await slowExpect(this.broadcastName(nameOfBroadcast), "verify broadcast name is visible").toBeVisible();
    await this.broadcastName(nameOfBroadcast).click();
    await slowExpect(this.broadcastCategory(categoryOfBroadcast).first(), "verify broadcast category is visible").toBeVisible();
    await expect(this.noticeText, "verify notice text is visible").toBeVisible();
    // await expect(this.attachmentOfBroadcast, "verify attachment of broadcast is visible").toBeVisible();
  }


}