import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const previousTestsResultPageUrl = '/previous-tests'

export class PreviousTestsResultPage extends WebPage {

    readonly createdTestCard: (testName: string) => Locator;
    readonly viewSyllabusOfTest: (testName: string) => Locator;
    readonly viewResultButtonOfTest: (testName: string) => Locator;
    readonly reAttemptTestButtonOfTest: (testName: string) => Locator;
    readonly viewTestSolutionButton: Locator;
    readonly provisionalResultsText: Locator;
    readonly overviewTestTab: Locator;
    readonly subjectTab: Locator;
    readonly chaptersTab: Locator;
    readonly waitUntilFinalResultText: Locator;
    readonly WaitUntilFinalResultImage: Locator;
    readonly backButtonToTestResults: Locator;
    readonly studentRankSection: Locator;
    readonly totalMarksTitle: Locator;
    readonly topicInfoTitle: Locator;
    readonly correctMarksTitle: Locator;
    readonly incorrectMarksTitle: Locator;
    readonly chapterListHeading: Locator;
    readonly doYouWantToStartText: Locator;
    readonly startTestButton: Locator;
    readonly allenRecommendationsText: Locator;
    readonly itGivesTimeToLearnText: Locator;
    readonly goBackButton: Locator;
    readonly continueAnywayButton: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, previousTestsResultPageUrl, isMobile);
        this.createdTestCard = (testName: string) => page.getByText(`${testName}`);
        this.viewSyllabusOfTest = (testName: string) => page.locator('div').filter({ hasText: new RegExp(`^${testName}`) }).getByRole('paragraph').nth(1);
        this.viewResultButtonOfTest = (testName: string) => page.locator(`//*[text()='${testName}']/ancestor::*[contains(@class,'-2xl p-4 bg-secondary')]//*[@data-testid="/taj-ui/str"]`);
        this.reAttemptTestButtonOfTest = (testName: string) => page.locator(`//*[text()='${testName}']/ancestor::*[contains(@class,'-2xl p-4 bg-secondary')]//*[@data-testid='dls-button']`);
        this.viewTestSolutionButton = page.getByTestId('View test solution');
        this.provisionalResultsText = page.getByText('You\'re viewing provisional resultRanks will be available once the final result');
        this.overviewTestTab = page.getByTestId('tab_Overview').getByText('Overview');
        this.subjectTab = page.getByTestId('tab_Subjects').getByText('Subjects');
        this.chaptersTab = page.getByTestId('tab_Chapters').locator('div').first();
        this.waitUntilFinalResultText = page.getByRole('heading', { name: 'Please wait until final' });
        this.WaitUntilFinalResultImage = page.getByRole('img', { name: 'empty-state' });
        this.backButtonToTestResults = page.getByTestId('back-btn');
        this.studentRankSection = page.getByTestId('student-ranks');
        this.totalMarksTitle = page.getByText('Total');
        this.topicInfoTitle = page.getByTestId('topic-info');
        this.correctMarksTitle = page.getByText('Correct', { exact: true });
        this.incorrectMarksTitle = page.getByText('Incorrect');
        this.chapterListHeading = page.getByRole('heading', { name: 'Chapter List' });
        this.doYouWantToStartText = page.getByText('Do you want to start your');
        this.startTestButton = page.getByTestId('primary-cta').getByText('Start');
        this.allenRecommendationsText = page.getByText('ALLEN recommends 2 weeks gap');
        this.itGivesTimeToLearnText = page.getByText('It gives you time to learn &');
        this.goBackButton = page.getByTestId('primary-cta').getByText('Go Back');
        this.continueAnywayButton = page.getByTestId('secondary-cta').getByText('Continue Anyway');
    }

    async validateProvisonalResultForTest(testName) {
        await slowExpect(this.createdTestCard(testName), 'Verify created test card is visible').toBeVisible();
        await expect(this.viewSyllabusOfTest(testName), "Verify view syllabus button for the test is visible").toBeVisible();
        await expect(this.viewResultButtonOfTest(testName), "Verify view result button for the test is visible").toBeVisible();
        await this.viewResultButtonOfTest(testName).click();
        await this.page.waitForLoadState('networkidle');
        await expect(this.page).toHaveURL(/.*str*/);
        await expect(this.viewTestSolutionButton, "Verify view test solution button is visible").toBeVisible();
        await expect(this.provisionalResultsText, "Verify provisional results text is visible").toBeVisible();
        await expect(this.overviewTestTab, "Verify over view test tab is visible").toBeVisible();
        await expect(this.subjectTab, "Verify subjects view test tab is visible").toBeVisible();
        await expect(this.chaptersTab, "Verify chapters view test tab is visible").toBeVisible();
        await this.chaptersTab.click();
        await slowExpect(this.waitUntilFinalResultText, 'Verify wait until final result text is visible').toBeVisible();
        await expect(this.WaitUntilFinalResultImage, 'Verify wait until final result image is visible').toBeVisible();
    }

    async validateFinalResultForTest(testName) {
        await slowExpect(this.createdTestCard(testName), 'Verify created test card is visible').toBeVisible();
        await expect(this.viewSyllabusOfTest(testName), "Verify view syllabus button for the test is visible").toBeVisible();
        await expect(this.viewResultButtonOfTest(testName), "Verify view result button for the test is visible").toBeVisible();
        await this.viewResultButtonOfTest(testName).click();
        await this.page.waitForLoadState('networkidle');
        await expect(this.page).toHaveURL(/.*str*/);
        await expect(this.viewTestSolutionButton, "Verify view test solution button is visible").toBeVisible();
        await expect(this.provisionalResultsText, "Verify provisional results text should not visible").not.toBeVisible();
        await expect(this.studentRankSection, "Verify student rank section is visible").toBeVisible();
        await expect(this.overviewTestTab, "Verify over view test tab is visible").toBeVisible();
        await expect(this.subjectTab, "Verify subjects view test tab is visible").toBeVisible();
        await expect(this.chaptersTab, "Verify chapters view test tab is visible").toBeVisible();
        await this.chaptersTab.click();
        await expect(this.topicInfoTitle, "Verify topic info title is visible").toBeVisible();
        await expect(this.chapterListHeading, "Verify chapter list header is visible").toBeVisible();
        await expect(this.incorrectMarksTitle, "Verify incorrect marks title is visible").toBeVisible();
        await expect(this.correctMarksTitle, "Verify correct marks title is visible").toBeVisible();
        await expect(this.totalMarksTitle, "Verify total marks section is visible").toBeVisible();
        await slowExpect(this.waitUntilFinalResultText, 'Verify wait until final result text should not visible').not.toBeVisible();
    }

    async validateReAttemptTestAndStartTest(testName) {
        await slowExpect(this.createdTestCard(testName), 'Verify created test card is visible').toBeVisible();
        await expect(this.viewSyllabusOfTest(testName), "Verify view syllabus button for the test is visible").toBeVisible();
        await expect(this.reAttemptTestButtonOfTest(testName), "Verify re-attempt test button for the test is visible").toBeVisible();
        await this.reAttemptTestButtonOfTest(testName).click();
        await this.page.waitForLoadState('networkidle');
        await expect(this.doYouWantToStartText, "Verify do you want to start test text is visible").toBeVisible();
        await expect(this.startTestButton, "Verify start test button is visible").toBeVisible();
        await this.startTestButton.click();
        await expect(this.allenRecommendationsText, "Verify allen recommendations text is visible").toBeVisible();
        await expect(this.itGivesTimeToLearnText, "Verify it gives time to learn sub text is visible").toBeVisible();
        await expect(this.goBackButton, "Verify go back button is visible").toBeVisible();
        await expect(this.continueAnywayButton, "Verify continue anyway button is visible").toBeVisible();
        await this.continueAnywayButton.click();
        await expect(this.page).toHaveURL(/.*attempt*/);

        // await expect(this.page).toHaveURL(/.*str*/);
        // await expect(this.viewTestSolutionButton, "Verify view test solution button is visible").toBeVisible();
        // await expect(this.provisionalResultsText, "Verify provisional results text should not visible").not.toBeVisible();
        // await expect(this.studentRankSection, "Verify student rank section is visible").toBeVisible();
        // await expect(this.overviewTestTab, "Verify over view test tab is visible").toBeVisible();
        // await expect(this.subjectTab, "Verify subjects view test tab is visible").toBeVisible();
        // await expect(this.chaptersTab, "Verify chapters view test tab is visible").toBeVisible();
        // await this.chaptersTab.click();
        // await expect(this.topicInfoTitle, "Verify topic info title is visible").toBeVisible();
        // await expect(this.chapterListHeading, "Verify chapter list header is visible").toBeVisible();
        // await expect(this.incorrectMarksTitle, "Verify incorrect marks title is visible").toBeVisible();
        // await expect(this.correctMarksTitle, "Verify correct marks title is visible").toBeVisible();
        // await expect(this.totalMarksTitle, "Verify total marks section is visible").toBeVisible();
        // await slowExpect(this.waitUntilFinalResultText, 'Verify wait until final result text should not visible').not.toBeVisible();
    }

}