
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';
const homeworkSummaryPageUrl = '/homework?'
export class HomeworkSummaryPage extends WebPage {
    readonly homeworkSummaryTitle: Locator;
    readonly answerSummaryTitle: Locator;
    readonly questionsSubHeading: Locator;
    readonly submitHomeworkText: Locator;
    readonly submitHomeworkSubHeading: Locator;
    readonly yesSubmitButton: Locator;
    readonly noCancelButton: Locator;
    readonly homeworkSubmittedTitle: Locator;
    readonly attemptedSubTitle: Locator;
    readonly accuracySubTitle: Locator;
    readonly viewReportButton: Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, homeworkSummaryPageUrl, isMobile);
        this.homeworkSummaryTitle = page.getByText('Homework Summary');
        this.answerSummaryTitle = page.getByText('Answer Summary');
        this.questionsSubHeading = page.getByText('Ques.');
        this.submitHomeworkText = page.getByText('Submit Homework');
        this.submitHomeworkSubHeading = page.getByText('Are you sure you want to');
        this.yesSubmitButton = page.getByTestId('submit-exam').getByText('Yes, Submit');
        this.noCancelButton = page.getByTestId('cancel-exam').getByText('No, Cancel')
        this.homeworkSubmittedTitle = EnvUtils.getInstance().isProd() ? page.getByRole('heading', { name: 'HW Submitted!' }) : page.getByRole('heading', { name: 'Homework' });
        this.attemptedSubTitle = page.getByText('Attempted');
        this.accuracySubTitle = page.getByText('Accuracy');
        this.viewReportButton = page.getByTestId('dls-button').getByText('View report');
    }

    async verifyHomeWorkSummaryPageAndFinalSubmit() {
        await slowExpect(this.homeworkSummaryTitle, 'verify homework summary title is visible').toBeVisible();
        await expect(this.answerSummaryTitle, 'verify answer summary title is visible').toBeVisible();
        await expect(this.questionsSubHeading, 'verify questions subheading is visible').toBeVisible();
        await expect(this.submitHomeworkText, 'verify submit Homework Text is visible').toBeVisible();
        await expect(this.submitHomeworkSubHeading, 'verify submit Homework sub-heading Text is visible').toBeVisible();
        await expect(this.yesSubmitButton, 'verify yes Submit Button is visible').toBeVisible();
        await expect(this.noCancelButton, 'verify no cancel Button is visible').toBeVisible();
        await this.yesSubmitButton.click();
        if(EnvUtils.getInstance().isProd()){
            await slowExpect(this.homeworkSubmittedTitle, "verify homework submitted heading is visible").toBeVisible();
        }
        await slowExpect(this.attemptedSubTitle, "verify attempted SubTitle is visible").toBeVisible();
        await slowExpect(this.accuracySubTitle, "verify accuracy SubTitle is visible").toBeVisible();
        await slowExpect(this.viewReportButton, "verify view Report Button is visible").toBeVisible();
    }
}
