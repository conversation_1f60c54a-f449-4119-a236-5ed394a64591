import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { homedir } from 'node:os';
import { HomePage } from './home-page';
import { EnvUtils } from '../../../commons/env-utilities';

const ReferAFriendPageUrl = '/refer-a-friend'
const currentEnv = EnvUtils.getInstance();

export class ReferAFriendPage extends WebPage {

    readonly referAndEarnTitleText: Locator;
    readonly yourFriendGetsText: Locator;
    readonly yourFriendGetsValue: Locator;

    readonly youGetText: Locator;
    readonly youGetValue: Locator;
    readonly youReferralCodeText: Locator;
    readonly shareButton: Locator;
    readonly copyIcon: Locator;
    readonly shareCodeText: Locator;
    readonly howReferralsWorksTitleText: Locator;

    readonly shareTheCodeText: Locator;
    readonly friendBuysCourseText: Locator;
    readonly getARewardText: Locator;
    readonly referAFriendTermsAndConditionLink: Locator;
    readonly copiedToClipBoardToastMessage: Locator;

    readonly shareYourReferralLinkText: Locator;
    readonly copyToClipBoardIcon: Locator;
    readonly gmailIcon: Locator;
    readonly allenOnlineReferralProgramText: Locator;
    readonly whatsAppIcon: Locator;
    readonly instaIcon: Locator;
    readonly telegramIcon: Locator;
    readonly referralLinkWhatsApp: Locator;
    readonly clickContinueText: Locator;
    readonly exploreAllenCourseText: Locator;
    readonly continueButton: Locator;
    readonly unlockedDiscountText: Locator;
    readonly autoAppliedText: Locator;
    readonly exploreCoursesButton: Locator;
    readonly invalidReferralCodeText: Locator;
    readonly speakWithAdvisorText: Locator;
    readonly referAndEarnTitleTextProd: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, ReferAFriendPageUrl, isMobile);

        this.referAndEarnTitleText = page.getByRole('heading', { name: 'Refer & Earn upto ₹10,000 in' });
        this.referAndEarnTitleTextProd = page.locator(`//*[contains(text(),"Refer & Earn upto ")]`);
        this.yourFriendGetsText = page.getByText('Your friend gets');
        this.yourFriendGetsValue = page.getByText('% discount on first purchase');
        this.youGetText = page.getByText('You get');
        this.youGetValue = page.getByText('Amazon voucher worth ₹');
        this.youReferralCodeText = page.getByText('Your referral code');
        this.shareButton = page.getByTestId('referral-cta-btn');
        this.shareCodeText = page.locator(`//span[text()="Your referral code"]/following-sibling::p`);
        this.copyIcon = page.locator(`//*[@data-testid="cta-button"]/*[1]`);
        this.howReferralsWorksTitleText = page.getByRole('heading', { name: 'How Referrals Works' });
        this.shareTheCodeText = page.getByText('Share the code or link with a');
        this.friendBuysCourseText = page.getByText('Your friend buys a course at');
        this.getARewardText = page.getByText('Get a reward for your referral');
        this.referAFriendTermsAndConditionLink = page.locator('section').filter({ hasText: 'How Referrals Works1Share the' }).getByRole('link')
        this.copiedToClipBoardToastMessage = page.getByRole('heading', { name: 'Copied to clipboard' });
        this.shareYourReferralLinkText = page.getByRole('heading', { name: 'Share your Referral Link' });
        this.copyToClipBoardIcon = page.getByRole('button', { name: 'Copy Copy to Clipboard' });
        this.gmailIcon = page.getByRole('button', { name: 'gmail' });
        this.allenOnlineReferralProgramText = page.getByText('ALLEN ONLINE REFERRAL PROGRAM');
        this.whatsAppIcon = page.getByRole('button', { name: 'facebook' }).first();
        this.instaIcon = page.getByRole('button', { name: 'facebook' }).nth(1);
        this.telegramIcon = page.getByRole('button', { name: 'facebook' }).nth(2);
        this.referralLinkWhatsApp = page.getByText('Hey! Learning with ALLEN has');
        this.clickContinueText = page.getByRole('heading', { name: 'Click Continue to get 10%' });
        this.exploreAllenCourseText = page.getByText('Explore ALLEN courses and');
        this.continueButton = page.getByTestId('cta-button');
        this.unlockedDiscountText = page.getByRole('heading', { name: 'You’ve unlocked 10% discount!' });
        this.autoAppliedText = page.getByText('10% discount will be auto-');
        this.exploreCoursesButton = page.getByRole('link', { name: 'Explore courses' });
        this.invalidReferralCodeText = page.getByRole('heading', { name: 'The referral link is invalid!' });
        this.speakWithAdvisorText = page.getByText('Speak with our advisor for');



    }

    async verifyReferAFriendPage() {
        if (currentEnv.isProd()) {
            await slowExpect(this.referAndEarnTitleTextProd, "Verify refer and earn title text is visible").toBeVisible();
        }
        else {
            await slowExpect(this.referAndEarnTitleText, "Verify refer and earn title text is visible").toBeVisible();
        }

        await expect(this.yourFriendGetsText, "Verify your friend gets text is visible").toBeVisible();
        await expect(this.yourFriendGetsValue, "Verify your friend gets value is visible").toBeVisible();
        await expect(this.youGetText, "Verify you get text is visible").toBeVisible();
        await expect(this.youGetValue, "Verify you get value is visible").toBeVisible();
        await expect(this.youReferralCodeText, "Verify your referral code text is visible").toBeVisible();
        await expect(this.shareButton, "Verify share button is visible").toBeVisible();
        await expect(this.shareCodeText, "Verify share code text is visible").toBeVisible();
        const referralCodeText = (await this.shareCodeText.textContent())?.trim() ?? "";
        console.log("Referral Code:", referralCodeText);
        await expect(this.howReferralsWorksTitleText, "Verify how to referral works title text is visible").toBeVisible();
        await expect(this.shareTheCodeText, "Verify share the code or link with friend text is visible").toBeVisible();
        await expect(this.friendBuysCourseText, "Verify your friend buys a course at a discount text is visible").toBeVisible();
        await expect(this.getARewardText, "Verify get a reward for your referral text is visible").toBeVisible();
        return referralCodeText;
    }

    async clickOnTermsAndConditions() {
        await expect(this.referAFriendTermsAndConditionLink, "Verify terms & conditions link is visible").toBeVisible();
        await this.referAFriendTermsAndConditionLink.click();
    }


    async verifyCodeReferralCodeToClipBoard() {
        await this.page.waitForTimeout(2000);
        await slowExpect(this.copyIcon, "Verify copy icon is visible").toBeVisible();
        await this.copyIcon.click();
        // await slowExpect(this.copiedToClipBoardToastMessage, "Verify copied to clipboard toast message is visible").toBeVisible();
    }

    async verifyClickOnShareReferralCode() {
        await expect(this.shareButton, "Verify share button is visible").toBeVisible();
        await this.shareButton.click();
        await slowExpect(this.shareYourReferralLinkText, "Verify share your referral link is visible").toBeVisible();
        await expect(this.copyToClipBoardIcon, "Verify copy to clipboard icon is visible").toBeVisible();
        await expect(this.gmailIcon, "Verify gmail icon is visible").toBeVisible();

    }

    async verifyShareLink(shareOptionMode) {
        switch (shareOptionMode) {
            case 'whatsAppIcon':
                await expect(this.whatsAppIcon, "Verify whatsapp icon is visible").toBeVisible();
                // await this.whatsAppIcon.click();
                break;
            case 'instIcon':
                await expect(this.instaIcon, "Verify insta icon is visible").toBeVisible();
                // await this.instaIcon.click();
                break;
            case 'telegramIcon':
                await expect(this.telegramIcon, "Verify telegram icon is visible").toBeVisible();
                // await this.telegramIcon.click();
                break;
            case 'gmail':
                await expect(this.gmailIcon, "Verify gmail icon is visible").toBeVisible();
                await this.gmailIcon.click();
                break;
            case 'copyToClipboard':
                await expect(this.copyToClipBoardIcon, "Verify copy to clipboard icon is visible").toBeVisible();
                await this.copyToClipBoardIcon.click();
                break;
            default:
                throw new Error(`Invalid referral share option mode icon: ${shareOptionMode}`);

        }
    }

    async verifyCopiedToClipBoardToastMessage() {
        // await slowExpect(this.copiedToClipBoardToastMessage, "Verify copied to clipboard toast message is visible").toBeVisible();
    }

    async verifyReferralProgramTC() {
        await slowExpect(this.allenOnlineReferralProgramText, "Verify allen online referral program terms & conditions text is visible").toBeVisible();
    }

    async verifyUnEnrolledStudentReferralCodePage() {
        await slowExpect(this.clickContinueText, "Verify click continueto get 10% discount text is visible").toBeVisible();
        await expect(this.exploreAllenCourseText, "Verify explore allen courses text is visible").toBeVisible();
        await expect(this.continueButton, "Verify continue button is visible").toBeVisible();
        await this.continueButton.click();
    }

    async verifyLoggedInStudentReferralCodePage() {
        await slowExpect(this.unlockedDiscountText, "Verify you've unlocked discount text is visible").toBeVisible();
        await expect(this.autoAppliedText, "Verify discount will be auto applied text is visible").toBeVisible();
        await expect(this.exploreCoursesButton, "Verify explore courses button is visible").toBeVisible();
        await this.exploreCoursesButton.click();
    }

    async verifyLoggedInStudentInvalidReferralCodePage() {
        await slowExpect(this.invalidReferralCodeText, "Verify invalid referral code text is visible").toBeVisible();
        await expect(this.speakWithAdvisorText, "Verify speak with advisor text is visible").toBeVisible();
    }
}