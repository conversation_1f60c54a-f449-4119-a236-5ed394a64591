import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const NeetOltsPageUrl = '/online-test-series';

export class NeetOltsPage extends WebPage {
  readonly streamHeading: (courseName: string) => Locator;
  readonly classPackageHeading: (className: string) => Locator;
  readonly courseEnrollLink: (courseEnroll: string) => Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, NeetOltsPageUrl, isMobile);
    this.streamHeading = (streamName: string) => page.getByRole('heading', { name: `${streamName}` });
    this.classPackageHeading = (className: string) => page.getByRole('heading', { name: 'Packages for Class ' + `${className}` });
    this.courseEnrollLink = (courseEnroll: string) => page.getByRole('link', { name: `${courseEnroll}` + ' Enroll' });
  }

  async verifyCourseNameAndClickToEnroll(streamName, className, courseEnroll) {
    await slowExpect(this.streamHeading(streamName), 'stream heading is visible').toBeVisible();
    await expect(this.classPackageHeading(className), 'class name heading is visible').toBeVisible();
    await expect(this.courseEnrollLink(courseEnroll), 'course enroll card is visible').toBeVisible();
    await this.courseEnrollLink(courseEnroll).click();
  }

}