
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';

const InstructionPageUrl = '/test'
export class InstructionPage extends WebPage {
  readonly instructionTitle: Locator;
  readonly instructioCheckBox: Locator;
  readonly proceedButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, InstructionPageUrl, isMobile);
    this.instructionTitle = page.getByText('Instructions:');
    this.instructioCheckBox = page.getByText('I have read and understood the instructions, I agree').locator('visible=true');
    this.proceedButton = page.getByTestId(/proceed-test|proceed-test-web/).locator('visible=true');
  }

  async verifyTheInstructionPageAndProceedTest() {
    await expect(this.instructionTitle, 'instruction title is not visible').toBeVisible();
    await this.instructioCheckBox.click();
    await this.proceedButton.click();
  }
}
