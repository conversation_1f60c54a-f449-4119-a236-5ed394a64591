import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const QuizHomePageUrl = '/quiz/home'

export class QuizHomePage extends WebPage {

  readonly customPracticeHeading: Locator;
  readonly createOwnQuizSubTitle: Locator;
  readonly subjectButton: Locator;
  readonly viewPreviousQuizButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, QuizHomePageUrl, isMobile);
    this.customPracticeHeading = page.getByText('CUSTOM PRACTICE', { exact: true });
    this.createOwnQuizSubTitle = page.getByText('Create your own quiz');
    this.subjectButton = page.locator("//*[@data-testid='dynamic_page_root']//*[@class='w-full object-contain']");
    this.viewPreviousQuizButton = page.getByText('View my previous quizzes');
  }
  async selectTheSubjectToCreateQuiz() {
    //commented below code until get confirmation
    // await slowExpect(this.customPracticeHeading, 'verify custom practice header is visible').toBeVisible();
    // await expect(this.createOwnQuizSubTitle, 'verify create your own quiz sub-header is visible').toBeVisible();
    await expect(this.viewPreviousQuizButton, 'verify view previous quizs button is visible').toBeVisible();
    await expect(this.subjectButton.nth(0), "verify subject is visible").toBeVisible();
    await this.subjectButton.nth(0).click();
  }

}