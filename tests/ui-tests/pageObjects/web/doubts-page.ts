import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { read } from 'fs';
import exp from 'constants';
const CoursePageUrl = '/doubts'
const topicForFilter = "Heat and Thermodynamics";
export class DoubtsPage extends WebPage {

  readonly subTitleDoubtsText: Locator;
  readonly askADoubtButton: Locator;
  readonly typeYourDoubtText: Locator;
  readonly enterYourDoubtInput: Locator;
  readonly uploadImageButton: Locator;
  readonly sendDoubtButton: Locator;
  readonly yesGotitText: Locator;
  readonly submitAnywayButton: Locator;
  readonly pleaseTryAndSubmitText: Locator;
  readonly studentAskedDoubt: (studentAskedDoubt: string) => Locator;
  readonly isDoubtResolvedText: Locator;
  readonly doneTickButton: Locator;
  readonly markedAsResolvedText: Locator;
  readonly doubtClosedText: Locator;
  readonly doubtsText: Locator;
  readonly allenVerifiedText: Locator;
  readonly showMoreSolutionsText: Locator;
  readonly dragAndDropText: Locator;
  readonly imageInputFile: Locator;
  readonly browseFileButton: Locator;
  readonly doneButton: Locator;
  readonly cropImageText: Locator;
  readonly uploadButton: Locator;
  readonly uploadedImage: Locator;
  readonly askYourDoubtText: Locator;
  readonly studentDoubtText: (studentDoubt: string) => Locator;
  readonly noIDintText: Locator;
  readonly transferToTeacherText: Locator;
  readonly followupWithTeacher: Locator;
  readonly sentDoubt: Locator;
  readonly recordedAudio: Locator;
  readonly needMoreHelp: Locator;
  readonly discussionClosed: Locator;
  readonly getStartedButton: Locator;
  readonly myDoubtsText: Locator;
  readonly askedDoubtSubject: Locator;
  readonly askedDoubtTopic: Locator;
  readonly selectSubjectAndTopic: Locator;
  readonly selectFilterOption: Locator;
  readonly sortByText: Locator;
  readonly latestRadioButton: Locator;
  readonly subjectForFilter: Locator;
  readonly topicForFilter: (topic: string) => Locator;
  readonly selectTopicText: Locator;
  readonly searchTopicInput: Locator;
  readonly applyFilterButton: Locator;
  readonly appliedFilterResult: (doubt: string) => Locator;
  readonly askAQuestionButton: Locator;
  readonly doubtsTextTitle: Locator;
  readonly askedDoubtTextInTeacherSide: Locator;
  readonly selectedTopicForDoubt: Locator;
  readonly transferYourDoubtToTeacherText: Locator;
  readonly askDoubtInput: Locator;
  readonly askAllieButton: Locator;
  readonly chatArea: Locator


  //locators for the neet account as of now
  readonly vtagTitle: Locator;
  readonly vtagInput: Locator;
  readonly qcodeSearchButton: Locator;
  readonly yesGotItTextBot: Locator;
  readonly noINeedHelpTextBot: Locator;
  readonly simplifyItText: Locator;
  readonly askTeacherText: Locator;
  readonly doubtTransferedToTeacherText: Locator;
  
  readonly askFiollowUpDoubtText: Locator;
  readonly okIGotIttext: Locator;
  readonly forMoreHelpAskTeacherText: Locator;
  readonly gotItTextFromBot: Locator;
  readonly gotItTextAfterBotReply: Locator;
  readonly skipTextBotIntroduction: Locator;
  readonly seeHowAllieWorksButton: Locator;
  readonly botVideoTag: Locator;
  readonly noINeedMoreHelpTextBot: Locator;
  readonly reelsContainer: Locator;
  readonly closeBotIntroduction: Locator;
  readonly smarterWayToAskText: Locator;
  readonly letsStartLearningText: Locator;
  readonly hopeThisHelpedText: Locator;
  readonly noIamOkayTtext: Locator;
  readonly realLifeExampleButton: Locator;
  readonly askATeacherButton: Locator;
  readonly suggestion0: Locator;
  readonly askYourFollowUpQuestionsText: Locator;

  readonly changeTopicButton: Locator;
  readonly physicsSubject: Locator;
  readonly biologySubject: Locator;
  readonly topicSelectionDropdown: Locator;
  readonly confirmButtonAfterTopicSelection: Locator;
  readonly confirmButton: Locator;
  readonly gotItTextFromHighlights: Locator;
  readonly imageSolution: Locator;
  readonly listenText: Locator;
  readonly stopAudio: Locator;
  readonly audioProgress: Locator;
  readonly pauseAudioButton: Locator;
  readonly currentSpeed1x: Locator;
  readonly speed1point25x: Locator;

  readonly subjectForStream: (stream: string) => Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, CoursePageUrl, isMobile);
    this.subTitleDoubtsText = page.getByText('Get expert solutions for your');
    this.askADoubtButton = page.getByText('Ask a doubt');
    this.typeYourDoubtText = page.getByText('Ask your doubt'); //updated the previous
    this.getStartedButton = page.getByText("Get started");
    this.enterYourDoubtInput = page.getByPlaceholder('Type your doubt here...');
    this.uploadImageButton = page.getByTestId('chat-file-exp_btn');
    this.sendDoubtButton = page.getByTestId('chat-send_btn');
    this.yesGotitText = page.getByTestId('suggestion').getByText('Yes, got it');
    this.submitAnywayButton = page.getByTestId('suggestion').getByText('Yes, got it');
    this.pleaseTryAndSubmitText = page.getByText('Please try and submit a valid');
    this.studentAskedDoubt = (studentAskedDoubt: string) => page.getByTestId('doubt_list').getByText(`${studentAskedDoubt}`);
    this.isDoubtResolvedText = page.getByText('Is your doubt resolved?');
    this.doneTickButton = page.locator('[id="Master\\ Components"] #Vector');
    this.markedAsResolvedText = page.getByText('Mark as Resolved');
    this.doubtClosedText = page.getByText('This doubt has been closed.');
    this.doubtsText = page.getByText('Doubts', { exact: true });
    this.allenVerifiedText = page.getByText('ALLEN Verified');
    this.showMoreSolutionsText = page.getByText('Show more solutions');
    this.dragAndDropText = page.getByText('Drag & drop your image here');
    this.imageInputFile = page.locator('//input[@type="file"]');
    this.browseFileButton = page.getByText('Browse Files');
    this.doneButton = page.getByText('Done');
    this.cropImageText = page.getByText('Crop Image');
    this.uploadButton = page.getByText('Upload', { exact: true });
    this.uploadedImage = page.getByRole('img').nth(1);
    this.askYourDoubtText = page.getByText('Ask your doubt');
    this.studentDoubtText = (studentDoubt: string) => page.getByText(`${studentDoubt}`).first();
    this.noIDintText = page.getByTestId('suggestion').getByText("No, I didn't get it");
    this.transferToTeacherText = page.getByText('Transferred to teacher');
    this.followupWithTeacher = page.getByTestId('chat_unres_text').getByText('Ask any follow-up questions you have');
    this.sentDoubt = page.getByTestId('doubt_list_card');
    this.recordedAudio = page.locator("//*[@id='vuesax/linear/play']");
    this.needMoreHelp = page.getByText("No, need more help", { exact: true }).locator('visible=true');
    this.discussionClosed = page.getByText('Discussion has been closed');
    this.myDoubtsText = page.getByText("My doubts");
    this.askedDoubtSubject = page.locator("//*[@data-testid='subject_preview_selected']");
    this.askedDoubtTopic = page.locator('//*[@data-testid="selection-dropdown"]//div[@class="text-primary"]');
    this.selectSubjectAndTopic = page.getByText("Select subject & topic");
    this.selectFilterOption = page.locator("//*[@data-testid='filter-text']");
    this.sortByText = page.getByText("Sort by");
    this.latestRadioButton = page.locator('//input[@type="radio" and @value="DESC"]');
    this.subjectForFilter = page.locator("//button[@data-testid='chipComponent' and .//p[text()='Physics']]");
    this.selectTopicText = page.getByText("Select topic");
    this.searchTopicInput = page.getByPlaceholder("Search...");
    this.topicForFilter = (topic: string) => this.page.locator(`//div[text()='${topic}']`);
    this.applyFilterButton = page.getByRole('button', { name: 'Apply Filters' });
    this.appliedFilterResult = (doubt: string) => this.page.locator(`//div[@data-testid='doubt_list_card']//p[text()='${doubt}']`);
    this.askAQuestionButton = page.getByText("Ask a question");
    this.selectedTopicForDoubt = page.getByTestId("dropdown-option-0");
    this.vtagTitle = page.getByTestId("vtag_title");
    this.vtagInput = page.getByPlaceholder("Eg: NBC0518");
    this.qcodeSearchButton = page.getByTestId("qcode-search-btn");
    this.askDoubtInput = page.getByTestId('askdoubt-input-area')
    this.askAllieButton = page.getByTestId('ask-allie-button')
    this.chatArea = page.getByTestId('chat_area');
    //neet
    this.yesGotItTextBot = page.getByTestId('suggestion').getByText('Yes, Got it');
    this.noINeedHelpTextBot = page.getByTestId('suggestion').getByText('No, I need help');
    this.simplifyItText = page.getByTestId('suggestion').getByText("✨ Simplify it");
    this.askTeacherText = page.getByTestId('suggestion').getByText("Ask Teacher");
    this.doubtTransferedToTeacherText = page.getByText("Ask Teacher");
    this.askFiollowUpDoubtText = page.getByTestId("chat_unres_text").getByText("Ask any follow-up questions you have");
    this.okIGotIttext = page.getByTestId('suggestion').getByText("Okay, I got it");
    this.forMoreHelpAskTeacherText = page.getByText("For more help, ask our teacher for a detailed answer or keep asking Allie.");
    this.gotItTextFromBot = page.getByTestId('spotlight-lottie-end').getByText("Got it");
    this.gotItTextAfterBotReply = page.getByText('Got it', { exact: true })
    this.skipTextBotIntroduction = page.getByTestId("ftue_skip_button");
    this.seeHowAllieWorksButton = page.getByText("See how ALLIE works");
    this.botVideoTag = page.getByTestId("ftue_video");
    this.noINeedMoreHelpTextBot = page.getByTestId("suggestion").getByText("No, I need more help");
    this.reelsContainer = page.getByTestId("reels_container");
    this.closeBotIntroduction = page.locator('div.cursor-pointer.ml-5');
    this.smarterWayToAskText = page.getByText("Now there's a smarter way to get answers for your doubts!");
    this.letsStartLearningText = page.getByText("Let's start learning");
    this.hopeThisHelpedText = page.getByText("Hope this helped! Keep asking your questions.");
    this.noIamOkayTtext = page.getByTestId("suggestion").getByText("No, I'm okay");
    this.transferYourDoubtToTeacherText = page.getByText("Ask your follow up questions");
    this.realLifeExampleButton = page.getByTestId("suggestion").getByText("Real life example");
    this.askATeacherButton = page.getByTestId('suggestion').getByText("Ask a teacher");
    this.suggestion0 = page.getByTestId("suggestion").locator('visible=true');
    this.askYourFollowUpQuestionsText = page.getByText("Ask your follow up questions");
    this.changeTopicButton = page.getByTestId("suggestion").getByText("Change");
    this.physicsSubject = page.getByTestId("subject-chip-1701187953vC_1656").getByText("Physics"); 
    this.topicSelectionDropdown = page.getByTestId("selection-dropdown").getByText("Select Topic");
    this.confirmButtonAfterTopicSelection = page.getByRole('dialog').getByText('Confirm');
    this.gotItTextFromHighlights = page.getByText("Got it");
    this.confirmButton = page.getByTestId("suggestion").getByText("Confirm");
    this.biologySubject = page.getByTestId("subject-chip-1701187953vC_1141").getByText("Biology");
    this.imageSolution = page.getByTestId('chatsection').locator('img');
    this.listenText = page.getByText('Listen text');
    this.stopAudio = page.getByText('Stop audio');
    this.audioProgress = page.locator('input[aria-label="Audio progress"]');
    this.pauseAudioButton = this.audioProgress.locator('xpath=../preceding-sibling::*');
    this.currentSpeed1x = page.getByText('1x');
    this.speed1point25x = page.getByText('1.25x')
    this.subjectForStream = (stream: string) => {
        const subjectIds = {
            'NEET': '1701187953vC_1656',  // Physics for NEET
            'JEE': '1701181887VZ_354',   // Physics for JEE
        };
        return this.page.getByTestId(`subject-chip-${subjectIds[stream]}`).getByText("Physics");
    };
  }

  async askARandomDoubtInDoubtsPage(academicQuestion) {
    await expect(this.chatArea, "verify chat area is visible").toBeVisible();
    await this.chatArea.click();
    await this.chatArea.fill(academicQuestion);
    await expect(this.uploadImageButton, "verify upload image button").toBeVisible();
    await expect(this.sendDoubtButton, "verify send doubt button").toBeVisible();
    await this.sendDoubtButton.click();
    // await this.page.waitForLoadState('networkidle');
  }

  async verifyResolvedStudentDoubt(raisedDoubt) {
    await expect(this.studentAskedDoubt(raisedDoubt).first(), "Verify raised doubt is visible").toBeVisible();
    await this.studentAskedDoubt(raisedDoubt).first().click();
    await expect(this.isDoubtResolvedText, "Verify is your Doubt Resolved Text").toBeVisible();
    await expect(this.doneTickButton, "Verify resolved green tick button is visible").toBeVisible();
    await this.doneTickButton.click();
    await expect(this.markedAsResolvedText, "verify marked as resolved text is visible").toBeVisible();
    await expect(this.doubtClosedText, "verify doubt is closed text is visible").toBeVisible();
  }

  async verifyDoubtNutGivesSolutionAndSuggestsMoreSolutions() {
    await customExpect(15000)(this.allenVerifiedText, "Verify doubtnut solution is visible").toBeVisible();
    await expect(this.showMoreSolutionsText, "Verify view More Solutions Text").toBeVisible();
    await this.showMoreSolutionsText.click();
    await slowExpect(this.allenVerifiedText.nth(2), "Verify 2nd doubtnut solution is visible").toBeVisible();
  }

  async askAValidDoubtWithImageInDoubtsPage(imageQuestion) {
    await customExpect(15000)(this.askAQuestionButton, "Verify ask a doubt button").toBeVisible();
    await this.askAQuestionButton.click();
    await expect(this.typeYourDoubtText, "Verify 'Type your doubt or upload an image' text").toBeVisible();
    await expect(this.enterYourDoubtInput, "Verify text input placeholder to enter doubt").toBeVisible();
    await expect(this.uploadImageButton, "Verify upload image button is visible").toBeVisible();
    await this.uploadImageButton.click();
    await expect(this.browseFileButton, "Verify browse files button is visible").toBeVisible();
    await this.imageInputFile.setInputFiles(imageQuestion);
    await expect(this.doneButton, "Verify done button is visible").toBeVisible();
    await expect(this.cropImageText, "Verify crop image text is visible").toBeVisible();
    await this.doneButton.click();
    await expect(this.uploadButton, "Verify upload button is visible").toBeVisible();
    await this.uploadButton.click();
    await expect(this.uploadedImage, "Verify uploaded image should is visible").toBeVisible();
    await expect(this.typeYourDoubtText, "Verify 'Type your doubt & upload an image' text").toBeVisible();
    await this.page.waitForTimeout(2000); //DOM is preloading in ms but in UI Wait is required to make the button enable to click. TO DO
    await this.sendDoubtButton.click();
  }

  async verifyImageDoubtResolvedAtStudent() {
    await expect(this.isDoubtResolvedText, "Verify is your Doubt Resolved Text").toBeVisible();
    await expect(this.doneTickButton, "Verify resolved green tick button is visible").toBeVisible();
    await this.doneTickButton.click();
    await expect(this.markedAsResolvedText, "verify marked as resolved text is visible").toBeVisible();
    await expect(this.doubtClosedText, "verify doubt is closed text is visible").toBeVisible();
  }

  async transferDoubtToTeacher(stream: String) {
    await slowExpect(this.yesGotitText.or(this.transferYourDoubtToTeacherText)).toBeVisible();
    if (await this.yesGotitText.isVisible()) {
      await slowExpect(this.yesGotitText, "Verify yes got it text is visible").toBeVisible();
      await expect(this.noIDintText, "Verify no i didn't get it text is visible").toBeVisible();
      await this.noIDintText.click();
    }

    if (stream == "NEET") {
      await slowExpect(this.transferToTeacherText, "Verify transfer to teacher text is visible").toBeVisible();
      await slowExpect(this.followupWithTeacher, "Verify follow up with teacher text is visible").toBeVisible();
    } else {
      await slowExpect(this.transferYourDoubtToTeacherText, "Verify transfer to teacher text is visible").toBeVisible();
    }
  }

  async verifyRepliedDoubtFromTeacherAndFollowUp() {
    await slowExpect(this.recordedAudio, "Verify recorded audio is visible").toBeVisible();
    await slowExpect(this.needMoreHelp, "Verify need more help button is visible").toBeVisible();
    await this.needMoreHelp.click();
  }

  async verifyFollowUpAnswerAndCloseDoubt(secondSolution) {
    await slowExpect(this.studentAskedDoubt(secondSolution).first(), "Verify second solution from teacher is visible").toBeVisible();
    await slowExpect(this.yesGotitText, "Verify yes got it text is visible").toBeVisible();
    await this.yesGotitText.click();
    await slowExpect(this.discussionClosed, "Verify discussion Closed Text is visible").toBeVisible();
  }

  async verifyDoubtMarkedAsResolved() {
    await customExpect(20000)(this.myDoubtsText, "Verify My doubts text is visible").toBeVisible();
    await expect(this.showMoreSolutionsText, "Verify show more solutions is visble").toBeVisible();
    await this.showMoreSolutionsText.click();
    await expect(this.yesGotitText, "Verify yes got it text is visible").toBeVisible();
    await this.yesGotitText.click();
    await this.markedAsResolvedText.scrollIntoViewIfNeeded();
    await slowExpect(this.markedAsResolvedText, "Verify Marked as resolved is visible").toBeVisible();
  }
  async verifyDoubtMarkedAsResolvedForVtag() {
    await expect(this.myDoubtsText, "Verify My doubts text is visible").toBeVisible();
    if(await this.confirmButton.isVisible()){
      await this.confirmButton.click();
    }
    await this.page.waitForTimeout(1000);
    if (await this.allenVerifiedText.isVisible()) {
      await slowExpect(this.yesGotitText, "Verify yes got it text is visible").toBeVisible();
      await this.yesGotitText.click();
      await this.markedAsResolvedText.scrollIntoViewIfNeeded();
      await slowExpect(this.markedAsResolvedText, "Verify Marked as resolved is visible").toBeVisible();
    } else {
      await slowExpect(this.yesGotitText, "Verify yes got it text is visible").toBeVisible();
      await this.yesGotitText.click();
      await expect(this.noIamOkayTtext, "Verify No I am Okay text is visible?").toBeVisible();
      await this.noIamOkayTtext.click();
      await expect(this.hopeThisHelpedText, "Verify hope this helped text by bot is visible").toBeVisible();
    }
  }
  async verifyFirstUserExperienceForNeetUsersAndAskAQuestion() {
    await slowExpect(this.skipTextBotIntroduction, "Verify skip button for the bot introduction is visible").toBeVisible();
    await expect(this.reelsContainer).toBeVisible();
    await this.skipTextBotIntroduction.click();
    await slowExpect(this.seeHowAllieWorksButton, "Verify See how allie works button").toBeVisible();
    await this.seeHowAllieWorksButton.click();
    await customExpect(20000)(this.gotItTextFromBot, "Verify got it text from the bot is visible").toBeVisible();
    await this.gotItTextFromBot.click();
    await customExpect(20000)(this.gotItTextAfterBotReply, "Verify got it text after bot reply is visible").toBeVisible();
    await this.gotItTextAfterBotReply.click();
    await this.myDoubtsText.hover();
    await customExpect(10000)(this.askAQuestionButton, "Verify My Doubts text is visible").toBeVisible();
    await this.askAQuestionButton.click();
  }

  async verifyFirstBotExperience() {
    await slowExpect(this.skipTextBotIntroduction, "Verify skip button for the bot introduction is visible").toBeVisible();
    await expect(this.reelsContainer).toBeVisible();
    await this.skipTextBotIntroduction.click();
    await expect(this.closeBotIntroduction, "Verify close button is visible").toBeVisible();
    await this.closeBotIntroduction.click();
    await slowExpect(this.gotItTextFromBot).toBeVisible();
    await this.gotItTextFromBot.click();

  }
  async verifyFirstBotExperienceDoubtNotAsked() {
    await slowExpect(this.skipTextBotIntroduction, "Verify skip button for the bot introduction is visible").toBeVisible();
    await expect(this.reelsContainer).toBeVisible();
    await this.skipTextBotIntroduction.click();
    await expect(this.closeBotIntroduction, "Verify close button is visible").toBeVisible();
    await this.myDoubtsText.hover();
    await customExpect(10000)(this.askAQuestionButton, "Verify My Doubts text is visible").toBeVisible();
    await this.askAQuestionButton.click();
  }

  async TransferDoubtToteacherFromBot() {
    await customExpect(20000)(this.yesGotItTextBot, "Verify yes got it text from bot").toBeVisible();
    await slowExpect(this.noINeedHelpTextBot, "Verify no i need help text is visible").toBeVisible();
    await this.noINeedHelpTextBot.click();
    await customExpect(20000)(this.realLifeExampleButton, "Verify real life example text is visible").toBeVisible();
    await this.realLifeExampleButton.click();
    await customExpect(20000)(this.okIGotIttext, "Verify ok i got it text is visible").toBeVisible();
    await customExpect(15000)(this.askATeacherButton).toBeVisible();
    await this.askATeacherButton.click();
    const timeout = 3000; // 3 seconds
    const interval = 50; // Check every 50ms
    let elapsedTime = 0;

    while (elapsedTime < timeout) {
      if (await this.suggestion0.isVisible()) {
        await this.suggestion0.click();
        break; // Exit the loop if the element is visible and clicked
      }
      await this.page.waitForTimeout(interval); // Wait for the interval before checking again
      elapsedTime += interval; // Increment the elapsed time
    }
    await slowExpect(this.askYourFollowUpQuestionsText).toBeVisible();
    await slowExpect(this.askFiollowUpDoubtText).toBeVisible();
  }

  async navigateToDoubtsIfAlreadyAsked() {
    await slowExpect(this.askDoubtInput, "Verify ask a doubt inout box is visible").toBeVisible();
    await this.askDoubtInput.click();
    await expect(this.askAllieButton, "Verify ask allie button is visible").toBeVisible();
    await this.askAllieButton.click();
  }

  async navigateToDoubtsWithFallbackUI() {
    await expect(this.askADoubtButton, "Verify Ask doubt title is visible").toBeVisible();
    await expect(this.getStartedButton, "Verify get started button is visible").toBeVisible();
    await this.getStartedButton.click();
  }

  async gotItTextPopUp(){
    await this.page.waitForTimeout(1000);  //needed to see the pop up for first time user
    if(await this.gotItTextFromHighlights.isVisible()){
      await this.gotItTextFromHighlights.click()
    }
  }
  async selectSubjectAndTopicPopUp(topic: string, subject: string, stream: string) {
    await this.gotItTextPopUp();

    await expect(this.changeTopicButton).toBeVisible();
    await this.gotItTextPopUp();
    await this.changeTopicButton.click();
    if(subject === "physics"){
    await expect(this.subjectForStream(stream)).toBeVisible();
    await this.subjectForStream(stream).click();
    }
    if(subject === "biology"){
      await expect(this.biologySubject).toBeVisible();
      await this.biologySubject.click();
    }
    
    await expect(this.topicSelectionDropdown).toBeVisible();
    await this.topicSelectionDropdown.click();
    
    await expect(this.searchTopicInput).toBeVisible();
    await this.searchTopicInput.fill(topic);
    
    await expect(this.topicForFilter(topic)).toBeVisible();
    await this.topicForFilter(topic).click();
    
    await expect(this.confirmButtonAfterTopicSelection).toBeVisible();
    await this.confirmButtonAfterTopicSelection.click();
    
    await this.page.waitForTimeout(1500); // needed to see the pop up for first time user
  }

  async navigateToDoubtsSection(isFTUE: boolean) {
    if (isFTUE) {
      await this.verifyFirstBotExperienceDoubtNotAsked();
    }
    await this.navigateToDoubtsIfAlreadyAsked();
  }

  async verifyImageSolution() {
    await expect(this.confirmButton).toBeVisible();
    await this.confirmButton.click();
    await this.page.waitForTimeout(30000);
    await slowExpect(this.imageSolution, "Verify the image solution is visible").toBeVisible();
  }

  async verifyBotAudioSupport() {
    await expect(this.confirmButton).toBeVisible();
    await this.confirmButton.click();
    await expect(this.listenText, "Listen button should appear after confirmation").toBeVisible({ timeout: 20000 });
    await this.listenText.click();
    await expect(this.audioProgress, "Verify audio progress is visible").toBeVisible();
    await this.audioProgress.fill('60'); //Verify user is able to change audio progress
    await this.audioProgress.fill('0');
    await expect(this.pauseAudioButton, "Verify pause audio button is visible").toBeVisible();
    await this.pauseAudioButton.click();
    await expect(this.currentSpeed1x, "Verify current speed is showing as 1x").toBeVisible();
    await this.currentSpeed1x.click();
    await expect(this.speed1point25x, "Verify select speed 1.25x is visible").toBeVisible();
    await this.speed1point25x.click();
    await this.page.waitForTimeout(1000);
    await expect(this.speed1point25x, "Verify current speed is showing as 1.25x").toBeVisible();
  }
}
 