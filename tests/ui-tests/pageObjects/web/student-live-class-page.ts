
import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const StudentLiveClassPageUrl = '/join'
export class StudentLiveClassPage extends WebPage {
  readonly micButton: Locator;
  readonly videoButton: Locator;
  readonly raiseHandButton: Locator;
  readonly chatButton: Locator;
  readonly settingsButton: Locator;
  readonly leaveClassButton: Locator;
  readonly classNameOnLiveClass: (className: string) => Locator;
  readonly selectPoll: (selectPoll: string) => Locator;
  readonly activePollTitle: Locator;
  readonly submitButton: Locator;
  readonly typeYourMessageHereFeild: Locator;
  readonly chatMessage: (message: string) => Locator;
  readonly sendButton: Locator;
  readonly queriesTab: Locator;
  readonly likeIcon: Locator;
  readonly yourBlockedByTeacherText: Locator;
  readonly noQueriesYetText: Locator;
  readonly chatTab: Locator;
  readonly unMuteBringOnStageButton: Locator;
  readonly cancelButton: Locator;
  readonly teacherAskingToUnmuteText: Locator;
  readonly videoBringOnStageButton: Locator;
  readonly teacherAskingToTurnOnText: Locator;
  readonly lowerHandButton: Locator;
  readonly yourHandIsBeenLoweredText: Locator;
  readonly closeIconPopUp: Locator;
  readonly leaveClassText: Locator;
  readonly leaveClassPopUpText: Locator;
  readonly leaveClassPopUpButton: Locator;
  readonly stayClassButton: Locator;
  readonly classEndedText: Locator;
  readonly timeToTakeBreakAndRechargeText: Locator;
  readonly chattingWithText: Locator;
  readonly teacherOnlyOption: Locator;
  readonly chatDisabledByTeacherText: Locator;
  readonly closePollPopUp: Locator;
  readonly emojiIcon: Locator;
  readonly frequentlyUsedText: Locator;
  readonly frequentlyUsedThumsupEmoji: Locator;
  readonly sentThumsupEmoji: Locator;
  readonly sentUrlMessage: (sentUrl: string) => Locator;
  readonly micEnabled: Locator;
  readonly micDisabled: Locator;
  readonly videoEnabled: Locator;
  readonly videoDisabled: Locator;

  readonly activePollEndPollTitle: Locator;
  readonly fullScreenButton: Locator;
  readonly enteredFullScreenButton: Locator;
  readonly exitedFullScreenButton: Locator;
  readonly activePollSubmittedPollTitle: Locator;
  readonly selectedPollOption: (selectPoll: string) => Locator;
  readonly anwerSubmittedText: Locator;
  readonly cancelPollPopUp: Locator;
  readonly watchImage: Locator;
  readonly activePollStoppedTitle: Locator;
  readonly veryWeekSignal: Locator;
  readonly errorMessageClose: Locator;
  readonly teacherOntheWayText: Locator;
  readonly exitFullScreenButton: Locator;
  readonly doubtsButton: Locator;
  readonly sendDoubtInput: Locator;
  readonly postDoubtButton: Locator;
  readonly openDoubtsTab: Locator;
  readonly resolvedDoubtsTab: Locator;
  readonly myDoubtsTab: Locator;
  readonly invalidDoubtToast: Locator;
  readonly upvoteButton: (voteCount: string) => Locator;
  readonly blockedByTeacherText: Locator;
  readonly resolveButton: Locator
  readonly doubtsText: (doubtsMessage: string) => Locator;
  readonly yesResolveOption: Locator;
  readonly noCancelOption: Locator;
  readonly doubtsDisabledText: Locator;
  readonly pollResultsText: Locator;
  readonly doubtDeletedPopUp: Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, StudentLiveClassPageUrl, isMobile);
    this.micButton = page.getByText('Mic');
    this.classNameOnLiveClass = (className: string) => page.getByText(`${className}`).first();
    this.videoButton = page.getByText('Video');
    this.raiseHandButton = page.getByText('Raise Hand');
    this.chatButton = page.getByTestId('controls-wrapper').getByText('Chat');
    this.settingsButton = page.getByText('Settings');
    this.leaveClassButton = page.getByTestId('dls-button').getByText("Leave Class");
    this.stayClassButton = page.getByRole('button', { name: 'Stay back' });
    this.activePollTitle = page.getByText('Active Poll');
    this.selectPoll = (selectPoll: string) => page.getByTestId(`poll-option-0`);
    this.submitButton = page.getByText('Submit');
    this.typeYourMessageHereFeild = page.getByPlaceholder('Type your message here');
    this.chatMessage = (message: string) => page.getByText(`${message}`).first();
    this.sendButton = page.getByRole('img', { name: 'send' });
    this.queriesTab = page.getByText('Queries');
    this.likeIcon = page.getByRole('img', { name: 'like button' });
    this.yourBlockedByTeacherText = page.getByText('You are blocked by teacher');
    this.noQueriesYetText = page.getByText('No queries yet');
    this.chatTab = page.getByRole('list').getByText('Chat');
    this.unMuteBringOnStageButton = page.getByRole('button', { name: 'Unmute' });
    this.cancelButton = page.getByRole('button', { name: 'Cancel' });
    this.teacherAskingToTurnOnText = page.getByText('Teacher is asking you to turn on video');
    this.teacherAskingToUnmuteText = page.getByText('Teacher is asking you to unmute');
    this.videoBringOnStageButton = page.getByRole('button', { name: 'Switch on video' });;
    this.lowerHandButton = page.getByText('Lower Hand');
    this.yourHandIsBeenLoweredText = page.getByText('You’re hand has been lowered.');
    this.closeIconPopUp = page.locator('(//*[text()="You’re hand has been lowered."]/ancestor::*[@data-testid="modal-element"]/descendant::*[contains(@class, "inline-flex rounded-full")]//*)[3]');
    this.leaveClassText = page.getByText('Leaving already?');
    this.leaveClassPopUpText = page.getByText('You will be marked absent. So');
    this.leaveClassPopUpButton = page.getByRole('dialog').getByRole('button', { name: 'Leave Class' });
    this.classEndedText = page.locator('//*[contains(text(),"Class Ended!")]');
    this.timeToTakeBreakAndRechargeText = page.getByText('Time to take a break and');
    this.teacherOnlyOption = page.getByText('Teacher only');
    this.chattingWithText = page.getByText('Chatting with');
    this.chatDisabledByTeacherText = page.getByText('Chat disabled by teacher');
    //there are 2 elements for close pop up
    this.closePollPopUp = page.locator('(//*[contains(@class,"shimmering-image")])[1]');
    this.emojiIcon = page.locator("(//*[@id='chat-input-textarea']/following-sibling::*)[1]/*[@alt='icons']");
    this.frequentlyUsedText = page.getByText('Frequently used');
    this.frequentlyUsedThumsupEmoji = page.locator('div').filter({ hasText: /^Frequently used👍😀😍😆😜😅😂😱$/ }).getByLabel('👍');
    this.sentThumsupEmoji = page.getByText('👍').first();
    this.sentUrlMessage = (sentUrl: string) => page.getByText(`${sentUrl}`).first();
    this.micEnabled = page.locator("//*[@data-testid='control-btn-icon']/*[contains(@alt,'Audio Enabled')]");
    this.micDisabled = page.locator("//*[@data-testid='control-btn-icon']/*[contains(@alt,'Audio Disabled')]");
    this.videoEnabled = page.locator("//*[@data-testid='control-btn-icon']/*[contains(@alt,'Video Enabled')]");
    this.videoDisabled = page.locator("//*[@data-testid='control-btn-icon']/*[contains(@alt,'Video Disabled')]");
    //there are 2 elements for close pop up
    this.closePollPopUp = page.getByRole('img', { name: 'cancel' });
    this.activePollEndPollTitle = page.getByText('Active Poll (Ended)');
    this.activePollSubmittedPollTitle = page.getByText('Active Poll (Submitted)');

    this.fullScreenButton = page.locator('//button[@id="fullScrBtn"]');
    this.enteredFullScreenButton = page.locator('//img[contains(@src,"enter-fullscreen")]');
    this.exitedFullScreenButton = page.locator('//img[contains(@src,"exit-fullscreen")]');
    this.exitFullScreenButton = page.locator('#fullScrBtn');

    this.selectedPollOption = (selectPoll: string) => page.getByText('Active Poll').locator('xpath=../following-sibling::div').locator('div:nth-of-type(2)').getByText(`${selectPoll}`);
    this.anwerSubmittedText = page.getByText('Your answer is submitted.');
    this.cancelPollPopUp = page.getByRole('img', { name: 'cancel' });
    this.watchImage = page.locator('//*[@class="shimmering-image-container w-full h-full"]//*[@alt="image watch"]');
    this.activePollStoppedTitle = page.getByText('Active Poll (Stopped)');
    this.veryWeekSignal = page.getByText('Very weak');
    this.errorMessageClose = page.getByRole('img', { name: 'close error' })
    this.teacherOntheWayText = page.getByText('Your teacher is on the way to')
    this.doubtsButton = page.locator('#controls-doubt-btn').getByTestId('control-btn-icon');
    this.sendDoubtInput = page.getByPlaceholder('Type your doubt here');
    this.postDoubtButton = page.getByText('POST');
    this.openDoubtsTab = page.getByText('Open');
    this.resolvedDoubtsTab = page.getByText('Resolved').first();
    this.myDoubtsTab = page.getByText('My Doubts');
    this.invalidDoubtToast = page.getByText('This does not look like a valid doubt');
    this.upvoteButton = (voteCount: string) => page.locator(`div span:text-is("${voteCount}")`);
    this.blockedByTeacherText = page.getByText('You are blocked by teacher').first();
    this.resolveButton = page.getByTestId('doubt-card').getByText('Resolve');
    this.doubtsText = (doubtsMessage: string) => page.getByTestId('doubt-text').getByText(`${doubtsMessage}`);
    this.yesResolveOption = page.getByRole('button', { name: 'Yes, Resolve' });
    this.noCancelOption = page.getByRole('button', { name: 'No, Cancel' });
    this.doubtsDisabledText = page.getByText('Doubts disabled by teacher');
    this.pollResultsText = page.getByText('Poll Results');
    this.doubtDeletedPopUp = page.getByText('Your doubt was deleted by teacher');
  }

  async validateStudentLiveClassPage(className) {
    await this.page.waitForTimeout(5000); //wait is requried due to gif animation of entering into the class
    await expect(this.leaveClassButton, "verify leave class button is visible").toBeVisible();
    await expect(this.classNameOnLiveClass(className), "verify Scheduled className on Teacher live class page is visible").toBeVisible();
    await expect(this.chatButton, "verify Chat button is visible").toBeVisible();
    await expect(this.raiseHandButton, "verify raise hand button is visible").toBeVisible();
    await expect(this.videoButton, "verify video button is visible").toBeVisible();
    await expect(this.micButton, "verify mic button is visible").toBeVisible();
    await expect(this.settingsButton, "verify settings button is visible").toBeVisible();
  }

  async submitThePoll(selectPoll) {

    await customExpect(15000)(this.activePollTitle, "verify active poll title is visible").toBeVisible();
    await customExpect(10000)(this.submitButton, "verify submit button is disbaled").toBeDisabled();
    await customExpect(10000)(this.selectPoll(selectPoll), "verify select poll option is visible").toBeVisible();
    slowExpect(await this.selectPoll(selectPoll).click());
    await slowExpect(this.selectPoll(selectPoll)).toHaveClass(/(?:^|\s)border(?:\s|$)/);

    await this.page.waitForTimeout(2000); // required this
    await customExpect(10000)(this.submitButton, "verify submit button is visible").toBeVisible();
    // await expect(this.veryWeekSignal, "verify very week signal is visible").not.toBeVisible();
    await this.submitButton.click();
    // try {
    //   await expect(this.veryWeekSignal, "verify very weak signal is visible").toBeVisible();
    //   await this.submitButton.click();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }
    await this.page.waitForTimeout(2000); //required
    await customExpect(10000)(this.anwerSubmittedText, "verifyanwser submitted text is visible").toBeVisible();
    await customExpect(10000)(this.watchImage, "verify watch image is visible").toBeVisible();
  }

  async clickOnChatButton() {
    await expect(this.chatButton, "verify chat button is visible").toBeVisible();
    await this.chatButton.click();
  }

  async verifyEndPoll() {
    // await this.page.waitForTimeout()
    await customExpect(15000)(this.pollResultsText, "verify poll results text is visible").toBeVisible();
  }

  /*validate chat from Teacher from Student live class  */
  async verifyChatFromTeacherToStudent(message) {
    await expect(this.typeYourMessageHereFeild, "verify type your message here feild is visible").toBeVisible();
    await expect(this.chatMessage(message), "verify chat message from Teacher is visible").toBeVisible();
  }

  /*Send chat from Student to Teacher in student live class  */
  async studentChatWithTeacher(message) {
    await expect(this.typeYourMessageHereFeild, "verify enable chat button is visible").toBeVisible();
    await this.typeYourMessageHereFeild.fill(message);
    await expect(this.sendButton, "verify send button is visible").toBeVisible();
    await this.sendButton.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await expect(this.chatMessage(message), "verify chat message from Teacher is visible").toBeVisible();
  }

  /* verify and like the chat is moved to queries tab by teacher in student live class  */
  async verifyChatIsMovedToQueryByTeacher(message) {
    await expect(this.queriesTab, "verify queries tab is visible").toBeVisible();
    await this.queriesTab.click()
    await expect(this.chatMessage(message), "verify chat message from Teacher is visible in queries tab").toBeVisible();
    await expect(this.likeIcon, "verify like Icon is visible").toBeVisible();
    await this.likeIcon.click();
  }

  /* verify chat is resolved from queries tab in student live class  */
  async verifyQueryIsResolvedByTeacher() {
    await expect(this.noQueriesYetText, "verify chat message is resolved from Teacher is visible ").toBeVisible();
    await expect(this.chatTab, "verify resolve query Icon from Teacher is visible").toBeVisible();
    await this.chatTab.click();
  }

  /* verify chat is blocked by teacher in student live class  */
  async verifyChatIsBlockedByTeacher() {
    await expect(this.yourBlockedByTeacherText.first(), "verify your blocked by Teacher text is visible in Student live class").toBeVisible();
  }

  /* verify chat is unblocked by teacher in student live class  */
  async verifyTeacherJoinClassInStudent() {
    await this.page.waitForTimeout(5000); //wait is requried due to gif animation of entering into the class
    await customExpect(20000)(this.teacherOntheWayText, "verify Teacher is on the way text is not visible in Student live class").toBeHidden();
  }

  /* verify chat is unblocked by teacher in student live class  */
  async verifyChatIsUnBlockedByTeacher() {
    await expect(this.errorMessageClose, "verify close error pop up in Student live class").toBeVisible();
    await this.errorMessageClose.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await expect(this.typeYourMessageHereFeild, "verify Type your Message here text is visible as teacher unblocks in Student live class").toBeVisible();
  }

  /* verify and click on unMute & Video On - Bring on Stage by teacher in student live class  */
  async clickOnUnMuteVideoForBringOnStage() {
    await expect(this.teacherAskingToUnmuteText, "verify teacher asking to UnMute is visible as teacher Bring on stage in Student live class").toBeVisible();
    await slowExpect(this.unMuteBringOnStageButton, "verify Unmute button is visible as teacher Bring on stage in Student live class").toBeVisible();
    await this.unMuteBringOnStageButton.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await slowExpect(this.unMuteBringOnStageButton, "verify teacher asking to UnMute is not visible").toBeHidden();
    await slowExpect(this.videoBringOnStageButton, "verify video button is visible as teacher Bring on stage in Student live class").toBeVisible();
    await expect(this.teacherAskingToTurnOnText, "verify teacher asking to turn on is visible as teacher Bring on stage in Student live class").toBeVisible();
    await this.videoBringOnStageButton.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await slowExpect(this.videoBringOnStageButton, "verify teacher asking to turn on is not visible as teacher Bring on stage in Student live class").toBeHidden();
  }

  /* click and verify raise hand in student live class  */
  async clickOnRaiseHandFromStudent() {
    await expect(this.raiseHandButton, "verify raise hand button is visible in Student live class").toBeEnabled();
    // await expect(this.veryWeekSignal, "verify very week signal is visible").not.toBeVisible();
    await this.raiseHandButton.click();
    await this.page.waitForTimeout(200); // required this due to websocket 
    // try {
    //   await expect(this.veryWeekSignal, "verify very weak signal is visible").toBeVisible();
    //   await this.raiseHandButton.click();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }
    await expect(this.lowerHandButton, "verify lower hand button is visible in Student live class").toBeVisible();
  }
  /* verify Lowered hand pop up after waiting for 30 sec in student live class  */
  async verifyLoweredHandPopUpFromStudent() {
    await customExpect(60000)(this.yourHandIsBeenLoweredText, "verify your lower hand is been lowered text is visible in Student live class").toBeVisible();
    await expect(this.closeIconPopUp, "verify close icon button is visible in Student live class").toBeVisible();
    await this.closeIconPopUp.click();
  }

  /* verify lowered hand by teacher in student live class  */
  async verifyHandRaised() {
    await expect(this.lowerHandButton, "verify lower hand button is visible in Student live class").toBeHidden();
  }

  /* click and verify leave class by student in student live class  */
  async clickOnLeaveClassButton() {
    await expect(this.leaveClassButton, "verify leave class button is visible in Student live class").toBeVisible();
    await this.leaveClassButton.click();
    await expect(this.leaveClassText, "verify leave class text is visible in Student live class").toBeVisible();
    await expect(this.leaveClassPopUpButton, "verify leave class button in pop up is visible in Student live class").toBeVisible();
    await expect(this.stayClassButton, "verify stay class button in pop up is visible in Student live class").toBeVisible();
    await this.leaveClassPopUpButton.click();
    await this.page.waitForTimeout(1000);//required for websocket
  }

  /*  verify class ended by teacher in student live class  */
  async verifyClassEndedPopUp() {
    await customExpect(10000)(this.classEndedText, "verify class ended text is visible in Student live class").toBeVisible();
    await expect(this.timeToTakeBreakAndRechargeText, "verify time to take break and recharge text is visible in Student live class").toBeVisible();
  }

  /*  verify teacher only option slected by teacher in student live class  */
  async verifyTeacherOnlyOption() {
    await expect(this.teacherOnlyOption, "verify teacher only option is visible in Student live class").toBeVisible();
  }
  /*  verify no one option selected by teacher in student live class  */
  async verifyNoOneOption() {
    await expect(this.chatDisabledByTeacherText, "verify chat disabled by teacher when slected no one option is visible in Student live class").toBeVisible();
  }



  async verifyFullScreenLiveClass() {
    await expect(this.enteredFullScreenButton, "verify student live class in full screen is visble").toBeVisible();
    await this.enteredFullScreenButton.click();
    await expect(this.exitedFullScreenButton, "verify class exit screen icon is visble").toBeVisible();
  }

  async verifyExitFullScreenLiveClass() {
    await this.exitedFullScreenButton.click({ force: true });
    await expect(this.enteredFullScreenButton, "verify full screen icon is visible").toBeVisible();
  }

  /*  verify student can send a emoji in chat  */
  async verifyStudentChatEmojiFeature() {
    await slowExpect(this.emojiIcon, "verify emoji icon is visible in Student live class").toBeVisible();
    await this.emojiIcon.click();
    await expect(this.frequentlyUsedText, "verify frequently used emoji text is visible in student live").toBeVisible();
    await expect(this.frequentlyUsedThumsupEmoji, "verify thumsup emoji is visible in emoji list").toBeVisible();
    await this.frequentlyUsedThumsupEmoji.click();
    await expect(this.typeYourMessageHereFeild, "veriy type your message here text is visible").toBeVisible();
    await this.typeYourMessageHereFeild.click();
    await expect(this.sendButton, "veriy send button is visible").toBeVisible();
    await this.sendButton.click();
    await expect(this.sentThumsupEmoji, "verify emoji is sent from student side").toBeVisible();
  }

  /*  verify student can send a url link in chat  */
  async verifyStudentChatUrlLinkFeature(UrlLink) {
    await slowExpect(this.typeYourMessageHereFeild, "veriy type your message here text is visible").toBeVisible();
    await this.typeYourMessageHereFeild.fill(UrlLink);
    await expect(this.sendButton, "veriy send button is visible").toBeVisible();
    await this.sendButton.click();
    await expect(this.sentUrlMessage(UrlLink), "veriy sent URL link is visible").toBeVisible();
    const newTabPromise = this.page.waitForEvent("popup");
    await this.sentUrlMessage(UrlLink).click();
    const newTab = await newTabPromise;
    await expect(newTab).toHaveURL(/.*google.com*/);
    await newTab.close();
  }
  /*  verify Raise Hand When Teacher Blocks Student  */
  async verifyRaiseHandWhenTeacherBlocksStudent() {
    await expect(this.raiseHandButton, "verify raise hand button is visible in Student live class").toBeVisible();
    await this.raiseHandButton.hover();
    await expect(this.yourBlockedByTeacherText.nth(2), "verify your blocked by Teacher text is visible after mouse hover on raise hand").toBeVisible();
    await this.page.hover('body');
  }

  /*  verify mic, video on/off from student side  */
  async veriyMicVideoOnAndOffFromStudent() {
    await expect(this.teacherAskingToUnmuteText, "verify teacher asking to UnMute is visible as teacher Bring on stage in Student live class").toBeVisible();
    await this.unMuteBringOnStageButton.click();
    await this.page.waitForTimeout(1000); // wait is required to load the mic enabled image
    await expect(this.micEnabled, "verify mic enabled is visible from student").toBeVisible();
    await expect(this.videoBringOnStageButton, "verify video button is visible as teacher Bring on stage in Student live class").toBeVisible();
    await this.videoBringOnStageButton.click();
    await this.page.waitForTimeout(1000); // wait is required to load the video enabled image
    await expect(this.videoEnabled, "verify video enabled is visible from student").toBeVisible();
    await this.micButton.click();
    await this.page.waitForTimeout(1000); // wait is required to load the mic enabled image
    await expect(this.micDisabled, "verify mic is disabled is visible").toBeVisible();
    await this.videoButton.click();
    await this.page.waitForTimeout(1000);// wait is required to load the muted video image
    await expect(this.videoDisabled, "verify video is disabled is visible").toBeVisible();
  }

  async navigateToDoubtsSection() {
    await slowExpect(this.doubtsButton, "Verify doubts button is visible").toBeVisible();
    await this.doubtsButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  async verifyDoubtsUI() {
    await slowExpect(this.openDoubtsTab, "Verify open doubts tab is visible").toBeVisible();
    await expect(this.resolvedDoubtsTab, "Verify resolved doubts tab is visible").toBeVisible();
    await expect(this.myDoubtsTab, "Verify my doubts tab is visible").toBeVisible();
  }

  async studentSendsDoubt(doubtMessage) {
    await expect(this.sendDoubtInput, "Verify send doubt input is visible").toBeVisible();
    await this.sendDoubtInput.fill(doubtMessage);
    await expect(this.postDoubtButton, "Verify post doubt button is visible").toBeVisible();
    await this.postDoubtButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  async upvoteDoubt(voteCount: number) {
    await expect(this.upvoteButton(`${voteCount}`)).toBeVisible();
    await this.upvoteButton(`${voteCount}`).click();
    voteCount += 1
    if (voteCount < 10) {
      await slowExpect(this.upvoteButton(`0${voteCount}`)).toBeVisible();
    }
    else {
      await slowExpect(this.upvoteButton(`${voteCount}`)).toBeVisible();
    }
  }

  async resolveDoubt(doubtMessage: string) {
    await expect(this.resolveButton, "Verify resolve Button is visible").toBeVisible();
    await this.resolveButton.click();
    await expect(this.noCancelOption, "Verify no cancel option is visible").toBeVisible();
    await this.noCancelOption.click();
    await expect(this.resolveButton, "Verify resolve Button is visible").toBeVisible();
    await this.resolveButton.click();
    await expect(this.yesResolveOption, "Verify yes approve option is visible").toBeVisible();
    await this.yesResolveOption.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.resolvedDoubtsTab, "Verify resolve doubts tab is visible").toBeVisible();
    await this.resolvedDoubtsTab.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.doubtsText(doubtMessage)).toBeVisible();
  }

  async myDoubts(doubtMessage: string) {
    await expect(this.myDoubtsTab, "Verify my Doubts tab is visible").toBeVisible();
    await this.myDoubtsTab.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.doubtsText(doubtMessage)).toBeVisible();
  }

  async verifyChatAndDoubtSeparation(chatMessage: string, doubtMessage: string) {
    await expect(this.chatTab, "Verify chat tab is visible").toBeVisible()
    await this.chatTab.click();
    await this.studentChatWithTeacher(chatMessage);
    await slowExpect(this.doubtsButton, "Verify doubts button is visible").toBeVisible();
    await this.doubtsButton.click();
    await expect(this.myDoubtsTab, "Verify my Doubts tab is visible").toBeVisible();
    await this.myDoubtsTab.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.doubtsText(chatMessage), "Verify chat message is not visible on doubts section").not.toBeVisible();
    await this.studentSendsDoubt(doubtMessage);
    await expect(this.chatTab, "Verify chat tab is visible").toBeVisible()
    await this.chatTab.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.chatMessage(doubtMessage), "Verify doubt message is not visible on chat section").not.toBeVisible();
  }

}
