import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { customExpect } from '../../fixtures/ui-fixtures';
const SubjectDetailsPageUrl = '/subject-details';

export class SubjectDetailsPage extends WebPage {

  readonly subjectText: (subjectName: string) => Locator;
  readonly subjectModules: Locator;
  readonly listOfSuperTopics: Locator;
  readonly listOfSubTopics: Locator;
  readonly atomicStructureText: Locator;
  readonly classesTitleText: Locator;
  readonly notesTitleText: Locator;
  readonly liveClassRecordingsSubTitleText: Locator;
  readonly preRecordedSubTitleText: Locator;
  readonly studyModuleSubTitleText: Locator;
  readonly practiceTitleText: Locator;
  readonly superTopicText: (superTopicName: string) => Locator;
  readonly subTopicText: (subTopicName: string) => Locator;
  readonly contentText: (contentName: string) => Locator;
  readonly subjectHeadingText: (subjectName: string) => Locator;
  readonly studyModuleText: Locator;
  readonly reviseTab: Locator;
  readonly revisionNotesStartButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, SubjectDetailsPageUrl, isMobile);
    this.subjectText = (subjectName: string) => page.getByText(`${subjectName}`, { exact: true });
    this.subjectHeadingText = (subjectName: string) => page.getByRole('heading', { name: `${subjectName}` });
    this.subjectModules = page.locator("//*[contains(text(),'modules')]");
    this.listOfSuperTopics = page.getByTestId('collapsibleTopicTitle');
    this.listOfSubTopics = page.getByTestId('chapterTitle');
    this.atomicStructureText = page.getByText('Atomic Structure');
    this.classesTitleText = page.getByText('Classes');
    this.notesTitleText = page.getByText('Notes');
    this.liveClassRecordingsSubTitleText = page.getByText('Live Class Recordings');
    this.preRecordedSubTitleText = page.getByText('Pre Recorded Videos');
    this.studyModuleSubTitleText = page.getByText('Study Module');
    this.practiceTitleText = page.getByText('Practice');
    this.superTopicText = (superTopicName: string) => page.getByText(`${superTopicName}`, { exact: true });
    this.subTopicText = (subTopicName: string) => page.locator(`//*[@data-testid='chapter']/descendant::*[text()='${subTopicName}']`)
    this.contentText = (contentName: string) => page.getByText(`${contentName}`, { exact: true });
    this.studyModuleText = page.getByText('Study Module');
    this.reviseTab = page.getByTestId('tabGroup').getByText('Revise');
    this.revisionNotesStartButton = page.getByText('Revision Notes').locator('xpath=../../following-sibling::div').getByTestId('dls-button');
  }

  async checkForSubjectModuleAndSupertopicsCount() {
    await slowExpect(this.subjectModules, "Verify subject modules text is visible").toBeVisible();
    let modulesFullText = await this.subjectModules.textContent();
    let moduleCount = modulesFullText!.split(' ')[0];
    expect(parseInt(moduleCount)).toBeGreaterThan(0); //under subject module number validation
    expect(await this.listOfSuperTopics.count()).toBeGreaterThan(0); //under subject supertopic count validation
  }

  async checkForSubjectSubtopicsCount() {
    if (await this.listOfSubTopics.first().isHidden()) {
      await this.listOfSuperTopics.first().click();
      await this.page.waitForLoadState('domcontentloaded');
    }
    await expect(this.listOfSuperTopics.first(), "verify the first super topic is visible").toBeVisible();
    expect(await this.listOfSubTopics.count(), "verify subtopics are present").toBeGreaterThan(0); //sub-topics of the super-topic count validation
  }

  async checkAndClickSubjectUnderContinueLearning(subjectName) {
    await slowExpect(this.subjectText(subjectName!).first(), "subject should be available under continue learning").toBeVisible();
    await this.subjectText(subjectName).first().click();
    await slowExpect(this.subjectText(subjectName).first(), "Verify subject text is visible").toBeVisible();
    await this.subjectText(subjectName).first().click();
  }

  async validateSubjectSuperTopicsDetails(superTopics) {
    await slowExpect(this.listOfSuperTopics.first(), 'verifying super topic is visible').toBeVisible();
    const count = superTopics.length;
    await expect(this.listOfSuperTopics).toHaveCount(count);
    await expect(this.listOfSuperTopics).toHaveText(superTopics);
  };

  async validateClassesNotesAndPracticeUnderSubTopic() {
    await slowExpect(this.classesTitleText, "verify classes title text is visible").toBeVisible();

    await expect(this.notesTitleText, "verify Notes title text is visible").toBeVisible();
    await expect(this.practiceTitleText, "verify Practice title text is visible").toBeVisible();
    // await expect(this.liveClassRecordingsSubTitleText, "verify live class recordings sub title text is visible").toBeVisible(); //For time being not validated live class recording videos title
    // await expect(this.preRecordedSubTitleText, "verify Pre Recorded Videos title text is visible").toBeVisible();
    await this.notesTitleText.click();
    await this.page.waitForLoadState('domcontentloaded'); // waiting to load the dom content
    //await expect(this.liveClassRecordingsSubTitleText, "verify live class recordings sub title text is not to be visible").not.toBeVisible();
    // await customExpect(15000)(this.studyModuleSubTitleText, "verify Study Module sub title text is visible").toBeVisible(); //For time being of API latency using 15 sces 
    await this.practiceTitleText.click();
    await this.page.waitForLoadState('domcontentloaded'); // waiting to load the dom content
    await expect(this.studyModuleSubTitleText, "verify Study Module sub title text is not to be visible").not.toBeVisible();
  };


  // async validateAssignedContentInSubject(subjectName: string, superTopicName: string, subTopicName: string, contentName: string, firstTopicName: string) {
  //   // Verify subject text is visible and click it
  //   await customExpect(15000)(this.subjectHeadingText(subjectName), "verify subject heading text is visible").toBeVisible();
  //   await this.subjectHeadingText(subjectName).click();
  //   // Verify super topic and sub topic are visible
  //   await customExpect(15000)(this.superTopicText(superTopicName), "verify super topic name is visible").toBeVisible();
  //   await expect(this.subTopicText(firstTopicName), "verify first sub topic name is visible").toBeVisible();
  //   await this.subTopicText(firstTopicName).click();
  //   await customExpect(15000)(this.subTopicText(subTopicName), "verify sub topic name is visible").toBeVisible();
  //   // Click on the sub topic
  //   await this.subTopicText(subTopicName).click();
  //   // Verify classes, practice, and notes titles are visible
  //   await customExpect(15000)(this.classesTitleText, "verify classes title is visible").toBeVisible();
  //   await expect(this.practiceTitleText, "verify practice title is visible").toBeVisible();
  //   await expect(this.notesTitleText, "verify notes title is visible").toBeVisible();

  //   // Click notes title
  //   await this.notesTitleText.click();

  //   // Verify sub topic and content are visible
  //   await expect(this.subTopicText(subTopicName), "verify sub topic name is visible").toBeVisible();
  // await expect(this.studyModuleText, "verify study module text is visible").toBeVisible();
  //   await customExpect(15000)(this.contentText(contentName), "verify content name is visible").toBeVisible();
  // }


  // async validateAssignedContentInSubjectLevel(subjectName: string, subTopicName: string, subTypeName: string, firstTopicName) {
  //   await customExpect(15000)(this.subjectHeadingText(subjectName), "verify subject heading text is visible").toBeVisible();
  //   await this.subjectHeadingText(subjectName).click();
  //   await expect(this.subTopicText(firstTopicName), "verify first sub topic name is visible").toBeVisible();
  //   await this.subTopicText(firstTopicName).click();
  //   await customExpect(15000)(this.subTopicText(subTopicName), "verify sub topic name is visible").toBeVisible();
  //   await this.subTopicText(subTopicName).click();

  //   await customExpect(15000)(this.classesTitleText, "verify classes title is visible").toBeVisible();
  //   await slowExpect(this.practiceTitleText, "verify practice title is visible").toBeVisible();
  //   await slowExpect(this.notesTitleText, "verify notes title is visible").toBeVisible();

  //   await this.page.reload();
  //   await this.page.waitForLoadState('networkidle');
  //   await customExpect(15000)(this.contentText(subTypeName), "verify sub type name is visible").toBeVisible();
  // }

  async validateOpenSubjectLevelContent(subTypeName: string) {
    await slowExpect(this.contentText(subTypeName), "verify sub type name is visible").toBeVisible();
    await this.contentText(subTypeName).click();
  }


  async validateOpenUploadedContent(contentName) {
    await expect(this.studyModuleText, "verify study module text is visible").toBeVisible();
    await slowExpect(this.contentText(contentName), "verify content name is visible").toBeVisible();
    await this.contentText(contentName).click();
  }

  async navigateToRevisionNotes() {
    await this.reviseTab.click();
    await expect(this.revisionNotesStartButton, "Verify revision notes start button is visible").toBeVisible();
    await this.revisionNotesStartButton.click();
  }
}
