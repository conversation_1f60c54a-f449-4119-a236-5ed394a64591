import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities'

const TeacherLiveClassUrl = '/liveclass'
let name: string

export class TeacherLiveClassPage extends ICPage {

  readonly leaveClassButton: Locator;
  readonly classNameOnLiveClass: (className: string) => Locator;
  readonly chatDisabledText: Locator;
  readonly enableChatButton: Locator;
  readonly chatButton: Locator;
  readonly videoButton: Locator;
  readonly studentYetToJoinText: Locator;
  readonly pollsButton: Locator;
  readonly micButton: Locator;
  readonly materialsButton: Locator;
  readonly selectPollOptions: Locator;
  readonly startPollTitle: Locator;
  readonly noLimitButton: Locator;
  readonly startPollButton: Locator;
  readonly answeredPoll: (selectPoll: string) => Locator;
  readonly endPoll: Locator;
  readonly teacherOnlyOption: Locator;
  readonly dropDownTeacherOnlyOption: Locator;
  readonly studentCanChatWithText: Locator;
  readonly everyOneOption: Locator;
  readonly typeYourMessageHereFeild: Locator;
  readonly sendButton: Locator;
  readonly chatMessage: (message: string) => Locator;
  readonly moveToQueryOption: (message: string) => Locator;
  readonly queriesTab: Locator;
  readonly likeIcon: Locator;
  readonly resolvedQueryIcon: Locator;
  readonly chatTab: Locator;
  readonly studentName: (studentName: string) => Locator;
  readonly blockChatOption: Locator;
  readonly unBlockChatOption: Locator;
  readonly bringOnStageOption: Locator;
  readonly bringAllOffStageButton: Locator;
  readonly bringOnStageButton: Locator;
  readonly studentNameChat: (studentName: string) => Locator;
  readonly lowerAllHandsButton: Locator;
  readonly attendanceText: Locator;
  readonly layoutButton: Locator;
  readonly changeLayoutText: Locator;
  readonly gridViewOption: Locator;
  readonly closeIconChangeLayout: Locator;
  readonly studentNameGridView: (studentName: string) => Locator;
  readonly studentCount: Locator;
  readonly endClassText: Locator;
  readonly classEndHeaderText: Locator;
  readonly cancelButton: Locator;
  readonly endClassButton: Locator;
  readonly chatContainer: Locator;
  readonly studentCard: Locator;
  readonly bringOnStageButtonGridView: (studentName: string) => Locator;
  readonly noOneOptionselect: Locator;
  readonly noOneOption: Locator;
  readonly invalidChatDisabledPopUp: Locator;
  readonly closeErrorPopUp: Locator;
  readonly selectFivePollOptions: Locator;
  readonly closePollPopUp: Locator;
  readonly searchForParticipants: Locator;
  readonly searchContainer: Locator;
  readonly studentNameBringOnStage: (studentName: string) => Locator;
  readonly searchBringOnStage: (studentName: string) => Locator;
  readonly searchLowerHand: (studentName: string) => Locator;
  readonly attendanceViewOption: Locator;
  readonly regularStudentsButton: Locator;
  readonly irregularStudentsButton: Locator;
  readonly allStudentsButton: Locator;
  readonly nameHeader: Locator;
  readonly timeSpentInClassHeader: Locator;
  readonly studentSentThumsupEmoji: Locator;
  readonly studentSentUrlLink: (studentSentUrl: string) => Locator;
  readonly micMuted: Locator;
  readonly videoMuted: Locator;
  readonly veryWeekSignal: Locator;
  readonly conceptButton: Locator;

  readonly fivePollsOption: Locator;
  readonly thirtySecPollOption: Locator;
  readonly oneMinPollOption: Locator;
  readonly endPollButton: Locator;
  readonly bringOnStageVideoOn: Locator;
  readonly blockChatIcon: Locator;
  readonly studentResponsesButton: Locator;
  readonly studentResponsesTitleText: Locator;
  readonly respondedTab: Locator;
  readonly notRespondedTab: Locator;
  readonly respondedAnwsered: (selectPoll: string) => Locator;
  readonly respondedAnwseredStudent: (studentName: string, selectPoll: string) => Locator;
  readonly responsesCloseIcon: Locator;
  readonly respondedBOS: (studentName: string, selectPoll: string) => Locator;
  readonly studentNameNotResponded: (studentName: string) => Locator;
  readonly studentNameNotRespondedHover: (studentName: string) => Locator;
  readonly respondedAnwseredStudentHover: (studentName: string, selectPoll: string) => Locator;
  readonly studentNameNotRespondedBOS: (studentName: string) => Locator;

  readonly studentButton: Locator;
  readonly studentListTitleText: Locator;
  readonly searchForParticipantsStudentList: Locator;
  readonly raisedHandStudentListTab: Locator;
  readonly bosStudentListTab: Locator;
  readonly studentNameStudentList: (studentName: string) => Locator;
  readonly studentNameStudentListBos: (studentName: string) => Locator;
  readonly studentNameStudentListRaisedHand: (studentName: string) => Locator;
  readonly raisedHandCloseIcon: Locator;
  readonly bosCloseIcon: Locator;
  readonly studentNameStudentListBOffStage: (studentName: string) => Locator;
  readonly studentNameListOffLine: (studentName: string) => Locator;

  readonly shareScreenOption: Locator;
  readonly togglePresenterAndGridViewInput: Locator;
  readonly stopSharingText: Locator;
  readonly gridViewInputRadio: Locator;
  readonly teacherOnTheWayText: Locator;
  readonly doubtsTab: Locator;
  readonly enableDoubtButton: Locator;
  readonly disableDoubts: Locator;
  readonly disableDoubtsText: Locator;
  readonly openDoubtsTab: Locator;
  readonly resolvedDoubtsTab: Locator;
  readonly refreshButton: Locator;
  readonly doubtsText: (doubtsMessage: string) => Locator;
  readonly upvoteButton: (voteCount: string) => Locator;
  readonly resolveButton: Locator;
  readonly actionsButton: Locator;
  readonly blockStudent: Locator;
  readonly unblockStudent: Locator;
  readonly pinIcon: (studentName: string) => Locator;
  readonly pinButton: Locator;
  readonly hamburgerIcon: Locator;
  readonly deleteDoubt: Locator;
  readonly noCancelButton: Locator;
  readonly yesDeleteButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, TeacherLiveClassUrl, isMobile);
    this.leaveClassButton = page.getByText('Leave class');
    this.classNameOnLiveClass = (className: string) => page.getByText(`${className}`).first();
    this.chatDisabledText = page.getByText('Chat is disabled');
    this.chatButton = page.locator('div').filter({ hasText: /^Chat$/ }).first();;
    this.videoButton = page.getByText('Video', { exact: true });
    this.studentYetToJoinText = page.getByText('Students are yet to join...');
    this.pollsButton = page.getByText('Polls');
    this.micButton = page.getByTestId('Mic').getByRole('img');
    this.materialsButton = page.getByText('Materials');
    this.selectPollOptions = page.getByText('4 Options');
    this.startPollTitle = page.getByRole('button', { name: 'Start Poll' });
    this.noLimitButton = page.getByRole('button', { name: 'No Limit' })
    this.startPollButton = page.getByRole('button', { name: 'Start Poll' });
    this.answeredPoll = (selectPoll: string) => page.getByText(`${selectPoll}1`)
    this.endPoll = page.getByRole('button', { name: 'End Poll' });
    this.teacherOnlyOption = page.getByText('Teacher only');
    this.dropDownTeacherOnlyOption = page.getByTestId('chat-option-dropdown').getByText('Teacher only');
    this.studentCanChatWithText = page.getByText('Students can chat with:');
    this.everyOneOption = page.getByText('Everyone');
    this.typeYourMessageHereFeild = page.getByPlaceholder('Type your message here');
    this.sendButton = page.getByRole('img', { name: 'send' });
    this.moveToQueryOption = (message: string) => page.locator(`//*[text()='${message}']//*[text()='Move to Query']`)
    this.chatMessage = (message: string) => page.getByText(`${message}`).first();
    this.queriesTab = page.getByText('Queries');
    this.likeIcon = page.getByRole('img', { name: 'like button' });
    this.resolvedQueryIcon = page.getByRole('img', { name: 'resolve doubt' });
    this.chatTab = page.getByRole('list').getByTestId('Chat');
    this.studentName = (studentName: string) => page.getByText(`${studentName}`).first();
    this.blockChatOption = page.getByText('Block Chat');
    this.unBlockChatOption = page.getByText('Unblock Chat');
    this.bringOnStageOption = page.getByRole('button', { name: 'Bring to Stage' });
    this.enableChatButton = page.locator('//div[text()="Enable Chat"]');
    this.bringAllOffStageButton = page.getByRole('button', { name: 'Bring all off stage' });
    this.bringOnStageButton = page.getByRole('button', { name: 'Bring to Stage' });
    this.chatContainer = page.locator('.chat_container');
    this.studentNameChat = (studentName: string) => this.chatContainer.locator(`//*[text()='${studentName}']`);
    this.lowerAllHandsButton = page.locator('//*[contains(text(),"Lower all hands")]');
    this.attendanceText = page.getByText('Attendance', { exact: true });
    this.layoutButton = page.getByText('Layout', { exact: true });
    this.changeLayoutText = page.getByText('Change Layout');
    this.gridViewOption = page.getByLabel('Grid View', { exact: true });
    this.closeIconChangeLayout = page.locator('(//*[text()=" Change Layout "]/ancestor::*[contains(@class,"MuiTooltip-popper") or @class="relative"]/descendant::*[@class="cursor-pointer"])[1]');
    this.studentCard = page.locator('//*[contains(@class,"group/card")]');
    this.studentNameGridView = (studentName: string) => this.studentCard.locator(`//*[text()='${studentName}']`);
    this.studentCount = page.locator('//*[@class="text-tertiary text-sm font-normal leading-relaxed whitespace-nowrap"]');
    this.endClassText = page.getByText('Apply and End Class');
    this.classEndHeaderText = page.getByText('Log what was taught today');
    this.cancelButton = page.getByRole('img', { name: 'close-circle' });
    this.endClassButton = page.getByRole('button', { name: 'Apply and End Clas' });
    this.bringOnStageButtonGridView = (studentName: string) => page.locator(`//*[contains(@data-testid,"bring-on-stage-audience-card-${studentName}")]`)
    this.noOneOptionselect = page.getByTestId('No one (chat disabled)');
    this.noOneOption = page.getByText('No one');
    this.invalidChatDisabledPopUp = page.getByText('Invalid chat state disabled');
    this.closeErrorPopUp = page.getByText('Invalid chat state disabled');
    this.selectFivePollOptions = page.getByText('5 Options');
    this.closePollPopUp = page.locator('//*[contains(@class,"cursor-pointer rounded-full border")]');
    this.searchForParticipants = page.getByPlaceholder('Search for participants');
    this.searchContainer = page.locator(`//*[contains(@data-testid,"container")]`);
    this.studentNameBringOnStage = (studentName: string) => this.searchContainer.locator(`//*[text()='${studentName}']`);
    this.searchBringOnStage = (studentName: string) => page.locator(`(//*[contains(@data-testid,"container")]//*[contains(text(),'${studentName}')]//ancestor::*//*[contains(@data-testid,"bring-on-stage-")])[1]`);
    this.searchLowerHand = (studentName: string) => page.locator(`(//*[contains(@data-testid,"container")]//*[contains(text(),'${studentName}')]//ancestor::*//*[contains(@data-testid,"lower-hand-search-grid")])[1]`);

    this.attendanceViewOption = page.getByLabel('Attendance View', { exact: true });
    this.regularStudentsButton = page.getByText('Regular Students', { exact: true });
    this.irregularStudentsButton = page.getByText('Irregular Students');
    this.allStudentsButton = page.getByText('All Students');
    this.nameHeader = page.getByText('Name');
    this.timeSpentInClassHeader = page.getByText('Time spent in class');
    this.studentSentThumsupEmoji = page.getByText('👍').first();
    this.studentSentUrlLink = (studentSentUrl: string) => page.getByText(`${studentSentUrl}`).first();
    this.micMuted = page.locator("//div[@data-testid='Mic']/*[contains(@src,'media/mute')]");
    this.videoMuted = page.locator("//*[@data-testid='Video']/*[contains(@src,'novideo')]");
    this.veryWeekSignal = page.getByText('Very weak');
    this.conceptButton = page.getByRole('button', { name: 'Concept' });
    this.fivePollsOption = page.getByText('5 Options');
    this.thirtySecPollOption = page.getByRole('button', { name: '30 Sec' })
    this.oneMinPollOption = page.getByRole('button', { name: '1 Min' })
    this.endPollButton = page.getByRole('button', { name: 'End Poll' });
    this.bringOnStageVideoOn = page.locator('//*[contains(@class,"remoteVideos_remote-block")]');
    this.blockChatIcon = page.locator('//*[@alt="block-chat"]').first();

    this.studentResponsesButton = page.getByTestId('poll-student-responses');
    this.studentResponsesTitleText = page.getByText('Student Responses');
    this.respondedTab = page.getByRole('button', { name: 'Responded', exact: true });
    this.notRespondedTab = page.getByRole('button', { name: 'Not Responded' });
    this.respondedAnwsered = (selectPoll: string) => page.locator(`//*[text()='${selectPoll}']`);
    this.respondedAnwseredStudent = (studentName: string, selectPoll: string) => page.locator(`//*[@aria-label='${selectPoll}']/ancestor::*[@class='flex flex-col flex-1 h-[calc(100vh-174px)] gap-4 px-4 pt-4 overflow-auto']/descendant::*[text()='${studentName}']`);
    this.respondedAnwseredStudentHover = (studentName: string, selectPoll: string) => page.locator(`//*[@aria-label='${selectPoll}']/ancestor::*[@class='flex flex-col flex-1 h-[calc(100vh-174px)] gap-4 px-4 pt-4 overflow-auto']/descendant::*[text()='${studentName}']/..`);
    this.responsesCloseIcon = page.locator('//*[contains(@class,"cursor-pointer flex rounded-full border")]');
    this.respondedBOS = (studentName: string, selectPoll: string) => page.locator(`//*[@aria-label='${selectPoll}']/ancestor::*[@class='flex flex-col flex-1 h-[calc(100vh-174px)] gap-4 px-4 pt-4 overflow-auto']/descendant::*[text()='${studentName}']//following-sibling::*`);
    this.studentNameNotResponded = (studentName: string) => page.getByLabel(`${studentName}`);
    this.studentNameNotRespondedHover = (studentName: string) => page.locator(`//*[text()='${studentName}']/..`);
    this.studentNameNotRespondedBOS = (studentName: string) => page.locator(`//*[text()='${studentName}']/..//following-sibling::button`);


    this.studentButton = page.getByText('Students', { exact: true });
    this.studentListTitleText = page.getByRole('heading', { name: 'Student List', level: 2 });
    this.searchForParticipantsStudentList = page.getByPlaceholder('Search for participants').nth(1);
    this.raisedHandStudentListTab = page.locator(`//*[@class='text-sm text-tertiary truncate'][text()='Raised Hand']`);

    this.bosStudentListTab = page.locator(`//*[@class='text-sm text-tertiary truncate'][text()='In Stage']`);
    this.studentNameStudentList = (studentName: string) => page.locator(`.text-secondary:has-text("${studentName}")`).first();
    this.studentNameStudentListBos = (studentName: string) => page.locator(`//*[contains(@class, 'text-secondary truncate')][text()='${studentName}']/../../..//following-sibling::button`);
    this.studentNameStudentListRaisedHand = (studentName: string) => page.locator(`//*[@class="text-secondary truncate"][text()='${studentName}']/../..//following-sibling::*//*[@class='cursor-pointer shrink-0']`);
    this.bosCloseIcon = page.locator(`//*[text()="In Stage"]//following-sibling::*[@class='flex items-center justify center cursor-pointer shrink-0']`);
    this.raisedHandCloseIcon = page.locator(`//*[text()="Raised Hand"]//following-sibling::*[@class="flex items-center justify-center cursor-pointer shrink-0"]`);
    this.studentNameStudentListBOffStage = (studentName: string) => page.locator(`xpath=//*[contains(@class, "text-secondary") and contains(., "${studentName}")]/ancestor::*[5]/following-sibling::*`);
    this.studentNameListOffLine = (studentName: string) => page.locator(`//*[text()='${studentName}']/..//following-sibling::*//*[text()='Student is offline']`);

    this.togglePresenterAndGridViewInput = page.locator("//label[@class='relative inline-block w-8 h-5 shrink-0']");
    this.shareScreenOption = page.getByTestId("ScreenSharing");
    this.stopSharingText = page.getByText("Stop Sharing");
    this.gridViewInputRadio = page.getByRole("radio").getByLabel("Grid View");
    this.teacherOnTheWayText = page.getByText('Your teacher is on the way to');
    this.doubtsTab = page.getByRole('list').getByTestId('Doubts');
    this.enableDoubtButton = page.getByTestId('enable-doubt');
    this.disableDoubtsText = page.getByText('Disable doubts');
    this.disableDoubts = this.disableDoubtsText.locator('xpath=following-sibling::*[1]');
    this.openDoubtsTab = page.getByText('Open');
    this.resolvedDoubtsTab = page.getByText('Resolved')
    this.refreshButton = page.locator('div').filter({ hasText: /^OpenResolved$/ }).first().locator('img');
    this.doubtsText = (doubtsMessage: string) => page.getByTestId('doubt-text').getByText(`${doubtsMessage}`);
    this.upvoteButton = (voteCount: string) => page.locator(`div span:text-is("${voteCount}")`);
    this.resolveButton = page.getByTestId('doubt-card').getByText('Resolve');
    this.actionsButton = page.getByTestId('doubt-card').getByRole('img').first();
    this.blockStudent = page.getByText('Block Student');
    this.unblockStudent = page.getByText('Unblock Student');
    this.pinIcon = (studentName: string) => page.getByText(studentName).locator('xpath=preceding-sibling::*[1]');
    this.pinButton = page.locator('#zoomSelector').getByRole('button');
    this.hamburgerIcon = page.getByTestId('doubt-card').getByRole('img').first();
    this.deleteDoubt = page.getByText('Delete Doubt');
    this.noCancelButton = page.getByRole('button', { name: 'No, Cancel' });
    this.yesDeleteButton = page.getByRole('button', { name: 'Yes, Delete' });
  }
  /*validating Teacher Live class page */
  async validateTeacherLiveClassPage(className) {
    await slowExpect(this.leaveClassButton, "Verify leave class button is visible").toBeVisible();
    await expect(this.classNameOnLiveClass(className), "Verify Scheduled className on Teacher live class page is visible").toBeVisible();
    // await expect(this.chatDisabledText, "Verify Chat is Disabled text is visible").toBeVisible();
    // await expect(this.chatButton, "Verify chat button is visible").toBeVisible();
    if (await this.conceptButton.isVisible()) {
      await this.conceptButton.click();
    }
    // await expect(this.studentYetToJoinText, "Verify student yet to join text is visible").toBeVisible();
    await expect(this.videoButton, "Verify video button is visible").toBeVisible();
    await expect(this.pollsButton, "Verify polls button is visible").toBeVisible();
    await expect(this.micButton, "Verify mic button is visible").toBeVisible();
    await expect(this.materialsButton, "Verify materials button is visible").toBeVisible();
  }

  /*create poll with in the shedcule live class  */
  async getStudentCount() {
    const countStudent = await this.studentCount.textContent();
    let count = countStudent?.split('/')[0];
    return count;
  }

  async createPollsUsingTimeOption(pollType) {
    // Verify the start poll title is visible
    await expect(this.startPollTitle, "Verify start poll title is visible").toBeVisible();
    // try {
    //   await expect(this.veryWeekSignal, "Verify very weak signal is visible").toBeVisible();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }
    // await this.page.waitForTimeout(2000); //commented for testing by removing waits
    switch (pollType) {
      case 'noLimits':
        await expect(this.noLimitButton, "Verify no limit poll option is visible").toBeVisible();
        await this.noLimitButton.click();
        break;
      case 'thirtySec':
        await expect(this.thirtySecPollOption, "Verify 30 sec poll option is visible").toBeVisible();
        await this.thirtySecPollOption.click();
        break;
      case 'oneMin':
        await expect(this.oneMinPollOption, "Verify one min poll option is visible").toBeVisible();
        await this.oneMinPollOption.click();
        break;
      default:
        throw new Error(`Invalid poll type: ${pollType} `);
    }

    // Start the poll
    await expect(this.startPollButton, "Verify start poll button is visible").toBeVisible();
    await expect(this.startPollButton, "Verify start poll button is enabled").toBeEnabled();
    await this.startPollButton.click();
    await this.page.waitForTimeout(1000); // required for websocket
    await expect(this.endPollButton, "Verify end poll button is visible").toBeVisible();
  }

  async verifyTheAnsweredPollAndEndPoll(selectPoll) {
    await this.page.waitForTimeout(1000); // required this
    await expect(this.endPoll, "Verify stop poll button is visible").toBeEnabled();
    await customExpect(15000)(this.answeredPoll(selectPoll), "Verify answered poll count is visible").toBeVisible();
    await expect(this.endPoll, "Verify close poll Icon is visible").toBeVisible();
    await this.endPoll.click();
    // await this.page.waitForTimeout(1000); // required for websocket
    await customExpect(10000)(this.studentResponsesButton, "Verify student responses button is visible").toBeVisible();
    await expect(this.closePollPopUp, "Verify close poll Icon is visible").toBeVisible();
    // await this.closePollPopUp.click();
    // await expect(this.closePollPopUp, "Verify close poll Icon is not visible").toBeHidden();
  }

  /*Enable chat and select Everyone option in Teacher live class  */
  async clickEnableChatAndSelectEveryoneOption() {
    await expect(this.chatDisabledText, "Verify chat disable text is visible").toBeVisible();
    await expect(this.enableChatButton, "Verify enable chat button is visible").toBeVisible();
    await this.enableChatButton.click();
    await expect(this.studentCanChatWithText, "Verify student can chat with text is visible").toBeVisible();
    await expect(this.teacherOnlyOption, "Verify teacherOnly option is visible").toBeVisible();
    await this.teacherOnlyOption.click();
    await expect(this.everyOneOption, "Verify everyone option is visible").toBeVisible();
    await expect(this.noOneOption, "Verify no one option is visible").toBeVisible();
    await expect(this.dropDownTeacherOnlyOption, "Verify dropdown teacherOnly option is visible").toBeVisible();
    await this.everyOneOption.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
  }

  /* validate chat send from Teacher live class */
  async teacherChatWithStudent(message) {
    await expect(this.typeYourMessageHereFeild, "Verify type your message field is visible.").toBeVisible();
    await this.typeYourMessageHereFeild.fill(message);
    await expect(this.sendButton, "Verify send button is visible").toBeVisible();
    await this.sendButton.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
  }
/*
  // chat is moved to query from Teacher live class 
  async clickOnChatMoveToQuery(message) {
    await expect(this.chatMessage(message), "Verify student chat is visible").toBeVisible();
    await this.chatMessage(message).hover();
    await expect(this.moveToQueryOption(message), "Verify move to query from Teacher is visible").toBeVisible();
    await this.moveToQueryOption(message).click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
  }

  // Verify chat is moved to query from Teacher live class 
  async verifyChatIsMovedToQuery(message) {
    await expect(this.queriesTab, "Verify queries tab is visible").toBeVisible();
    await this.queriesTab.click();
    await expect(this.chatMessage(message), "Verify move to query from Teacher is visible").toBeVisible();
  }

  // Like and resolove the query from Teacher live class 
  async clickOnLikeAndResolveQueryInQueryTab(message) {
    await expect(this.likeIcon, "Verify like Icon is visible").toBeVisible();
    await this.likeIcon.click();
    await expect(this.resolvedQueryIcon, "Verify resolve query Icon from Teacher is visible").toBeVisible();
    await this.resolvedQueryIcon.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    // need to change assertion
    await expect(this.chatMessage(message), "Verify chat message from Teacher is visible").toBeHidden();
    await expect(this.chatTab, "Verify resolve query Icon from Teacher is visible").toBeVisible();
    await this.chatTab.click();
  }

  /* Block the chat from Teacher live class */
  async clickOnBlockChatFromTeacher(studentName) {
    await expect(this.studentNameChat(studentName).nth(1), "Verify student Name is visible").toBeVisible();
    await this.studentNameChat(studentName).nth(1).click();
    await expect(this.blockChatOption, "Verify block chat option from Teacher is visible").toBeVisible();
    await this.blockChatOption.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
  }
  /* Block the chat from Teacher live class */
  async verifyBlockByTeacher(studentName) {
    await customExpect(15000)(this.studentNameChat(studentName).nth(1), "Verify student Name is visible").toBeVisible();
    await this.studentNameChat(studentName).nth(1).hover({ force: true });
    await customExpect(15000)(this.unBlockChatOption, "Verify unblock chat option from Teacher is visible").toBeVisible();
  }

  /* unBlock the chat from Teacher live class */
  async clickOnUnBlockChatFromTeacher(studentName) {
    await expect(this.studentNameChat(studentName).nth(1), "Verify student Name is visible").toBeVisible();
    await this.studentNameChat(studentName).nth(1).hover({ force: true });
    await expect(this.unBlockChatOption, "Verify unblock chat option from Teacher is visible").toBeVisible();
    await this.unBlockChatOption.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await expect(this.studentNameChat(studentName).nth(1), "Verify student Name is visible").toBeVisible();
    await this.studentNameChat(studentName).nth(1).hover({ force: true });
    await expect(this.blockChatOption, "Verify block chat option from Teacher is visible").toBeVisible();
  }

  /* bring on stage from chat from Teacher live class */
  async clickOnBringOnStageChatFromTeacher(studentName) {
    await this.page.waitForTimeout(1000); // to wait for websocket
    await expect(this.chatTab, "Verify student Name is visible").toBeVisible();
    await this.chatTab.click();
    await this.page.waitForTimeout(1000);// to wait for websocket
    await expect(this.studentNameChat(studentName).nth(1), "Verify student Name is visible").toBeVisible();
    await this.studentNameChat(studentName).nth(1).click();
    await expect(this.bringOnStageOption, "Verify bring on stage by chat from Teacher is visible").toBeVisible();

    await this.bringOnStageOption.click();
    await this.page.waitForTimeout(1000); // to wait for websocket 
    // try {
    //   await expect(this.veryWeekSignal, "Verify very weak signal is visible").toBeVisible();
    //   await this.bringOnStageOption.click();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }
    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all of stage button is visible").toBeVisible();
  }

  /* bring off stage from chat from Teacher live class */
  async verifyBringOffStageFromTeacher() {
    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all of stage button is visible").toBeVisible();
    await expect(this.bringAllOffStageButton, "Verify bring all of stage button is visible").toBeEnabled();
    // await expect(this.veryWeekSignal, "Verify very week signal is not visible").not.toBeVisible();
    await this.bringAllOffStageButton.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    // try {
    //   await expect(this.veryWeekSignal, "Verify very weak signal is visible").toBeVisible();
    //   await this.bringOnStageButtonGridView(name).click();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }
    try {
      await expect(this.bringAllOffStageButton, "Verify bring all off stage is visible").toBeVisible();
      await this.bringAllOffStageButton.click();
    }
    catch {
      console.log("bring all off stage button is not present");
    }
    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all of stage button is hidden").toBeHidden();
  }


  /* bring off stage from chat from Teacher live class */
  async verifyBringOnStage() {
    await slowExpect(this.bringOnStageVideoOn, "Verify bring on stage video is visible").toBeVisible();
    // try {
    //   await expect(this.veryWeekSignal, "Verify very weak signal is visible").toBeVisible();
    //   await this.bringOnStageButtonGridView(name).click();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }
  }

  /* bring on stage from Teacher live class */
  async verifyBringOnStageFromTeacher(studentName) {
    await expect(this.studentNameChat(studentName), "Verify student Name is visible").toBeVisible();
    await this.studentNameChat(studentName).hover();
    await expect(this.studentNameChat(studentName), "Verify bring on stage by chat from Teacher is visible").toBeVisible();
    await this.bringOnStageButton.click();
  }

  /* Verify and click Lower Hand button from Teacher live class */
  async clickOnLowerHandButton() {
    await slowExpect(this.chatDisabledText, "Verify Chat is Disabled text is visible").toBeVisible();
    if (await this.conceptButton.isVisible()) {
      if (await this.lowerAllHandsButton.isVisible()) {
        console.log("Its already in concept tab")
      }
      else {
        await this.conceptButton.click();
      }
    }
    await expect(this.lowerAllHandsButton, "Verify lower hands button is visible").toBeVisible();
    await this.lowerAllHandsButton.click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
  }

  /* Verify and click grid view  option in live class  */
  async verifyAndClickGridViewOption() {
    await expect(this.attendanceText, "Verify attendance text is visible").toBeVisible();
    await expect(this.layoutButton, "Verify layout button is visible").toBeVisible();
    await this.layoutButton.click();
    await expect(this.changeLayoutText, "Verify change layout text is visible").toBeVisible();
    await expect(this.gridViewOption, "Verify grid view option is visible").toBeVisible();
    await this.gridViewOption.click();
    await expect(this.closeIconChangeLayout, "Verify close Icon is visible").toBeVisible();
    await this.closeIconChangeLayout.click();
  }
  async verifyAndClickGridViewOptionAfterScreenShare() {
    await expect(this.attendanceText, "Verify attendance text is visible").toBeVisible();
    await expect(this.layoutButton, "Verify layout button is visible").toBeVisible();
    await expect(this.changeLayoutText, "Verify change layout text is visible").toBeVisible();
    await expect(this.gridViewOption, "Verify grid view option is visible").toBeVisible();
    await this.gridViewOption.check();
    await expect(this.closeIconChangeLayout, "Verify close Icon is visible").toBeVisible();
    await this.closeIconChangeLayout.click();
  }

  /*validating Teacher Live class page after student joins the class */
  async validateTeacherLiveClassPageAfterStudentJoins(studentName) {
    await customExpect(20000)(this.studentYetToJoinText, "Verify student yet to join text is hidden").toBeHidden();
    if (await this.conceptButton.isVisible()) {
      await this.conceptButton.click();
    }
    await expect(this.studentNameGridView(studentName), "Verify student name is visible on Gride view").toBeVisible();
  }

  /*validating Teacher Live class page after student leaves the class */
  async validateTeacherLiveClassPageAfterStudentLeavesClass(studentName) {
    await expect(this.studentNameGridView(studentName), "Verify student name is not visible on Gride view").toBeHidden();
  }


  /*click and Verify Teacher end class in teacher live class page */
  async clickAndValidateTeacherEndClass() {
    await expect(this.leaveClassButton, "Verify leave class button visible in Teacher live class page").toBeVisible();
    await this.leaveClassButton.click();
    await expect(this.endClassText, "Verify end class Text visible in Teacher live class page").toBeVisible();
    await expect(this.classEndHeaderText, "Verify Log what was taught today text is visible in Teacher live class page").toBeVisible();
    await expect(this.cancelButton, "Verify cancel button is visible in teacher end class pop up").toBeVisible();
    await this.page.waitForTimeout(2000); //This is required as due to button to load to get enable
    await expect(this.endClassButton, "Verify apply and end class button is visible in teacher end class pop up").toBeVisible();
    await this.endClassButton.click();
  }


  /*click and validate bring on stage Grid view on Teacher Live class page */
  async clickOnBringOnStage(studentName) {
    await slowExpect(this.chatDisabledText, "Verify Chat is Disabled text is visible").toBeVisible();
    try {
      await slowExpect(this.conceptButton, "Verify concept tab is visible").toBeVisible();
      await this.conceptButton.click();
    }
    catch {
      console.log("Concet tab is not present");
    }
    await expect(this.studentNameGridView(studentName), "Verify student name is visible on Gride view").toBeVisible();
    await this.studentNameGridView(studentName).hover();
    let nameStudent: string = studentName;
    nameStudent = nameStudent.toLocaleLowerCase();
    const name = nameStudent?.split(' ')[0];
    await expect(this.bringOnStageButtonGridView(name), "Verify bring on stage is visible on Gride view").toBeVisible();
    // await expect(this.veryWeekSignal, "Verify very week signal is visible").not.toBeVisible();
    await this.bringOnStageButtonGridView(name).click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    // try {
    //   await expect(this.veryWeekSignal, "Verify very weak signal is visible").toBeVisible();
    //   await this.bringOnStageButtonGridView(name).click();
    // }
    // catch {
    //   console.log("very weak signal is not present");
    // }

    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all off stage is visible").toBeVisible();
  }

  /*Click eyeryone option in Teacher live class  */
  async clickOnEveryOneOption() {
    await expect(this.everyOneOption, "Verify evreyone option is visible").toBeVisible();
    await this.everyOneOption.click();
  }

  /*Click teacher only option in Teacher live class  */
  async clickOnTeacherOnlyOption() {
    await expect(this.teacherOnlyOption, "Verify teacher Only option is visible").toBeVisible();
    await this.teacherOnlyOption.click();
  }

  /*Click No One option in Teacher live class  */
  async clickOnNoOneOption() {
    await expect(this.noOneOptionselect, "Verify teacher Only option is visible").toBeVisible();
    await this.noOneOptionselect.click();
  }

  /*Verify select teacher only option in Teacher live class  */
  async clickOnTeacherOnlyAndVerify() {
    await this.clickOnEveryOneOption();
    await this.clickOnTeacherOnlyOption();
    await expect(this.teacherOnlyOption, "Verify teacher Only option is visible").toBeVisible();
  }

  /*validate chat from student from Teacher live class  */
  async verifyChatFromStudentToTeacher(message) {
    await expect(this.typeYourMessageHereFeild, "Verify type your message here feild is visible").toBeVisible();
    await expect(this.chatMessage(message), "Verify chat message from Teacher is visible").toBeVisible();
  }

  /*Verify select no oneoption in Teacher live class  */
  async clickOnNoOneOptionAndVerify() {
    await this.clickOnTeacherOnlyOption();
    await this.clickOnNoOneOption();
  }

  /*close error pop up from Teacher live class  */
  async verifyErrorAndclickOnClosePopUp() {
    await expect(this.closeErrorPopUp, "Verify close pop up icon is visible").toBeVisible();
    await this.closeErrorPopUp.click();
  }

  /* Verify and click attendance view  option in live class  */
  async clickOnAttendanceViewOption() {
    await slowExpect(this.chatDisabledText, "Verify Chat is Disabled text is visible").toBeVisible();
    try {
      await slowExpect(this.conceptButton, "Verify concept tab is visible").toBeVisible();
      await this.conceptButton.click();
    }
    catch {
      console.log("concept tab is not present");
    }
    await expect(this.studentYetToJoinText, "Verify student yet to join text is visible").toBeVisible();
    await expect(this.layoutButton, "Verify layout button is visible").toBeVisible();
    await this.layoutButton.click();
    await expect(this.changeLayoutText, "Verify change layout text is visible").toBeVisible();
    await expect(this.gridViewOption, "Verify grid view option is visible").toBeVisible();
    await expect(this.attendanceViewOption, "Verify attendance view option is visible").toBeVisible();
    await this.attendanceViewOption.click();
    await expect(this.closeIconChangeLayout, "Verify close Icon is visible").toBeVisible();
    await this.closeIconChangeLayout.click();
  }

  async clickOnAttendanceViewOptionAfterScreenShare() {
    await slowExpect(this.chatDisabledText, "Verify Chat is Disabled text is visible").toBeVisible();
    await expect(this.layoutButton, "Verify layout button is visible").toBeVisible();
    await this.layoutButton.click();
    await expect(this.changeLayoutText, "Verify change layout text is visible").toBeVisible();
    await expect(this.gridViewOption, "Verify grid view option is visible").toBeVisible();
    await expect(this.attendanceViewOption, "Verify attendance view option is visible").toBeVisible();
    await this.attendanceViewOption.click();
  }

  /* Verify attendance view page  */
  async veriyAttendanceViewPage() {
    await expect(this.regularStudentsButton, "Verify regular student button is visible").toBeVisible();
    await expect(this.irregularStudentsButton, "Verify irregular student button is visible").toBeVisible();
    await expect(this.allStudentsButton, "Verify all students button  is visible").toBeVisible();
    await expect(this.nameHeader, "Verify name header title is visible").toBeVisible();
    await expect(this.timeSpentInClassHeader, "Verify time spent in class header title is visible").toBeVisible();
  }

  /* Verify grid view page  */
  async veriyGridViewPage() {
    await expect(this.studentYetToJoinText, "Verify student yet to join text is visible").toBeVisible();
  }

  async navigateWithCommand(page: Page, key: string) {
    // Check if the platform is macOS for using 'Meta' key
    const isMac = process.platform === 'darwin';
    const commandKey = isMac ? 'Meta' : 'Control'; // Use 'Meta' for macOS, 'Control' for other systems

    await page.keyboard.down(commandKey); // Press Command or Control
    await page.keyboard.press(key);      // Press the numeric key
    await page.keyboard.up(commandKey);  // Release Command or Control
  }

  /* Verify grid view page  */
  async veriySwitchingFronGridViewToAttendanceView() {
    // Step 2: Simulate the keyboard shortcut Ctrl+2 to navigate to the Concept tab
    await this.navigateWithCommand(this.page, '2');
  }

  /* Verify grid view page  */
  async veriySwitchingFronAttendanceViewToGridView() {
    // Optional: Small delay to allow for view change (if needed)
    await this.navigateWithCommand(this.page, '1');
  }

  /*click on poll with in the shedcule live class  */
  async clickOnPolls() {
    await slowExpect(this.pollsButton, "Verify polls buttons is visible").toBeVisible();
    await this.pollsButton.click();
  }

  /*close poll pop up with in the shedcule live class  */
  async closePollsPopUp() {
    await expect(this.closePollPopUp, "Verify close poll Icon is visible").toBeVisible();
    await this.closePollPopUp.click();
  }

  /*create poll with in the shedcule live class  */
  async verifyPollsTimeOptions() {
    await expect(this.thirtySecPollOption, "Verify 30 sec poll option is visible").toBeVisible();
    await expect(this.oneMinPollOption, "Verify 1 min poll option is visible").toBeVisible();
    await expect(this.noLimitButton, "Verify no limit poll option is visible").toBeVisible();
  }
  /* Verify mic, video on/off from teacher side */
  async veriyMicVideoOnAndOffFromTeacher() {
    await slowExpect(this.micButton, "Verify mic button is visible").toBeVisible();
    await this.micButton.click();
    await this.page.waitForTimeout(1000); // wait is required to load the muted mic image
    await expect(this.micMuted, "Verify mic is muted is visible").toBeVisible();
    await expect(this.videoButton, "Verify video button is visible").toBeVisible();
    await this.videoButton.click();
    await this.page.waitForTimeout(1000);// wait is required to load the muted video image
    await expect(this.videoMuted, "Verify video is muted is visible").toBeVisible();
  }


  /*search view student  */
  async searchStudent(studentName) {
    await expect(this.searchForParticipants, "Verify search for participants feild is visible").toBeVisible();
    await this.searchForParticipants.fill(studentName);
    await expect(this.studentNameBringOnStage(studentName), "Verify student Name is visible on search").toBeVisible();
    name = studentName?.split('/')[0];
    console.log("Student trim " + name);
    await expect(this.searchBringOnStage(name), "Verify search bring on stage button is visible").toBeVisible();
  }

  /*search bring on stage  */
  async searchStudentBringOnstage() {
    await expect(this.searchBringOnStage(name), "Verify search view bring on stage button is visible").toBeVisible();
    await this.searchBringOnStage(name).click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all of stage button is visible").toBeVisible();
  }

  /*search lower hand  */
  async searchStudentLowerHand() {
    await expect(this.searchForParticipants, "Verify search for participants feild is visible").toBeVisible();
    await this.searchForParticipants.click();
    await expect(this.searchLowerHand(name), "Verify search lower hand button in search view is visible").toBeVisible();
    await this.searchLowerHand(name).click();
    await this.page.waitForTimeout(1000); // required this due to websocket 
    await slowExpect(this.lowerAllHandsButton, "Verify lower all hand button is visible").toBeHidden();
  }

  async verifyStudentResponsesTab() {
    await this.page.waitForTimeout(1000); // required this
    await expect(this.studentResponsesButton, "Verify student responses button is visible").toBeEnabled();
    await this.studentResponsesButton.click();
    await expect(this.respondedTab, "Verify  responded tab is visible").toBeEnabled();
    await expect(this.notRespondedTab, "Verify not responded tab is visible").toBeEnabled();
    await expect(this.responsesCloseIcon, "Verify student responses close icon is visible").toBeEnabled();
  }

  async verifyEndPoll() {
    await this.page.waitForTimeout(1000); // required this
    await expect(this.endPoll, "Verify stop poll button is visible").toBeEnabled();
    await expect(this.endPoll, "Verify close poll Icon is visible").toBeVisible();
    await this.endPoll.click();
    await this.page.waitForTimeout(1000); // required for websocket
    await customExpect(10000)(this.studentResponsesButton, "Verify student responses button is visible").toBeVisible();
    await expect(this.closePollPopUp, "Verify close poll Icon is visible").toBeVisible();
  }

  async verifyStudentNotResponses(name) {
    await this.page.waitForTimeout(1000); // required this
    await expect(this.notRespondedTab, "Verify not responded tab is visible").toBeEnabled();
    await this.notRespondedTab.click();
    await expect(this.studentNameNotResponded(name), "Verify student name which is not responeded poll is visible").toBeVisible();
  }

  async verifyStudentNotResponsesBOS(name) {
    await expect(this.studentNameNotResponded(name), "Verify student name which is not responeded poll is visible").toBeVisible();
    await this.studentNameNotRespondedHover(name).hover();
    await expect(this.studentNameNotRespondedBOS(name), "Verify student responded poll bos is visible").toBeEnabled();
    await this.studentNameNotRespondedBOS(name).click();
    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all of stage button is visible").toBeVisible();
  }

  /*responses close poll pop up with in the shedcule live class  */
  async responsesClosePollsPopUp() {
    await expect(this.responsesCloseIcon, "Verify student responses close icon is visible").toBeEnabled();
    await this.responsesCloseIcon.click();
    await expect(this.responsesCloseIcon, "Verify student responses close icon is not visible").toBeHidden();
  }


  async verifyStudentResponses(name, pollType) {
    await this.page.waitForTimeout(1000); // required this
    await expect(this.studentResponsesButton, "Verify student responses button is visible").toBeEnabled();
    await this.studentResponsesButton.click();
    await expect(this.respondedTab, "Verify  responded tab is visible").toBeEnabled();
    await expect(this.notRespondedTab, "Verify not responded tab is visible").toBeEnabled();
    await expect(this.responsesCloseIcon, "Verify student responses close icon is visible").toBeEnabled();
    await expect(this.respondedAnwseredStudent(name, pollType), "Verify student responded poll is listed is visible").toBeEnabled();
  }

  async verifyStudentResponsesBOS(name, pollType) {
    await expect(this.respondedAnwseredStudent(name, pollType), "Verify student responded poll is listed is visible").toBeVisible();
    await this.respondedAnwseredStudentHover(name, pollType).hover();
    await expect(this.respondedBOS(name, pollType), "Verify student responded poll bos is visible").toBeEnabled();
    await this.respondedBOS(name, pollType).click();
    await customExpect(15000)(this.bringAllOffStageButton, "Verify bring all of stage button is visible").toBeVisible();
  }

  /*student list from Teacher live class  */
  async clickOnStudentListButton(studentName) {
    await expect(this.studentButton, "Verify student button is visible").toBeVisible();
    await this.studentButton.click();
    await expect(this.studentListTitleText, "Verify student list title text is visible").toBeVisible();
    await expect(this.searchForParticipantsStudentList, "Verify student list search participants feild is visible").toBeVisible();
  }

  /*student empty list raised hand from Teacher live class  */
  async verifyStudentListRaisedHandEmptyList(studentName) {
    await expect(this.raisedHandStudentListTab, "Verify student list raised hand tab is visible").toBeVisible();
    await this.raisedHandStudentListTab.click();
    await expect(this.studentNameStudentList(studentName), "Verify student name is not listed").toBeHidden();
    await expect(this.raisedHandCloseIcon, "Verify student list raised hand close icon is visible").toBeVisible();
    await this.raisedHandCloseIcon.click();
  }


  /*student raised hand from Teacher live class  */
  async verifyStudentListRaisedHand(studentName) {
    await expect(this.raisedHandStudentListTab, "Verify student list raised hand tab is visible").toBeVisible();
    await this.raisedHandStudentListTab.click();
    await expect(this.studentNameStudentListRaisedHand(studentName), "Verify raised hand for student name is visible").toBeVisible();
    await expect(this.raisedHandCloseIcon, "Verify student list raised hand close icon is visible").toBeVisible();
    await this.raisedHandCloseIcon.click();
    await this.studentNameStudentListRaisedHand(studentName).click();
  }

  /*student empty list bosfrom Teacher live class  */
  async verifyStudentListBOSEmptyList(studentName) {
    await expect(this.bosStudentListTab, "Verify student list bos tab is visible").toBeVisible();
    await this.bosStudentListTab.click();
    await expect(this.studentNameStudentList(studentName), "Verify student name is not listed").toBeHidden();
    await expect(this.bosCloseIcon, "Verify student list bos close icon is visible").toBeVisible();
    await this.bosCloseIcon.click();
  }

  /*student bos from Teacher live class  */
  async verifyStudentListBOS(studentName) {

    await expect(this.studentNameStudentListBos(studentName), "Verify bring on stage for student name is visible").toBeVisible();
    await this.studentNameStudentListBos(studentName).click();
    await expect(this.studentNameStudentListBOffStage(studentName), "Verify student name is present on student list bring on stage tab is visible").toBeVisible();
    await expect(this.bosStudentListTab, "Verify student list bos tab is visible").toBeVisible();
    await this.bosStudentListTab.click();
    await expect(this.studentNameStudentList(studentName), "Verify student name is listed").toBeVisible();
    await expect(this.bosCloseIcon, "Verify student list bring on stage close icon is visible").toBeVisible();
    await this.bosCloseIcon.click();
  }

  /*student bring off stage from Teacher live class  */
  async verifyStudentListBOffStage(studentName) {
    await expect(this.studentNameStudentListBOffStage(studentName), "Verify bring off stage for student name is visible").toBeVisible();
    await this.studentNameStudentList(studentName).click();
    await this.studentNameStudentListBOffStage(studentName).click();
    await expect(this.studentNameStudentListBOffStage(studentName), "Verify bring off stage for student name is not visible").toBeHidden();

  }

  /*student bring off stage from Teacher live class  */
  async verifyStudentListSearch(studentName) {
    await expect(this.searchForParticipantsStudentList, "Verify student list search for participants feild is visible").toBeVisible();
    await this.searchForParticipantsStudentList.fill(studentName);
    await expect(this.studentNameStudentList(studentName), "Verify student name is visible once search ").toBeVisible();
  }

  /*student bring off stage from Teacher live class  */
  async verifyStudentListInvalidSearch(studentName) {
    await expect(this.searchForParticipantsStudentList, "Verify student list search for participants feild is visible").toBeVisible();
    await this.searchForParticipantsStudentList.fill(studentName);
    await expect(this.studentNameStudentList(studentName), "Verify student name is not visible once search invalid name").toBeHidden();
  }

  /*student offline from Teacher live class  */
  async verifyStudentListOffLine(studentName) {
    await expect(this.studentNameListOffLine(studentName), "Verify student name is not listed").toBeHidden();
  }

  async enableDisableDoubts() {
    await slowExpect(this.doubtsTab, 'Verify doubts tab is visible').toBeVisible();
    await this.doubtsTab.click();
    await expect(this.enableDoubtButton, 'Verify enable doubts button is visible').toBeVisible();
    await this.enableDoubtButton.click();
    await this.page.waitForLoadState('networkidle');
    await expect(this.disableDoubtsText, 'Verify disable doubts text is visible').toBeVisible();
    await expect(this.disableDoubts, 'Verify disable doubts toggle switch is visible').toBeVisible();
    await this.disableDoubts.click();
    await expect(this.enableDoubtButton, 'Verify enable doubts button is visible').toBeVisible();
    await this.enableDoubtButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  async doubtVisibleToTeacher(doubtMessage: string) {
    await slowExpect(this.refreshButton).toBeVisible();
    await this.refreshButton.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.doubtsText(doubtMessage)).toBeVisible();
  }

  async blockStudentDoubts() {
    await expect(this.actionsButton, "Verify actions button is visible").toBeVisible();
    await this.actionsButton.click();
    await slowExpect(this.blockStudent, "Verify block student option is visible").toBeVisible();
    await this.blockStudent.click();
    await this.page.waitForLoadState('networkidle');
    await expect(this.blockStudent, "Verify block student option is hidden").not.toBeVisible();
  }

  async unblockStudentDoubts() {
    await slowExpect(this.actionsButton, "Verify actions button is visible").toBeVisible();
    await this.actionsButton.click();
    await expect(this.unblockStudent, "Verify unblock student option is visible").toBeVisible();
    await this.unblockStudent.click();
    await expect(this.unblockStudent, "Verify unblock student option is hidden").not.toBeVisible();
  }

  async upvoteDoubt(voteCount: number) {
    await expect(this.upvoteButton(`${voteCount}`)).toBeVisible();
    await this.upvoteButton(`${voteCount}`).click();
    voteCount += 1
    if (voteCount < 10) {
      await slowExpect(this.upvoteButton(`0${voteCount}`)).toBeVisible();
    }
    else {
      await slowExpect(this.upvoteButton(`${voteCount}`)).toBeVisible();
    }
  }

  async resolveDoubt(doubtMessage: string) {
    await expect(this.resolveButton, "Verify resolve Button is visible").toBeVisible();
    await this.resolveButton.click();
    await expect(this.resolvedDoubtsTab, "Verify resolve doubts tab is visible").toBeVisible();
    await this.resolvedDoubtsTab.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.doubtsText(doubtMessage)).toBeVisible();
  }

  async enableChat() {
    await expect(this.chatTab, "Verify chat tab is visible").toBeVisible();
    await this.chatTab.click();
    await expect(this.enableChatButton, "Verify enable chat button is visible").toBeVisible();
    await this.enableChatButton.click();
  }

  async pinUnpinStudent(studentName: string) {
    await expect(this.pinButton, "Verify pin student button is visible").toBeVisible();
    await this.pinButton.click();
    await expect(this.pinIcon(studentName), "Verify pinned icon is visible").toBeVisible();
    await expect(this.pinButton, "Verify unpin student button is visible").toBeVisible();
    await this.pinButton.click();
  }

  async deleteStudentDoubt(doubtMessage: string) {
    await slowExpect(this.refreshButton).toBeVisible();
    await this.refreshButton.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.doubtsText(doubtMessage)).toBeVisible();
    await this.doubtsText(doubtMessage).hover();
    await this.page.waitForTimeout(1000);
    await expect(this.hamburgerIcon, "Verify hamburger icon is visible").toBeVisible();
    await this.hamburgerIcon.click();
    await expect(this.deleteDoubt, "Verify delete doubt button is visible").toBeVisible();
    await this.deleteDoubt.click();
    await expect(this.noCancelButton, "Verify no cancel button is visible").toBeVisible();
    await this.noCancelButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.doubtsText(doubtMessage).hover();
    await this.page.waitForTimeout(1000);
    await expect(this.hamburgerIcon, "Verify hamburger icon is visible").toBeVisible();
    await this.hamburgerIcon.click();
    await expect(this.deleteDoubt, "Verify delete doubt button is visible").toBeVisible();
    await this.deleteDoubt.click();
    await expect(this.yesDeleteButton, "Verify yes delete button is visible").toBeVisible();
    await this.yesDeleteButton.click();
  }

}