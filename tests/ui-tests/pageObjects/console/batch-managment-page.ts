import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import exp from 'constants';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities'

const BatchManagementPageUrl = '/content-management/lmm'


export class BatchManagementPage extends ICPage {

    readonly batchManagementTitleText: Locator;
    readonly specialBatchTab: Locator;
    readonly searchInput: Locator;
    readonly searchButton: Locator;
    readonly specialBatchName: (contentName: string) => Locator;
    readonly contentDetailsButton: Locator;
    readonly contentAttachedToText: Locator;
    readonly attachNewContentButton: Locator;
    readonly apiLoader: Locator;
    readonly pageResultsCount: Locator;
    readonly academicSessionDropdown: Locator;
    readonly academicSession2025: Locator;
    readonly filterDropDown: Locator;
    readonly applyFilterButton: Locator;
    readonly clearFilterOption: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, BatchManagementPageUrl, isMobile);

        this.batchManagementTitleText = page.getByText('Batch Management');
        this.specialBatchTab = page.getByText('Special Batches');
        this.searchInput = page.getByTestId('textInput');
        this.searchButton = page.getByTestId('text-inside-input');
        this.contentDetailsButton = page.getByRole('button', { name: 'Content Details' });
        this.contentAttachedToText = page.getByText('Content attached to');
        this.attachNewContentButton = page.getByTestId('sort-search-primary-button');
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage')
        this.specialBatchName = (specialBatchName: string) => page.getByText(`${specialBatchName}`);
        this.pageResultsCount = page.getByTestId('pagination-total-count');
        this.academicSessionDropdown = page.getByText('Session').first();
        this.academicSession2025 = page.locator('[data-state="visible"]').getByText('04/2024 - 03/2025');
        this.filterDropDown = page.getByTestId('filter-panel-closed').getByTestId('shownImage');
        this.applyFilterButton = page.getByTestId('apply-filter-panel');
        this.clearFilterOption = page.getByTestId('clear-filter-panel');

    }

    async validateSearchSpecialBatch(specialBatchName: string) {
        await customExpect(15000)(this.searchInput, "Verify search input field is visible").toBeVisible();
        await this.searchInput.click();
        await this.searchInput.fill(specialBatchName);
        await expect(this.searchButton, "Verify search button is visible").toBeVisible();
        await this.searchButton.click();
        await this.page.waitForTimeout(1000); // to load the search
        await slowExpect(this.specialBatchName(specialBatchName)).toBeVisible();
        await this.specialBatchName(specialBatchName).click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    }

    async clickOnContentDetailsButton() {
        await customExpect(15000)(this.contentDetailsButton, "Verify content details button is visible").toBeVisible();
        await this.contentDetailsButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await this.page.waitForTimeout(1000);
        await customExpect(15000)(this.attachNewContentButton, "Verify attch new content button is visible").toBeVisible();
        await this.attachNewContentButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await this.page.waitForTimeout(1000);
    }

    async clickOnSpecialBatchTab(baseURL) {
        await expect(this.specialBatchTab, "Verify special batch tab is visble").toBeVisible();
        const responsePromise = this.page.waitForResponse(baseURL + '/resource/batches/listing', { timeout: 10000 });
        await this.specialBatchTab.click();
        await responsePromise;
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    }

    async applyFilterTo2025AccadamicSession() {
        await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
        await this.filterDropDown.click();
        await expect(this.academicSessionDropdown, "Verify academic session drop down is visible").toBeVisible();
        await this.academicSessionDropdown.click();
        await expect(this.academicSession2025, "Verify academic session 2025 drop down is visible").toBeVisible();
        await this.academicSession2025.click();
        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await this.applyFilterButton.click();
    }


}