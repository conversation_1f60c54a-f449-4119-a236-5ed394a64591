import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const PhaseManagementPageUrl = 'course-management/phase';

export class PhaseManagementPage extends ICPage {
    readonly phaseManagementHeader: Locator;
    readonly filterOption: Locator;
    readonly filterAcademicSessionText: Locator;
    readonly filterPanelClosedIcon: Locator;
    readonly filterAcadamicSessionDropDown: Locator;
    readonly filterAcadamicSession24_25: Locator;
    readonly filterCenterFacilityDropdown: Locator;
    readonly searchPlaceholder: Locator;
    readonly automationCenterId: Locator;
    readonly filterStreamJeeDropdown: Locator;
    readonly filterStreamTitle: Locator;
    readonly filterStreamDropdown: Locator;
    readonly filterCourseDropdown: Locator;
    readonly uiAutomationCourseId: Locator;
    readonly commencementStartDateTitle: Locator;
    readonly applyFilter: Locator;
    readonly cancelFilter: Locator;
    readonly filterPannelExpanded: Locator;
    readonly createNewPhaseButton: Locator;
    readonly createPhaseManagementHeader: Locator;
    readonly sessionTitle: Locator;
    readonly academicSessionDropDown: Locator;
    readonly session2425Value: Locator;
    readonly centerTitle: Locator;
    readonly selectCenter: Locator;
    readonly automationCenterOption: Locator;
    readonly streamTitle: Locator;
    readonly selectStream: Locator;
    readonly streamJee: Locator;
    readonly courseTtile: Locator;
    readonly selectCourse: Locator;
    readonly uiAutomationCourse: Locator;
    readonly phaseNumberTitle: Locator;
    readonly phaseNumberInput: Locator;
    readonly erpCodeTitle: Locator;
    readonly erpCodeInput: Locator;
    readonly startDatePlaceholder: Locator;
    readonly syllabusCompleteDateTitle: Locator;
    readonly syllabusEndDate: Locator;
    readonly endDateTitle: Locator;
    readonly endDatePlaceholder: Locator;
    readonly purchaseStartDateTitle: Locator;
    readonly liveDatePlaceholder: Locator;
    readonly expiryDatePlaceholder: Locator;
    readonly saveButton: Locator;
    readonly cancelButton: Locator;
    readonly defaultBatchCreationTitle: Locator;
    readonly continueButton: Locator;
    readonly phaseManagementList: Locator;
    readonly apiLoader: Locator;
    readonly phaseCreatedSuccessfully: Locator;
    readonly commencementStartDate: Locator;
    readonly createdPhaseNumber: (phaseNumber: string) => Locator;
    readonly purchaseExpiryDateTitle: Locator;
    readonly deletePhaseButton: Locator;
    readonly areYouSureText: Locator;
    readonly editPhaseButton: Locator;
    readonly exitButton: Locator;
    readonly deleteButton: Locator;
    readonly phaseDeletedSuccessfully: Locator;
    readonly phaseDetailsHeader: Locator;
    readonly classTitleText: Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, PhaseManagementPageUrl, isMobile);
        this.phaseManagementHeader = page.getByText('Phase Management');
        this.filterOption = page.getByText('Filter', { exact: true });
        this.filterAcademicSessionText = page.getByText('AcademicSession:');
        this.filterPanelClosedIcon = page.getByTestId('filter-panel-closed').getByTestId('shownImage');
        this.filterAcadamicSessionDropDown = page.getByTestId('academic_session-dropdown');
        this.filterAcadamicSession24_25 = page.getByTestId('academic_session-dropdown-option-04_2024__03_2025');
        this.filterCenterFacilityDropdown = page.getByTestId('facility_id-dropdown');
        this.searchPlaceholder = page.getByPlaceholder('Search...');
        this.automationCenterId = page.getByTestId('facility_id-dropdown-option-fa_npghQRtILotkeoZyLmfAO');
        this.filterStreamJeeDropdown = page.getByTestId('stream-dropdown-option-STREAM_JEE_MAIN_ADVANCED');
        this.filterStreamTitle = page.getByText('Stream');
        this.filterStreamDropdown = page.getByTestId('stream-dropdown');
        this.filterCourseDropdown = page.getByTestId('course_id-dropdown');
        this.uiAutomationCourseId = page.getByTestId('course_id-dropdown-option-cr_W2E1Lh09L9TzHX54bQbKI');
        this.commencementStartDateTitle = page.locator('label').filter({ hasText: 'Commencement Start Date' });
        this.applyFilter = page.getByTestId('apply-filter-panel');
        this.cancelFilter = page.getByTestId('cancel-filter-panel');
        this.filterPannelExpanded = page.getByTestId('filter-panel-expanded').getByTestId('shownImage');
        this.createNewPhaseButton = page.getByTestId('sort-search-primary-button').getByText('Create new phase');
        this.createPhaseManagementHeader = page.getByRole('heading', { name: 'Create New Phase' });
        this.sessionTitle = page.getByText('Session *');
        this.academicSessionDropDown = page.getByText('Select Academic Session');
        this.session2425Value = page.getByTestId('dropdown-option-04_2024__03_2025');
        this.centerTitle = page.getByText('Center *');
        this.selectCenter = page.getByText('Select Center');
        this.automationCenterOption = page.getByTestId('dropdown-option-fa_npghQRtILotkeoZyLmfAO');
        this.streamTitle = page.getByText('Stream *');
        this.selectStream = page.getByText('Select Stream');
        this.streamJee = page.getByTestId('dropdown-option-STREAM_JEE_MAIN_ADVANCED');
        this.courseTtile = page.getByText('Courses *');
        this.selectCourse = page.getByText('Select Course');
        this.uiAutomationCourse = page.getByTestId('dropdown-option-cr_W2E1Lh09L9TzHX54bQbKI');
        this.phaseNumberTitle = page.getByText('Phase Number*');
        this.phaseNumberInput = page.getByLabel('Phase Number*');
        this.erpCodeTitle = page.getByText('ERP code*');
        this.erpCodeInput = page.getByLabel('ERP code*');
        this.commencementStartDate = page.getByText('Commencement Date *');
        this.startDatePlaceholder = page.getByPlaceholder('Start Date');
        this.syllabusCompleteDateTitle = page.getByText('Syllabus Completion Date *');
        this.syllabusEndDate = page.getByPlaceholder('Syllabus Date');
        this.endDateTitle = page.getByText('End Date *');
        this.endDatePlaceholder = page.getByPlaceholder('End Date');
        this.purchaseStartDateTitle = page.getByText('Purchase Start Date *');
        this.liveDatePlaceholder = page.getByPlaceholder('Live Date');
        this.expiryDatePlaceholder = page.getByPlaceholder('Expiry Date');
        this.saveButton = page.getByRole('button', { name: 'Save' });
        this.cancelButton = page.getByRole('button', { name: 'Cancel' });
        this.defaultBatchCreationTitle = page.getByText('A default Batch will be');
        this.continueButton = page.getByRole('button', { name: 'Continue' });
        this.phaseManagementList = page.getByTestId("phaseListView");
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.phaseCreatedSuccessfully = page.getByText('Phase created successfully');
        this.createdPhaseNumber = (phaseNumber: string) => page.getByText(`${phaseNumber}`).first();
        this.phaseDetailsHeader = page.getByRole('heading', { name: 'Phase Details' });
        this.purchaseExpiryDateTitle = page.getByText('Purchase Expiry Date');
        this.deletePhaseButton = page.getByRole('button', { name: 'Delete phase' });
        this.editPhaseButton = page.getByRole('button', { name: 'Edit phase' });
        this.areYouSureText = page.getByText('Are you sure you want to');
        this.exitButton = page.getByRole('button', { name: 'Exit' });
        this.deleteButton = page.getByRole('button', { name: 'Delete', exact: true });
        this.phaseDeletedSuccessfully = page.getByText('Phase Deleted Successfully');
        this.classTitleText = page.getByText('Class');
    }

    async validatePhaseManagementPage() {
        await slowExpect(this.phaseManagementHeader, "Verify phase management header is visible").toBeVisible();
        await expect(this.filterOption, "Verify filter option is visible").toBeVisible();
        await expect(this.filterPanelClosedIcon, "Verify filter closed icon is visible").toBeVisible();
        await expect(this.phaseManagementList, "Verify phase management is visible").toBeAttached();
    }

    async verifyAndCreatePhase(phaseNumber, erpNumber, CommencementStartDate, syllabusCompletedDate, endDate, liveDate, purchaseEndDate) {
        await slowExpect(this.createNewPhaseButton, "Verify create new phase button is visible").toBeVisible();
        await this.createNewPhaseButton.click();
        await expect(this.page, "Verify page navigated to create phase page").toHaveURL(/.*create/);
        await slowExpect(this.createPhaseManagementHeader, "Verify create new phase header is visible").toBeVisible();
        /* session selection */
        await expect(this.sessionTitle, "Verify session title is visible").toBeVisible();
        await expect(this.academicSessionDropDown, "Verify academic session dropDown is visible").toBeVisible();
        await this.academicSessionDropDown.click();
        await expect(this.session2425Value, "Verify academic session 24-25 value is visible").toBeVisible();
        await this.session2425Value.click();
        /* center selection */
        await expect(this.centerTitle, "Verify center title is visible").toBeVisible();
        await expect(this.selectCenter, "Verify select center is visible").toBeVisible();
        await this.selectCenter.click();
        await expect(this.automationCenterOption, "Verify automation center option is visible").toBeVisible();
        await this.automationCenterOption.click();
        /* stream selection */
        await expect(this.streamTitle, "Verify stream title is visible").toBeVisible();
        await expect(this.selectStream, "Verify select stream is visible").toBeVisible();
        await this.selectStream.click();
        await expect(this.streamJee, "Verify stream jee is visible").toBeVisible();
        await this.streamJee.click();
        /* coures selection */
        await expect(this.courseTtile, "Verify course title is visible").toBeVisible();
        await expect(this.selectCourse, "Verify select stream is visible").toBeVisible();
        await this.selectCourse.click();
        await expect(this.uiAutomationCourse, "Verify ui automation course is visible").toBeVisible();
        await this.uiAutomationCourse.click();
        /* fill phase number */
        await expect(this.phaseNumberTitle, "Verify phase number title is visible").toBeVisible();
        await expect(this.phaseNumberInput, "Verify phase number input is visible").toBeVisible();
        await this.phaseNumberInput.click();
        await this.phaseNumberInput.fill(phaseNumber);
        /* fill ERP number */
        await expect(this.erpCodeTitle, "Verify erp code title is visible").toBeVisible();
        await expect(this.erpCodeInput, "Verify erp input is visible").toBeVisible();
        await this.erpCodeInput.click();
        await this.erpCodeInput.fill(erpNumber);
        /* fill Commencement date */
        await expect(this.commencementStartDate, "Verify commencement start date title is visible").toBeVisible();
        await expect(this.startDatePlaceholder, "Verify start input is visible").toBeVisible();
        await expect(this.startDatePlaceholder, "Verify start input is enabled").toBeEnabled();
        await this.startDatePlaceholder.click();
        await this.startDatePlaceholder.fill(CommencementStartDate);
        /* fill syllabus completion date */
        await expect(this.syllabusCompleteDateTitle, "Verify syllabus complete date is visible").toBeVisible();
        await expect(this.syllabusEndDate, "Verify syllabus end date input is visible").toBeVisible();
        await expect(this.syllabusEndDate, "Verify syllabus end date input is enabled").toBeEnabled();
        await this.syllabusEndDate.click();
        await this.syllabusEndDate.fill(syllabusCompletedDate);
        /* fill end date */
        await expect(this.endDateTitle, "Verify end date title is visible").toBeVisible();
        await expect(this.endDatePlaceholder, "Verify end date input is visible").toBeVisible();
        await expect(this.endDatePlaceholder, "Verify end date input is enabled").toBeEnabled();
        await this.endDatePlaceholder.click();
        await this.endDatePlaceholder.fill(endDate);
        /* fill purchase start date */
        await expect(this.purchaseStartDateTitle, "Verify purchase start date title is visible").toBeVisible();
        await expect(this.liveDatePlaceholder, "Verify live date input is visible").toBeVisible();
        await expect(this.liveDatePlaceholder, "Verify live date input is enabled").toBeEnabled();
        await this.liveDatePlaceholder.click();
        await this.liveDatePlaceholder.fill(liveDate);
        /* fill purchase end date */
        await expect(this.expiryDatePlaceholder, "Verify purchase end date input is visible").toBeVisible();
        await expect(this.expiryDatePlaceholder, "Verify purchase end date input is enabled").toBeEnabled();
        await this.expiryDatePlaceholder.click();
        await this.expiryDatePlaceholder.fill(purchaseEndDate);
        /* save details */
        await expect(this.saveButton, "Verify save button is visible").toBeVisible();
        await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
        await this.saveButton.click();
        await expect(this.defaultBatchCreationTitle, "Verify default batch will create title is visible").toBeVisible();
        await expect(this.continueButton, "Verify continue button is visible").toBeVisible();
        await this.continueButton.click();
        /* validate success tost */
        await slowExpect(this.phaseCreatedSuccessfully, "Verify continue button is visible").toBeVisible();
    }

    async validateFilterFunctionality(centerName, streamName) {
        await slowExpect(this.filterOption, "Verify filter option is visible").toBeVisible();
        await expect(this.filterPanelClosedIcon, "Verify filter closed icon is visible").toBeVisible();
        await this.filterPanelClosedIcon.click();
        /* session secltion */
        await slowExpect(this.filterAcadamicSessionDropDown, "Verify academic session dropdown is visible").toBeVisible();
        await this.filterAcadamicSessionDropDown.click();
        await expect(this.filterAcadamicSession24_25, "Verify session 24-25 is visible").toBeAttached();
        await this.filterAcadamicSession24_25.click();
        /* center secltion */
        await expect(this.filterCenterFacilityDropdown, "Verify center dropdown is visible").toBeVisible();
        await this.filterCenterFacilityDropdown.click();
        await expect(this.searchPlaceholder, "Verify search input is visible").toBeVisible();
        await this.searchPlaceholder.fill(centerName);
        await expect(this.automationCenterId, "Verify center id is visible").toBeAttached();
        await this.automationCenterId.click();
        /* stream secltion */
        await expect(this.filterStreamDropdown, "Verify stream dropdown is visible").toBeVisible();
        await this.filterStreamDropdown.click();
        await expect(this.searchPlaceholder, "Verify search input is visible").toBeVisible();
        await this.searchPlaceholder.fill(streamName);
        await expect(this.filterStreamJeeDropdown, "Verify stream value is visible").toBeVisible();
        await this.filterStreamJeeDropdown.click();
        /* course secltion */
        await expect(this.classTitleText, "Verify class title is visible").toBeVisible();
        await this.classTitleText.click();
        await expect(this.filterCourseDropdown, "Verify course dropdown is visible").toBeVisible();
        await this.filterCourseDropdown.click();
        await expect(this.uiAutomationCourseId, "Verify course id is visible").toBeAttached();
        await this.uiAutomationCourseId.click();
        /* apply filters */
        await expect(this.applyFilter, "Verify apply filter option is visible").toBeVisible();
        await expect(this.cancelFilter, "Verify cnacel filter option is visible").toBeVisible();
        await this.applyFilter.click();
    }

    async validateAndDelteCreatedPhase() {
        await slowExpect(this.phaseDetailsHeader, "Verify phase details header is visible").toBeVisible();
        await expect(this.purchaseExpiryDateTitle, "Verify purchase expiry date title is visible").toBeVisible();
        await expect(this.deletePhaseButton, "Verify delete phase button is visible").toBeVisible();
        await expect(this.editPhaseButton, "Verify edit phase is visible").toBeVisible();
        await this.deletePhaseButton.click();
        await slowExpect(this.areYouSureText, "Verify are you sure to delete text is visible").toBeVisible();
        await expect(this.exitButton, "Verify exit button is visible").toBeVisible();
        await expect(this.deleteButton, "Verify delete button is visible").toBeVisible();
        await this.deleteButton.click();
        await slowExpect(this.phaseDeletedSuccessfully, "Verify phase deleted successfully text is visible").toBeVisible();


    }
}