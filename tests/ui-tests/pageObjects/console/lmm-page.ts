import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import exp from 'constants';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';

const LMMPageUrl = '/content-management/lmm'
const waitTime = 15000;

export class LMMPage extends ICPage {

  readonly learningAndManagementText: Locator;
  readonly newUploadButton: Locator;
  readonly UploadButton: Locator;
  readonly newUploadContent: Locator;
  readonly browseFileButton: Locator;
  readonly fileInput: Locator;
  readonly csvUploadFileInput: Locator;
  readonly uploadCsvText: Locator;
  readonly filesSelectedText: Locator;
  readonly validateButton: Locator;
  readonly metaDataFilledText: Locator;
  readonly contentNameInput: Locator;
  readonly newContentInput: Locator;
  readonly typeFieldText: Locator;
  readonly contentUploadButton: Locator;
  readonly uploadedSuccessfullyText: Locator;
  readonly uploadedContent: (contentName: string) => Locator;
  readonly taxonomyFieldText: Locator;
  readonly taxonomyDropdown: Locator;
  readonly taxonomySelectDropdown: Locator;
  readonly searchTextFeild: Locator;
  readonly taxonamyName: (taxonamyName: string) => Locator;
  readonly fillFromConsoleButton: Locator;
  readonly typeDropDown: Locator;
  readonly typeName: (typeName: string) => Locator;
  readonly subTypeDropDown: Locator;
  readonly subTypeName: (typeName: string) => Locator;
  readonly learningCategoryDropDown: Locator;
  readonly learningCategoryName: (typeName: string) => Locator;
  readonly sessionDropDown: Locator;
  readonly sessionName: (typeName: string) => Locator;
  readonly centerDropDown: Locator;
  readonly centerName: (typeName: string) => Locator;
  readonly taxonamyNameDropDown: Locator;
  readonly taxonamyNameName: (typeName: string) => Locator;
  readonly classDropDown: Locator;
  readonly className: (typeName: string) => Locator;
  readonly subjectDropDown: Locator;
  readonly subjectName: (typeName: string) => Locator;
  readonly superTopicDropDown: Locator;

  readonly languageDropDown: Locator;
  readonly languageName: (typeName: string) => Locator;
  readonly filesSelectCheckBox: Locator;
  readonly deleteButton: Locator;
  readonly cancelButton: Locator;
  readonly contentNameTitleText: Locator;
  readonly contentNameFeild: Locator;
  readonly classTitleText: Locator;
  readonly subjectTitleText: Locator;
  readonly languageTitleText: Locator;
  readonly searchInput: Locator;
  readonly searchButton: Locator;
  readonly editButton: Locator;
  readonly saveButton: Locator;
  readonly cancelTopologyButton: Locator;
  readonly addMultitoplogyButton: Locator;
  readonly taxonomySelect: Locator;
  readonly subjectSelect: Locator;
  readonly classSelect: Locator;
  readonly topicSelect: Locator;
  readonly superTopicSelect: Locator;
  readonly superTopicName: (superTopicName: string) => Locator;
  readonly topicName: (topicName: string) => Locator;
  readonly subTopicName: (subTopicName: string) => Locator;
  readonly topicDropDown: Locator;
  readonly addTopologyMetaData: Locator;
  readonly multipleFilesSelectCheckBox: Locator;
  readonly checkBox: Locator;
  readonly superTopicTitleText: Locator;
  readonly subTopicTitleText: Locator;
  readonly addedMultiTopology: Locator;
  readonly classHeaderText: Locator;
  readonly taxonamyNameHeaderText: Locator;
  readonly multiTopologySelectOption: (selectName: string) => Locator;

  readonly centerLmmDropDown: Locator;
  readonly taxonamyNameLmmDropDown: Locator;
  readonly classLmmDropDown: Locator;
  readonly subjectLmmDropDown: Locator;
  readonly superTopicLmmDropDown: Locator;
  readonly topicLmmDropDown: Locator;
  readonly filterDropDown: Locator;
  readonly applyFilterButton: Locator;
  readonly cancelFilterButton: Locator;
  readonly centerNameListed: (centerName: string) => Locator;
  readonly taxonomyNameListed: (taxonamyName: string) => Locator;
  readonly classNameListed: (className: string) => Locator;
  readonly subjectNameListed: (subjectName: string) => Locator;
  readonly superTopicNameListed: (superTopicName: string) => Locator;
  readonly topicListed: (topicName: string) => Locator;
  readonly topicHeaderText: Locator;
  readonly contentId: Locator;
  readonly clearButton: Locator;
  readonly contentName: Locator;
  readonly sortDropDown: Locator;
  readonly lastModifiedOption: Locator;
  readonly lastAddedOption: Locator;
  readonly relevanceOption: Locator;

  readonly studyMaterialTab: Locator;
  readonly recordedContentTab: Locator;
  readonly liveLectureRecordingTab: Locator;
  readonly subTypeHeaderText: Locator;
  readonly typeNameStudyMaterailListed: Locator;
  readonly typeNameRecordedContentListed: Locator;
  readonly typeNameLiveLectureListed: Locator;
  readonly allTab: Locator;
  readonly replaceIcon: Locator;
  readonly browseFilePopUpTitle: Locator;
  readonly uploadTheSameFormatText: Locator;
  readonly replaceUploadButton: Locator;
  readonly contentFileOpen: Locator;
  readonly searchTextFeildLmm: Locator;
  readonly lmmSelectOption: (selectName: string) => Locator;
  readonly inputFile: Locator;
  readonly contentNameInput2: Locator;

  readonly statusActive: Locator;
  readonly statusHeaderText: Locator;
  readonly apiLoader: Locator;
  readonly taxonamyNameNameProd: (typeName: string) => Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, LMMPageUrl, isMobile);
    this.learningAndManagementText = page.getByText('Learning Material Management');
    this.newUploadButton = page.getByText('New Upload');
    this.UploadButton = page.getByTestId('popup-menu-cta');
    this.newUploadContent = page.getByTestId('upload-new-content');
    this.browseFileButton = page.getByRole('button', { name: 'Browse Files' });
    this.fileInput = page.getByTestId('file-input');
    this.uploadCsvText = page.getByText('Upload .csv for bulk files');
    this.csvUploadFileInput = page.getByTestId('metadata-file-input');
    this.filesSelectedText = page.getByText('files selected');
    this.validateButton = page.getByRole('button', { name: 'Validate' });
    this.metaDataFilledText = page.getByText('Meta data successfully filled');
    this.contentNameInput = page.locator("//*[@col-id='name']//*[contains(@class,'ag-cell-value')]").first();
    this.newContentInput = page.getByLabel('Input Editor');
    this.contentNameTitleText = page.getByText('Content Name*', { exact: true });
    this.typeFieldText = page.getByText('Type*', { exact: true });
    this.contentUploadButton = page.getByRole('button', { name: 'UPLOAD' });
    this.uploadedSuccessfullyText = page.getByTestId('toast_message_text').getByText('Uploaded successfully')
    this.uploadedContent = (contentName: string) => page.getByText(`${contentName}`);
    this.fillFromConsoleButton = page.getByRole('button', { name: 'Fill from console' });

    //*[contains(@id,'cell-learning_category')]//*[@data-testid='shownImage']
    this.typeDropDown = page.locator(`//*[@role="gridcell" and @col-id="type"]`).getByText('Select...');
    this.typeName = (typeName: string) => page.getByText(`${typeName}`);
    this.subTypeDropDown = page.locator(`//*[@role="gridcell" and @col-id="sub_type"]`).getByText('Select...');
    this.subTypeName = (subTypeName: string) => page.getByText(`${subTypeName}`);
    this.learningCategoryDropDown = page.locator(`//*[@role="gridcell" and @col-id="learning_category"]`).getByText('Select...');
    this.learningCategoryName = (learningCategoryName: string) => page.getByText(`${learningCategoryName}`);
    this.sessionDropDown = page.locator(`//*[@role="gridcell" and @col-id="session"]`).getByText('Select...');
    this.sessionName = (sessionName: string) => page.getByText(`${sessionName}`);
    this.centerDropDown = page.locator(`//*[@role="gridcell" and @col-id="center_id"]`).getByText('Select...');
    this.centerName = (centerName: string) => page.getByText(`${centerName}`, { exact: true });
    this.taxonamyNameName = (taxonamyNameName: string) => page.getByText(`${taxonamyNameName}`).nth(0);
    this.taxonamyNameNameProd = (taxonamyNameName: string) => page.locator(`//*[text()='${taxonamyNameName}']//*`)
    this.classDropDown = page.locator(`//*[@role="gridcell" and @col-id="class"]`).getByText('Select...');
    this.className = (className: string) => page.getByText(`${className}`);
    this.subjectDropDown = page.locator(`//*[@role="gridcell" and @col-id="subject"]`).getByText('Select...');
    this.taxonamyNameDropDown = page.locator(`//*[@role="gridcell" and @col-id="taxonomy_name"]`).getByText('Select...');
    this.subjectName = (subjectName: string) => page.getByText(`${subjectName}`);
    this.languageDropDown = page.locator(`//*[@role="gridcell" and @col-id="language"]`).getByText('Select...');
    this.languageName = (languageName: string) => page.getByText(`${languageName}`);
    this.filesSelectCheckBox = page.getByLabel('Press Space to toggle row');
    this.cancelButton = page.getByRole('button', { name: 'CANCEL' });
    this.deleteButton = page.getByRole('button', { name: 'DELETE' });
    this.contentNameFeild = page.locator('.ag-center-cols-container > .ag-row-even > div').first();
    this.searchTextFeild = page.getByPlaceholder('Search', { exact: true });
    this.classTitleText = page.getByText('Class*', { exact: true });
    this.subjectTitleText = page.getByText('Subject', { exact: true });
    this.languageTitleText = page.getByText('Language*', { exact: true });
    this.searchInput = page.getByTestId('search-input');
    this.searchButton = page.getByTestId('text-inside-input');
    this.editButton = page.getByRole('img', { name: 'edit' }).first();
    this.saveButton = page.getByRole('img', { name: 'save' });
    this.cancelTopologyButton = page.getByTitle('Cancel').locator('path');
    this.addMultitoplogyButton = page.getByTitle('Add Multitoplogy').getByRole('img');
    this.taxonomySelect = page.locator(`//*[contains(@id,"cell-taxonomy_name")]//*[text()="Select..."]`);
    this.subjectSelect = page.locator(`//*[contains(@id,"cell-subject")]//*[text()="Select..."]`);
    this.classSelect = page.locator(`//*[contains(@id,"cell-class")]//*[text()="Select..."]`);
    this.topicSelect = page.locator(`//*[contains(@id,"cell-topic")]//*[text()="Select..."]`);
    this.superTopicSelect = page.locator(`//*[contains(@id,"cell-super_topic")]//*[text()="Select..."]`);
    this.superTopicName = (superTopicName: string) => page.getByText(`${superTopicName}`);
    this.topicName = (topicName: string) => page.locator(`//*[contains(text(),'${topicName}')]`);
    this.subTopicName = (topicName: string) => page.getByText(`${topicName}`);
    this.superTopicDropDown = page.locator(`//*[@role="gridcell" and @col-id="super_topic"]`).getByText('Select...');
    this.topicDropDown = page.locator(`//*[@role="gridcell" and @col-id="topic"]`).getByText('Select...');
    this.addTopologyMetaData = page.locator('svg');
    this.multipleFilesSelectCheckBox = page.getByLabel('Press Space to toggle row selection (unchecked)');
    this.checkBox = page.locator('input[type="checkbox"][aria-label*="Press Space to toggle row selection"]');
    this.superTopicTitleText = page.locator(`//*[@role="gridcell" and @col-id="super_topic"]`).nth(1);
    this.subTopicTitleText = page.locator(`//*[@role="gridcell" and @col-id="sub_topic"]`).nth(1);
    this.taxonamyName = (taxonamyName: string) => page.getByText(`${taxonamyName}`);
    this.addedMultiTopology = page.getByText('(3)');
    this.classHeaderText = page.getByRole('columnheader', { name: 'Class' });
    this.taxonamyNameHeaderText = page.locator(`//*[@class="ag-header-cell-text" and text()="Taxonomy Name"]`);
    this.multiTopologySelectOption = (selectName: string) => page.locator(`//*[contains(@class,"overflow-scroll")]//*[contains(text(),'${selectName}')]`);
    this.centerNameListed = (centerName: string) => page.locator(`//*[@role="gridcell" and @col-id="center_id"]/descendant::*[text()='${centerName}']`).first();
    this.classDropDown = page.locator(`//*[@role="gridcell" and @col-id="class"]`).getByText('Select...');
    this.subjectDropDown = page.locator(`//*[@role="gridcell" and @col-id="subject"]`).getByText('Select...');
    this.taxonomyNameListed = (taxonamyName: string) => page.locator(`//*[@role="gridcell" and @col-id="taxonomy_name"]/descendant::*[text()='${taxonamyName}']`).first();
    this.classNameListed = (className: string) => page.locator(`//*[@role="gridcell" and @col-id="class"]/descendant::*[text()='${className}']`).first();

    this.subjectNameListed = (subjectName: string) => page.locator(`//*[@role="gridcell" and @col-id="subject"]/descendant::*[text()='${subjectName}']`).first();
    this.topicListed = (topicName: string) => page.locator(`//*[@role="gridcell" and @col-id="topic"]/descendant::*[text()='${topicName}']`).first();
    this.superTopicNameListed = (superTopicName: string) => page.locator(`//*[@role="gridcell" and @col-id="super_topic"]/descendant::*[text()='${superTopicName}']`).first();
    this.contentId = page.locator(`//*[contains(@id,"cell-id-")]`).first();


    this.centerLmmDropDown = page.getByTestId('center_id-dropdown').getByRole('img');
    this.taxonamyNameLmmDropDown = page.getByTestId('taxonomy_name-dropdown').getByRole('img');
    this.classLmmDropDown = page.getByTestId('class-dropdown').getByRole('img');
    this.subjectLmmDropDown = page.getByTestId('subject-dropdown').getByRole('img');
    this.superTopicLmmDropDown = page.getByTestId('super_topic-dropdown').getByRole('img');
    this.topicLmmDropDown = page.getByTestId('topic-dropdown').getByRole('img');
    this.filterDropDown = page.getByTestId('filter-panel-closed').getByTestId('shownImage');
    this.applyFilterButton = page.getByTestId('apply-filter-panel');
    this.cancelFilterButton = page.getByTestId('cancel-filter-panel');
    this.topicHeaderText = page.locator(`//*[@class="ag-header-cell-text" and text()="Topic"]`);
    this.subTypeHeaderText = page.locator(`//*[@class="ag-header-cell-text" and text()="Sub Type"]`);
    this.clearButton = page.getByTestId('text-inside-input');
    this.contentName = page.locator(`//*[contains(@id,"cell-name-")]//*[contains(text(),"Test")]`).first();
    this.sortDropDown = page.getByTestId('sort-by-dropdown').getByRole('img');

    this.lastModifiedOption = page.getByTestId('sort-by-dropdown-option-updated_at');
    this.lastAddedOption = page.getByTestId('sort-by-dropdown-option-created_at');
    this.relevanceOption = page.getByTestId('sort-by-dropdown-option-Relevance');
    this.studyMaterialTab = page.getByText('Study Materials').first();
    this.liveLectureRecordingTab = page.getByText('Live Lecture Recording').first();
    this.recordedContentTab = page.getByText('Recorded Content').first();
    this.allTab = page.getByText('All').first();

    this.typeNameStudyMaterailListed = page.locator(`//*[@role="gridcell" and @col-id="type"]/descendant::*[text()='Study Materials']`).first();
    this.typeNameRecordedContentListed = page.locator(`//*[@role="gridcell" and @col-id="type"]/descendant::*[text()='Recorded Content']`).first();
    this.typeNameLiveLectureListed = page.locator(`//*[@role="gridcell" and @col-id="type"]/descendant::*[text()='Live Lecture Recording']`).first();
    this.replaceIcon = page.getByRole('img', { name: 'replace' });
    this.replaceUploadButton = page.getByRole('button', { name: 'Upload' });
    this.browseFilePopUpTitle = page.getByText('Browse File for Replace Study');
    this.uploadTheSameFormatText = page.getByText('Upload the same format being');
    this.contentFileOpen = page.locator(`//*[contains(text(),".pdf")]`);
    this.searchTextFeildLmm = page.getByPlaceholder('Search...', { exact: true });
    this.lmmSelectOption = (selectName: string) => page.locator(`//*[contains(@data-testid,"dropdown-option") and text()='${selectName}']`);
    this.inputFile = page.getByTestId('excel-file-input');
    this.contentNameInput2 = page.locator("//*[@col-id='name']//*[contains(@class,'ag-cell-value')]").nth(1);
    this.statusHeaderText = page.locator(`//*[@class="ag-header-cell-text" and text()="Status"]`);
    this.statusActive = page.getByRole('gridcell', { name: 'Active down-arrow' }).getByTestId('shownImage');
    this.contentId = page.locator(`//*[contains(@id,"cell-id-")]`).first();
    this.clearButton = page.getByTestId('text-inside-input');
    this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage')
  }



  async uploadNewContent(file: string, ContentCSVFile: string, newContentName: string) {
    await expect(this.learningAndManagementText, "Verify learning and management text is visible").toBeVisible();

    await expect(this.UploadButton, "Verify upload button is visible").toBeVisible();
    await this.UploadButton.click();
    await expect(this.newUploadContent, "Verify new upload content is visible").toBeVisible();
    await this.newUploadContent.click();


    await expect(this.browseFileButton, "Verify browse file button is visible").toBeVisible();
    await this.fileInput.setInputFiles(file);

    await expect(this.uploadCsvText, "Verify upload csv text is visible").toBeVisible();
    await this.csvUploadFileInput.setInputFiles(ContentCSVFile);
    await expect(this.filesSelectedText, "Verify files selected text is visible").toBeVisible();
    await expect(this.validateButton, "Verify validate button is visible").toBeVisible();
    await this.validateButton.click();
    await expect(this.metaDataFilledText, "Verify meta data filled successfully text is visible").toBeVisible();
    await expect(this.contentNameInput, "Verify content name input field is visble").toBeVisible();
    await this.contentNameInput.dblclick();
    await this.newContentInput.fill(newContentName);
    await expect(this.typeFieldText, "Verify type filed text is visible").toBeVisible();
    await this.typeFieldText.click();
    await expect(this.contentUploadButton, "Verify content upload button is visible").toBeVisible();
    await this.contentUploadButton.click();
    await slowExpect(this.uploadedSuccessfullyText).toBeVisible();

  }


  async navigateToUploadContent(file: string) {
    await expect(this.learningAndManagementText, "Verify learning and management text is visible").toBeVisible();
    await expect(this.UploadButton, "Verify upload button is visible").toBeVisible();
    await this.UploadButton.click();
    await expect(this.newUploadContent, "Verify new upload content is visible").toBeVisible();
    await this.newUploadContent.click();
    await expect(this.browseFileButton, "Verify browse file button is visible").toBeVisible();
    await this.fileInput.setInputFiles(file);
    await expect(this.filesSelectedText, "Verify files selected text is visible").toBeVisible();
    await expect(this.fillFromConsoleButton, "Verify fill from console button is visible").toBeVisible();
    await this.fillFromConsoleButton.click();
  }

  async enterDetailsOnMetaDataPage(uploadType: string, contentName: string, typeName: string, subTypeName: string, learningCategoryName: string, sessionName: string, centerName: string, taxonamyNameName: string, className: string, subjectName: string, superTopicName: string, topicName: string, languageName: string) {
    // Helper function to handle dropdown interactions
    const selectFromDropdown = async (dropdown, searchText, selectFunction) => {
      await slowExpect(dropdown, `verify ${dropdown} is visible`).toBeVisible();
      await dropdown.click();
      await this.page.waitForTimeout(500); //required to load the dropdown
      if (await this.searchTextFeild.isHidden()) {
        await slowExpect(dropdown, `Verify ${dropdown} is visible`).toBeVisible();
        await dropdown.click();
      }
      await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
      await this.searchTextFeild.fill(searchText);
      await selectFunction(searchText).click();
    };

    // Enter content name
    await slowExpect(this.contentNameTitleText, "Verify content name title text is visible").toBeVisible();
    await slowExpect(this.contentNameFeild, "Verify content name input field is visible").toBeVisible();
    await this.contentNameFeild.dblclick();
    await this.newContentInput.fill(contentName);
    await slowExpect(this.typeFieldText, "Verify content name input field is visible").toBeVisible();
    await this.typeFieldText.click();

    // Select options from dropdowns
    await selectFromDropdown(this.typeDropDown, typeName, this.typeName);
    await selectFromDropdown(this.subTypeDropDown, subTypeName, this.typeName);
    await selectFromDropdown(this.learningCategoryDropDown, learningCategoryName, this.learningCategoryName);
    await this.taxonamyNameDropDown.scrollIntoViewIfNeeded({ timeout: 10000 });
    await selectFromDropdown(this.sessionDropDown, sessionName, this.sessionName);
    await selectFromDropdown(this.centerDropDown, centerName, this.centerName);
    await selectFromDropdown(this.taxonamyNameDropDown, taxonamyNameName, this.taxonamyNameName);
    await this.languageDropDown.scrollIntoViewIfNeeded({ timeout: 10000 });
    await selectFromDropdown(this.classDropDown, className, this.className);
    await selectFromDropdown(this.subjectDropDown, subjectName, this.subjectName);
    if (uploadType == "MultiTopology") {
      await selectFromDropdown(this.superTopicDropDown, superTopicName, this.superTopicName);
      await selectFromDropdown(this.topicDropDown, topicName, this.topicName);
    }
    await this.languageTitleText.click();
    await selectFromDropdown(this.languageDropDown, languageName, this.languageName);
    await expect(this.filesSelectCheckBox, "Verify file select checkbox is visible").toBeVisible();
    await this.filesSelectCheckBox.check();
    await expect(this.deleteButton, "Verify delete button is visible").toBeVisible();
    await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
  }

  async validateUploadFromFillFromConsole() {
    await expect(this.contentUploadButton, "Verify content upload button is visible").toBeVisible();
    await this.contentUploadButton.click();
    await customExpect(15000)(this.uploadedSuccessfullyText).toBeVisible();
  }


  async validateUploadedContentName(contentNewName: string) {
    await this.page.waitForTimeout(3000); //required to load the page
    // await customExpect(15000)(this.uploadedContent(contentNewName)).toBeVisible();
    await expect(this.searchInput, "Verify search input field is visible").toBeVisible();
    await this.searchInput.click();
    await this.searchInput.fill(contentNewName);
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();
    await this.page.waitForTimeout(2000); // to load the search
    await slowExpect(this.uploadedContent(contentNewName)).toBeVisible();
  }

  async clickOnEditContentAttached() {
    // Verify and click the edit button
    await expect(this.editButton, "Verify edit button is visible").toBeVisible();
    await this.editButton.click();
    // Verify and click the add multitopology button
    await expect(this.addMultitoplogyButton, "Verify add multitopology button is visible").toBeVisible();
    await this.addMultitoplogyButton.click();
    await expect(this.saveButton, "Verify save icon is visible").toBeVisible();
    await expect(this.cancelTopologyButton, "Verify cancel icon is visible").toBeVisible();
    await expect(this.editButton, "Verify edit button is hidden").toBeHidden();

  }

  async editContentAttachedAndSave(taxonomyName: string, className: string, subjectName: string, superTopicName: string, topicName: string) {
    // Helper function to select a value in a dropdown
    const selectDropdownValue = async (dropdown, value) => {
      await this.page.waitForTimeout(500); //required to load the dropdown
      await this.page.waitForLoadState('networkidle')
      await slowExpect(dropdown, `Verify ${dropdown} is visible`).toBeVisible();
      await dropdown.click();
      await this.page.waitForTimeout(500); //required to load the dropdown
      if (await this.searchTextFeild.isHidden()) {
        await dropdown.click();
      }
      await expect(this.searchTextFeild, `Verify search field is visible`).toBeVisible();
      await this.searchTextFeild.fill(value);

      await this.multiTopologySelectOption(value).click();
    };

    await selectDropdownValue(this.taxonomySelect, taxonomyName);
    await this.page.waitForTimeout(500); //required 
    await this.classHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    // Select class
    await selectDropdownValue(this.classSelect, className);
    // Select subject
    await selectDropdownValue(this.subjectSelect, subjectName);
    // Select super topic
    await selectDropdownValue(this.superTopicSelect, superTopicName);
    await selectDropdownValue(this.topicSelect, topicName);
    await expect(this.saveButton, "Verify save button is visible").toBeVisible();
    await expect(this.cancelTopologyButton, "Verify cancel topology button is visible").toBeVisible();
    await this.saveButton.click();
  }

  async selectFromDropdown(dropdown, searchText, selectFunction) {
    // Ensure the dropdown is visible and click it
    await expect(dropdown, `Verify ${dropdown} is visible`).toBeVisible();
    await dropdown.click();
    await this.page.waitForTimeout(500); //required to load the dropdown
    // Handle the case where the dropdown doesn't load properly on the first click
    if (await this.searchTextFeild.isHidden()) {
      await dropdown.click();
    }
    // Search and select the option
    await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(searchText);
    await selectFunction(searchText).click(); // Ensure `select function` is a valid function
  }

  async clickOnAddTopology() {
    await expect(this.addTopologyMetaData, "Verify add (plus) button is visible").toBeVisible();
    await this.addTopologyMetaData.click();
  }

  async enterMultiTopologyDetails(taxonamyNameName: string, className: string, subjectName: string, superTopicName: string, topicName: string) {

    await this.taxonamyNameDropDown.scrollIntoViewIfNeeded({ timeout: 10000 });
    // Select options from dropdowns
    await this.selectFromDropdown(this.taxonamyNameDropDown, taxonamyNameName, this.taxonamyNameNameProd);
    await this.classDropDown.scrollIntoViewIfNeeded({ timeout: 10000 });
    await this.selectFromDropdown(this.classDropDown, className, this.className);
    await this.selectFromDropdown(this.subjectDropDown, subjectName, this.subjectName);
    await this.selectFromDropdown(this.superTopicDropDown, superTopicName, this.superTopicName);
    await this.selectFromDropdown(this.topicDropDown, topicName, this.topicName);
  }


  async selectMultipleFilesInMetaDataPage() {
    const checkboxes = this.checkBox;
    // Loop through each checkbox and check it
    const checkboxCount = await checkboxes.count();
    for (let i = 0; i < checkboxCount; i++) {
      const checkbox = checkboxes.nth(i);
      if (!(await checkbox.isChecked())) {
        await checkbox.check();
      }
    }
  }

  async applyFilterOnLmmPageTillClass(centerName, taxonomyName, className) {

    const selectDropdownValue = async (dropdown, value) => {
      await this.page.waitForTimeout(500); //required to load the dropdown
      await slowExpect(dropdown, `Verify ${dropdown} is visible`).toBeVisible();
      await dropdown.click();
      await this.page.waitForTimeout(500); //required to load the dropdown
      if (await this.searchTextFeildLmm.isHidden()) {
        await dropdown.click();
      }
      await expect(this.searchTextFeildLmm, `Verify search field is visible`).toBeVisible();
      await this.searchTextFeildLmm.fill(value);
      await this.lmmSelectOption(value).click();
    };
    // Select center
    await selectDropdownValue(this.centerLmmDropDown, centerName);
    // Select taxonomy
    await selectDropdownValue(this.taxonamyNameLmmDropDown, taxonomyName);
    // Select center
    await selectDropdownValue(this.classLmmDropDown, className);
    await expect(this.cancelFilterButton, "Verify cancel filter button is visible").toBeVisible();
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
  }

  async validateFilteronLmmPageTillClass(centerName, taxonomyName, className) {
    await expect(this.centerNameListed(centerName), "Verify center name is listed after apply filter button").toBeVisible();
    await this.classHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    await expect(this.taxonomyNameListed(taxonomyName), "Verify taxonomy name is listed after apply filter button").toBeVisible();
    await expect(this.classNameListed(className), "Verify class name is listed after apply filter button").toBeVisible();
  }


  async applyFilterOnLmmPageTillTopic(subjectName, superTopicName, topicName) {

    const selectDropdownValue = async (dropdown, value) => {
      await this.page.waitForTimeout(500); //required to load the dropdown
      await slowExpect(dropdown, `Verify ${dropdown} is visible`).toBeVisible();
      await dropdown.click();
      await this.page.waitForTimeout(500); //required to load the dropdown
      if (await this.searchTextFeildLmm.isHidden()) {
        await dropdown.click();
      }
      await expect(this.searchTextFeildLmm, `Verify search field is visible`).toBeVisible();
      await this.searchTextFeildLmm.fill(value);
      await this.lmmSelectOption(value).click();
    };
    // Select center
    await selectDropdownValue(this.subjectLmmDropDown, subjectName);
    // Select taxonomy
    await selectDropdownValue(this.superTopicLmmDropDown, superTopicName);
    // Select center
    await selectDropdownValue(this.topicLmmDropDown, topicName);
    await expect(this.cancelFilterButton, "Verify cancel filter button is visible").toBeVisible();
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
  }
  async updateStatusToInActive(contentNewName: string, status: string) {
    await slowExpect(this.editButton, "Verify edit button is visible").toBeVisible();
    await customExpect(15000)(this.searchInput, "Verify search input field is visible").toBeVisible();
    await this.searchInput.click();
    await this.searchInput.fill(contentNewName);
    await this.page.waitForTimeout(1000); // to load the search
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();
    await this.page.waitForTimeout(2000); // to load the search
    await slowExpect(this.uploadedContent(contentNewName)).toBeVisible();

    // Verify and click the edit button
    await expect(this.editButton, "Verify edit button is visible").toBeVisible();
    await this.editButton.click();

    const selectDropdownValue = async (dropdown, value) => {
      await this.page.waitForTimeout(500); //required to load the dropdown
      await slowExpect(dropdown, `Verify ${dropdown} is visible`).toBeVisible();
      await dropdown.click();
      await this.page.waitForTimeout(1000); //required to load the dropdown
      if (await this.searchTextFeild.isHidden()) {
        await dropdown.click();
      }
      await expect(this.searchTextFeild, `Verify search field is visible`).toBeVisible();
      await this.searchTextFeild.fill(value);

      await this.multiTopologySelectOption(value).click();
    };
    // await this.taxonamyNameHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    await selectDropdownValue(this.statusActive, status);
    await expect(this.saveButton, "Verify save button is visible").toBeVisible();
    await expect(this.cancelTopologyButton, "Verify cancel topology button is visible").toBeVisible();
    await this.saveButton.click();
  }

  async validateSearchWithContentId(contentName) {
    const contentIdLocator = await this.contentId;
    const contentID = await contentIdLocator.textContent() ?? "";

    await expect(this.clearButton, "Verify clear button is visible").toBeVisible();
    await this.clearButton.click();
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    await this.page.waitForLoadState('networkidle');
    await expect(this.searchInput, "Verify search input field is visible").toBeVisible();
    await this.searchInput.click();
    await this.searchInput.fill(contentID);
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();
    await this.page.waitForTimeout(1000); // to load the search
    await slowExpect(this.uploadedContent(contentName)).toBeVisible();
    return contentID;
  }



  async clickOnFilterButton() {
    await expect(this.filterDropDown, "Verify filter dropdown button is visible").toBeVisible();
    await this.filterDropDown.click();
  }


  async validateFilteronLmmPageTillTopic(subjectName, superTopicName, topicName) {
    await this.topicHeaderText.waitFor({ state: 'attached', timeout: 10000 });
    await this.topicHeaderText.scrollIntoViewIfNeeded();
    await expect(this.subjectNameListed(subjectName), "Verify subject name is listed after apply filter button").toBeVisible();
    await this.topicHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    await expect(this.superTopicNameListed(superTopicName), "Verify super topic name is listed after apply filter button").toBeVisible();
    await expect(this.topicListed(topicName), "Verify topic name is listed after apply filter button").toBeVisible();
  }



  async validateSearchWithSort(contentName) {

    await expect(this.clearButton, "Verify clear button is visible").toBeVisible();
    await this.clearButton.click();
    await this.page.reload();

    await this.page.waitForLoadState('networkidle');
    await expect(this.searchInput, "Verify search input field is visible").toBeVisible();
    await this.searchInput.click();
    await this.searchInput.fill(contentName);
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();

    await expect(this.sortDropDown, "Verify search input field is visible").toBeVisible();
    await this.sortDropDown.click();
    await expect(this.lastModifiedOption, "Verify last modified option is visible").toBeVisible();
    await expect(this.lastAddedOption, "Verify last added option is visible").toBeVisible();
    await expect(this.relevanceOption, "Verify relevance option is visible").toBeVisible();
    await this.relevanceOption.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.contentName).toBeVisible();
  }

  async validateTabNaviagtions() {
    await expect(this.studyMaterialTab, "Verify study material tab is visible").toBeVisible();
    await expect(this.recordedContentTab, "Verify recorded content tab is visible").toBeVisible();
    await expect(this.liveLectureRecordingTab, "Verify live lecture recording tab is visible").toBeVisible();
    await customExpect(15000)(this.editButton, "Verify edit button is visible").toBeVisible();

    await this.studyMaterialTab.click();
    await this.page.waitForTimeout(1000); //required to load the page
    await this.subTypeHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    await expect(this.typeNameStudyMaterailListed, "Verify study material type name is listed as navigated to study material tab").toBeVisible();

    await expect(this.recordedContentTab, "Verify recorded content tab is visible").toBeVisible();
    await this.recordedContentTab.click();
    await this.page.waitForTimeout(1000); //required to load the page
    await this.subTypeHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    await expect(this.typeNameRecordedContentListed, "Verify recorded content type name is listed as navigated to study material tab").toBeVisible();

    await expect(this.liveLectureRecordingTab, "Verify live lecture recording tab is visible").toBeVisible();
    await this.liveLectureRecordingTab.click();
    await this.page.waitForTimeout(1000); //required to load the page
    await this.subTypeHeaderText.scrollIntoViewIfNeeded({ timeout: 10000 });
    await expect(this.typeNameLiveLectureListed, "Verify live lecture type name is listed as navigated to study material tab").toBeVisible();
    await expect(this.allTab, "Verify all tab is visible").toBeVisible();
    await this.allTab.click();
  }
  async validateReplaceFile(file: string) {
    await expect(this.replaceIcon, "Verify replace icon is visible").toBeVisible();
    await this.replaceIcon.click();

    await expect(this.browseFilePopUpTitle, "Verify browse file for replace Study material pop up title is visible").toBeVisible();
    await expect(this.uploadTheSameFormatText, "Verify upload the same foematbeing replaced is visible").toBeVisible();
    await expect(this.replaceUploadButton, "Verify replace upload button is not enabled").toBeDisabled();

    await expect(this.browseFileButton, "Verify browse file button is visible").toBeVisible();
    await this.inputFile.setInputFiles(file);
    await expect(this.replaceUploadButton, "Verify replace upload button is enabled").toBeEnabled();
    await this.replaceUploadButton.click();
    await slowExpect(this.uploadedSuccessfullyText, "Verify uploaded successfully is visible").toBeVisible();
  }

  async validatePreviewUploadedContent(contentNewName: string) {
    // await slowExpect(this.uploadedContent(contentNewName)).toBeVisible();
    await this.uploadedContent(contentNewName).click();
    await slowExpect(this.contentFileOpen, "Verify content file open is visible").toBeVisible();

  }


  async verifyBulkUploadNewContent(files: string[], ContentCSVFile: string, contentNameFirst, contanetNameSecond) {
    await expect(this.learningAndManagementText, "Verify learning and management text is visible").toBeVisible();


    await expect(this.UploadButton, "Verify upload button is visible").toBeVisible();
    await this.UploadButton.click();
    await expect(this.newUploadContent, "Verify new upload content is visible").toBeVisible();
    await this.newUploadContent.click();

    await expect(this.browseFileButton, "Verify browse file button is visible").toBeVisible();
    // Ensure files is handled as an array
    const filesArray = Array.isArray(files) ? files : [files];
    await this.fileInput.setInputFiles(files);

    await expect(this.uploadCsvText, "Verify upload csv text is visible").toBeVisible();
    await this.csvUploadFileInput.setInputFiles(ContentCSVFile);


    await expect(this.filesSelectedText, "Verify files selected text is visible").toBeVisible();
    await expect(this.validateButton, "Verify validate button is visible").toBeVisible();
    await this.validateButton.click();
    await expect(this.metaDataFilledText, "Verify meta data filled successfully text is visible").toBeVisible();
    await expect(this.typeFieldText, "Verify type filed text is visible").toBeVisible();

    const contentName1Loc = await this.contentNameInput;
    const contentName1 = await contentName1Loc.textContent() ?? "";

    const contentName2Loc = await this.contentNameInput2;
    const contentName2 = await contentName2Loc.textContent() ?? "";
    await expect(this.contentNameInput, "Verify content name is visble").toBeVisible();
    await expect(this.contentNameInput2, "Verify content name is visble").toBeVisible();

    await expect(this.contentNameInput, "Verify content name input field is visble").toBeVisible();
    await this.contentNameInput.dblclick();
    await this.newContentInput.fill(contentNameFirst);
    await expect(this.learningAndManagementText, "Verify type filed text is visible").toBeVisible();
    await this.learningAndManagementText.click();

    await expect(this.contentNameInput2, "Verify content name input field is visble").toBeVisible();
    await this.contentNameInput2.dblclick();
    await this.newContentInput.fill(contanetNameSecond);
    await expect(this.typeFieldText, "Verify type filed text is visible").toBeVisible();
    await this.typeFieldText.click();
    await expect(this.contentUploadButton, "Verify content upload button is visible").toBeVisible();
    await this.contentUploadButton.click();
    await slowExpect(this.uploadedSuccessfullyText).toBeVisible();

  }

}