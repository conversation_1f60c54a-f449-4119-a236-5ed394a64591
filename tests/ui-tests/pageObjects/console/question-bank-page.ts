import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import exp from 'constants';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';


const QuestionBankPageUrl = '/content-management/taxonomy?tabs=QB'

export class QuestionBankPage extends ICPage {

    readonly uploadQBMAppingaTitleText: Locator;
    readonly viewMappingsButton: Locator;
    readonly uploadQBMappingButton: Locator;
    readonly downloadTemplateButton: Locator;
    readonly streamDropDownQb: Locator;
    readonly sessionDropDownQb: Locator;
    readonly classDropDownQb: Locator;
    readonly streamDropDownCenter: Locator;
    readonly sessionDropDownCenter: Locator;
    readonly classDropDownCenter: Locator;
    readonly searchTextFeild: Locator;
    readonly centerDropdown: Locator;
    readonly streamName: (streamName: string) => Locator;
    readonly className: (className: string) => Locator;
    readonly sessionName: (sessionName: string) => Locator;
    readonly centerName: (centerName: string) => Locator;
    readonly editAndViewQBMappingsText: Locator;
    readonly downloadMappingsButton: Locator;
    readonly nextButton: Locator;
    readonly uploadMappingsButton: Locator;
    readonly taxonamyNameTitle: Locator;
    readonly editButton: Locator;
    readonly centerSubjectDropDown: Locator;
    readonly centerSuperTopicDropDown: Locator;
    readonly centerTopicDropDown: Locator;
    readonly subjectName: (subjectName: string) => Locator;
    readonly superTopicName: (superTopicName: string) => Locator;
    readonly topicName: (topicName: string) => Locator;
    readonly showMappingsButton: Locator;
    readonly cancelButton: Locator;

    readonly qbStreamText: Locator;
    readonly qbClassText: Locator;
    readonly qbSubjectText: Locator;
    readonly qbTopicText: Locator;
    readonly qbSubTopicText: Locator;
    readonly centerSubjectText: Locator;
    readonly editIcon: Locator;
    readonly saveIcon: Locator;
    readonly cancelIcon: Locator;
    readonly physicsDropDown: Locator;
    readonly centerSubjectSelect: Locator;
    readonly selectAllCheckBox: Locator;
    readonly delectSelectedButton: Locator;
    readonly delectSelecteMappingsTitleText: Locator;
    readonly cancelPopUpButton: Locator;
    readonly selectFirstCheckBox: Locator;
    readonly subjectSelect: (subjectName: string) => Locator;
    readonly searchFeild: Locator;




    constructor(page: Page, isMobile: boolean) {
        super(page, QuestionBankPageUrl, isMobile);
        this.uploadQBMAppingaTitleText = page.getByText('Upload QB MappingsView');
        this.viewMappingsButton = page.getByText('View Mappings');
        this.uploadQBMappingButton = page.locator("//*[text()='Upload QB Mapping']");
        this.downloadTemplateButton = page.getByRole('button', { name: 'Download Template' });
        this.searchTextFeild = page.getByPlaceholder('Search...');

        this.streamDropDownQb = page.locator('div').filter({ hasText: /^CenterQBStreamAllSessionAllClassAll$/ }).getByRole('img').nth(1);
        this.sessionDropDownQb = page.locator('div').filter({ hasText: /^CenterQBStreamJEE AdvancedSessionAllClassAll$/ }).getByTestId('session-dropdown');
        this.classDropDownQb = page.getByTestId('class-dropdown').first();
        this.centerDropdown = page.locator('div').filter({ hasText: /^CenterAll$/ }).getByRole('img');
        this.streamDropDownCenter = page.locator('div').filter({ hasText: /^StreamAll$/ }).getByRole('img');
        this.sessionDropDownCenter = page.locator('div').filter({ hasText: /^SessionAll$/ }).getByTestId('session-dropdown');
        this.classDropDownCenter = page.locator('div').filter({ hasText: /^ClassAll$/ }).getByRole('img');
        this.streamName = (streamName: string) => page.locator(`//*[contains(@data-testid,"stream-dropdown-option-") and text()='${streamName}']`);
        this.className = (className: string) => page.locator(`//*[contains(@data-testid,"class-dropdown-option-") and text()='${className}']`);
        this.sessionName = (sessionName: string) => page.locator(`//*[contains(@data-testid,"session-dropdown-option-") and text()='${sessionName}']`);
        this.centerName = (centerName: string) => page.locator(`//*[contains(@data-testid,"center_id-dropdown-option-") and text()='${centerName}']`);
        this.editAndViewQBMappingsText = page.getByText('Edit & View QB Mappings');
        this.downloadMappingsButton = page.locator('div').filter({ hasText: /^Download Mappings$/ }).first()
        this.nextButton = page.locator(`//*[text()='Next']`);
        this.uploadMappingsButton = page.getByText('Upload Mappings');
        this.taxonamyNameTitle = page.getByText('Taxonomy Name:');

        this.centerSubjectDropDown = page.getByTestId('subject-dropdown').getByRole('img');
        this.centerSuperTopicDropDown = page.getByTestId('super_topic-dropdown').getByRole('img');
        this.centerTopicDropDown = page.getByTestId('topic-dropdown').getByRole('img');
        this.subjectName = (subjectName: string) => page.locator(`//*[contains(@data-testid,"subject-dropdown-option-") and text()='${subjectName}']`);
        this.superTopicName = (superTopicName: string) => page.locator(`//*[contains(@data-testid,"super_topic-dropdown-option-") and text()='${superTopicName}']`);
        this.topicName = (topicName: string) => page.locator(`//*[contains(@data-testid,"topic-dropdown-option-") and text()='${topicName}']`);
        this.showMappingsButton = page.getByTestId('apply-filter-panel');
        this.cancelButton = page.getByTestId('cancel-filter-panel');

        this.qbStreamText = page.getByText('QB Stream');
        this.qbClassText = page.getByText('QB Class');
        this.qbSubjectText = page.getByText('QB Subject');
        this.qbTopicText = page.getByText('QB Topic');
        this.qbSubTopicText = page.getByText('QB Sub Topic');
        this.centerSubjectText = page.getByText('Center Subject', { exact: true });
        this.editIcon = page.locator(`//img[@alt="edit"]`).first();
        this.saveIcon = page.getByRole('img', { name: 'save' });
        this.cancelIcon = page.getByRole('img', { name: 'cancel' });
        this.physicsDropDown = page.getByRole('gridcell', { name: 'Physics down-arrow' }).getByRole('img');
        this.centerSubjectSelect = page.locator(`//*[contains(@id,"cell-destination_subject-")]//descendant::*[contains(@class,"whitespace-nowrap overflow-hidden text-ellipsi")]`).first();
        this.selectAllCheckBox = page.getByRole('columnheader', { name: 'QB Stream' }).getByLabel('Press Space to toggle all');
        this.delectSelectedButton = page.getByRole('button', { name: 'Delete Selected' });
        this.delectSelecteMappingsTitleText = page.getByText('Delete Selected Mappings?');
        this.cancelPopUpButton = page.getByRole('button', { name: 'Cancel' });
        this.selectFirstCheckBox = page.getByLabel('Press Space to toggle row selection (unchecked)').first();
        this.subjectSelect = (selectName: string) => page.locator(`//*[contains(@class,"overflow-scroll")]//*[text()='${selectName}']`);
        this.searchFeild = page.getByPlaceholder('Search');


    }

    async verifyQBMappingPage() {
        await expect(this.uploadQBMAppingaTitleText, "Verify upload qb mapping title text is visible").toBeVisible();
        await expect(this.viewMappingsButton, "Verify view mapping button is visible").toBeVisible();
        await expect(this.uploadQBMappingButton, "Verify upload qb mapping button is disabled").toBeDisabled();
        await expect(this.downloadTemplateButton, "Verify download template button is visible").toBeVisible();
    }

    async selectQuestionBankDropdown(streamName, session, className) {

        await expect(this.streamDropDownQb, "Verify stream dropdown under question bank is visible").toBeVisible();
        await this.streamDropDownQb.click();
        await expect(this.searchTextFeild, "Verify qb search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(streamName);
        await this.streamName(streamName).click();
        await this.page.waitForTimeout(1000);

        await slowExpect(this.sessionDropDownQb, "Verify session dropdown under question bank is visible").toBeVisible();
        await this.sessionDropDownQb.click();
        await expect(this.searchTextFeild, "Verify qb search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(session);
        await this.sessionName(session).click();

        await slowExpect(this.classDropDownQb, "Verify class dropdown under question bank is visible").toBeVisible();
        await this.classDropDownQb.click();
        await expect(this.searchTextFeild, "Verify qb search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(className);
        await this.className(className).click();
    }


    async selectCenterDropdown(centerName, streamName, session, className) {
        await slowExpect(this.centerDropdown, "Verify stream dropdown under question bank is visible").toBeVisible();
        await this.centerDropdown.click();
        await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(centerName);
        await this.centerName(centerName).click();

        await slowExpect(this.streamDropDownCenter, "Verify stream dropdown under center is visible").toBeVisible();
        await this.streamDropDownCenter.click();
        await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(streamName);
        await this.streamName(streamName).click();

        await slowExpect(this.sessionDropDownCenter, "Verify session dropdown under center is visible").toBeVisible();
        await this.sessionDropDownCenter.click();
        await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(session);
        await this.sessionName(session).click();

        await expect(this.classDropDownCenter, "Verify class dropdown under center is visible").toBeVisible();
        await this.classDropDownCenter.click();
        await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(className);
        await this.className(className).click();
    }

    async verifyUploadQBMappingButtonEnabled() {
        await expect(this.uploadQBMappingButton, "Verify upload qb mapping title text is Enabled").toBeEnabled();
    }

    async clickOnViewMappingsButtonAndVerify() {
        await expect(this.viewMappingsButton, "Verify view mappings button is visible").toBeVisible();
        await this.viewMappingsButton.click();
        await expect(this.editAndViewQBMappingsText, "Verify edit and view qb mappings text is visible").toBeVisible();
        await expect(this.downloadMappingsButton, "Verify download mappings text is visible").toBeVisible();
        await expect(this.nextButton, "Verify next button is disabled").toBeDisabled();
        await expect(this.uploadMappingsButton, "Verify upload mappings button is visible").toBeVisible();
    }

    async verifyNextButtonEnabledAndClick() {
        await expect(this.nextButton, "Verify next button is Enabled").toBeEnabled();
        await this.nextButton.click();
    }

    async selectAndFilter(subjectName, superTopicName, topicName) {
        await slowExpect(this.centerSubjectDropDown, "Verify center subject dropdown is visible").toBeVisible();
        await this.centerSubjectDropDown.click();
        await expect(this.searchTextFeild, "Verify  search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(subjectName);
        await this.subjectName(subjectName).click();

        await slowExpect(this.centerSuperTopicDropDown, "Verify center super topic dropdown is visible").toBeVisible();
        await this.centerSuperTopicDropDown.click();
        await expect(this.searchTextFeild, "Verify  search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(superTopicName);
        await this.superTopicName(superTopicName).click();

        await slowExpect(this.centerTopicDropDown, "Verify center topic dropdown is visible").toBeVisible();
        await this.centerTopicDropDown.click();
        await expect(this.searchTextFeild, "Verify  search text feild is visible").toBeVisible();
        await this.searchTextFeild.fill(topicName);
        await this.topicName(topicName).click();
    }

    async verifyShowMappingsButtonEnabledAndClick() {
        await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
        await expect(this.showMappingsButton, "Verify show mappings button is enabled").toBeEnabled();
        await this.showMappingsButton.click();
    }

    async verifyAfterFilterPage() {
        await expect(this.qbStreamText, "Verify qb stream text is visible").toBeVisible();
        await expect(this.qbClassText, "Verify qb class text is visible").toBeVisible();
        await expect(this.qbSubTopicText, "Verify qb sub topic text is visible").toBeVisible();
        await expect(this.qbSubjectText, "Verify qb subject text is visible").toBeVisible();
        await expect(this.qbTopicText, "Verify qb topic text is visible").toBeVisible();
        await expect(this.centerSubjectText, "Verify center subject text is visible").toBeVisible();
    }

    async editDestinationData(subjectName) {
        await expect(this.editIcon, "Verify edit icon is visible").toBeVisible();
        await this.editIcon.click();
        await expect(this.saveIcon, "Verify save icon is visible").toBeVisible();
        await expect(this.cancelIcon, "Verify cancel icon is visible").toBeVisible();
        await expect(this.centerSubjectSelect, "Verify center subject dropdown is visible").toBeVisible();
        await this.centerSubjectSelect.click();
        await expect(this.subjectSelect(subjectName), "Verify subject name is visible").toBeVisible();
        await this.subjectSelect(subjectName).click();
        await this.page.waitForLoadState('networkidle');
        await expect(this.centerSubjectSelect, "Verify center subject dropdown is visible").toBeVisible();
        await this.centerSubjectSelect.click();
        await slowExpect(this.searchFeild, "Verify  search text feild is visible").toBeVisible();
        await this.searchFeild.fill(subjectName);
        await expect(this.subjectSelect(subjectName), "Verify subject name is visible").toBeVisible();
        await this.subjectSelect(subjectName).click();
        await expect(this.cancelIcon, "Verify cancel icon is visible").toBeVisible();
        await this.cancelIcon.click();
    }

    async verifyDeleteAllPopUp() {
        await expect(this.selectAllCheckBox, "Verify select all checkbox is visible").toBeVisible();
        this.selectAllCheckBox.check();
        await expect(this.delectSelectedButton, "Verify delete selected button is visible").toBeVisible();
        this.delectSelectedButton.click();
        await expect(this.delectSelecteMappingsTitleText, "Verify delete selected mappings title text is visible").toBeVisible();
        await expect(this.cancelPopUpButton, "Verify cancel button is visible").toBeVisible();
        this.cancelPopUpButton.click();
        await expect(this.cancelPopUpButton, "Verify cancel button is hidden").toBeHidden();
        await expect(this.selectAllCheckBox, "Verify all checkbox is visible").toBeVisible();
        this.selectAllCheckBox.uncheck();
    }

    async verifySingleDeletePopUp() {
        await expect(this.selectFirstCheckBox, "Verify select  checkbox is visible").toBeVisible();
        this.selectFirstCheckBox.check();
        await expect(this.delectSelectedButton, "Verify delete selected button is visible").toBeVisible();
        this.delectSelectedButton.click();
        await expect(this.delectSelecteMappingsTitleText, "Verify delete selected mappings title text is visible").toBeVisible();
        await expect(this.cancelPopUpButton, "Verify cancel button is visible").toBeVisible();
        this.cancelPopUpButton.click();
        await this.page.waitForTimeout(500); //required 
        if (await this.cancelPopUpButton.isVisible()) {
            await slowExpect(this.cancelPopUpButton, `verify cancel button is still visible`).toBeVisible();
            await this.cancelPopUpButton.click();
        }
        await expect(this.cancelPopUpButton, "Verify cancel button is hidden").toBeHidden();
        await expect(this.selectFirstCheckBox, "Verify selected checkbox is visible").toBeVisible();
        this.selectFirstCheckBox.uncheck();
    }

}