import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const TeacherEnterClassPageUrl = '/live/teacher-liveclass'

export class TeacherEnterClassPage extends ICPage {

  readonly goLiveButton: Locator;
  readonly liveLectureText: Locator;
  readonly classCardName: (className: string) => Locator;
  readonly mentorshipSessionText: Locator;
  readonly materialWrapper: Locator;
  readonly lectureMaterial: (className: string) => Locator;
  readonly enterClassButton: Locator;
  readonly popUpDiv: Locator;
  readonly popUpCrossIcon: Locator;
  readonly goToMyScheduleButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, TeacherEnterClassPageUrl, isMobile);
    this.goLiveButton = page.getByText('Go Live');
    this.liveLectureText = page.getByText('LIVE_LECTURE');
    this.classCardName = (className: string) => page.getByText(`${className}`).first();
    this.mentorshipSessionText = page.getByText('MENTORSHIP_SESSION');
    this.materialWrapper = page.getByTestId('materials-wrapper');
    this.lectureMaterial = (className: string) => this.materialWrapper.locator(`//*[contains(text(),'${className}')]`).first();
    this.enterClassButton = page.getByText('Enter Class');
    this.popUpDiv = page.getByText('There is still time for class to startYou can join our classes 3 hrs before');
    this.popUpCrossIcon = page.getByTestId('modal-cross-icon').locator('path');
    this.goToMyScheduleButton = page.getByTestId('modal-element').getByTestId('cta-button');
  }

  async naviageToLiveClass(className) {
    await customExpect(15000)(this.classCardName(className), 'live class card display name is visible').toBeVisible();
    await expect(this.liveLectureText, "verify live lecture text is visible").toBeVisible();
    await this.goLiveButton.click();
  }

  async teacherJoingTheClass(meetingId) {
    await this.page.goto(`${this.url}?meetingId=${meetingId}`);
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.goLiveButton, 'Go live button is visible').toBeVisible();
    await this.goLiveButton.click();
    await this.page.waitForTimeout(1000); //wait is requried as time taking to show teacher name
  }

  async teacherJoiningMentorshipSession(meetingId) {
    await this.page.goto(`${this.url}?meetingId=${meetingId}`);
    await this.page.waitForLoadState('networkidle');
    await customExpect(15000)(this.mentorshipSessionText, 'Mentorship session text is visible').toBeVisible();
    await slowExpect(this.goLiveButton, 'Go live button is visible').toBeVisible();
    await this.goLiveButton.click();
  }

  async verifyUploadFileInPreClasPage(meetingId, className) {
    await this.page.goto(`${this.url}?meetingId=${meetingId}`);
    await slowExpect(this.liveLectureText, 'lecture material text is visible').toBeVisible();
    await slowExpect(this.lectureMaterial(className), 'Self File upload file name is visible').toBeVisible();
  }
  async teacherClickOnGoLiveButton() {
    await this.page.waitForLoadState('networkidle');
    await customExpect(15000)(this.goLiveButton, 'Go live button is visible').toBeVisible();
    await this.goLiveButton.click();
    await this.page.waitForTimeout(1000); //wait is requried as time taking to show teacher name
  }
  async verifyEarlyJoinPopUp() {
    await expect(this.popUpDiv, 'Verify early join pop up is visible').toBeVisible();
    await expect(this.popUpCrossIcon, 'Verify pop up cross icon is visible').toBeVisible();
    await expect(this.goToMyScheduleButton, 'Verify go to my schedule button is visible').toBeVisible();
  }
}