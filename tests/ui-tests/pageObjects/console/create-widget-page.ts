
import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreateWidgetPageUrl = '/page-management/widget/create'

export class CreateWidgetPage extends ICPage {

    readonly apiLoader: Locator;
    readonly createWidgetTitleText: Locator;
    readonly widgetDetailsTab: Locator;
    readonly widgetConfigurationTab: Locator;
    readonly widgetReviewTab: Locator;
    readonly widgetNameFeild: Locator;
    readonly widgetContentTypeDropdown: Locator;
    readonly widgetTypeSelectDropdown: Locator;
    readonly searchFeild: Locator;
    readonly selectDropdown: (selectName: string) => Locator;
    readonly widgetStartTime: Locator;
    readonly widgetEndTime: Locator;
    readonly widgetCriteriaText: Locator;
    readonly selectCriteriaTypeDropdown: Locator;
    readonly selectCriteriaOptionDropdown: Locator;
    readonly criteriaSelectOptionTrue: Locator;
    readonly nextButton: Locator;
    readonly cancelButton: Locator;
    readonly widgetPlatformText: Locator;
    readonly desktopPlatform: Locator;
    readonly titleNameField: Locator;
    readonly imageDataMobileTitle: Locator;
    readonly darkThemeMobile: Locator;
    readonly lightThemeMobile: Locator;

    readonly imageDataDesktopTitle: Locator;
    readonly darkThemeDesktop: Locator;
    readonly lightThemeDesktop: Locator;
    readonly imageURLDarkThemeMobile: Locator;
    readonly imageURLLightThemeMobile: Locator;

    readonly imageURLDarkThemeDesktop: Locator;
    readonly imageURLLightThemeDesktop: Locator;
    readonly backgroundColorText: Locator;
    readonly backgroundSelectDropdown: Locator;
    readonly primaryColorSelect: Locator;
    readonly actionText: Locator;
    readonly selectActionDropdown: Locator;
    readonly selectActionDropdownInCard: (sectionIndex: number, cardIndex: number) => Locator;
    readonly navigationActionSelect: Locator;
    readonly navigationUriText: Locator;
    readonly navigationUriField: Locator;
    readonly cta1LabelText: Locator;
    readonly cta1LabelField: Locator;

    readonly cta1LabelActionText: Locator;
    readonly ctaActionDropdown: Locator;

    readonly cta1LabelURLText: Locator;
    readonly cta1LabelURLField: Locator;
    readonly externalNavigationOption: Locator;

    readonly submitForApprovalButton: Locator;
    readonly backButton: Locator;
    readonly widgetDetailsTitleText: Locator;
    readonly widgetCreatedSuccessfullyToastMessage: Locator;
    readonly widgetEditedSuccessfullyToastMessage: Locator;
    readonly removeActionButton: Locator;
    readonly addFilterDataButton: Locator;
    readonly filterDimensionNameFeild: Locator;
    readonly quickFilterOption: Locator;
    readonly quickFilterCheckbox: Locator;
    readonly createWidgetCtaButtonOption: Locator;
    readonly createWidgetCtaButtonCheckbox: Locator;
    readonly filterValueNameOption: Locator;
    readonly filterValueField: Locator;
    readonly addValueButton: Locator;
    readonly currentDefaultText: Locator;
    readonly currentDefaultValueFeild: Locator;
    readonly selectDefaultValueText: Locator;
    readonly selectDefaultValueDropDown: Locator;
    readonly classEleventhDropdown: Locator;
    readonly setDefaultButton: Locator;
    readonly singleSelectQuickFilterCTAsText: Locator;
    readonly ctaForClassEleventh: Locator;
    readonly ctaForClassTwelveth: Locator;
    readonly ctaLabelEleventh: Locator;
    readonly ctaLabelTwelveth: Locator;
    readonly filterPreferencesText: Locator;
    readonly removeStickyFilterCheckBox: Locator;
    readonly removeScrollToTopCheckBox: Locator;
    readonly widgetHeadingText: Locator;
    readonly widgetHeadingField: Locator;
    readonly cardDataButton: Locator;
    readonly cardDataOneText: Locator;
    readonly titleText: Locator;
    readonly titleField: Locator;
    readonly programModeText: Locator;
    readonly programModeField: Locator;
    readonly actualPriceText: Locator;
    readonly actualPriceField: Locator;
    readonly discountedPriceText: Locator;
    readonly discountedPriceField: Locator;

    readonly addProgramInfoText: Locator;
    readonly addProgramInfoField: Locator;
    readonly addButton: Locator;
    readonly cardCTALabelText: Locator;
    readonly cardCTALabelField: Locator;
    readonly filterDimensionText: Locator;
    readonly filterDimensionField: Locator;
    readonly filterDimensionValueText: Locator;
    readonly filterDimensionValueField: Locator;
    readonly selectActionClassDropdown: Locator;
    readonly navigationActionClass11Select: Locator;
    readonly navigationActionClass12Select: Locator;
    readonly navigationUri12Field: Locator;
    readonly navigationUriCardField: Locator;
    readonly addFilterCardValueButton: Locator;




    constructor(page: Page, isMobile: boolean) {
        super(page, CreateWidgetPageUrl, isMobile);

        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.createWidgetTitleText = page.getByRole('heading', { name: 'Create Widget' });
        this.widgetDetailsTab = page.getByText('Step 1: Widget Details');
        this.widgetConfigurationTab = page.getByText('Step 2: Widget Configuration');
        this.widgetReviewTab = page.getByText('Step 3: Widget Review');

        this.widgetReviewTab = page.getByText('Step 3: Widget Review');
        this.widgetNameFeild = page.locator('div').filter({ hasText: /^Widget Name$/ }).getByTestId('textInput')
        this.widgetContentTypeDropdown = page.locator('div').filter({ hasText: /^Static$/ }).nth(1);
        this.widgetTypeSelectDropdown = page.locator('div').filter({ hasText: /^Widget TypeSelect a Type$/ }).getByTestId('dropdown');
        this.searchFeild = page.getByPlaceholder('Search...');
        this.selectDropdown = (selectName: string) => page.getByText(`${selectName}`);
        this.widgetStartTime = page.getByText('Widget Start Time');
        this.widgetEndTime = page.getByText('Widget End Time');
        this.widgetCriteriaText = page.getByRole('heading', { name: 'Criteria 1:' });
        this.selectCriteriaTypeDropdown = page.getByText('Select Criteria Type');
        this.selectCriteriaOptionDropdown = page.getByText('Select Option');
        this.criteriaSelectOptionTrue = page.getByTestId('dropdown-option-true');
        this.nextButton = page.getByRole('button', { name: 'Next >' });
        this.cancelButton = page.locator('div').filter({ hasText: /^Cancel$/ }).getByRole('button');
        this.widgetPlatformText = page.getByText('Widget Platform');
        this.desktopPlatform = page.getByLabel('Desktop');
        this.titleNameField = page.locator('input[name="title"]');

        this.imageDataMobileTitle = page.getByRole('heading', { name: 'Image Data Mobile' });
        this.darkThemeMobile = page.getByRole('heading', { name: 'Theme - dark' }).first();
        this.lightThemeMobile = page.getByRole('heading', { name: 'Theme - light' }).first();
        this.imageDataDesktopTitle = page.getByRole('heading', { name: 'Image Data Desktop' });
        this.darkThemeDesktop = page.getByRole('heading', { name: 'Theme - dark' }).nth(1);
        this.lightThemeDesktop = page.getByRole('heading', { name: 'Theme - light' }).nth(1);

        this.imageURLDarkThemeMobile = page.getByPlaceholder('Enter Image URL').first();
        this.imageURLLightThemeMobile = page.getByPlaceholder('Enter Image URL').nth(1);

        this.imageURLDarkThemeDesktop = page.getByPlaceholder('Enter Image URL').nth(2);
        this.imageURLLightThemeDesktop = page.getByPlaceholder('Enter Image URL').nth(3);
        this.backgroundColorText = page.getByText('Background Color');
        this.backgroundSelectDropdown = page.locator('div').filter({ hasText: /^Background ColorSelect a Type$/ }).getByTestId('dropdown');
        this.primaryColorSelect = page.getByTestId('dropdown-option-bg-primary');
        this.actionText = page.getByText('Action', { exact: true }).first();
        this.selectActionDropdown = page.getByText('Select an action').first();
        this.selectActionDropdownInCard = (sectionIndex: number, cardIndex: number) => page.locator(`[data-rfd-draggable-id='section-${sectionIndex}-card-${cardIndex}']`).getByText('Select an action');
        this.navigationActionSelect = page.getByTestId('dropdown-option-NAVIGATION');
        this.navigationUriText = page.getByText('Navigation Uri');
        this.navigationUriField = page.getByPlaceholder('Navigation URL');
        this.cta1LabelText = page.getByText('CTA 1 Label');
        this.cta1LabelField = page.locator('input[name="firstCtaLabel"]');
        this.cta1LabelActionText = page.getByText('Action', { exact: true }).nth(1);
        this.ctaActionDropdown = page.getByText('Select an action').first();
        this.externalNavigationOption = page.getByTestId('dropdown-option-EXTERNAL_NAVIGATION');

        this.submitForApprovalButton = page.getByRole('button', { name: 'Submit For Approval' });
        this.backButton = page.getByRole('button', { name: '< Back' });
        this.widgetDetailsTitleText = page.getByRole('heading', { name: 'Widget Details' });
        this.widgetCreatedSuccessfullyToastMessage = page.getByText('Widget Created Successfully');
        this.widgetEditedSuccessfullyToastMessage = page.locator(`//*[contains(text(),"Widget Updated and Activated Successfully, Active")]`);
        this.removeActionButton = page.locator('div').filter({ hasText: /^ActionNavigationRemove Action$/ }).getByTestId('cta-button');
        this.addFilterDataButton = page.getByRole('button', { name: 'Add Filter Data' });
        this.filterDimensionNameFeild = page.getByPlaceholder('Please Enter Filter Dimension');
        this.quickFilterOption = page.getByText('Quick Filter');
        this.quickFilterCheckbox = page.getByTestId('checkbox-container').first();
        this.createWidgetCtaButtonOption = page.getByText('Cta button');
        this.createWidgetCtaButtonCheckbox = page.locator('div:nth-child(5) > .false > .Checkbox-module_checkmark-checkbox__U9w6g');
        this.filterValueNameOption = page.getByText('Filter Value');
        this.filterValueField = page.getByPlaceholder('Please Enter Filter Value');
        this.addValueButton = page.getByRole('button', { name: 'Add Value' });
        this.currentDefaultText = page.getByText('Current Default:');
        this.currentDefaultValueFeild = page.getByText('No Default Value Set');
        this.selectDefaultValueText = page.getByText('Select Default Value:');
        this.selectDefaultValueDropDown = page.locator('div').filter({ hasText: /^Select Default Value:Select a Default ValueSet DefaultRemove Default$/ }).getByTestId('dropdown');
        this.classEleventhDropdown = page.getByTestId('dropdown-option-Class 11');
        this.setDefaultButton = page.getByRole('button', { name: 'Set Default' });
        this.singleSelectQuickFilterCTAsText = page.getByRole('heading', { name: 'CTAs for single select quick' });
        this.ctaForClassEleventh = page.getByRole('heading', { name: 'CTA for "Class 11"' });
        this.ctaLabelEleventh = page.locator('//input[@name="cta-label-Class 11"]');
        this.ctaForClassTwelveth = page.getByRole('heading', { name: 'CTA for "Class 12"' });
        this.ctaLabelTwelveth = page.locator('//input[@name="cta-label-Class 12"]');

        this.filterPreferencesText = page.getByText('Filter Preference');
        this.removeStickyFilterCheckBox = page.locator('div').filter({ hasText: /^Remove Sticky filters$/ }).locator('span');
        this.removeScrollToTopCheckBox = page.locator('div').filter({ hasText: /^Remove Scroll To Top$/ }).locator('span');
        this.widgetHeadingText = page.getByText('Widget Heading');
        this.widgetHeadingField = page.locator('div').filter({ hasText: /^Widget Heading$/ }).getByRole('textbox');

        this.cardDataButton = page.locator(`//*[@class="flex items-center gap-4 flex-1"]//*[text()='Card 1']`);
        this.cardDataOneText = page.getByRole('heading', { name: 'Card Data' })
        this.titleText = page.getByText('Title*', { exact: true });
        this.titleField = page.locator('div').filter({ hasText: /^Title\*$/ }).getByRole('textbox');

        this.programModeText = page.getByText('Program Mode*');
        this.programModeField = page.locator('input[name="program_mode"]');
        this.actualPriceText = page.getByText('Actual Price');
        this.actualPriceField = page.locator('input[name="actual_price"]');
        this.discountedPriceText = page.getByText('Discounted Price*');
        this.discountedPriceField = page.locator('//input[@name="discounted_price"]');

        this.addButton = page.getByRole('button', { name: 'Add', exact: true }).nth(2);
        this.cardCTALabelText = page.getByText('CTA Label*');
        this.cardCTALabelField = page.locator('input[name="label"]');
        this.addProgramInfoText = page.getByText('Add Program Info');
        this.addProgramInfoField = page.locator('div').filter({ hasText: /^Add Program Info$/ }).getByRole('textbox');
        // getByRole('button', { name: 'Add Filter Data' });
        this.filterDimensionText = page.getByText('Filter Dimension').nth(1);
        this.filterDimensionField = page.locator('div').filter({ hasText: /^Filter DimensionFilter ValueAdd ValueDelete FilterAdd Filter Dimenion$/ }).getByPlaceholder('Please Enter Filter Dimension');
        this.filterDimensionValueText = page.getByText('Filter Value').nth(1);
        this.filterDimensionValueField = page.locator('div').filter({ hasText: /^Filter DimensionFilter ValueAdd ValueDelete FilterAdd Filter Dimenion$/ }).getByPlaceholder('Please Enter Filter Value');

        this.navigationActionClass11Select = page.locator('div').filter({ hasText: /^CTA for "Class 11"CTA LabelActionSelect a ValueRemove Action$/ }).locator('span');
        this.selectActionClassDropdown = page.getByText('Action', { exact: true }).first();
        this.navigationActionClass12Select = page.locator('div').filter({ hasText: /^CTA for "Class 12"CTA LabelActionSelect a ValueRemove Action$/ }).locator('span');
        this.navigationUri12Field = page.getByPlaceholder('Navigation URL').nth(1);
        this.navigationUriCardField = page.getByPlaceholder('Navigation URL').nth(2);
        this.addFilterCardValueButton = page.getByRole('button', { name: 'Add Value' }).nth(1)

    }

    /* verifying to create widget page */
    async verifyCreateWidgetPage() {
        await expect(this.createWidgetTitleText, "Verify create widget title text is visible").toBeVisible();
        await expect(this.widgetDetailsTab, "Verify widget details tab is visible").toBeVisible();
        await expect(this.widgetConfigurationTab, "Verify widget configuration tab is visible").toBeVisible();
        await expect(this.widgetReviewTab, "Verify widget review tab is visible").toBeVisible();
        await this.widgetDetailsTab.click();

    }


    /* enter widget details */
    async enterWidgetDetails(widgetName, widgetType, criteriaType) {
        await expect(this.widgetNameFeild, "Verify widget name feild is visible").toBeVisible();
        await this.widgetNameFeild.fill(widgetName);
        await expect(this.widgetTypeSelectDropdown, "Verify widget type select dropdown is visible").toBeVisible();
        await this.widgetTypeSelectDropdown.click();
        await expect(this.searchFeild, "Verify search feild is visible").toBeVisible();
        await this.searchFeild.fill(widgetType);
        await expect(this.selectDropdown(widgetType), "Verify selected widget type is visible").toBeVisible();
        await this.selectDropdown(widgetType).click();

        await expect(this.widgetStartTime, "Verify widget start time is visible").toBeVisible();
        await expect(this.widgetEndTime, "Verify widget end time is visible").toBeVisible();
        await expect(this.widgetCriteriaText, "Verify criteria 1 is visible").toBeVisible();
        await expect(this.widgetCriteriaText, "Verify criteria 1 is visible").toBeVisible();
        await expect(this.widgetPlatformText, "Verify widget platform is visible").toBeVisible();
        await expect(this.desktopPlatform, "Verify widget platform is checked").toBeChecked();
        await expect(this.selectCriteriaTypeDropdown, "Verify select criteria type dropdown is visible").toBeVisible();
        await this.selectCriteriaTypeDropdown.click();
        await expect(this.searchFeild, "Verify search feild is visible").toBeVisible();
        await this.searchFeild.fill(criteriaType);
        await expect(this.selectDropdown(criteriaType), "Verify selected criteria is visible").toBeVisible();
        await this.selectDropdown(criteriaType).click();

        await expect(this.selectCriteriaOptionDropdown, "Verify select criteria option dropdown is visible").toBeVisible();
        await this.selectCriteriaOptionDropdown.click();
        await expect(this.criteriaSelectOptionTrue, "Verify true option is visible").toBeVisible();
        await this.criteriaSelectOptionTrue.click();
    }

    async clickOnNextButton() {
        await expect(this.nextButton, "Verify next button is visible").toBeVisible();
        await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
        await this.nextButton.click();

    }


    async enterWidgetConfiguration(titleName, urlMobile, urlDesktop, naviagtionUrl, cta1LabelName) {
        await expect(this.titleNameField, "Verify title name feild is visible").toBeVisible();
        await this.titleNameField.fill(titleName);
        await expect(this.imageDataMobileTitle, "Verify image data mobile title is visible").toBeVisible();
        await expect(this.darkThemeMobile, "Verify dark theme mobile is visible").toBeVisible();
        await expect(this.imageURLDarkThemeMobile, "Verify dark theme image url mobile is visible").toBeVisible();
        await this.imageURLDarkThemeMobile.fill(urlMobile);
        await expect(this.lightThemeMobile, "Verify light theme mobile is visible").toBeVisible();
        await expect(this.imageURLLightThemeMobile, "Verify light theme image url mobile is visible").toBeVisible();
        await this.imageURLLightThemeMobile.fill(urlMobile);

        await expect(this.imageDataDesktopTitle, "Verify image data desktop title is visible").toBeVisible();
        await expect(this.darkThemeDesktop, "Verify dark theme desktop is visible").toBeVisible();
        await expect(this.imageURLDarkThemeDesktop, "Verify dark theme image url desktop is visible").toBeVisible();
        await this.imageURLDarkThemeDesktop.fill(urlDesktop);
        await expect(this.lightThemeDesktop, "Verify light theme desktop is visible").toBeVisible();
        await expect(this.imageURLLightThemeDesktop, "Verify light theme image url desktop is visible").toBeVisible();
        await expect(this.imageURLLightThemeDesktop, "Verify cancel button is visible").toBeVisible();
        await this.imageURLLightThemeDesktop.fill(urlDesktop);

        await expect(this.backgroundColorText, "Verify background color text is visible").toBeVisible();
        await expect(this.backgroundSelectDropdown, "Verify background color dropdown is visible").toBeVisible();
        await this.backgroundSelectDropdown.click();
        await expect(this.primaryColorSelect, "Verify primary background color option is visible").toBeVisible();
        await this.primaryColorSelect.click();

        await expect(this.actionText, "Verify action text is visible").toBeVisible();
        await expect(this.selectActionDropdown, "Verify select action dropdown is visible").toBeVisible();
        await this.selectActionDropdown.click();

        await expect(this.navigationActionSelect, "Verify navigation action option is visible").toBeVisible();
        await this.navigationActionSelect.click();

        await expect(this.navigationUriText, "Verify navigation uri text is visible").toBeVisible();
        await expect(this.navigationUriField, "Verify navigation url feild is visible").toBeVisible();
        await this.navigationUriField.fill(naviagtionUrl);

        await expect(this.cta1LabelText, "Verify cta 1 label text is visible").toBeVisible();
        await expect(this.cta1LabelField, "Verify cta 1 label feild is visible").toBeVisible();
        await this.cta1LabelField.fill(cta1LabelName);

        await expect(this.cta1LabelActionText, "Verify cta 1 action text is visible").toBeVisible();
        await expect(this.ctaActionDropdown, "Verify cta 1 action dropdown is visible").toBeVisible();
        await this.ctaActionDropdown.click();
        await expect(this.externalNavigationOption, "Verify external navigation optionis visible").toBeVisible();
        await this.externalNavigationOption.click();
    }

    async verifyWidgetReviewTab() {
        await slowExpect(this.widgetDetailsTitleText, "Verify widget details title text is visible").toBeVisible();
        await expect(this.backButton, "Verify back button is visible").toBeVisible();
        await expect(this.submitForApprovalButton, "Verify submit for approval button is visible").toBeVisible();
        await this.submitForApprovalButton.click();
    }

    async verifyWidgetCreatedSuccessfully() {
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.widgetCreatedSuccessfullyToastMessage, "Verify widget created successfully toast message is visible").toBeVisible();
    }

    async editWidget(widgetReName, naviagtionNewUrl) {
        await slowExpect(this.widgetNameFeild, "Verify widget name feild is visible").toBeVisible();
        await this.widgetNameFeild.fill(widgetReName);

        await expect(this.nextButton, "Verify next button is visible").toBeVisible();
        await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
        await this.nextButton.click();

        await slowExpect(this.removeActionButton, "Verify remove action button is visible").toBeVisible();
        await this.removeActionButton.click();

        await expect(this.actionText, "Verify action text is visible").toBeVisible();
        await expect(this.selectActionDropdown, "Verify select action dropdown is visible").toBeVisible();
        await this.selectActionDropdown.click();

        await expect(this.navigationActionSelect, "Verify navigation action option is visible").toBeVisible();
        await this.navigationActionSelect.click();


        await expect(this.navigationUriText, "Verify navigation uri text is visible").toBeVisible();
        await expect(this.navigationUriField, "Verify navigation url feild is visible").toBeVisible();
        await this.navigationUriField.fill(naviagtionNewUrl);
        await this.navigationUriText.click();

        await expect(this.nextButton, "Verify next button is visible").toBeVisible();
        await this.nextButton.click();

    }


    async verifyWidgetEditedSuccessfully() {
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.widgetEditedSuccessfullyToastMessage, "Verify widget edited successfully toast message is visible").toBeVisible();
    }

    /* verifying to create single filter plp page */
    async enterSingleFilterDimensionsValues(className11, className12, className) {
        await expect(this.addFilterDataButton, "Verify add filter button is visible").toBeVisible();
        await this.addFilterDataButton.click();
        await expect(this.filterDimensionNameFeild, "Verify filter dimensions name feild is visible").toBeVisible();
        await this.filterDimensionNameFeild.fill(className);
        await expect(this.quickFilterOption, "Verify quick filter option name is visible").toBeVisible();
        await expect(this.quickFilterCheckbox, "Verify quick filter checkbox is visible").toBeVisible();
        await this.quickFilterCheckbox.click();

        await expect(this.createWidgetCtaButtonOption, "Verify create widget cta button option name is visible").toBeVisible();
        await expect(this.createWidgetCtaButtonCheckbox, "Verify create widget cta button checkbox is visible").toBeVisible();
        await this.createWidgetCtaButtonCheckbox.click();

        await expect(this.filterValueNameOption, "Verify filter value text is visible").toBeVisible();
        await expect(this.filterValueField, "Verify filter value feild is visible").toBeVisible();
        await this.filterValueField.fill(className11);
        await expect(this.addValueButton, "Verify add value button is visible").toBeVisible();
        await this.addValueButton.click();

        await expect(this.filterValueNameOption, "Verify filter value text is visible").toBeVisible();
        await expect(this.filterValueField, "Verify filter value feild is visible").toBeVisible();
        await this.filterValueField.fill(className12);
        await expect(this.addValueButton, "Verify add value button is visible").toBeVisible();
        await this.addValueButton.click();

        await expect(this.selectDefaultValueText, "Verify select default value text is visible").toBeVisible();
        await expect(this.selectDefaultValueDropDown, "Verify select default value dropdwon is visible").toBeVisible();
        await this.selectDefaultValueDropDown.click();

        await expect(this.classEleventhDropdown, "Verify class 11th dropdwon is visible").toBeVisible();
        await this.classEleventhDropdown.click();
        await expect(this.setDefaultButton, "Verify set default button is visible").toBeVisible();
        await this.setDefaultButton.click();

    }

    /* enter to cta quick filter plp page */
    async enterCTAsQuickFilterValues(ctaLabelName, navigation11Url, navigation2Url) {
        await expect(this.singleSelectQuickFilterCTAsText, "Verify cta for single select quick filter text is visible").toBeVisible();

        await expect(this.ctaForClassEleventh, "Verify cta label for class 11th text is visible").toBeVisible();
        await expect(this.ctaLabelEleventh, "Verify cta label for class 11th feild is visible").toBeVisible();
        await this.ctaLabelEleventh.fill(ctaLabelName);

        await expect(this.selectActionClassDropdown, "Verify navigation action option is visible").toBeVisible();
        await expect(this.navigationActionClass11Select, "Verify navigation action for class 11 is visible").toBeVisible();
        await this.navigationActionClass11Select.click();
        await expect(this.navigationActionSelect, "Verify navigation action option is visible").toBeVisible();
        await this.navigationActionSelect.click();

        await expect(this.navigationUriText, "Verify navigation uri text is visible").toBeVisible();
        await expect(this.navigationUriField, "Verify navigation url feild is visible").toBeVisible();
        await this.navigationUriField.fill(navigation11Url);

        await expect(this.ctaForClassTwelveth, "Verify cta label for class 11th text is visible").toBeVisible();
        await expect(this.ctaLabelTwelveth, "Verify cta label for class 12th text is visible").toBeVisible();
        await this.ctaLabelTwelveth.fill(ctaLabelName);
        await expect(this.selectActionClassDropdown, "Verify navigation action option is visible").toBeVisible();
        await expect(this.navigationActionClass12Select, "Verify navigation action for class 12 is visible").toBeVisible();
        await this.navigationActionClass12Select.click();
        await expect(this.navigationActionSelect, "Verify navigation action option is visible").toBeVisible();
        await this.navigationActionSelect.click();
        await expect(this.navigationUri12Field, "Verify navigation url feild is visible").toBeVisible();
        await this.navigationUri12Field.fill(navigation2Url);

    }

    /* enter filter prefernces page */
    async enterFilterPreferences(widgetHeadingName) {
        await expect(this.filterPreferencesText, "Verify filter preferences text is visible").toBeVisible();
        await expect(this.removeStickyFilterCheckBox, "Verify remove sticky filter checkbox is visible").toBeVisible();
        await this.removeStickyFilterCheckBox.click();
        await expect(this.removeScrollToTopCheckBox, "Verify remove scroll to top checkbox is visible").toBeVisible();
        await this.removeScrollToTopCheckBox.click();
        await expect(this.widgetHeadingText, "Verify widget heading text is visible").toBeVisible();
        await expect(this.widgetHeadingField, "Verify widget heading feild is visible").toBeVisible();
        await this.widgetHeadingField.fill(widgetHeadingName);

    }

    /* enter card details */
    async addCardDetails(titleName, programName, actual_price, discounted_price, ctaName, programInfo1, programInfo2, navigation11Url, className, classValue) {
        await expect(this.cardDataButton, "Verify card data button is visible").toBeVisible();
        await this.cardDataButton.click();
        await expect(this.cardDataOneText, "Verify card data 1 is visible").toBeVisible();

        await expect(this.titleText, "Verify title text is visible").toBeVisible();
        await expect(this.titleField, "Verify title feild is visible").toBeVisible();
        await this.titleField.fill(titleName);

        await expect(this.programModeText, "Verify program mode text is visible").toBeVisible();
        await expect(this.programModeField, "Verify program mode feild is visible").toBeVisible();
        await this.programModeField.fill(programName);


        await expect(this.actualPriceText, "Verify actual price text is visible").toBeVisible();
        await expect(this.actualPriceField, "Verify actual price feild is visible").toBeVisible();
        await this.actualPriceField.fill(actual_price);

        await expect(this.discountedPriceText, "Verify discounted price text is visible").toBeVisible();
        await expect(this.discountedPriceField, "Verify discounted price feild is visible").toBeVisible();
        await this.discountedPriceField.fill(discounted_price);

        await expect(this.addProgramInfoText, "Verify add program info text is visible").toBeVisible();
        await expect(this.addProgramInfoField, "Verify add program info feild is visible").toBeVisible();
        await this.addProgramInfoField.fill(programInfo1);

        await expect(this.addButton, "Verify add button is visible").toBeVisible();
        await this.addButton.click();

        await expect(this.addProgramInfoText, "Verify add program info text is visible").toBeVisible();
        await expect(this.addProgramInfoField, "Verify add program info feild is visible").toBeVisible();
        await this.addProgramInfoField.fill(programInfo2);

        await expect(this.addButton, "Verify add button is visible").toBeVisible();
        await this.addButton.click();

        await expect(this.cardCTALabelText, "Verify card cta label text is visible").toBeVisible();
        await expect(this.cardCTALabelField, "Verify card cta label feild is visible").toBeVisible();
        await this.cardCTALabelField.fill(ctaName);

        await expect(this.actionText, "Verify action text is visible").toBeVisible();
        await expect(this.selectActionDropdownInCard(0, 0), "Verify select action dropdown in card1 is visible").toBeVisible();
        await this.selectActionDropdownInCard(0, 0).click();

        await expect(this.navigationActionSelect, "Verify navigation action option is visible").toBeVisible();
        await this.navigationActionSelect.click();
        await expect(this.navigationUriCardField, "Verify navigation url feild is visible").toBeVisible();
        await this.navigationUriCardField.fill(navigation11Url);

        await expect(this.addFilterDataButton, "Verify add filter data button is visible").toBeVisible();
        await this.addFilterDataButton.click();
        await expect(this.filterDimensionText, "Verify filter dimension text is visible").toBeVisible();
        await expect(this.filterDimensionField, "Verify filter dimension feild is visible").toBeVisible();
        await this.filterDimensionField.fill(className);

        await expect(this.filterDimensionValueText, "Verify add filter dimension value text is visible").toBeVisible();
        await expect(this.filterDimensionValueField, "Verify add filter dimension value feild is visible").toBeVisible();
        await this.filterDimensionValueField.fill(classValue);
        await expect(this.addFilterCardValueButton, "Verify add filter dimension value button is visible").toBeVisible();
        await this.addFilterCardValueButton.click();

    }

}