import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { customExpect } from '../../fixtures/ui-fixtures';

import { EnvUtils } from '../../../commons/env-utilities';

const CourseAndSyllabusPageUrl = '/course-management'
const timeout = 50000;
const interval = 1000;


export class CourseAndSyllabusPage extends ICPage {

  readonly filterDropDown: Locator;
  readonly centerDropDown: Locator;
  readonly adplCenterOption: Locator;
  readonly automationCenterOption: (centerName: string) => Locator;
  readonly applyFilterButton: Locator;
  readonly automationCourse: (courseName_Lmm: string) => Locator;
  readonly contentButton: Locator;
  readonly addContentToPhaseButton: Locator;
  readonly uploadContentButton: Locator;
  readonly attachContentText: Locator;
  readonly topicDropDown: Locator;
  readonly topicText: (topicName: string) => Locator;
  readonly searchInput: Locator;
  readonly searchButton: Locator;
  readonly contentText: (contentName: string) => Locator;
  readonly contentCheckBox: (contentName: string) => Locator;
  readonly choosePhasesButton: Locator;
  readonly contentSelectedText: Locator;
  readonly phaseCheckbox: (phaseNumber: string) => Locator;
  readonly publishDatesText: Locator;
  readonly dateInput: (phaseNumber: string) => Locator;
  readonly newContentAttachedText: Locator;
  readonly courseDetailsButton: Locator;
  readonly uploadSuccessfullyStatus: Locator;
  readonly processingStatus: Locator;
  readonly selectAllCheckBox: Locator;
  readonly statusTitle: Locator;
  readonly centerDropDownAttachPage: Locator;
  readonly subjectDropDownAttachPage: Locator;
  readonly streamDropDown: Locator;
  readonly searchTextFeild: Locator;
  readonly streamOption: (streamName: string) => Locator;
  readonly classOption: (className: string) => Locator;
  readonly classDropDown: Locator;
  readonly courseSelect: Locator;
  readonly subjectDropDown: Locator;
  readonly subjectOption: (subjectName: string) => Locator;
  readonly searchFeild: Locator;
  readonly languageDropDown: Locator;
  readonly languageDropDown1: Locator;
  readonly languageText: (languageName: string) => Locator;
  readonly languageEnglishOption: Locator;
  readonly apiLoader: Locator;
  readonly academicSessionDropdown: Locator;
  readonly academicSessionDropdown1: Locator;
  readonly academicSession2025: Locator;
  readonly academicSession_2026: Locator;
  readonly courseSelectNext: Locator;
  readonly courseDotsIcon: Locator;
  readonly addSyllabusButton: Locator;
  readonly addSyllabusForCourseText: Locator;
  readonly chooseAMethodText: Locator;
  readonly howWouldYouLikeText: Locator;
  readonly createNewSyllabusFromText: Locator;
  readonly addAManualTopicsText: Locator;
  readonly createNewSyllabusButton: Locator;
  readonly addTopicsForEveryStream: Locator;
  readonly noStreamAddedToSyllabus: Locator;
  readonly addStreamButton: Locator;
  readonly selectSyllabusDetails: Locator;
  readonly jeeAdvancedStream: Locator;
  readonly addTopicsButton: Locator;
  readonly addSyllabusStreamButton: Locator;
  readonly editModeButton: Locator;
  readonly addTopicButton: Locator;
  readonly chemistrySubject: Locator;
  readonly atomicStructureTopic: Locator;
  readonly confirmTopicsList: Locator;
  readonly nextButton: Locator;
  readonly viewTopicButton: Locator;
  readonly addStreamsButton: Locator;
  readonly groupSubjectsButton: Locator;
  readonly createSyllabusButton: Locator;
  readonly syllabusCreatedSuccessfullyText: Locator;
  readonly syllabusIcon: Locator;
  readonly deleteSyllabusButton: Locator;
  readonly syllabusDeletedSuccessfullyTost: Locator;
  readonly selectAcademicSessionDropdown: Locator;
  readonly academicSession2024_25: Locator;
  readonly academicSession2026: Locator;
  readonly attachNewContentButton: Locator;
  readonly addContentToCourseButton: Locator;
  // New locators for course management
  readonly createCourseButton: Locator;
  readonly saveButton: Locator;
  readonly courseNameInput: Locator;
  readonly courseDescriptionInput: Locator;
  readonly createPhaseButton: Locator;
  readonly createNewPhaseHeading: Locator;
  readonly phaseNameInput: Locator;

  readonly syllabusNameInput: Locator;
  readonly createListingButton: Locator;
  readonly submitForApprovalButton: Locator;
  readonly approveListingButton: Locator;
  readonly successMessage: Locator;
  readonly phaseCreatedMessage: Locator;
  readonly syllabusAddedMessage: Locator;
  readonly syllabusDeletedMessage: Locator;
  readonly listingSubmittedMessage: Locator;
  readonly listingApprovedMessage: Locator;

  // Add a new locator for the Create New Course heading
  readonly createNewCourseHeading: Locator;

  // Update locators for phase creation
  readonly phaseNumberInput: Locator;
  readonly campusDropdown: Locator;
  readonly preferenceDropdown: Locator;
  readonly startDateInput: Locator;
  readonly syllabusDateInput: Locator;
  readonly endDateInput: Locator;
  readonly purchaseStartDateInput: Locator;
  readonly purchaseEndDateInput: Locator;
  readonly editPhaseButton: Locator;
  // Add a new locator for the search field
  readonly searchField: Locator;

  // Add a new locator for the SS option with specific test ID
  readonly ssOption: Locator;

  // Add new locators for weekday and weekend options
  readonly weekdayOption: Locator;
  readonly weekendOption: Locator;

  // Update the Continue button locator with the exact selector
  readonly continueButton: Locator;

  // Add a new locator for the ERP code input
  // readonly erpCodeInput: Locator;

  // Add new locators for meta ID and course ID fields
  readonly metaIdInput: Locator;
  readonly courseIdInput: Locator;
  // readonly courseModeDropDown: Locator;

  // New locators for listing management
  readonly listingManagementTab: Locator;
  readonly courseListingSubTab: Locator;

  // New locators for course creation
  // Add locators to class properties
  readonly courseTypeDropdown: Locator;
  readonly courseTypeOption: Locator;
  readonly centerSelectDropdown: Locator;
  readonly centerSelectOption: (centerName: string) => Locator;
  readonly streamSelectDropdown: Locator;
  readonly streamSelectOption: Locator;
  readonly classSelectDropdown: Locator;
  readonly classSelectOption: Locator;


  readonly courseModeDropdown: Locator;
  readonly courseModeRegularOption: Locator;
  readonly courseSpecialDropdown: Locator;
  readonly courseSpecialCustomizedOption: Locator;
  readonly courseOfferingsDropdown: Locator;
  readonly courseOfferingMentoringOption: Locator;
  readonly faqUrlInput: Locator;

  // Syllabus management locators
  readonly courseActionsIcon: Locator;
  readonly reuseExistingSyllabusButton: Locator;
  readonly selectSyllabusToReuseText: Locator;
  readonly firstSyllabusCheckbox: Locator;
  readonly confirmDeleteButton: Locator;
  readonly chooseAMethod: Locator;
  readonly courseNameText: (courseName: string) => Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, CourseAndSyllabusPageUrl, isMobile);
    this.filterDropDown = page.getByTestId('filter-panel-closed').getByTestId('shownImage');
    this.centerDropDown = page.getByTestId('center_id-dropdown');
    this.adplCenterOption = page.locator(`//*[contains(@data-testid,"center_id-dropdown-option-facility_") and text()="ADPL"]//*[@type="checkbox"]`);
    this.automationCenterOption = (centerName: string) => page.locator(`//*[contains(@data-testid,'center_id-dropdown-option') and text()='${centerName}']`);
    this.applyFilterButton = page.getByTestId('apply-filter-panel');
    this.automationCourse = (courseName_Lmm: string) => page.locator(`//*[contains(@data-testid,'view-redirecting-cell-') and text()='${courseName_Lmm}']`);
    this.contentButton = page.getByRole('button', { name: 'Content' });
    this.courseDetailsButton = page.getByRole('button', { name: 'Course Details' });
    this.addContentToPhaseButton = page.getByText('Add content to Phase');
    this.uploadContentButton = page.getByTestId('dropdown-option-ADD');
    this.attachContentText = page.getByText('Attach Content');
    this.topicDropDown = page.getByTestId('topic-dropdown').getByRole('img');
    this.topicText = (topicName: string) => page.getByText(`${topicName}`);
    this.searchInput = page.getByTestId('search-input');
    this.searchButton = page.getByTestId('text-inside-input');
    this.contentText = (contentName: string) => page.locator(`//*[contains(@id,'cell-name')]/descendant::*[text()='${contentName}']`);
    this.contentCheckBox = (contentName: string) => page.getByRole('row', { name: `${contentName}` }).getByRole('checkbox').first();
    this.choosePhasesButton = page.locator(`//*[@data-testid="sort-search-primary-button"]`);
    this.contentSelectedText = page.getByText('1 selected');
    this.phaseCheckbox = (phaseNumber: string) => page.getByRole('row', { name: `${phaseNumber}` }).getByRole('checkbox');
    this.publishDatesText = page.getByText('Preview phases and publish dates');
    this.dateInput = (phaseNumber: string) => page.getByRole('row', { name: `${phaseNumber}` }).getByRole('textbox');
    this.newContentAttachedText = page.locator("//*[contains(text(),'new content attached')]");
    this.uploadSuccessfullyStatus = page.locator(`//*[text()='Upload Successful']`).first();
    this.processingStatus = page.getByRole('gridcell', { name: 'Processing', exact: true }).first();
    this.selectAllCheckBox = page.getByTestId('topic-dropdown-option-select-all');
    this.statusTitle = page.getByText('Status');
    this.centerDropDownAttachPage = page.getByTestId('center_id-dropdown').getByRole('img');
    this.subjectDropDownAttachPage = page.getByTestId('subject-dropdown').getByRole('img');
    this.streamDropDown = page.getByTestId('stream-dropdown');
    this.searchTextFeild = page.getByPlaceholder('Search...', { exact: true });
    // Initialize in constructor
    this.centerSelectDropdown = this.page.getByText('Select Center');
    this.centerSelectOption = (centerName: string) => this.page.getByTestId(`dropdown-option-${centerName}`);
    this.streamSelectDropdown = this.page.getByText('Select Stream');
    this.streamSelectOption = this.page.getByTestId('dropdown-option-STREAM_JEE_MAIN_ADVANCED');
    this.classSelectDropdown = this.page.getByText('Select Class');
    this.classSelectOption = this.page.getByTestId('dropdown-option-CLASS_11');
    this.streamOption = (streamName: string) => page.locator(`//*[contains(@data-testid,'stream-dropdown-option') and text()='${streamName}']`);
    this.classOption = (className: string) => page.locator(`//*[contains(@data-testid,'class-dropdown-option') and text()='${className}']`);
    this.classDropDown = page.getByTestId('class-dropdown');
    this.subjectDropDown = page.getByTestId('subject-dropdown');
    this.courseSelect = page.locator(`//*[contains(@data-testid,'view-redirecting-cell-')]`).first();
    this.courseSelectNext = page.locator(`//*[contains(@data-testid,'view-redirecting-cell-')]`).nth(1);
    this.subjectOption = (subjectName: string) => page.locator(`//*[contains(@data-testid,'subject-dropdown-option') and text()='${subjectName}']`);
    this.searchFeild = page.getByPlaceholder('Search...)', { exact: true });
    this.languageDropDown = page.getByTestId('language-dropdown').getByRole('img');
    this.languageText = (languageName: string) => page.locator(`//*[contains(@class,"dropdown_dropdown_menu")]//*[text()='${languageName}']`);
    this.languageDropDown1 = page.getByText('Select Language');
    this.languageEnglishOption = page.getByTestId('dropdown-option-LANGUAGE_ENGLISH');
    this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
    this.academicSessionDropdown = page.getByText('Session').first();
    this.academicSessionDropdown1 = page.getByText('Select Academic Session').first();
    this.academicSession2025 = page.locator('[data-state="visible"]').getByText('04/2024 - 03/2025');
    this.academicSession_2026 = page.getByTestId('dropdown-option-04_2025__03_2026');
    this.courseDotsIcon = page.getByTestId('courseViewActionsCell-0').getByTestId('shownImage');
    // Initialize new locators
    this.createCourseButton = page.getByRole('button', { name: 'Create Course' });
    this.saveButton = page.getByRole('button', { name: 'Save' });
    this.courseNameInput = page.getByPlaceholder('Course Name');
    this.courseDescriptionInput = page.getByPlaceholder('Description');
    this.createPhaseButton = page.getByRole('button', { name: 'Create Phase' });
    this.phaseNameInput = page.getByLabel('Phase Number');
    this.addSyllabusButton = page.getByText('Add Syllabus');
    this.addSyllabusForCourseText = page.getByText('Add Syllabus for');
    this.chooseAMethodText = page.getByText('CHOOSE A METHOD');
    this.howWouldYouLikeText = page.getByText('How would you like to add');
    this.createNewSyllabusFromText = page.getByText('Create a new syllabus from');
    this.addAManualTopicsText = page.getByText('Manually add topics, topic');
    this.createNewSyllabusButton = page.getByRole('button', { name: 'Create New Syllabus' });
    this.addTopicsForEveryStream = page.getByText('Add topics for every stream');
    this.noStreamAddedToSyllabus = page.getByText('No stream added to syllabus');
    this.addStreamButton = page.getByTestId('cta-button');
    this.selectSyllabusDetails = page.getByText('Select Syllabus Details');
    this.jeeAdvancedStream = page.getByText('JEE Advanced');
    this.addTopicsButton = page.getByRole('button', { name: '+ Add Topics' });
    this.addSyllabusStreamButton = page.getByRole('button', { name: 'Add Stream', exact: true });
    this.editModeButton = page.getByText('Edit Mode');
    this.addTopicButton = page.getByTestId('add-edit-topic-0').getByText('Add Topic');
    this.chemistrySubject = page.getByTestId('sidebar-tab-0').getByText('Chemistry');
    this.atomicStructureTopic = page.getByText('Atomic Structure');
    this.confirmTopicsList = page.getByRole('button', { name: 'Confirm Topics Lists' });
    this.nextButton = page.getByRole('button', { name: 'Next' });
    this.viewTopicButton = page.getByText('View Topic');
    this.addStreamsButton = page.getByRole('button', { name: '← Add Streams' });
    this.groupSubjectsButton = page.getByRole('button', { name: '← Group Subjects' });
    this.createSyllabusButton = page.getByRole('button', { name: 'Create Syllabus' });
    this.syllabusCreatedSuccessfullyText = page.getByText('Successfully created syllabus');
    this.syllabusIcon = page.getByTestId('syllabus-0').getByTestId('shownImage');
    this.deleteSyllabusButton = page.getByText('Delete Syllabus');
    this.syllabusDeletedSuccessfullyTost = page.getByText('Syllabus deleted successfully');
    this.selectAcademicSessionDropdown = page.getByText('Select academic_session');
    this.academicSession2024_25 = page.getByTestId('dropdown-option-04_2024__03_2025');
    this.academicSession2026 = page.locator('[data-state="visible"]').getByText('04/2025 - 03/2026');
    this.attachNewContentButton = page.locator(`//*[@data-testid="sort-search-primary-button"]`);
    this.addContentToCourseButton = page.getByText('Add content to course');
    this.syllabusNameInput = page.getByLabel('Syllabus Name');
    this.createListingButton = page.getByRole('button', { name: 'Create Listing' });
    this.submitForApprovalButton = page.getByRole('button', { name: 'Submit for Approval' });
    this.approveListingButton = page.getByRole('button', { name: 'Approve Listing' });
    this.successMessage = page.getByText('Course created successfully');
    this.syllabusAddedMessage = page.getByText('Successfully created syllabus');
    this.syllabusDeletedMessage = page.getByText('Syllabus deleted successfully, Please refresh.');
    this.listingSubmittedMessage = page.getByText('Course listing submitted for approval');
    this.listingApprovedMessage = page.getByText('Course listing approved');
    this.phaseCreatedMessage = page.getByText('Phase created successfully');
    this.createNewCourseHeading = page.getByRole('heading', { name: 'Create New Course' });

    // Update locators for phase creation
    this.phaseNumberInput = page.getByLabel('Phase Number*', { exact: true });
    this.campusDropdown = page.locator('div').filter({ hasText: /^Select Campus$/ }).nth(1);
    this.preferenceDropdown = page.locator('div').filter({ hasText: /^Select Preference$/ }).first();
    this.startDateInput = page.getByPlaceholder('Start Date');
    this.syllabusDateInput = page.getByPlaceholder('Syllabus Date');
    this.endDateInput = page.getByPlaceholder('End Date');
    this.editPhaseButton = page.getByRole('button', { name: 'Edit phase' });
    this.purchaseStartDateInput = page.getByPlaceholder('Live Date');
    this.purchaseEndDateInput = page.getByPlaceholder('Expiry Date');
    this.searchField = page.getByPlaceholder('Search...');
    this.ssOption = page.getByTestId('dropdown-option-fa_FNBfmKs3exDgDQd97Of6J');
    this.weekdayOption = page.getByTestId('dropdown-option-WEEK_CYCLE_WEEKDAY');
    this.weekendOption = page.getByTestId('dropdown-option-WEEK_CYCLE_WEEKEND');
    this.continueButton = page.getByRole('button', { name: 'Continue' });
    this.createNewPhaseHeading = page.getByText('Create New Phase');
    this.metaIdInput = page.getByLabel('Meta ID*', { exact: true });
    this.courseIdInput = page.getByLabel('Course ID*', { exact: true });
    this.listingManagementTab = page.getByText('Listing Management');
    this.courseListingSubTab = page.getByTestId('sidebar-subtab-item-Course Listing');
    this.courseTypeDropdown = page.getByText('Select Course Type');
    this.courseTypeOption = page.getByTestId('dropdown-option-COURSE_TYPE_REGULAR');
    this.courseSpecialDropdown = page.getByText('Select Course Special');
    this.courseSpecialCustomizedOption = page.locator('[id="\\30 _COURSE_SPECIAL_100_PERCENT_CUSTOMIZED_FOR_YOU"]');
    this.courseOfferingsDropdown = page.getByText('Select Course Offerings');
    this.courseOfferingMentoringOption = page.locator('[id="\\30 _COURSE_OFFERING_1_1_MENTORING_SESSIONS_FOR_STUDENTS"]');
    this.courseModeDropdown = this.page.getByText('Select Course Mode');
    this.courseModeRegularOption = this.page.getByTestId('dropdown-option-MODE_LIVE');
    this.faqUrlInput = this.page.getByPlaceholder('url for FAQ');
    this.courseActionsIcon = this.page.getByTestId('courseViewActionsCell-0').getByTestId('shownImage');
    this.reuseExistingSyllabusButton = this.page.getByRole('button', { name: 'Reuse Existing Syllabus' });
    this.selectSyllabusToReuseText = this.page.getByText('Select a syllabus to reuse');
    this.firstSyllabusCheckbox = this.page.getByRole('checkbox').first();
    this.confirmDeleteButton = this.page.getByRole('button', { name: 'Yes' });
    this.chooseAMethod = this.page.getByText('CHOOSE A METHOD');
    this.courseNameText = (courseName: string) => this.page.getByText(courseName);
    
  }

  async applyFilterToAutomationCenter(centerName, courseName_Lmm) {
    await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
    await this.filterDropDown.click();
    await expect(this.academicSessionDropdown, "Verify academic session drop down is visible").toBeVisible();
    await this.academicSessionDropdown.click();
    await expect(this.academicSession2025, "Verify academic session 2025 drop down is visible").toBeVisible();
    await this.academicSession2025.click();
    await expect(this.centerDropDown, "Verify center drop down is visible").toBeVisible();
    await this.centerDropDown.click();
    await expect(this.adplCenterOption, "Verify ADPL center is checked").toBeVisible();

    if (await this.adplCenterOption.isChecked()) {
      await this.adplCenterOption.click();
    }
    await expect(this.automationCenterOption(centerName), "Verify automation center is visibl").toBeVisible();
    await this.automationCenterOption(centerName).click();
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    await expect(this.automationCourse(courseName_Lmm), "verVerifyify automation course is visible").toBeVisible();
    await this.automationCourse(courseName_Lmm).click();
    await slowExpect(this.courseDetailsButton, "Verify course button is visible").toBeVisible();
    await this.page.waitForLoadState('networkidle');
  }
  async navigateToAttachContentPage() {
    await slowExpect(this.courseDetailsButton, "Verify course details button is visible").toBeVisible();
    await expect(this.contentButton, "Verify content button is visible").toBeVisible();
    await this.contentButton.click();
    await expect(this.addContentToPhaseButton, "Verify add content to phase button is visible").toBeVisible();
    await this.addContentToPhaseButton.click();
    await expect(this.uploadContentButton, "Verify upload content to phase button is visible").toBeVisible();
    await this.uploadContentButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000); // wait is required to sync content
    await expect(this.attachContentText.first(), "Verify attach content text is visible").toBeVisible();
  }

  async filterUploadedContent(topicName, baseURL) {
    await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
    await this.filterDropDown.click();

    await expect(this.topicDropDown, "Verify topic drop down is visible").toBeVisible();
    await this.topicDropDown.click();
    if (topicName === "None") {
      await expect(this.selectAllCheckBox, "Verify select all topic drop down is visible").toBeVisible();
      await this.selectAllCheckBox.check();
      await expect(this.selectAllCheckBox, "Verify select all topic drop down is checked").toBeChecked();
      await this.selectAllCheckBox.uncheck();
      await expect(this.selectAllCheckBox, "Verify select all topic drop down is unchecked").not.toBeChecked();
      await expect(this.topicText(topicName), "Verify topic text is visible").toBeVisible();
      await this.topicText(topicName).click();
    }
    else {
      await expect(this.topicText(topicName), "Verify topic text is visible").toBeVisible();
      await this.topicText(topicName).click();
    }
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();

    const responsePromise = this.page.waitForResponse(baseURL + '/lmmStatic/getStaticLmmData', { timeout: 10000 });
    await this.applyFilterButton.click();
    await responsePromise;
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
  }

  async verifyContentName(contentNewName: string, maxRetries = 1, delayBetweenRetries = 500): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    // await this.page.waitForTimeout(2000); // Required delay for loading the DB before searching the content
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    await expect(this.searchInput, "Verify search input field is visible").toBeVisible();
    await this.searchInput.click();
    await this.searchInput.fill(contentNewName);
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();
    // await this.page.reload();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
    await customExpect(15000)(this.contentText(contentNewName), "Verify content is visible").toBeVisible();
  }


  async assignTheUploadedContentToCourse(contentNewName, phaseNumber, todayDate) {
    await expect(this.contentCheckBox(contentNewName), "Verify content check box is visible").toBeVisible()
    await this.contentCheckBox(contentNewName).click();
    await expect(this.contentSelectedText, "Verify content selected text is visible").toBeVisible();
    await expect(this.choosePhasesButton, "Verify choose phases button is visible").toBeVisible();
    await this.choosePhasesButton.click();
    await slowExpect(this.publishDatesText, "Verify publish dates text is visible").toBeVisible();
    await expect(this.phaseCheckbox(phaseNumber), "Verify phase is visible").toBeVisible();
    await this.phaseCheckbox(phaseNumber).click();
    await expect(this.attachContentText, "Verify attach content is visible").toBeVisible();
    await this.attachContentText.click();
    await customExpect(15000)(this.newContentAttachedText).toBeVisible();
  }


  async verifyUploadSuccessful() {
    const startTime = Date.now();
    await customExpect(15000)(this.statusTitle).toBeVisible();
    while (Date.now() - startTime < timeout) {
      await this.page.reload(); // Reload the page to get updated statuses
      await customExpect(15000)(this.statusTitle).toBeVisible();
      // Check if the processing status exists
      const isProcessingVisible = await this.processingStatus.isVisible();

      if (isProcessingVisible) {
        console.log("Status is still 'Processing'. Retrying...");
        await new Promise(resolve => setTimeout(resolve, interval)); // Wait before retrying
        continue;
      }
      // Check if the upload successful status exists
      const isUploadSuccessfulVisible = customExpect(15000)(this.uploadSuccessfullyStatus.isVisible());
      if (isUploadSuccessfulVisible) {
        console.log("Upload successful!");
        return; // Exit the loop once the status is "Upload Successful"
      }
    }
    throw new Error("Timeout exceeded: Upload successful status was not found");
  }

  async applyFilterToMultiTopology(centerName, streamName, className, courseName) {
    await this.page.waitForLoadState('networkidle');
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
    await this.filterDropDown.click();
    await expect(this.academicSessionDropdown, "Verify academic session drop down is visible").toBeVisible();
    await this.academicSessionDropdown.click();
    await expect(this.academicSession2025, "Verify academic session 2025 drop down is visible").toBeVisible();
    await this.academicSession2025.click();
    await expect(this.centerDropDown, "Verify center drop down is visible").toBeVisible();
    await this.centerDropDown.click();
    await expect(this.adplCenterOption, "Verify ADPL center is checked").toBeVisible();
    if (await this.adplCenterOption.isChecked()) {
      await this.adplCenterOption.click();
    }
    // await this.adplCenterOption.click();
    await expect(this.automationCenterOption(centerName), "Verify automation center is visibl").toBeVisible();
    await this.automationCenterOption(centerName).click();

    await expect(this.streamDropDown, "Verify stream drop down is visible").toBeVisible();
    await this.streamDropDown.click();
    // Search and select the option
    await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(streamName);
    await expect(this.streamOption(streamName), "Verify stream name is visible").toBeVisible();
    await this.streamOption(streamName).click();

    await expect(this.classDropDown, "Verify class drop down is visible").toBeVisible();
    await this.classDropDown.click();
    // Search and select the option
    await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(className);
    await expect(this.classOption(className), "Verify class name is visible").toBeVisible();
    await this.classOption(className).click();

    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(1000); // Required delay for loading the db before search the content
    await this.page.waitForSelector('[data-testid="api-loader"]', { state: 'detached' });
    if (className == "Class 11") {
      await slowExpect(this.automationCourse(courseName), "Verify course is visible").toBeVisible();
      await this.automationCourse(courseName).click({ force: true });
    }
    else if (className == "Class 12 Plus") {
      await slowExpect(this.courseSelectNext, "Verify course is visible").toBeVisible();
      await this.courseSelectNext.click({ force: true });
    }
    else {
      await slowExpect(this.courseSelect, "Verify course is visible").toBeVisible();
      await this.courseSelect.click({ force: true });
    }

    // await slowExpect(this.courseSelect, "Verify course is visible").toBeVisible();
    // await this.courseSelect.click({ force: true });
    // // if (await this.courseDetailsButton.isHidden) {
    // //   await this.courseSelect.click();
    // // }
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector('[data-testid="api-loader"]', { state: 'detached' });

  }

  async filterUploadedContentMultiTopology(centerName, subjectName, topicName, languageName, contentNewName) {
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    await this.page.waitForLoadState('networkidle');
    await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
    await this.filterDropDown.click();
    await expect(this.centerDropDown, "Verify center drop down is visible").toBeVisible();
    await this.centerDropDown.click();
    await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(centerName);
    await expect(this.automationCenterOption(centerName), "Verify center name is visible").toBeVisible();
    await this.automationCenterOption(centerName).click();

    await expect(this.subjectDropDown, "Verify subject drop down is visible").toBeVisible();
    await this.subjectDropDown.click();
    await slowExpect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(subjectName);
    await expect(this.subjectOption(subjectName), "Verify subject name is visible").toBeVisible();
    await this.subjectOption(subjectName).click();

    await expect(this.topicDropDown, "Verify topic drop down is visible").toBeVisible();
    await this.topicDropDown.click();
    await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(topicName);
    await expect(this.topicText(topicName), "Verify topic text is visible").toBeVisible();
    await this.topicText(topicName).click();

    await expect(this.languageDropDown, "Verify language drop down is visible").toBeVisible();
    await this.languageDropDown.click();
    await expect(this.searchTextFeild, "Verify search field is visible").toBeVisible();
    await this.searchTextFeild.fill(languageName);
    await expect(this.languageText(languageName), "Verify language text is visible").toBeVisible();
    await this.languageText(languageName).click();

    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
    await this.page.waitForLoadState('networkidle');
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
  }

  async validateAttachContentToSpecialBatch(contentNewName) {
    await expect(this.contentCheckBox(contentNewName), "Verify content check box is visible").toBeVisible()
    await this.contentCheckBox(contentNewName).click();
    await expect(this.contentSelectedText, "Verify content selected text is visible").toBeVisible();
    await expect(this.choosePhasesButton, "Verify attach content button is visible").toBeVisible();
    await this.choosePhasesButton.click();

  }

  async validateAndApplyCourseFilter(centerName, courseName, languageName) {
    await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
    await this.filterDropDown.click();
    await expect(this.academicSessionDropdown, "Verify academic session drop down is visible").toBeVisible();
    await this.academicSessionDropdown.click();
    await expect(this.academicSession2025, "Verify academic session 2025 drop down is visible").toBeVisible();
    await this.academicSession2025.click();
    await expect(this.centerDropDown, "Verify center drop down is visible").toBeVisible();
    await this.centerDropDown.click();
    await expect(this.adplCenterOption, "Verify ADPL center is checked").toBeVisible();
    await this.adplCenterOption.click();
    await expect(this.automationCenterOption(centerName), "Verify automation center is visible").toBeVisible();
    await this.automationCenterOption(centerName).click();
    // await expect(this.languageDropDown, "Verify language dropdown is visible").toBeVisible();
    // await this.languageDropDown.click();
    // await expect(this.languageText(languageName), "Verify language name is visible").toBeVisible();
    // await this.languageText(languageName).click();
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
    await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
    await expect(this.automationCourse(courseName), "Verify ui automation course is visible").toBeVisible();
  }

  async addSyllabusToCourseAndValidate() {
    await slowExpect(this.courseDotsIcon, "Verify course dots icon is visible").toBeVisible();
    await this.courseDotsIcon.click();
    if (await this.deleteSyllabusButton.isVisible()) {
      await this.deleteSyllabusButton.click();
      await slowExpect(this.syllabusIcon, "Verify syllabus icon is not visible").not.toBeVisible();
      await slowExpect(this.courseDotsIcon, "Verify course dots icon is visible").toBeVisible();
      await this.courseDotsIcon.click();
    }
    await expect(this.addSyllabusButton, "Verify add syllabus button is visible").toBeVisible();
    await this.addSyllabusButton.click();
    await slowExpect(this.addSyllabusForCourseText, "Verify add syllabus for course text is visible").toBeVisible();
    await expect(this.chooseAMethodText, "Verify choose a method text is visible").toBeVisible();
    await expect(this.howWouldYouLikeText, "Verify how would you like to add text is visible").toBeVisible();
    await expect(this.createNewSyllabusFromText, "Verify create new syllabus from text is visible").toBeVisible();
    await expect(this.addAManualTopicsText, "Verify add a manual topics text is visible").toBeVisible();
    await expect(this.createNewSyllabusButton, "Verify create new syllabus button is visible").toBeVisible();
    await this.createNewSyllabusButton.click();
    await slowExpect(this.addTopicsForEveryStream, "Verify add topics for every stream is visible").toBeVisible();
    await expect(this.noStreamAddedToSyllabus, "Verify no stream added to syllabus is visible").toBeVisible();
    await this.page.reload();
    await expect(this.addStreamButton, "Verify add stream button is visible").toBeVisible();
    await this.addStreamButton.click();
    await slowExpect(this.selectSyllabusDetails, "Verify select syllabus details text is visible").toBeVisible();
    await expect(this.jeeAdvancedStream, "Verify jee advanced stream text is visible").toBeVisible();
    await expect(this.addTopicsButton, "Verify add topics button is visible").toBeVisible();
    await expect(this.selectAcademicSessionDropdown, "Verify select academic session dropdown is visible").toBeVisible();
    await this.selectAcademicSessionDropdown.click();
    await expect(this.academicSession2024_25, "Verify academic session 2024-2025 is visible").toBeVisible();
    await this.academicSession2024_25.click();

    await expect(this.addSyllabusStreamButton, "Verify add syllabus stream button is visible").toBeVisible();
    await this.addSyllabusStreamButton.click();
    await slowExpect(this.addTopicButton, "Verify add topic button is visible").toBeVisible();
    await expect(this.editModeButton, "Verify edit topic button is visible").toBeVisible();
    await this.addTopicButton.click();
    await slowExpect(this.chemistrySubject, "Verify chemistry subject is visible").toBeVisible();
    await this.chemistrySubject.click();
    await slowExpect(this.atomicStructureTopic, "Verify atomic structure topic is visible").toBeVisible();
    await this.atomicStructureTopic.click();
    await slowExpect(this.confirmTopicsList, "Verify confirm list topics button is visible").toBeVisible();
    await this.confirmTopicsList.click();
    await slowExpect(this.viewTopicButton, "Verify view topic button is visible").toBeVisible();
    await expect(this.nextButton, "Verify next button is visible").toBeVisible();
    await this.nextButton.click();
    await slowExpect(this.addStreamsButton, "Verify add streams button is visible").toBeVisible();
    await expect(this.nextButton, "Verify next button is visible").toBeVisible();
    await this.nextButton.click();
    await slowExpect(this.groupSubjectsButton, "Verify group subjects button is visible").toBeVisible();
    await expect(this.nextButton, "Verify next button is visible").toBeVisible();
    await this.nextButton.click();
    await slowExpect(this.createSyllabusButton, "Verify create syllabus button is visible").toBeVisible();
    await this.createSyllabusButton.click();
    await slowExpect(this.syllabusCreatedSuccessfullyText, "Verify syllabus created successfully tost is visible").toBeVisible();
  }

  async verifyAndDeleteSyllabusToCourse() {
    await slowExpect(this.courseDotsIcon, "Verify course dots icon is visible").toBeVisible();
    await slowExpect(this.syllabusIcon, "Verify syllabus icon is visible").toBeVisible();
    await this.courseDotsIcon.click();
    await slowExpect(this.deleteSyllabusButton, "Verify delete syllabus button is visible").toBeVisible();
    await this.deleteSyllabusButton.click();
    await slowExpect(this.syllabusDeletedSuccessfullyTost, "Verify syllabus deleted successfully button is visible").toBeVisible();
  }

  async applyFilterToNewSessionCourse(centerName, courseName_Lmm) {
    await expect(this.filterDropDown, "Verify filter drop down is visible").toBeVisible();
    await this.filterDropDown.click();
    await expect(this.academicSessionDropdown, "Verify academic session drop down is visible").toBeVisible();
    await this.academicSessionDropdown.click();
    await expect(this.academicSession2026, "Verify academic session 2026 drop down is visible").toBeVisible();
    await this.academicSession2026.click();
    await expect(this.centerDropDown, "Verify center drop down is visible").toBeVisible();
    await this.centerDropDown.click();
    await expect(this.adplCenterOption, "Verify ADPL center is checked").toBeVisible();

    if (await this.adplCenterOption.isChecked()) {
      await this.adplCenterOption.click();
    }
    await expect(this.automationCenterOption(centerName), "Verify automation center is visibl").toBeVisible();
    await this.automationCenterOption(centerName).click();
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
    await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    await expect(this.automationCourse(courseName_Lmm), "verVerifyify automation course is visible").toBeVisible();
    await this.automationCourse(courseName_Lmm).click();
    await slowExpect(this.courseDetailsButton, "Verify course button is visible").toBeVisible();
    await this.page.waitForLoadState('networkidle');
  }

  async assignTheUploadedContentToNewSessionCourse(contentNewName) {
    await expect(this.contentCheckBox(contentNewName), "Verify content check box is visible").toBeVisible()
    await this.contentCheckBox(contentNewName).click();
    await expect(this.contentSelectedText, "Verify content selected text is visible").toBeVisible();
    await expect(this.attachNewContentButton, "Verify attach content button is visible").toBeVisible();
    await this.attachNewContentButton.click();
    await customExpect(15000)(this.newContentAttachedText).toBeVisible();
  }

  async navigateToAttachContentToCoursePage() {
    await slowExpect(this.courseDetailsButton, "Verify course details button is visible").toBeVisible();
    await expect(this.contentButton, "Verify content button is visible").toBeVisible();
    await this.contentButton.click();
    await expect(this.addContentToCourseButton, "Verify add content to course button is visible").toBeVisible();
    await this.addContentToCourseButton.click();
    await expect(this.uploadContentButton, "Verify upload content to course button is visible").toBeVisible();
    await this.uploadContentButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000); // wait is required to sync content
    await expect(this.attachContentText.first(), "Verify attach content text is visible").toBeVisible();
  }

  async createNewCourse(courseName: string, centerName: string, url_id: string) {
    await expect(this.createCourseButton).toBeVisible();
    await this.createCourseButton.click();

    // Check for the Create New Course heading
    await expect(this.createNewCourseHeading, "Verify Create New Course heading is visible").toBeVisible();

    // course type
    await expect(this.courseTypeDropdown).toBeVisible();
    await this.courseTypeDropdown.click();
    await expect(this.courseTypeOption).toBeVisible();
    await this.courseTypeOption.click();

    await expect(this.courseNameInput).toBeVisible();
    await this.courseNameInput.fill(courseName);

    // Select Center
    await expect(this.centerSelectDropdown).toBeVisible();
    await this.centerSelectDropdown.click();
    await this.centerSelectOption(centerName).click();

    // Select Stream  
    await expect(this.streamSelectDropdown).toBeVisible();
    await this.streamSelectDropdown.click();
    await this.streamSelectOption.click();

    // Select Class
    await expect(this.classSelectDropdown).toBeVisible();
    await this.classSelectDropdown.click();
    await this.classSelectOption.click();

    // Select Course Type
    await expect(this.courseModeDropdown).toBeVisible();
    await this.courseModeDropdown.click();
    await this.courseModeRegularOption.click();

    // Select Language
    await expect(this.languageDropDown1).toBeVisible();
    await this.languageDropDown1.click();
    await this.languageEnglishOption.click();

    // Select Academic Session
    await expect(this.academicSessionDropdown1).toBeVisible();
    await this.academicSessionDropdown1.click();
    await this.academicSession_2026.click();

    // Select Course Special
    await expect(this.courseSpecialDropdown).toBeVisible();
    await this.courseSpecialDropdown.click();
    await this.courseSpecialCustomizedOption.check();

    // Select Course Offerings
    await expect(this.courseOfferingsDropdown).toBeVisible();
    await this.courseOfferingsDropdown.click();
    await this.courseOfferingMentoringOption.check();

    // Fill FAQ URL
    await expect(this.faqUrlInput).toBeVisible();
    await this.faqUrlInput.click();
    await this.faqUrlInput.fill(url_id);

    // Save the course
    await expect(this.saveButton).toBeVisible();
    await this.saveButton.click();
  }

  async createNewPhase(courseName: string, phaseName: string, tomorrowFormatted:string, syllabuscompletionDate:string, endDateofPhase:string, todayFormatted:string, twoWeeksLaterFormatted:string) {
    await expect(this.courseNameText(courseName), "Verify course name text is visible").toBeVisible();
    await this.courseNameText(courseName).click();
    await expect(this.createPhaseButton, "Verify create phase button is visible").toBeVisible();
    await this.createPhaseButton.click();

    // Wait for the Create New Phase heading to be visible
    await expect(this.createNewPhaseHeading, "Verify create new phase heading is visible").toBeVisible();

    //Wait for the loader to close before proceeding
    await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();

    // Set phase number directly
    await slowExpect(this.phaseNumberInput, "Verify phase number input is visible").toBeVisible();
    await this.phaseNumberInput.fill(phaseName);

    // Set preferences to both weekdays and weekends using specific test IDs
    await expect(this.preferenceDropdown, "Verify preference dropdown is visible").toBeVisible();
    await this.preferenceDropdown.click();

    // Select weekday option
    await expect(this.weekdayOption, "Verify weekday option is visible").toBeVisible();
    await this.weekdayOption.click();

    // Select weekend option
    await expect(this.weekendOption, "Verify weekend option is visible").toBeVisible();
    await this.weekendOption.click();

    // Set dates
    await expect(this.startDateInput, "Verify start date input is visible").toBeVisible();
    await this.startDateInput.fill(tomorrowFormatted);

    await expect(this.syllabusDateInput, "Verify syllabus date input is visible").toBeVisible();
    await this.syllabusDateInput.fill(syllabuscompletionDate);

    await expect(this.endDateInput, "Verify end date input is visible").toBeVisible();
    await this.endDateInput.fill(endDateofPhase);

    await this.page.waitForLoadState('networkidle');
    await expect(this.purchaseStartDateInput, "Verify purchase start date input is visible").toBeVisible();
    await this.purchaseStartDateInput.fill(todayFormatted);

    await expect(this.purchaseEndDateInput, "Verify purchase end date input is visible").toBeVisible();
    await this.purchaseEndDateInput.fill(twoWeeksLaterFormatted);

    // Save the phase
    await expect(this.saveButton, "Verify save button is visible").toBeVisible();
    await this.saveButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();

    // Click the Continue button in the popup
    await slowExpect(this.continueButton, "Verify continue button is visible").toBeVisible();
    await this.continueButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    await slowExpect(this.phaseCreatedMessage, "Verify phase created successfully text is visible").toBeVisible();
  }


  async addSyllabusToCourse(courseName: string) {
    await slowExpect(this.searchInput, "Verify search input is visible").toBeVisible();
    await this.searchInput.fill(courseName);
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(1000);

    await expect(this.courseActionsIcon, "Verify course actions icon is visible").toBeVisible();
    await this.courseActionsIcon.click();
    await this.page.waitForLoadState('networkidle');
    await expect(this.addSyllabusButton, "Verify add syllabus button is visible").toBeVisible();
    await this.addSyllabusButton.click();
    await expect(this.chooseAMethod, "Verify choose a method text is visible").toBeVisible();
    await this.reuseExistingSyllabusButton.click();

    await expect(this.selectSyllabusToReuseText, "Verify select syllabus to reuse text is visible").toBeVisible();
    await this.firstSyllabusCheckbox.click();
    await expect(this.nextButton, "Verify next button is visible").toBeVisible();
    await this.nextButton.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await expect(this.createSyllabusButton, "Verify create syllabus button is visible").toBeVisible();
    await this.createSyllabusButton.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await expect(this.syllabusAddedMessage, "Verify syllabus created success message").toBeVisible();
  }

  async deleteSyllabus(courseName: string) {
    await slowExpect(this.searchInput, "Verify search input is visible").toBeVisible();
    await expect(this.searchInput).toBeVisible();
    await this.searchInput.fill(courseName);
    await expect(this.searchButton).toBeVisible();
    await this.searchButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(1000);

    await expect(this.courseActionsIcon, "Verify course actions icon is visible").toBeVisible();
    await this.courseActionsIcon.click();
    await this.page.waitForLoadState('networkidle');
    await expect(this.deleteSyllabusButton, "Verify delete syllabus button is visible").toBeVisible();
    await this.deleteSyllabusButton.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.syllabusDeletedMessage, "Verify syllabus deleted message is visible").toBeVisible();
  }

  async createCourseListing(courseName: string) {
    await this.page.getByText(courseName).click();
    await expect(this.createListingButton).toBeVisible();
    await this.createListingButton.click();
    await this.page.getByRole('button', { name: 'Save' }).click();
  }

  async submitForApproval() {
    await expect(this.submitForApprovalButton).toBeVisible();
    await this.submitForApprovalButton.click();
  }

  async approveListing(courseName: string) {
    await this.page.getByText(courseName).click();
    await expect(this.approveListingButton).toBeVisible();
    await this.approveListingButton.click();
  }
}



