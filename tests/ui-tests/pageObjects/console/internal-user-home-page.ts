import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { execPath } from 'process';

const InternalUserHomePageUrl = '/resource-management/internal-user'


export class InternalUserHomePage extends ICPage {

  readonly testManagementText: Locator;
  readonly testManagementButton: Locator;
  readonly studentTestButton: Locator;
  readonly classManagementButton: Locator;
  readonly classSchedulingButton: Locator;
  readonly apiLoader: Locator;
  readonly contentManagementButton: Locator;
  readonly lmmButton: Locator;
  readonly contentReviewButton: Locator;
  readonly courseManagementButton: Locator;
  readonly courseAndSyllabusButton: Locator;
  readonly broadcastManagementButton: Locator;
  readonly newClassSchedulingButton: Locator;
  readonly batchManagementButton: Locator;

  readonly sideBarClassManagementTab: Locator;
  readonly doubtTeacherMappingText: Locator;
  readonly internalUserHeaderTitle: Locator;
  readonly sideBarDoubtsManagement: Locator;
  readonly editMappingText: Locator;
  readonly selectActionText: Locator;
  readonly transferLabel: Locator;
  readonly applybutton: Locator;
  readonly enterOldIdInput: Locator;
  readonly enterNewIdInput: Locator;
  readonly submitMappingButton: Locator;
  readonly messageSucessText: Locator;
  readonly nextButtonOnEditMapping: Locator;
  readonly errorMessageNoDoubts: Locator;
  readonly sidebarMentorshipButton: Locator;
  readonly sidebarSubItemSchedule: Locator;
  readonly sidebarSubItemCalendar: Locator;
  readonly sidebarApproverManagementButton: Locator;
  readonly sidebarListingManagementButton: Locator;
  readonly sidebarSubItemListingApproval: Locator;
  readonly sidebarSubItemCourseChange: Locator;
  readonly sidebarSubItemCourseListing: Locator;
  readonly createCourseListingButton: Locator;
  readonly rejectedButton: Locator;
  readonly sidebarResourseManagementButton: Locator;
  readonly sidebarStudentManagement: Locator;
  readonly selectParameter: Locator;
  readonly formIdDropdownOption: Locator;
  readonly searchInput: Locator;
  readonly searchButton: Locator;
  readonly userNameOfStudent: Locator;
  readonly courseText: Locator;
  readonly paymentText: Locator;
  readonly courseChangeEditButton: Locator;
  readonly courseChangeButton: Locator;
  readonly editStudentButton: Locator;
  readonly selectCampusDropdown: Locator;
  readonly kotaCampusButton: Locator;
  readonly jeeAdvancedStream: Locator;
  readonly selectCourseOption: Locator;
  readonly jeeAdvancedCourse: Locator;
  readonly selectPhaseOption: Locator;
  readonly phase1Aoption: Locator;
  readonly checkFeasibilityButton: Locator;
  readonly studentEligibleText: Locator;
  readonly neetStreamOption: Locator;
  readonly courseChangeRequestHeader: Locator;
  readonly createdText: Locator;
  readonly cancelRequest: Locator;
  readonly selectReasonForCancelation: Locator;
  readonly dropdownOption: Locator;
  readonly incompleteDetailsOption: Locator;
  readonly yesCancelButton: Locator;
  readonly feePaidText: Locator;
  readonly reasonForChange: Locator;
  readonly addComments: Locator;
  readonly selectAReasonToChange: Locator;
  readonly dropdownReason2: Locator;
  readonly commentsTypeHere: Locator;
  readonly consession: Locator;
  readonly totalAmountPayable: Locator;
  readonly sendForApproval: Locator;
  readonly feeAmount: Locator;
  readonly requestSentToast: Locator;
  readonly streamName: (stream: string) => Locator;
  readonly presentStreamValue: Locator;
  readonly jeeStreamOptionInSidePannel: Locator;
  readonly classRoomText: Locator;
  readonly neetStreamId: Locator;
  readonly neetCourseId: Locator;
  readonly neetPhase1A: Locator;
  readonly refundPaymentTitle: Locator;
  readonly editAppliedAmmountField: Locator;
  readonly reasonTochangeAmountHeader: Locator;
  readonly changeAmountReasonDropdownOption2: Locator;
  readonly selectReasonForAmountChangeDropdown: Locator;
  readonly sidebarCourseManagement: Locator;
  readonly sidebarSubItemPhaseManagement: Locator;
  readonly sidebarpageManagementButton: Locator;
  readonly sidebarurlTab: Locator;
  readonly createUrlButton: Locator;
  readonly createOfferButton: Locator;
  readonly sidebarSubItemOffersDiscounts: Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, InternalUserHomePageUrl, isMobile);
    this.testManagementButton = page.getByText('Test Management', { exact: true });
    this.studentTestButton = page.getByText('Student Tests');
    this.classManagementButton = page.getByText('Class Management', { exact: true });
    this.classSchedulingButton = page.getByTestId('sidebar-subtab-item-Class Scheduling');
    this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
    this.contentManagementButton = page.getByTestId('sidebar-tab-Content-Management').getByText('Content Management');
    this.lmmButton = page.getByTestId('sidebar-subtab-item-Learning Material Management');
    this.contentReviewButton = page.getByTestId('sidebar-subtab-item-Content Review');
    this.courseManagementButton = page.getByTestId('sidebar-tab-Course-Management').getByRole('img', { name: 'block logo' })
    this.courseAndSyllabusButton = page.getByTestId('sidebar-subtab-item-Course & Syllabus Management');
    this.broadcastManagementButton = page.getByText('Broadcast Management', { exact: true });
    this.newClassSchedulingButton = page.getByText('New Class Scheduling');
    this.batchManagementButton = page.getByTestId('sidebar-subtab-item-Batch Management');
    this.sideBarClassManagementTab = page.getByTestId('sidebar-tab-Class-Management');
    this.doubtTeacherMappingText = page.getByText("Doubt teacher - Batch Mapping");
    this.internalUserHeaderTitle = page.getByText('Internal Users');
    this.sideBarDoubtsManagement = page.getByTestId("sidebar-subtab-item-Doubts Management");
    this.selectActionText = page.getByText("Select action");
    this.transferLabel = page.getByLabel("Transfer");
    this.applybutton = page.getByRole("button", { name: "Apply" });
    this.enterOldIdInput = page.getByPlaceholder("Enter Old Employee ID");
    this.enterNewIdInput = page.getByPlaceholder("Enter New Employee ID");
    this.submitMappingButton = page.getByText("Submit");
    this.editMappingText = page.getByText("Edit Mapping - Non NDC");
    this.messageSucessText = page.getByText("Success");
    this.nextButtonOnEditMapping = page.getByText("Next");
    this.errorMessageNoDoubts = page.getByTestId("toast_message_text").getByText("Looks like there are no open doubts to be transferred!");
    this.sidebarMentorshipButton = page.getByTestId('sidebar-tab-1:1-Mentorship').getByText(':1 Mentorship');
    this.sidebarSubItemSchedule = page.getByTestId('sidebar-subtab-item-Schedule');
    this.sidebarSubItemCalendar = page.getByTestId('sidebar-subtab-item-Calendar');
    this.sidebarStudentManagement = page.getByTestId('sidebar-subtab-item-Student Management');
    this.sidebarApproverManagementButton = page.getByTestId('sidebar-tab-Approver-Management');
    this.sidebarSubItemCourseChange = page.getByTestId('sidebar-subtab-item-Course Change');
    this.sidebarSubItemCourseListing = page.getByTestId('sidebar-subtab-item-Course Listing');
    this.createCourseListingButton = page.getByText('Create Course Listing');
    this.sidebarListingManagementButton = page.getByTestId('sidebar-tab-Listing-Management');
    this.sidebarSubItemListingApproval = page.getByTestId('sidebar-subtab-item-Listing Approval');
    this.rejectedButton = page.getByText('Rejected');
    this.sidebarResourseManagementButton = page.getByTestId('sidebar-tab-Resource-Management').getByTestId('shownImage');
    this.selectParameter = page.getByText('Select parameter');
    this.formIdDropdownOption = page.getByTestId('search-by-dropdown-option-form_id');
    this.searchInput = page.getByTestId('search-input');
    this.searchButton = page.getByTestId('text-inside-input');
    this.userNameOfStudent = page.getByTestId('redirecting-user-cell-SAKSHAM');
    this.courseText = page.locator("//*[@data-value='Course_details']");
    this.paymentText = page.getByText('Payment');
    this.courseChangeEditButton = page.getByRole('button', { name: 'Edit' });
    this.courseChangeButton = page.getByText('Course Change');
    this.editStudentButton = page.getByLabel('Edit Student');
    this.selectCampusDropdown = page.getByText('Select Campus');
    this.kotaCampusButton = page.getByTestId('dropdown-option-fa_1C4sZGYJiwxxcSBSA0bqT');
    this.neetStreamOption = page.locator('div').filter({ hasText: /^NEET$/ }).first();
    this.jeeAdvancedStream = page.getByTestId('dropdown-option-STREAM_JEE_MAIN_ADVANCED');
    this.selectCourseOption = page.getByText('Select Course');
    this.jeeAdvancedCourse = page.getByTestId('dropdown-option-cr_ynqISsgNxq3eQQJS8Cum9');
    this.selectPhaseOption = page.getByText('Select Phase');
    this.phase1Aoption = page.getByTestId('dropdown-option-ph_wkSfspGvU9zWaDURpPtkk');
    this.checkFeasibilityButton = page.getByRole('button', { name: 'Check feasibility' });
    this.studentEligibleText = page.getByText('Student is eligible for');
    this.courseChangeRequestHeader = page.getByText('Course Change Request');
    this.createdText = page.getByText('Created');
    this.cancelRequest = page.getByRole('button', { name: 'Cancel Request' });
    this.selectReasonForCancelation = page.getByRole('heading', { name: 'Select reason for cancellation' });
    this.dropdownOption = page.getByTestId('dropdown');
    this.incompleteDetailsOption = page.getByTestId('dropdown-option-Incomplete Details Filled');
    this.yesCancelButton = page.getByRole('button', { name: 'Yes,Cancel' });
    this.feePaidText = page.getByText('Fee Paid');
    this.reasonForChange = page.getByText('Reason for change*');
    this.addComments = page.getByText('Add comments');
    this.selectAReasonToChange = page.getByText('Select a reason for change');
    this.dropdownReason2 = page.getByTestId('dropdown-option-Reason2');
    this.commentsTypeHere = page.getByPlaceholder('Type here');
    this.consession = page.getByText('Concession');
    this.totalAmountPayable = page.getByText('Total amount payable');
    this.sendForApproval = page.getByRole('button', { name: 'Send for approval' });
    this.feeAmount = page.locator("(//*[text()='Amount ']/ancestor::*[@class='col-span-1']//descendant::p)[2]");
    this.requestSentToast = page.getByText('Request sent for approval');
    this.streamName = (stream: string) => page.getByText(`${stream}`);
    this.presentStreamValue = page.locator("(//*[text()='Stream']/ancestor::*[contains(@class,'w-full flex flex-row')]//*)[4]");
    this.jeeStreamOptionInSidePannel = page.locator('div').filter({ hasText: /^JEE Advanced$/ }).first();
    this.classRoomText = page.locator('div').filter({ hasText: /^Classroom Program$/ }).first();
    this.neetStreamId = page.getByTestId('dropdown-option-STREAM_PRE_MEDICAL');
    this.neetCourseId = page.getByTestId('dropdown-option-cr_eaPGcTKDYiRuevptpn6yR');
    this.neetPhase1A = page.getByTestId('dropdown-option-ph_4SwRKDR72pLGiJ2qBtWU1');
    this.refundPaymentTitle = page.getByText('Refund Payment mode');
    this.editAppliedAmmountField = page.locator('input[name="editedAppliedAmount"]');
    this.reasonTochangeAmountHeader = page.getByText('Reason for changing the');
    this.changeAmountReasonDropdownOption2 = page.getByTestId('dropdown-option-Reason3');
    this.selectReasonForAmountChangeDropdown = page.getByText('Select Reason');
    this.sidebarCourseManagement = page.getByTestId('sidebar-tab-Course-Management').getByText('Course Management');
    this.sidebarSubItemPhaseManagement = page.getByTestId('sidebar-subtab-item-Phase Management');
    this.sidebarpageManagementButton = page.getByTestId('sidebar-tab-Page-Management');
    this.sidebarurlTab = page.getByTestId('sidebar-subtab-item-URL');
    this.createUrlButton = page.getByRole('button', { name: 'Create URL' });
    this.createOfferButton = page.getByRole('button', { name: 'Create Offer' });
    this.sidebarSubItemOffersDiscounts = page.getByTestId('sidebar-subtab-item-Offers/Discounts');
  }

  async navigateToCreateTestPage() {
    await this.testManagementButton.scrollIntoViewIfNeeded();
    await expect(this.testManagementButton, "Verify test management button is visible").toBeVisible();
    await this.testManagementButton.click();
    await expect(this.studentTestButton, "Verify student test button is visible").toBeVisible();
    await this.studentTestButton.click();
  }

  async navigateToSchedulingPage() {
    await expect(this.classManagementButton, "verify class management button is  present").toBeVisible();
    await this.classManagementButton.scrollIntoViewIfNeeded();
    await this.classManagementButton.click();
    await expect(this.classSchedulingButton, "verify class scheduling button is present").toBeVisible();
    await this.classSchedulingButton.click();
  }

  async navigateToV2SchedulingPage() {
    await expect(this.classManagementButton, "verify class management button is present").toBeVisible();
    await this.classManagementButton.scrollIntoViewIfNeeded();
    await this.classManagementButton.click();
    await expect(this.newClassSchedulingButton, "verify new class scheduling button is present").toBeVisible();
    await this.newClassSchedulingButton.click();
  }

  async navigateToCourseAndSyllabusPage() {
    await expect(this.courseManagementButton, "verify course management button is visible").toBeVisible();
    await this.courseManagementButton.click();
    if (await this.courseAndSyllabusButton.isHidden()) {
      await this.courseManagementButton.click();
    }
    await expect(this.courseAndSyllabusButton, "verify course and syllabus button is visible").toBeVisible();
    await this.courseAndSyllabusButton.click();
  }

  async navigateToCourseListingTab() {
    await expect(this.sidebarListingManagementButton, "Verify sidebar listing management button is visible").toBeVisible();
    await this.sidebarListingManagementButton.click();
    await expect(this.sidebarSubItemCourseListing, "Verify sidebar course listing button is visible").toBeVisible();
    await this.sidebarSubItemCourseListing.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    await slowExpect(this.createCourseListingButton).toBeVisible();
  }

  async navigateToBroadcastManagementPage() {
    await expect(this.classManagementButton, "Class Management button is not present").toBeVisible();
    await this.classManagementButton.scrollIntoViewIfNeeded();
    await this.classManagementButton.click();
    await expect(this.broadcastManagementButton, "broadcast management button is not present").toBeVisible();
    await this.broadcastManagementButton.click();
  }
  async navigateToDoubtsManagementPage() {
    await expect(this.sideBarClassManagementTab, "Verify class management sidebar is visible").toBeVisible();
    await this.sideBarClassManagementTab.click();


    await this.page.waitForLoadState('networkidle');
    await this.sideBarDoubtsManagement.scrollIntoViewIfNeeded();
    await expect(this.sideBarDoubtsManagement, "Verify doubts managements option is visible in sidebar").toBeVisible();
    await this.sideBarDoubtsManagement.click();
  }

  async mapDoubtToAnotherTeacher(originalTeacher, teacherToBeMapped) {
    await this.page.waitForLoadState("networkidle")
    await slowExpect(this.doubtTeacherMappingText, "Verify the doubt mapping text is visible").toBeVisible();
    await this.page.waitForLoadState("domcontentloaded");
    await this.page.waitForLoadState("networkidle");
    await slowExpect(this.editMappingText, "Verify edit mapping text is visible").toBeVisible();
    await this.editMappingText.click();
    await expect(this.selectActionText, "Verify choose action text").toBeVisible();
    await expect(this.transferLabel, "Verify transfer radio button is visible").toBeVisible();
    await this.transferLabel.click();
    await expect(this.nextButton, "Verify apply button is visible").toBeVisible();
    await this.nextButton.click();
    await expect(this.enterNewIdInput, "Verify the new id input is visible").toBeVisible();
    await expect(this.enterOldIdInput, "Verify old id input is visible").toBeVisible();
    await this.enterOldIdInput.fill(`${originalTeacher}`);
    await this.enterNewIdInput.fill(`${teacherToBeMapped}`);
    await expect(this.submitMappingButton, "Verify Submit button is visible").toBeVisible();
    await this.submitMappingButton.click();
    await this.page.waitForLoadState("networkidle");
    if (await this.errorMessageNoDoubts.isVisible()) {
      console.log("no doubts to map");
    }
    else {
      await expect(this.messageSucessText, "Verify sucess toast message is visible").toBeVisible();
    }

  }
  async navigateToBatchManagementPage() {
    await expect(this.courseManagementButton, "verify course management button is visible").toBeVisible();
    await this.courseManagementButton.click();
    await expect(this.batchManagementButton, "verify batch management button is visible").toBeVisible();
    await this.batchManagementButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  async navigateToMentorshipSchedulePage() {
    await slowExpect(this.sidebarMentorshipButton, "Verify 1:1 mentorship sidebar button is visible").toBeVisible();
    await this.sidebarMentorshipButton.click();
    await expect(this.sidebarSubItemSchedule, "Verify schedule button is visible").toBeVisible();
    await expect(this.sidebarSubItemCalendar, "Verify calendar button is visible").toBeVisible();
    await this.sidebarSubItemSchedule.click();
    await this.page.waitForLoadState('networkidle');
  }
  async navigateToStudentManagementPage() {
    await slowExpect(this.sidebarStudentManagement, "Verify sidebar student management option is visible").toBeVisible();
    await this.sidebarStudentManagement.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
  }

  async navigateToCourseChangeApproverPage() {
    await slowExpect(this.sidebarApproverManagementButton, "Verify sidebar approver management option is visible").toBeVisible();
    await this.sidebarApproverManagementButton.click();
    await slowExpect(this.sidebarSubItemCourseChange, "Verify sidebar course change button is visible").toBeVisible();
    await this.sidebarSubItemCourseChange.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.rejectedButton, "Verify reject title is visible in course change page").toBeVisible();
  }

  async navigateToListingApproverPage() {
    await slowExpect(this.sidebarListingManagementButton, "Verify sidebar approver management option is visible").toBeVisible();
    await this.sidebarListingManagementButton.click();
    await slowExpect(this.sidebarSubItemListingApproval, "Verify sidebar listing approval button is visible").toBeVisible();
    await this.sidebarSubItemListingApproval.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.rejectedButton, "Verify reject title is visible in course change page").toBeVisible();
  }
  
  async navigateToOffersDiscountsPage() {
    await slowExpect(this.sidebarListingManagementButton, "Verify sidebar listing management option is visible").toBeVisible();
    await this.sidebarListingManagementButton.click();
    await slowExpect(this.sidebarSubItemOffersDiscounts, "Verify offers/discounts subtab is visible").toBeVisible();
    await this.sidebarSubItemOffersDiscounts.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.createOfferButton, "Verify create offer button is visible").toBeVisible();
  }
  async navigateToUrlManagement() {
    await this.sidebarpageManagementButton.click();
    await this.sidebarurlTab.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    await slowExpect(this.createUrlButton, "Verify create url button is visible").toBeVisible();
  }
  
 

  async verifyAndFilterForStudent(StudentFormId) {
    await slowExpect(this.selectParameter, "Verify select parameter option is visible").toBeVisible();
    await this.selectParameter.click();
    await expect(this.formIdDropdownOption, "Verify form id dropdown option is visible").toBeVisible();
    await this.formIdDropdownOption.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.searchInput, "Verify search input option is visible").toBeVisible();
    await this.searchInput.fill(StudentFormId);
    await expect(this.searchButton, "Verify search button is visible").toBeVisible();
    await this.searchButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.userNameOfStudent, "Verify student data is visible").toBeVisible();
  }

  async verifyStudentAndNavigateTocoursePage() {
    await this.userNameOfStudent.click();
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.courseText, "Verify course text is visible").toBeVisible();
    await slowExpect(this.paymentText, "Verify payment text is visible").toBeVisible();
    await this.courseText.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.feePaidText, "Verify fee paid text is visible").toBeVisible();
    // await this.page.waitForTimeout(3000);
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
  }

  async verifyAndCreateCourseChangeRequest() {
    /* added loop to check courseChangeRequestHeader is present or not for 3 secs */
    const maxAttempts = 6; // 3000ms / 500ms = 6 attempts
    for (let i = 0; i < maxAttempts; i++) {
      if (await this.courseChangeRequestHeader.isVisible()) {
        await slowExpect(this.cancelRequest, "Verify cancel request option is visible").toBeVisible();
        await this.cancelRequest.click();
        await slowExpect(this.selectReasonForCancelation, "Verify select reason for cancel header is visible").toBeVisible();
        await slowExpect(this.dropdownOption, "Verify dropdown is visible").toBeVisible();
        await this.dropdownOption.click();
        await slowExpect(this.incompleteDetailsOption, "Verify incomplete details dropdown is visible").toBeVisible();
        await this.incompleteDetailsOption.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
        await slowExpect(this.yesCancelButton, "Verify yes cancel button is visible").toBeVisible();
        await slowExpect(this.yesCancelButton, "Verify yes cancel button is enabled").toBeEnabled();
        await this.yesCancelButton.click();
        await slowExpect(this.courseChangeRequestHeader, "Verify course change request header should not visible").toBeHidden();
        break; // Exit loop if condition is met
      }
      if (i < maxAttempts - 1) { // Don't wait on the last iteration
        await this.page.waitForTimeout(500);
      }
    }
    await this.page.waitForLoadState('networkidle');
    await slowExpect(this.courseChangeEditButton, "Verify course change edit is visible").toBeVisible();
    await this.courseChangeEditButton.click();
    await slowExpect(this.courseChangeButton, "Verify course change button is visible").toBeVisible();
    await this.courseChangeButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await slowExpect(this.selectCampusDropdown, "Verify select campus dropdown is visible").toBeVisible();
    await this.page.waitForLoadState('networkidle');
    // await this.page.waitForTimeout(3000);
    await slowExpect(this.classRoomText, "Verify class room program value is visible").toBeVisible();
    await this.selectCampusDropdown.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await expect(this.kotaCampusButton, "Verify kota campus button is visible").toBeVisible();
    await this.kotaCampusButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    /* according to the current stream option if condition will apply to do course change dynamically */
    if (await this.neetStreamOption.isVisible()) {
      await this.neetStreamOption.click();
      await expect(this.jeeAdvancedStream, "Verify jee advanced stream is visible").toBeVisible();
      await this.jeeAdvancedStream.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.selectCourseOption, "Verify select course option is visible").toBeVisible();
      await this.selectCourseOption.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.jeeAdvancedCourse, "Verify jee advanced course option is visible").toBeVisible();
      await this.jeeAdvancedCourse.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.selectPhaseOption, "Verify select phase option is visible").toBeVisible();
      await this.selectPhaseOption.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.phase1Aoption, "Verify select phase 1A option is visible").toBeVisible();
      await this.phase1Aoption.click();
    } else if (await this.jeeStreamOptionInSidePannel.isVisible()) {
      await this.jeeStreamOptionInSidePannel.click();
      await expect(this.neetStreamId, "Verify neet stream is visible").toBeVisible();
      await this.neetStreamId.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.selectCourseOption, "Verify select course option is visible").toBeVisible();
      await this.selectCourseOption.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.neetCourseId, "Verify neet course option is visible").toBeVisible();
      await this.neetCourseId.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.selectPhaseOption, "Verify select phase option is visible").toBeVisible();
      await this.selectPhaseOption.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
      await expect(this.neetPhase1A, "Verify select neet phase 1A option is visible").toBeVisible();
      await this.neetPhase1A.click();
    }
    /* after stream and course values entered, validate check feasibility */
    await expect(this.checkFeasibilityButton, "Verify check feasibility option is visible").toBeVisible();
    await this.checkFeasibilityButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    await expect(this.studentEligibleText, "Verify student eligible text is visible").toBeVisible();
    /* After check for eligibility check conssesion and send for approval */
    await expect(this.reasonForChange, "Verify reason for change text is visible").toBeVisible();
    await expect(this.addComments, "Verify add coments text is visible").toBeVisible();
    await expect(this.selectAReasonToChange, "Verify select for a reason dropdown is visible").toBeVisible();
    await this.selectAReasonToChange.click();
    await expect(this.dropdownReason2, "Verify reason 2 dropdown is visible").toBeVisible();
    await this.dropdownReason2.click();
    await expect(this.commentsTypeHere, "Verify comments type here is visible").toBeVisible();
    await this.commentsTypeHere.fill("testing");
    await expect(this.consession, "Verify consession type is visible").toBeVisible();
    let fee = await this.feeAmount.innerText();
    const priceValue = parseInt(fee.replace(/[^0-9]/g, ""), 10);
    // console.log("fee amount " + priceValue);
    if (priceValue === 0) {
      await expect(this.totalAmountPayable, "Verify total amount payable is visible").toBeVisible();
      await expect(this.sendForApproval, "Verify send for approval button is visible").toBeVisible();
      await expect(this.sendForApproval, "Verify send for approval button is enabled").toBeEnabled();
      await this.sendForApproval.click();
    } else if (await this.refundPaymentTitle.isVisible()) {
      await slowExpect(this.editAppliedAmmountField, "Verify applied amount field is visible").toBeVisible();
      await this.editAppliedAmmountField.fill('0');
      await this.page.waitForTimeout(1000);
      await slowExpect(this.reasonTochangeAmountHeader, "Verify reason for amount change dropdown is visible").toBeVisible();
      await expect(this.selectReasonForAmountChangeDropdown, "Verify select for a reason dropdown is visible").toBeVisible();
      await this.selectReasonForAmountChangeDropdown.click();
      await slowExpect(this.changeAmountReasonDropdownOption2, "Verify reason for amount change dropdown option 2 is visible").toBeVisible();
      await this.changeAmountReasonDropdownOption2.click();
      await expect(this.sendForApproval, "Verify send for approval button is visible").toBeVisible();
      await expect(this.sendForApproval, "Verify send for approval button is enabled").toBeEnabled();
      await this.sendForApproval.click();
    }
    await slowExpect(this.requestSentToast, "Verify request sent toast is visible").toBeVisible();
  }

  async navigateToPhaseManagementPage() {
    await slowExpect(this.sidebarCourseManagement, "Verify sidebar course management is visible").toBeVisible();
    await this.sidebarCourseManagement.click();
    await expect(this.sidebarSubItemPhaseManagement, "Verify sidebar sub item phase management is visible").toBeVisible();
    await this.sidebarSubItemPhaseManagement.click();
    await this.page.waitForLoadState('networkidle');
  }
}


