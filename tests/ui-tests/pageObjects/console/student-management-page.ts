import { expect, type Locator, type Page } from '@playwright/test';
import { ScheduleBatchPage } from './schedule-batch-page';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const StudentManagementPageUrl = '/student-support/resource-management/student';

export class StudentManagementPage extends ICPage {
    readonly studentsHeader: Locator;
    readonly sortby: Locator;
    readonly searchBy: Locator;
    readonly searchInput: Locator;
    readonly searchButton: Locator;
    readonly selectParameter: Locator;
    readonly dropdownOptionOrderId: Locator;
    readonly dropdownOptionPhoneNumber: Locator;
    readonly dropdownOptionEmail: Locator;
    readonly dropdownOptionFormId: Locator;
    readonly studentProfileHeader: Locator;
    readonly personalInformationSubSection: Locator;
    readonly batchdetailsSubSection: Locator;
    readonly purchasDetailsSubSection: Locator;
    readonly trackCourseChangeSubSection: Locator;
    readonly editPersonalDetailsIcon: Locator;
    readonly editBasicDetailsHeader: Locator;
    readonly nameTitle: Locator;
    readonly genderTitle: Locator;
    readonly bloodGroupInput: Locator;
    readonly genderDropDown: Locator;
    readonly dropdownMaleOption: Locator;
    readonly dropdownFemaleOption: Locator;
    readonly dropdownOtherOption: Locator;
    readonly bloodGroupDropDown: Locator;
    readonly oPositiveDropdownOption: Locator;
    readonly oNegativeDropdownOption: Locator
    readonly saveButton: Locator;
    readonly apiLoader: Locator;
    readonly studentName: (student: string) => Locator;
    readonly detailsUpdatedTost: Locator;
    readonly maleGenderText: Locator;



    constructor(page: Page, isMobile: boolean) {
        super(page, StudentManagementPageUrl, isMobile);
        this.studentsHeader = page.getByTestId('page-content').getByText('Students');
        this.sortby = page.getByText('SORT BY');
        this.searchBy = page.getByText('SEARCH BY:');
        this.searchInput = page.getByTestId('search-input');
        this.searchButton = page.getByTestId('text-inside-input');
        this.selectParameter = page.getByText('Select parameter');
        this.dropdownOptionOrderId = page.getByTestId('search-by-dropdown-option-order_id');
        this.dropdownOptionPhoneNumber = page.getByTestId('search-by-dropdown-option-phone_number');
        this.dropdownOptionEmail = page.getByTestId('search-by-dropdown-option-email_id');
        this.dropdownOptionFormId = page.getByTestId('search-by-dropdown-option-form_id');
        this.personalInformationSubSection = page.getByText('Personal Information');
        this.studentProfileHeader = page.getByText('Student Profile');
        this.batchdetailsSubSection = page.getByText('Batch Details');
        this.purchasDetailsSubSection = page.getByText('Purchase Details');
        this.trackCourseChangeSubSection = page.getByText('Track Course Change');
        this.editPersonalDetailsIcon = page.getByRole('img', { name: 'Add' }).first();
        this.editBasicDetailsHeader = page.getByRole('heading', { name: 'Edit basic details' });
        this.nameTitle = page.getByText('Name *');
        this.genderTitle = page.getByText('Gender *');
        this.bloodGroupInput = page.getByLabel('Edit Student').getByText('Blood Group');
        this.genderDropDown = page.locator('//*[@data-testid="dropdown"]//*[contains(@class, "dropdown_dropdown_selected_value__4tfeU")]').first();
        this.dropdownMaleOption = page.getByTestId('dropdown-option-MALE');
        this.dropdownFemaleOption = page.getByTestId('dropdown-option-FEMALE');
        this.dropdownOtherOption = page.getByTestId('dropdown-option-OTHER');
        this.bloodGroupDropDown = page.locator('//*[@data-testid="dropdown"]//*[contains(@class, "dropdown_dropdown_selected_value__4tfeU")]').nth(1);
        this.oPositiveDropdownOption = page.getByTestId('dropdown-option-O+');
        this.oNegativeDropdownOption = page.getByTestId('dropdown-option-O-');
        this.saveButton = page.getByRole('button', { name: 'Save' });
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.studentName = (student: string) => page.getByTestId('detailCardComponent').getByText(`${student}`);
        this.detailsUpdatedTost = page.getByText('Details updated');
        this.maleGenderText = page.getByText('MALE');
    }

    async validateStudentManagementPage() {
        await slowExpect(this.studentsHeader, "Verify students header is visible").toBeVisible();
        await expect(this.sortby, "Verify sort by is visible").toBeVisible();
        await expect(this.searchBy, "Verify search by is visible").toBeVisible();
        await expect(this.searchButton, "Verify search button is visible").toBeVisible();
        await expect(this.selectParameter, "Verify select parameter to seach is visible").toBeVisible();
    }

    async validatesearchFeature(phoneNumber, studentName) {
        await slowExpect(this.selectParameter, "Verify select parameter is visible").toBeVisible();
        await this.selectParameter.click();
        await customExpect(15000)(this.dropdownOptionOrderId, "Verify order id option is visible").toBeVisible();
        await expect(this.dropdownOptionPhoneNumber, "Verify phone number option is visible").toBeVisible();
        await expect(this.dropdownOptionEmail, "Verify email option is visible").toBeVisible();
        await expect(this.dropdownOptionFormId, "Verify form id option is visible").toBeVisible();
        await this.dropdownOptionPhoneNumber.click();
        await expect(this.page).toHaveURL(/.*search_by=phone_number/);
        await expect(this.searchInput, "Verify search input is visible").toBeVisible();
        await this.searchInput.fill(phoneNumber);
        await this.searchButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").not.toBeVisible();
        await slowExpect(this.studentName(studentName), "Verify student name is visible").toBeVisible();
    }

    async validateAndUpdateStudentDetails(studentName) {
        await slowExpect(this.studentName(studentName), "Verify student name is visible").toBeVisible();
        await this.studentName(studentName).click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").not.toBeVisible();
        await slowExpect(this.studentProfileHeader, "Verify student profile header is visible").toBeVisible();
        /* commented below assertions due to some new changes are appending to stage and confirmed by N */
        // await slowExpect(this.personalInformationSubSection, "Verify personal details sub section is visible").toBeVisible();
        // await expect(this.batchdetailsSubSection, "Verify batch details sub section is visible").toBeVisible();
        // await expect(this.purchasDetailsSubSection, "Verify purchase details sub section is visible").toBeVisible();
        // await expect(this.trackCourseChangeSubSection, "Verify track course change sub section is visible").toBeVisible();
        await expect(this.editPersonalDetailsIcon, "Verify edit personal details icon is visible").toBeVisible();
        await this.editPersonalDetailsIcon.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").not.toBeVisible();
        await slowExpect(this.editBasicDetailsHeader, "Verify edit basic details header is visible").toBeVisible();
        await expect(this.nameTitle, "Verify name title is visible").toBeVisible();
        await expect(this.genderTitle, "Verify gender title is visible").toBeVisible();
        await expect(this.genderDropDown, "Verify gender dropdown is visible").toBeVisible();
        await this.genderDropDown.click();

        if (await this.genderDropDown.innerText() === "Male") {
            await slowExpect(this.dropdownFemaleOption, "Verify male dropdown is visible").toBeVisible();
            await this.dropdownFemaleOption.click();
        }
        else{
            await slowExpect(this.dropdownMaleOption, "Verify male dropdown is visible").toBeVisible();
            await this.dropdownMaleOption.click();
        }

        await slowExpect(this.bloodGroupDropDown, "Verify blood group dropdown is visible").toBeVisible();
        await this.bloodGroupDropDown.click();

        if (await this.bloodGroupDropDown.innerText() === "O +ve") {
            await slowExpect(this.oNegativeDropdownOption, "Verify o positive dropdown is visible").toBeVisible();
            await this.oNegativeDropdownOption.click();
        }

        else {
            await slowExpect(this.oPositiveDropdownOption, "Verify o positive dropdown is visible").toBeVisible();
            await this.oPositiveDropdownOption.click();
        }

        await slowExpect(this.saveButton, "Verify save button is visible").toBeVisible();
        await this.saveButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").not.toBeVisible();
        await slowExpect(this.detailsUpdatedTost, "Verify details updated tost is visible").toBeVisible();
        await slowExpect(this.editBasicDetailsHeader, "Verify edit basic details header should not visible").not.toBeVisible();
        await slowExpect(this.maleGenderText, "Verify male gender is visible").toBeVisible();

    }
}