import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const TeacherDoubtsPageUrl = '/doubt/view'
const repliedTextFromTeacherVar = "It is nothing"
const doubtForTestingNeet = "what is photosynthesis?"
const doubtForTestingJEE = "what is dc voltage?"

export class TeacherDoubtsPage extends ICPage {

  readonly studentDoubt: (studentDoubt: string) => Locator;
  readonly chatInputField: Locator;
  readonly sendButton: Locator;
  readonly greatTextForSuccess: Locator;
  readonly doubtsTitle: Locator;
  readonly listOfDoubtsSubTitle: Locator;
  readonly unresolvedDoubts: Locator;
  readonly answeredDoubts: Locator;
  readonly filterDoubts: Locator;
  readonly doubtTransferedToTeacher: Locator; //Jee
  readonly doubtTransferedToTeacherText: Locator; //Neet

  readonly incorrectSubject: Locator;
  readonly audioMicButton: Locator;
  readonly imageFileButton: Locator;
  readonly pleaseCheckOtherText: Locator;
  readonly closeGreatPopup: Locator;
  readonly fileInput: Locator;
  readonly uploadImage: Locator;
  readonly browseYourComputer: Locator;
  readonly browseFilesText: Locator;
  readonly closeImageUploadPopUp: Locator;
  readonly imageText: Locator;
  readonly imageEditButton: Locator;
  readonly deleteImageButton: Locator;
  readonly imageUploadButton: Locator;
  readonly takeAudioOption: Locator;
  readonly recordMaximumLength: Locator;
  readonly clickTheRecordingButton: Locator;
  readonly startAudioButton: Locator;
  readonly audioInProgress: Locator;
  readonly audioDoneButton: Locator;
  readonly recordedAudio: Locator;
  readonly needMoreHelpText: Locator;
  readonly doubtsTextTitle: Locator;
  readonly askedDoubtTextInTeacherSideNeet: Locator;
  readonly topicSelectForFilter: Locator;
  readonly sortBySelectForFilter: Locator;
  readonly searchTopicInput: Locator;
  readonly sortByLatestText: Locator;
  readonly searchResultFirstOption: Locator;
  readonly applyFilterButton: Locator;
  readonly reAssignDoubtModal: Locator;
  readonly selectBatchText: Locator;
  readonly batchSearchInput: Locator;
  readonly firstOptionOfBatchDropDown: Locator;
  readonly subjectsText: Locator;
  readonly mathsSubjectText: Locator;
  readonly selectTopicText: Locator;
  readonly reAssignButton: Locator;
  readonly doubtReAssignedToast: Locator;
  readonly closeButton: Locator;
  readonly reportIncorrectText: Locator;
  readonly allenVerifiedText: Locator;
  readonly whyDidYouReportText: Locator;
  readonly notFactuallyCorrect: Locator;
  readonly reportIncorrectBtnEnabled: Locator;
  readonly reportSubmittedSuccesfullyToast: Locator;
  readonly repliedTextFromTeacher: Locator;
  readonly editOptionDropdown: Locator;
  readonly editReplyOption: Locator;
  readonly editedReplyText: Locator;
  readonly closeIconDoubtsAssignedToast: Locator;
  readonly imageReply: Locator;
  readonly doubtAssigned: Locator;
  readonly doubtTopic: Locator;



  constructor(page: Page, isMobile: boolean) {
    super(page, TeacherDoubtsPageUrl, isMobile);
    this.studentDoubt = (studentDoubt: string) => page.getByText(`${studentDoubt}`);
    this.chatInputField = page.getByTestId('chat_area');
    this.sendButton = page.getByTestId('send_btn').getByRole('img');
    this.greatTextForSuccess = page.getByText('Great!');
    this.doubtsTitle = page.getByText('Doubts', { exact: true });
    this.listOfDoubtsSubTitle = page.getByText('Here is the list of doubts');
    this.unresolvedDoubts = page.getByTestId('tabGroup').getByText('Open');
    this.answeredDoubts = page.getByText('Answered');
    this.filterDoubts = page.getByText('Filter');
    this.doubtTransferedToTeacher = page.getByText('Transferred to Teacher'); //jee
    this.doubtTransferedToTeacherText = page.getByText("Ask a Teacher"); //Neet
    this.incorrectSubject = page.getByText('Incorrect subject & topic?');
    this.audioMicButton = page.getByTestId('audio_btn').getByRole('img');
    this.imageFileButton = page.getByTestId('file-exp_btn').getByRole('img');
    this.pleaseCheckOtherText = page.getByText('Please check your other');
    this.closeGreatPopup = page.getByRole('dialog').locator('svg');
    this.fileInput = page.getByTestId('file-input');
    this.uploadImage = page.getByText('Upload Image');
    this.browseYourComputer = page.getByText('Browse your computer and');
    this.browseFilesText = page.getByText('Browse Files');
    this.closeImageUploadPopUp = page.getByTestId('close-icon').locator('circle');
    this.imageText = page.getByText('Image', { exact: true });
    this.imageEditButton = page.getByTestId('edit-button');
    this.deleteImageButton = page.getByTestId('delete-button');
    this.imageUploadButton = page.getByText('Upload', { exact: true });
    this.takeAudioOption = page.getByText('Record audio');
    this.recordMaximumLength = page.getByText('You can record for max 8 min.');
    this.clickTheRecordingButton = page.getByText('Click record button to start recording');
    this.startAudioButton = page.getByTestId('start-audio').locator('div').first();
    this.audioInProgress = page.getByRole('img', { name: 'audio_progress' });
    this.audioDoneButton = page.getByTestId('audio-done');
    this.recordedAudio = page.locator("//*[@id='vuesax/linear/play']");
    this.needMoreHelpText = page.getByText("No, need more help");
    this.askedDoubtTextInTeacherSideNeet = page.locator(`[role="presentation"] >> text='${doubtForTestingNeet}'`);
    this.topicSelectForFilter = page.locator('//div[text()="Topic"]/following-sibling::div//div[contains(@class, "cursor-pointer")]');
    this.sortBySelectForFilter = page.locator('//div[text()="Sort by"]/following-sibling::div//div[contains(@class, "cursor-pointer")]');
    this.searchTopicInput = page.getByPlaceholder("Search...");
    this.sortByLatestText = page.getByText("Latest");
    this.searchResultFirstOption = page.locator("//div[data-testid='dropdown-option-0']");
    this.applyFilterButton = page.getByText("Apply Filter");
    this.reAssignDoubtModal = page.getByText("Select the batch, subject & topic for the doubt to be assigned correctly:");
    this.selectBatchText = page.getByText("SELECT BATCH");
    this.batchSearchInput = page.getByPlaceholder("Search...");
    this.firstOptionOfBatchDropDown = page.getByTestId("dropdown-option-0");
    this.subjectsText = page.getByText("Subject");
    this.mathsSubjectText = page.locator("//div[@data-testid='chipComponent']:has-text('Maths')");
    this.selectTopicText = page.getByText("SELECT TOPIC");
    this.reAssignButton = page.getByText("Re-assign doubt");
    this.doubtReAssignedToast = page.getByTestId("toast_message_text");
    this.closeButton = page.getByTestId("close");
    this.reportIncorrectText = page.getByTestId('right_incorrect').getByText("Report incorrect").first();
    this.allenVerifiedText = page.getByText("ALLEN Verified");
    this.whyDidYouReportText = page.getByText("Why did you report this response as incorrect?");
    this.notFactuallyCorrect = page.getByTestId("report_incorrect_NOT_FACTUALLY_CORRECT");
    this.reportIncorrectBtnEnabled = page.getByTestId("report_incorrect_btn_enabled");
    this.reportSubmittedSuccesfullyToast = page.getByText("Report submitted successfully");
    this.repliedTextFromTeacher = page.getByText(`${repliedTextFromTeacherVar}`);
    this.editOptionDropdown = page.locator("//div[@id='anchorPosition']");
    this.editReplyOption = page.getByText("Edit");
    this.editedReplyText = page.getByText("987654321test");
    this.closeIconDoubtsAssignedToast = page.getByTestId("close-icon");
    this.imageReply = page.getByTestId('single_image').getByRole('img');
    this.doubtAssigned = page.getByText('Doubts assigned', { exact: true });
    this.doubtTopic = page.locator("xpath=//*[@class='ag-center-cols-container']//*[@row-index='0']//*[@col-id='topic']//*[contains(@class, 'leading-lg')]")
  }
  async verifyStudentRaisedDoubtAndTextReply(studentDoubtString, solutionForDoubt, stream) {
    await slowExpect(this.doubtsTitle, "Verify doubts title is visible").toBeVisible();
    await expect(this.listOfDoubtsSubTitle, "Verify list of doubts subtitle is visible").toBeVisible();
    await expect(this.unresolvedDoubts, "Verify unresolved tag is visible").toBeVisible();
    await expect(this.answeredDoubts, "Verify answered tag is visible").toBeVisible();
    await expect(this.filterDoubts, "Verify filter doubts is visible").toBeVisible();
    await expect(this.studentDoubt(studentDoubtString).first(), "Verify student raised doubt is visible in teacher side").toBeVisible();
    await this.studentDoubt(studentDoubtString).first().click();
    if (stream == "NEET") {
      await slowExpect(this.doubtTransferedToTeacher, "Verify doubt transfered to teacher is visible").toBeVisible();

    } else {
      await slowExpect(this.doubtTransferedToTeacher, "Verify doubt transfered to teacher is visible").toBeVisible();
    }
    await expect(this.incorrectSubject, "Verify incorrect subject option is visible").toBeVisible();
    await expect(this.audioMicButton, "Verify audio mic send option is visible").toBeVisible();
    await expect(this.imageFileButton, "Verify image file send option is visible").toBeVisible();
    //report incorrect

    // await this.verifyReportIncorrectDoubt(stream); // need fix for this, student side pop handling
    await expect(this.chatInputField, "Verify chat input field is visible").toBeVisible();
    await this.chatInputField.click();
    await this.chatInputField.fill(solutionForDoubt);

    await slowExpect(this.sendButton, "Verify send button is visible").toBeVisible();
    await this.sendButton.click();

    // await slowExpect(this.greatTextForSuccess, "Verify successfully resolved message").toBeVisible();
    // await expect(this.pleaseCheckOtherText, "Verify please check other text is visible").toBeVisible();
    // await expect(this.closeGreatPopup, "Verify close popup option is visible").toBeVisible();
    // await this.closeGreatPopup.click();
    //edit functionality
    await this.verifyEditOption();
  }

  async sendImageReply() {
    await slowExpect(this.imageFileButton, "Verify image file send option is visible").toBeVisible();
    await this.imageFileButton.click();
    await slowExpect(this.uploadImage, "Verify upload image text is visible").toBeVisible();
    await expect(this.browseYourComputer, "Verify browse from your computer text is visible").toBeVisible();
    await expect(this.browseFilesText, "Verify browse files text is visible").toBeVisible();
    await this.fileInput.setInputFiles("test-data/doubt-Image.png");
    await slowExpect(this.imageText, "Verify image text post image upload is visible").toBeVisible();
    await expect(this.imageEditButton, "Verify edit image button is visible").toBeVisible();
    await expect(this.deleteImageButton, "Verify delete image button is visible").toBeVisible();
    await expect(this.imageUploadButton, "Verify image upload button is visible").toBeVisible();
    await this.imageUploadButton.click();
    // await slowExpect(this.imageReply, "Verify image reply is visible").toBeVisible();
  }
  async verifyAndsendAudioReply() {
    await slowExpect(this.audioMicButton, "Verify audio mic send option is visible").toBeVisible();
    await this.audioMicButton.click();
    await expect(this.takeAudioOption, "Verify take audio option is visible").toBeVisible();
    await expect(this.recordMaximumLength, "Verify record maximum length is visible").toBeVisible();
    await expect(this.clickTheRecordingButton, "Verify click The Recording Button is visible").toBeVisible();
    await expect(this.startAudioButton, "Verify start Audio Button is visible").toBeVisible();
    await this.startAudioButton.click();
    await this.page.waitForTimeout(7000); // wait is required to record some audio
    await expect(this.audioInProgress, "Verify audio In Progress is visible").toBeVisible();
    await expect(this.audioDoneButton, "Verify audio done button is visible").toBeVisible();
    await this.audioDoneButton.click();
    await slowExpect(this.audioMicButton, "Verify audio mic button is visible").toBeVisible();
    await slowExpect(this.recordedAudio, "Verify recorded audio is visible").toBeVisible();
    await expect(this.sendButton, "Verify send button is visible").toBeVisible();
    await this.sendButton.click();
  }

  async verifyNeedMoreHelpAndDoSecondReply(secondSolutionForDoubt) {
    await slowExpect(this.needMoreHelpText, "Verify need more help text is visible").toBeVisible();
    await expect(this.audioMicButton, "Verify audio mic send option is visible").toBeVisible();
    await expect(this.imageFileButton, "Verify image file send option is visible").toBeVisible();
    await expect(this.chatInputField, "Verify chat input field is visible").toBeVisible();
    await this.chatInputField.click();
    await this.chatInputField.fill(secondSolutionForDoubt);
    await expect(this.sendButton, "Verify send button is visible").toBeVisible();
    await this.sendButton.click();
  }

  async verifyAssignDoubtToAnotherTeacher() {
    await expect(this.incorrectSubject, "Verify Incorrect subject and topic").toBeVisible();
    await this.incorrectSubject.click();
    await expect(this.reAssignDoubtModal, "Verify the Re-assign doubt modal is visible").toBeVisible();
    await expect(this.selectBatchText, "Verify Select batch text is visible").toBeVisible();
    await this.selectBatchText.click();
    await expect(this.batchSearchInput, "Verify batch Search Input").toBeVisible();
    await expect(this.firstOptionOfBatchDropDown, "Verify that he first option of the batch selection is visible").toBeVisible();
    await this.firstOptionOfBatchDropDown.click();
    await expect(this.subjectsText).toBeVisible();
    await expect(this.mathsSubjectText, "Verify Maths subject is visible").toBeVisible();
    await this.mathsSubjectText.click();
    await expect(this.selectTopicText, "Verify select topic text is visible").toBeVisible();
    await expect(this.firstOptionOfBatchDropDown, "Verify first option of search result").toBeVisible();
    await this.firstOptionOfBatchDropDown.click();
    await expect(this.reAssignButton, "Verify re-assign button is visible").toBeVisible();
    await this.reAssignButton.click();
    await slowExpect(this.doubtReAssignedToast, "Verify doubt re-assigned toast is visible").toBeVisible();
  }

  async verifyReportIncorrectDoubt(stream: String) {
    if (stream == "JEE") await expect(this.allenVerifiedText, "Verify Allen verified text is visible").toBeVisible();
    await expect(this.reportIncorrectText, "Verift report incorrect text is visible").toBeVisible();
    await this.reportIncorrectText.click();
    await expect(this.whyDidYouReportText, "Verify report incorrect modal is visible and why did you report text is visible").toBeVisible();
    await expect(this.notFactuallyCorrect, "Verify not factually correct text is visible").toBeVisible();
    await this.notFactuallyCorrect.click();
    await slowExpect(this.reportIncorrectBtnEnabled, "Verify enabled submit button is visible").toBeVisible();
    await this.reportIncorrectBtnEnabled.click();
    await slowExpect(this.reportSubmittedSuccesfullyToast, "Verify report submitted succesfully toast is visible").toBeVisible();
  }

  async verifyEditOption() {
    await slowExpect(this.repliedTextFromTeacher, "Verify replied text is visible").toBeVisible();
    setTimeout(async ()=>{await this.repliedTextFromTeacher.hover()}, 500);
    await expect(this.editOptionDropdown, "Verify edit option dropdown is visible").toBeVisible();
    await this.editOptionDropdown.click();
    await expect(this.editReplyOption, "Verify edit option is visible").toBeVisible();
    await this.editReplyOption.click();
    await expect(this.chatInputField, "Verify Chat input is visible").toBeVisible();
    await this.chatInputField.clear();
    await this.chatInputField.fill("987654321test");
    await expect(this.sendButton, "Verify send button is visble").toBeVisible();
    await this.sendButton.click();
    await this.editedReplyText.isVisible({ timeout: 10000 })
  }

  async filterAndVerifyDoubt(topic: string, stream: String) {
    await slowExpect(this.doubtsTitle, "Verify doubts title is visible").toBeVisible();
    await expect(this.filterDoubts, "Verify filter doubts is visible").toBeVisible();
    await this.filterDoubts.click();
    await slowExpect(this.applyFilterButton).toBeVisible();
    await slowExpect(this.topicSelectForFilter, "Verify Topic selector is visible").toBeVisible();
    await expect(this.sortBySelectForFilter, "Verify Sort by select is visible").toBeVisible();
    await this.topicSelectForFilter.click();
    await expect(this.searchTopicInput, "Verify search topic input is visible").toBeVisible();
    await this.searchTopicInput.fill(topic);
    await expect(this.firstOptionOfBatchDropDown, "Verify searched result is visible").toBeVisible();
    await this.firstOptionOfBatchDropDown.click();
    await this.sortBySelectForFilter.click();
    await slowExpect(this.sortByLatestText, "Verify sort by Latest option is visible").toBeVisible();
    await this.sortByLatestText.click();
    await this.applyFilterButton.click();
    if (stream == "JEE") {
      await expect(this.studentDoubt(`${doubtForTestingJEE}`).first(), "Verify student raised doubt is visible in teacher side").toBeVisible();
    }
    else {
      await expect(this.studentDoubt(`${doubtForTestingNeet}`).first(), "Verify student raised doubt is visible in teacher side").toBeVisible();
    }
  }
}