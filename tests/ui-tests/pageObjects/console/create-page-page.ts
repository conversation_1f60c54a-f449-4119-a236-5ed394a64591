
import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreatePagePageUrl = '/page-management/widget/create'

export class CreatePagePage extends ICPage {

    readonly apiLoader: Locator;
    readonly createPageTitleText: Locator;
    readonly pageDetailsTab: Locator;
    readonly pageConfigurationTab: Locator;
    readonly pageReviewTab: Locator;
    readonly pageNameFeild: Locator;
    readonly pageTitleFeild: Locator;
    readonly pageTypeDropdoen: Locator;
    readonly searchFeild: Locator;
    readonly selectDropdown: (selectName: string) => Locator;
    readonly addButton: Locator;
    readonly drageAndDropText: Locator;
    readonly pageDetailsHeaderText: Locator;
    readonly usePageLayoutButton: Locator;
    readonly widgetDetailsText: Locator;
    readonly widgetConstIdFeildName: Locator;
    readonly widgetConstIdFeild: Locator;
    readonly getButton: Locator;
    readonly saveDetailsButton: Locator;
    readonly cancelDetailsButton: Locator;
    readonly useWidgetFlowButton: Locator;
    readonly addNewSectionButton: Locator;
    readonly containerStyleButton: Locator;
    readonly sectionContainerConfigText: Locator;
    readonly backgroundColorText: Locator;
    readonly primaryColor: Locator;
    readonly cancelContainerButton: Locator;
    readonly backButton: Locator;
    readonly pageCreatedSuccessfullyToastMessage: Locator;
    readonly submitForApprovalButton: Locator;

    readonly pageDescriptionFeildName: Locator;
    readonly pageDescriptionFeild: Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, CreatePagePageUrl, isMobile);
        this.createPageTitleText = page.getByRole('heading', { name: 'Create Page' });
        this.pageDetailsTab = page.getByText('Step 1: Page Details');
        this.pageConfigurationTab = page.getByText('Step 2: Page Configuration');
        this.pageReviewTab = page.getByText('Step 3: Page Review');
        this.pageNameFeild = page.locator('div').filter({ hasText: /^Page Name$/ }).getByTestId('textInput');
        this.pageTitleFeild = page.locator('div').filter({ hasText: /^Page Title$/ }).getByTestId('textInput');
        this.pageTypeDropdoen = page.getByText('Select a Type');
        this.searchFeild = page.getByPlaceholder('Search...');
        this.selectDropdown = (selectName: string) => page.getByText(`${selectName}`);
        this.addButton = page.getByTestId('cta-button');
        this.drageAndDropText = page.getByRole('heading', { name: 'Drag and Drop Items here' });
        this.pageDetailsHeaderText = page.getByRole('heading', { name: 'Page Details' });
        this.usePageLayoutButton = page.getByRole('button', { name: 'Use Page Layout' });
        this.widgetDetailsText = page.getByText('Widget Details:');
        this.widgetConstIdFeildName = page.getByText('Widget Const ID');
        this.widgetConstIdFeild = page.getByRole('textbox');
        this.getButton = page.getByRole('button', { name: 'Get', exact: true });
        this.saveDetailsButton = page.getByRole('button', { name: 'Save Details' });
        this.cancelDetailsButton = page.getByRole('button', { name: 'Cancel' }).first();
        this.useWidgetFlowButton = page.getByRole('button', { name: 'Use Widget Flow' });
        this.addNewSectionButton = page.getByRole('button', { name: 'Add a new section', exact: true });
        this.containerStyleButton = page.locator('div').filter({ hasText: /^Align HorizontallyContainer Style$/ }).getByTestId('cta-button');
        this.sectionContainerConfigText = page.getByRole('heading', { name: 'Section Container Config' });
        this.backgroundColorText = page.getByText('Background color');
        this.primaryColor = page.getByText('primary');
        this.cancelContainerButton = page.getByRole('button', { name: 'Cancel' });
        this.backButton = page.getByRole('button', { name: '< Back' });
        this.submitForApprovalButton = page.getByRole('button', { name: 'Submit For Approval' });
        this.pageCreatedSuccessfullyToastMessage = page.getByText('Page Created Successfully');
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.pageDescriptionFeildName = page.getByText('Page Description');
        this.pageDescriptionFeild = page.locator('div').filter({ hasText: /^Page Description$/ }).getByTestId('textInput');

    }

    /* verifying to create page page */
    async verifyCreatePagePage() {
        await expect(this.createPageTitleText, "Verify create page title text is visible").toBeVisible();
        await expect(this.pageDetailsTab, "Verify page details tab is visible").toBeVisible();
        await expect(this.pageConfigurationTab, "Verify page configuration tab is visible").toBeVisible();
        await expect(this.pageReviewTab, "Verify page review tab is visible").toBeVisible();
        await this.pageDetailsTab.click();
    }

    /* verifying to create page page */
    async enterPageDetails(pageName, pageType) {
        await expect(this.pageNameFeild, "Verify page name feild is visible").toBeVisible();
        await this.pageNameFeild.fill(pageName);
        await expect(this.pageTitleFeild, "Verify page title feild is visible").toBeVisible();
        await this.pageTitleFeild.fill(pageName);
        await expect(this.pageTypeDropdoen, "Verify page type select dropdown is visible").toBeVisible();
        await this.pageTypeDropdoen.click();
        await expect(this.searchFeild, "Verify search feild is visible").toBeVisible();
        await this.searchFeild.fill(pageType);
        await expect(this.selectDropdown(pageType), "Verify selected widget type is visible").toBeVisible();
        await this.selectDropdown(pageType).click();
    }

    async clickOnAddButton() {
        await expect(this.addButton, "Verify add button is visible").toBeVisible();
        await this.addButton.click();
    }

    /* verifying to create page page */
    async verifyEnterPageConfiguration() {
        await expect(this.drageAndDropText, "Verify drage and drop here text is visible").toBeVisible();
        await expect(this.pageDetailsHeaderText, "Verify page details header text is visible").toBeVisible();
        await expect(this.usePageLayoutButton, "Verify use page layout button is visible").toBeVisible();
        await expect(this.widgetDetailsText, "Verify widget details text is visible").toBeVisible();
        await expect(this.widgetConstIdFeildName, "Verify widget const id feild name is visible").toBeVisible();
    }

    /* enter widget const id and save details */
    async enterWidgetIdAndSaveDetails(widgetconstId) {
        await expect(this.widgetConstIdFeild, "Verify widget const id feild is visible").toBeVisible();
        await this.widgetConstIdFeild.fill(widgetconstId);
        await expect(this.getButton, "Verify get button is visible").toBeVisible();
        await this.getButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.saveDetailsButton, "Verify save details button is visible").toBeVisible();
        await expect(this.cancelDetailsButton, "Verify cancel details button is visible").toBeVisible();
        await this.saveDetailsButton.click();

    }

    async verifyClickOnPageLaout() {
        await expect(this.usePageLayoutButton, "Verify use page layout button is visible").toBeVisible();
        await this.usePageLayoutButton.click();
        await expect(this.useWidgetFlowButton, "Verify use widget flow button is visible").toBeVisible();
        await expect(this.addNewSectionButton, "Verify add new section button is visible").toBeVisible();
        await this.addNewSectionButton.click();
        await expect(this.containerStyleButton, "Verify container style button is visible").toBeVisible();
        await this.containerStyleButton.click();
        await expect(this.sectionContainerConfigText, "Verify section container config text is visible").toBeVisible();
        await expect(this.backgroundColorText, "Verify background color text is visible").toBeVisible();
        await expect(this.primaryColor, "Verify primary color selected is visible").toBeVisible();
        await expect(this.cancelContainerButton, "Verify container cancel button is visible").toBeVisible();
        await this.cancelContainerButton.click();
        await expect(this.useWidgetFlowButton, "Verify use widget flow button is visible").toBeVisible();
        await this.useWidgetFlowButton.click();
        await slowExpect(this.usePageLayoutButton, "Verify use page layout button is visible").toBeVisible();
    }

    async verifyPageReviewTab(pageName) {
        await expect(this.pageDetailsHeaderText, "Verify widget details title text is visible").toBeVisible();
        await expect(this.backButton, "Verify back button is visible").toBeVisible();
        await expect(this.selectDropdown(pageName), "Verify page name is visible").toBeVisible();
        await expect(this.submitForApprovalButton, "Verify cancel button is visible").toBeVisible();
        await this.submitForApprovalButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.pageCreatedSuccessfullyToastMessage, "Verify page created successfully toast message is visible").toBeVisible();
    }

    async verifyEditPage(pageDescriptionName) {
        await expect(this.pageDescriptionFeildName, "Verify page description feild name is visible").toBeVisible();
        await expect(this.pageDescriptionFeild, "Verify page description feild is visible").toBeVisible();
        await this.pageDescriptionFeild.fill(pageDescriptionName);

        await expect(this.nextButton, "Verify next button is visible").toBeVisible();
        await this.nextButton.click();
        await slowExpect(this.nextButton, "Verify next button is visible").toBeVisible();
        await this.nextButton.click();
        await expect(this.submitForApprovalButton, "Verify submit for approval button is visible").toBeVisible();
        await this.submitForApprovalButton.click();
    }
}