import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { EnvUtils } from '../../../commons/env-utilities';

const currentEnv = EnvUtils.getInstance();

const BasicDetailsPageUrl = '/test-management/student-tests/create'

export class BasicDetailsPage extends ICPage {

  readonly basicDetailsTitle: Locator;
  readonly testDetailsTitle: Locator;
  readonly testNameInput: Locator;
  readonly testTypeDropDownButton: Locator;
  readonly selectObjective: Locator;
  readonly selectSubjective: Locator;
  readonly testDisplayNameDropDownButton: Locator;
  readonly selectDisplayName: (displayName: string) => Locator;
  readonly testDisplayNumber: Locator;
  readonly enterSyllabusPublicURL: Locator;
  readonly enterOnlineBatch: Locator;
  readonly enterOfflineBatchId: Locator;
  readonly TestModeOfflineText: Locator;
  readonly TestModeCBTText: Locator;
  readonly enterDateAndTime: Locator;
  readonly enterTestDuration: Locator;
  readonly enterWindowLoginDuration: Locator;
  readonly createAndNextButton: Locator;
  readonly categoryDropdown: Locator;
  readonly classroomCategory: Locator;
  readonly uploadSyllabusInput: Locator;
  readonly generateRank: Locator;
  readonly testLevelOption: Locator;
  readonly eyCategoryDropdown: Locator;
  readonly otherOption: Locator;
  readonly assignOnlineBatch: Locator;
  readonly assignOfflineBatch: Locator;
  readonly selectSession: Locator;
  readonly selectCenter: Locator;
  readonly selectStream: Locator;
  readonly selectClass: Locator;
  readonly selectLanguage: Locator;
  readonly selectCourseMode: Locator;
  readonly selectCourse: Locator;
  readonly selectPhase: Locator;
  readonly selectCampus: Locator;
  readonly selectBatch: Locator;
  readonly selectSessionOption: Locator;
  readonly selectCenterOption: (centerId: string) => Locator;
  readonly selectStreamOption: Locator;
  readonly selectClassOption: Locator;
  readonly selectLanguageOption: (language: string) => Locator;
  readonly selectCourseModeOption: (courseMode: string) =>  Locator;
  readonly selectCourseOption: (courseId: string) => Locator;
  readonly selectPhaseOption: (courseName: string) => Locator;
  readonly selectPhaseCheckbox: Locator;
  readonly selectCampusOption: (facilityId) => Locator;
  readonly selectBatchOption: (batchName) => Locator;
  readonly assignBatchButton: Locator;
  readonly applyFilterButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, BasicDetailsPageUrl, isMobile);
    this.basicDetailsTitle = page.getByText('Basic Details', { exact: true });
    this.testDetailsTitle = page.getByText('Test Detail', { exact: true });
    this.testNameInput = page.getByTestId('display_name');
    this.testTypeDropDownButton = page.getByTestId('type');
    this.selectObjective = page.getByText('Objective', { exact: true });
    this.selectSubjective = page.getByText('Subjective', { exact: true });
    this.testDisplayNameDropDownButton = page.getByTestId('student_test_display_details.name');
    this.selectDisplayName = (displayName: string) => page.getByText(`${displayName}`, { exact: true });
    this.testDisplayNumber = page.getByTestId('student_test_display_details.number');
    this.enterSyllabusPublicURL = page.getByTestId('syllabus_pdf_link');
    this.enterOnlineBatch = page.getByTestId('batchOnline');
    this.enterOfflineBatchId = page.getByTestId('batchOffline');
    this.TestModeOfflineText = page.getByText('Test Mode: Offline *')
    this.TestModeCBTText = page.getByText('Test Mode: CBT *')
    this.enterDateAndTime = page.getByPlaceholder('Select start time & date');
    this.enterTestDuration = page.getByTestId('schedule.duration_in_minutes');
    this.enterWindowLoginDuration = page.getByTestId('schedule.login_window_in_minutes');
    this.createAndNextButton = page.getByRole('button', { name: 'Create & next' });
    this.categoryDropdown = page.getByTestId('category');
    this.classroomCategory = page.getByTestId('category').getByText('Classroom');
    this.uploadSyllabusInput = page.getByTestId('file-input');
    this.generateRank = page.getByTestId('generate_ranks');
    this.testLevelOption = page.getByText('Test Level');
    this.eyCategoryDropdown = page.getByTestId('ey_category');
    this.otherOption = page.getByText("Other");
    this.assignOnlineBatch = page.getByTestId('batch-field-batchOnline');
    this.assignOfflineBatch = page.getByTestId('batch-field-batchOffline');
    this.selectSession = page.getByTestId('academic_session-dropdown-grouping');
    this.selectCenter = page.getByTestId('facilities-dropdown-grouping');
    this.selectStream = page.getByTestId('streams-dropdown-grouping');
    this.selectClass = page.getByTestId('classes-dropdown-grouping');
    this.selectLanguage = page.getByTestId('languages-dropdown-grouping');
    this.selectCourseMode = page.getByTestId('modes-dropdown-grouping');
    this.selectCourse = page.getByTestId('courses-dropdown-grouping');
    this.selectPhase = page.getByTestId('phases-dropdown-grouping');
    this.selectCampus = page.getByTestId('campus-dropdown-grouping');
    this.selectBatch = page.getByTestId('batch-dropdown-grouping');
    this.selectSessionOption= page.getByTestId('academic_session-dropdown-grouping-option-04_2024__03_2025');
    this.selectCenterOption = (centerId: string) => page.getByTestId(`facilities-dropdown-grouping-option-${centerId}`);
    this.selectStreamOption = page.getByTestId('streams-dropdown-grouping-option-STREAM_JEE_MAIN_ADVANCED');
    this.selectClassOption = page.getByTestId('classes-dropdown-grouping-option-CLASS_11');
    this.selectLanguageOption = (language: string) => page.getByTestId(`languages-dropdown-grouping-option-LANGUAGE_${language}`);
    this.selectCourseModeOption = (courseMode: string) => page.getByTestId(`modes-dropdown-grouping-option-MODE_${courseMode}`);
    this.selectCourseOption = (courseId: string) => page.getByTestId(`courses-dropdown-grouping-option-${courseId}`);
    this.selectPhaseOption = (courseName: string) => page.getByText(`${courseName}`);
    this.selectPhaseCheckbox = page.getByTestId('batch-modal').locator('div').filter({ hasText: /^1$/ }).getByRole('checkbox');
    this.selectCampusOption = (facilityId) => page.getByTestId(`campus-dropdown-grouping-option-${facilityId}`);
    this.selectBatchOption = (batchName) => page.getByTestId(`batch-dropdown-grouping-option-${batchName}`);
    this.assignBatchButton = page.getByTestId('batch-assign-btn')
    this.applyFilterButton = page.getByTestId('apply-filter-btn');
  }

  async enterBasicDetailsForTestCreation(testName, displayName, testDisplayNumber, SyllabusPublicURL, batchId, centerId, language, courseMode, courseId, courseName, facilityId, currentDateAndTime, testDuration, windowLoginDuration, testTypeValue, batchType, eyCatergory) {
    await expect(this.basicDetailsTitle, 'Verify basic details title is not visible').toBeVisible();
    await expect(this.testDetailsTitle, 'test details sub-title is not visible').toBeVisible();
    await this.testNameInput.fill(testName);
    await expect(this.categoryDropdown, "verify category dropdown is visible").toBeVisible();
    await this.categoryDropdown.click();
    await expect(this.classroomCategory, "verify category type classroom dropdown is visible").toBeVisible();
    await this.classroomCategory.click();
    await expect(this.testTypeDropDownButton, "verify test type dropdown is visible").toBeVisible();
    await this.testTypeDropDownButton.click();
    switch (testTypeValue) {
      case 'objective':
        await expect(this.selectObjective, "verify objective dropdown is visible").toBeVisible();
        await this.selectObjective.click();
        await expect(this.eyCategoryDropdown, "Verify EY category dropdown is visible").toBeVisible();
        await this.eyCategoryDropdown.click();
        await this.otherOption.click();
        break;
      case 'subjective':
        await expect(this.selectSubjective, "verify objective dropdown is visible").toBeVisible();
        await this.selectSubjective.click();
        break;
      default:
        throw new Error(`Invalid test type: ${testTypeValue}`);
    }
    // await expect(this.selectObjective, "verify objective dropdown is visible").toBeVisible();
    // await this.selectObjective.click();
    await expect(this.testDisplayNameDropDownButton, "verify test display name dropdown is visible").toBeVisible();
    await this.testDisplayNameDropDownButton.click();
    await this.selectDisplayName(displayName).click();
    await this.testDisplayNumber.fill(testDisplayNumber);
    await expect(this.generateRank, "verify generate rank dropdown is visible").toBeVisible();
    await this.generateRank.click();
    await expect(this.testLevelOption, "verify no rank option is visible").toBeVisible();
    await this.testLevelOption.click();
    await this.enterSyllabusPublicURL.fill(SyllabusPublicURL);
    await this.page.waitForLoadState('networkidle');
    switch (batchType) {
      case 'onlineBatch':
        await expect(this.assignOnlineBatch, 'assign online batch is visible').toBeVisible();
        await this.assignOnlineBatch.click();
        break;
      case 'offlineBatch':
        await expect(this.TestModeOfflineText, 'test mode offline text is not visible').toBeVisible();
        await expect(this.assignOfflineBatch, 'assign online batch is visible').toBeVisible();
        await this.assignOfflineBatch.click();
        break;
      // default:
      //   throw new Error(`Invalid batch type: ${batchType}`);
    }
    // await this.enterOnlineBatch.fill(onlineBatch);
    await expect(this.selectSession, "Verify select session dropdown is visible").toBeVisible();
    await this.selectSession.click();
    await expect(this.selectSessionOption, "Verify select session option is visible").toBeVisible();
    await this.selectSessionOption.click();
    await expect(this.selectCenter, "Verify select center dropdown is visible").toBeVisible();
    await this.selectCenter.click();
    await expect(this.selectCenterOption(centerId), "Verify select center option is visible").toBeVisible();
    await this.selectCenterOption(centerId).click();
    await expect(this.selectStream, "Verify select stream dropdown is visible").toBeVisible();
    await this.selectStream.click();
    await expect(this.selectStreamOption, "Verify select stream option is visible").toBeVisible();
    await this.selectStreamOption.click();
    await expect(this.selectClass, "Verify select class dropdown is visible").toBeVisible();
    await this.selectClass.click();
    await expect(this.selectClassOption, "Verify select class option is visible").toBeVisible();
    await this.selectClassOption.click();
    await expect(this.selectLanguage, "Verify select language dropdown is visible").toBeVisible();
    await this.selectLanguage.click();
    await expect(this.selectLanguageOption(language), "Verify select language option is visible").toBeVisible();
    await this.selectLanguageOption(language).click();
    await expect(this.selectCourseMode, "Verify select course mode dropdown is visible").toBeVisible();
    await this.selectCourseMode.click();
    await expect(this.selectCourseModeOption(courseMode), "Verify select course mode option is visible").toBeVisible();
    await this.selectCourseModeOption(courseMode).click();
    await expect(this.selectCourse, "Verify select course dropdown is visible").toBeVisible();
    await this.selectCourse.click();
    await expect(this.selectCourseOption(courseId), "Verify select course option is visible").toBeVisible();
    await this.selectCourseOption(courseId).click();
    await expect(this.selectPhase, "Verify select phase dropdown is visible").toBeVisible();
    await this.selectPhase.click();
    await expect(this.selectPhaseOption(courseName), "Verify select phase option is visible").toBeVisible();
    await this.selectPhaseOption(courseName).click();
    await this.selectPhaseCheckbox.click();
    await expect(this.selectCampus, "Verify select campus dropdown is visible").toBeVisible();
    await this.selectCampus.click();
    await expect(this.selectCampusOption(facilityId), "Verify select campus option is visible").toBeVisible();
    await this.selectCampusOption(facilityId).click();
    await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
    await this.applyFilterButton.click();
    await expect(this.selectBatch, "Verify select batch dropdown is visible").toBeVisible();
    await this.selectBatch.click();
    await expect(this.selectBatchOption(batchId), "Verify select batch option is visible").toBeVisible();
    await this.selectBatchOption(batchId).click();
    await expect(this.assignBatchButton, "Verify assign batch button is visible").toBeVisible();
    await this.assignBatchButton.click();
    await expect(this.TestModeOfflineText, 'test mode offilne text is not visible').toBeVisible();
    await expect(this.TestModeCBTText, 'test mode cbt text is not visible').toBeVisible();
    await this.enterDateAndTime.fill(currentDateAndTime);
    await this.enterTestDuration.fill(testDuration);
    await this.enterWindowLoginDuration.fill(windowLoginDuration);
  }
}
