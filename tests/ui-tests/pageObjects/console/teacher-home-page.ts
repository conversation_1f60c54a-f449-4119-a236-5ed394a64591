import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const TeacherHomePageUrl = (isMobile: boolean): string =>
  isMobile ? '/teacher/home' : '/my-classes/schedule';
const uploadFile = "test-data/uploadfile.pdf"

export class TeacherHomePage extends ICPage {

  readonly profileNameButton: Locator;
  readonly internalUserButton: Locator;
  readonly internalUserHeaderTitle: Locator;
  readonly studentHelpButtonText: Locator;
  readonly doubtsSideTabButton: Locator;
  readonly myClassesText: Locator;
  readonly userRolesText: Locator;
  readonly prevWeekButton: Locator;
  readonly todayButton: Locator;
  readonly nextWeeKButton: Locator;
  readonly scheduledClassName: (className: string) => Locator;
  readonly scheduledClassNameOnCalenderJoin: (className: string) => Locator;
  readonly scheduledBatchCode: (batchCode: string) => Locator;
  readonly selfMaterialText: Locator;
  readonly joinClassButton: Locator;
  readonly homeworkForTodayText: Locator;
  readonly createHomeworkButton: Locator;
  readonly materialsForStudents: Locator;
  readonly noMaterialsAddedText: Locator;
  readonly uploadSelfMaterialButton: Locator;
  readonly uploadStudentMaterialButton: Locator;
  readonly uploadMaterialsForSelfUseText: Locator;
  readonly filesSelectedText: Locator;
  readonly fileInput: Locator;
  readonly dragAndDropYourFilesButton: Locator;
  readonly fileUploadedIcon: Locator;
  readonly deleteUploadIcon: Locator;
  readonly uploadFileCloseIcon: Locator;
  readonly uploadMaterialsForStudentText: Locator;
  readonly deleteFileIcon: Locator;
  readonly downloadFileIcon: Locator;
  readonly gridCell: Locator;
  readonly materialsTab: Locator;
  readonly viewHomeworkButton: Locator;
  readonly liveClassClassMode: Locator;
  readonly liveTelecastClassMode: Locator;
  readonly offlineClassClassMode: Locator;
  readonly recordingOnlyClassMode: Locator;
  readonly zoomClassClassMode: Locator;
  readonly scheduleRoomId: (roomId: string) => Locator;
  readonly profileNameText: Locator;
  readonly studentsDoubtOption: Locator;
  readonly sipClassMode: Locator;
  readonly apiLoader: Locator;
  readonly arrowDown: (className: string) => Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, TeacherHomePageUrl(isMobile), isMobile);
    this.profileNameButton = page.getByTestId('user-display-name');
    this.internalUserButton = page.getByTestId('option-access-role-internal_user').getByText('internal_user');
    this.internalUserHeaderTitle = page.getByText('Internal Users');
    this.studentHelpButtonText = page.getByText('Student Help');
    this.doubtsSideTabButton = page.getByTestId('sidebar-subtab-item-Doubts');
    this.myClassesText = page.getByText('My Classes');
    this.userRolesText = page.getByTestId('user-all-roles');
    this.prevWeekButton = page.getByText('Prev Week');
    this.todayButton = page.getByText('Today');
    this.nextWeeKButton = page.getByText('Next Week');
    this.scheduledClassName = (className: string) => isMobile ? page.getByText(`${className}`).first() : page.getByRole('button', { name: `${className}` });
    this.scheduledClassNameOnCalenderJoin = (className: string) => page.getByText(`${className}`).nth(1);
    this.scheduledBatchCode = (batchCode: string) => page.getByRole('dialog').getByText(`${batchCode}`);
    this.selfMaterialText = page.getByText('Self Material');
    this.materialsForStudents = page.getByText('Materials for students');
    this.joinClassButton = page.getByText('Join Class');
    this.homeworkForTodayText = page.getByText('Homework for today');
    this.createHomeworkButton = isMobile ? page.locator('[style="transform: translateY(0%);"]').getByText('Create Homework') : page.getByText('Create Homework');
    this.noMaterialsAddedText = page.getByText('No materials added');
    this.uploadSelfMaterialButton = page.locator(`//*[text()="Self Material"]/ancestor::div[contains(@class,"justify-between")]/descendant::div[text()="Upload"]`);
    this.uploadStudentMaterialButton = page.locator(`//*[text()="Materials for students"]/ancestor::div[contains(@class,"justify-between")]/descendant::div[text()="Upload"]`);
    this.uploadMaterialsForSelfUseText = page.getByText('Upload materials for self-use');
    this.fileInput = page.locator(`//input[@type="file"]`);
    this.dragAndDropYourFilesButton = page.getByRole('button', { name: 'Document Drag & Drop your' });
    this.fileUploadedIcon = page.getByTestId('file-modal-cross-icon').getByTestId('shownImage');
    this.deleteUploadIcon = page.locator("//*[contains(@data-testid,'delete-file-')]").locator('visible=true');
    this.uploadFileCloseIcon = page.locator("//*[@data-testid='file-modal-cross-icon']").locator('visible=true');
    this.uploadMaterialsForStudentText = page.getByText('Upload materials for students');
    this.deleteFileIcon = page.locator("//*[contains(@src,'trash.svg')]");
    this.downloadFileIcon = page.locator("//*[contains(@data-testid,'download-file-')]").locator('visible=true');
    this.gridCell = page.getByRole('gridcell').first();
    this.gridCell = page.getByRole('gridcell').first();
    this.materialsTab = page.getByText('Materials');
    this.viewHomeworkButton = isMobile ? page.locator('[style="transform: translateY(0%);"]').getByText('View Homework') : page.getByRole('button', { name: 'View Homework' });

    this.studentsDoubtOption = page.locator('[data-testid="sidebar-subtab-item-Doubts"]');
    this.liveClassClassMode = page.getByText('Live Class');
    this.liveTelecastClassMode = page.getByText('Live Telecast', { exact: true });
    this.offlineClassClassMode = page.getByText('Offline Class', { exact: true });
    this.recordingOnlyClassMode = page.getByText('Recording Only');
    this.zoomClassClassMode = page.getByRole('button', { name: 'View Homework' });
    this.scheduleRoomId = (roomId: string) => page.getByText(`${roomId}`);
    this.profileNameText = page.getByTestId('user-about');
    this.sipClassMode = page.getByText('SIP Class');
    this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
    this.arrowDown = (className: string) => page.getByText(`${className}`).locator('xpath=../../../following-sibling::div[.//*[@data-testid="cta-button"]]/div').first();
  }

  /* navigating to doubts page */
  async navigateToDoubtsPage() {
    await expect(this.profileNameButton, "verify profile name button is visible").toBeVisible();
    await expect(this.myClassesText, "verify my class text is visible").toBeVisible();
    await expect(this.userRolesText, "verify user roles text is visible").toBeVisible();
    await expect(this.studentHelpButtonText, "verify student help button is visible").toBeVisible();
    await this.studentHelpButtonText.click();
    await expect(this.doubtsSideTabButton, "Verify doubts side menu button is visible").toBeVisible();
    await this.doubtsSideTabButton.click();
  }

  /* navigating to InternalUser List page */
  async navigateToInternalUser() {

    if (await this.profileNameButton.isVisible()) {
      await expect(this.profileNameButton, 'verify profile name is visible').toBeVisible();
      await this.profileNameButton.click();
    }
    else {
      await expect(this.profileNameText, 'verify profile name text is visible').toBeVisible();
      await this.profileNameText.click();
    }
    // await this.page.waitForTimeout(2000); //commented for testing by removing waits
    await slowExpect(this.internalUserButton, 'verify internal user button is visible').toBeVisible();
    await this.internalUserButton.click();
    await this.page.waitForLoadState('networkidle');
    await expect(this.internalUserHeaderTitle, 'verify internal user header title is visible').toBeVisible();
    await slowExpect(this.gridCell).toBeVisible();
  }

  async validateTeacherCalender() {
    await customExpect(15000)(this.prevWeekButton, 'verify Previous Week button is visible').toBeVisible();
    await expect(this.todayButton, 'Verify Today button is visible').toBeVisible();
    await expect(this.nextWeeKButton, 'verify Next week button is visible').toBeVisible();
  }

  async clickOnClassScheduled(className: string) {
    await expect(this.scheduledClassName(className), "verify Schedule className is visible").toBeVisible;
    await this.scheduledClassName(className).click();
  }

  async validateScheduledClassDetailsOnTeacherSide(className, batchCode, roomId) {
    await expect(this.scheduledClassNameOnCalenderJoin(className), "verify scheduled className on calender side join is visible").toBeVisible();
    await expect(this.scheduledBatchCode(batchCode), "verify scheduled batch code on calender side join is visible").toBeVisible();
    // await expect(this.scheduleRoomId(roomId), "verify scheduled room id on calender side join is visible").toBeVisible();

    await customExpect(20000)(this.homeworkForTodayText, "verify home work for today text is visible").toBeVisible();
    await expect(this.createHomeworkButton, "verify create homework button is visible").toBeVisible();
    await expect(this.materialsTab, "verify materials tab is visible").toBeVisible();
    await this.materialsTab.click();
    await expect(this.selfMaterialText, "verify self material text is visible").toBeVisible();
    await expect(this.materialsForStudents, "verify materials for student text is visible").toBeVisible();
  }

  async clickOnJoinClassButton() {
    await this.joinClassButton.click();
  }

  async uploadSelfMaterial() {
    await expect(this.uploadSelfMaterialButton, "verify upload material button is visible").toBeVisible();
    await this.uploadSelfMaterialButton.click();
    await expect(this.uploadMaterialsForSelfUseText, "verify upload material for self use text is visible").toBeVisible();
    await this.fileInput.setInputFiles(uploadFile);
    await slowExpect(this.fileUploadedIcon, "verify file uploaded is visible").toBeVisible();
    await expect(this.deleteUploadIcon, "verify delete icon is visible").toBeVisible();
    await expect(this.uploadFileCloseIcon, "verify upload file close icon is visible").toBeVisible();
    await this.uploadFileCloseIcon.click();

  }

  async uploadMaterialsForStudent() {
    await expect(this.uploadStudentMaterialButton, "verify upload materials for student button is visible").toBeVisible();
    await this.uploadStudentMaterialButton.click();
    await expect(this.uploadMaterialsForStudentText, "verify upload material for self use text is visible").toBeVisible();
    await this.fileInput.setInputFiles(uploadFile);
    await slowExpect(this.fileUploadedIcon, "verify file uploaded is visible").toBeVisible();
    await expect(this.deleteUploadIcon, "verify delete icon is visible").toBeVisible();
    await expect(this.uploadFileCloseIcon, "verify upload file close icon is visible").toBeVisible();
    await this.uploadFileCloseIcon.click();
  }

  async teacherNavigatingToCalenderPage() {
    await this.page.goto(`${this.url}`);

    if (this.isMobile) {
      await expect(this.page).toHaveURL(/.*home/);
    }
    else {
      await expect(this.page).toHaveURL(/.*schedule/);
    }
  }

  async verifyDownloadDeleteIcon() {
    await expect(this.downloadFileIcon, "verify upload file close icon is visible").toBeVisible();
    await expect(this.deleteFileIcon, "verify upload file close icon is visible").toBeVisible();
    await this.deleteFileIcon.click();
  }

  async navigateToCreateHomeWorkPage(className) {
    if (!this.isMobile) {
      await slowExpect(this.homeworkForTodayText, "verify home work for today text is visible").toBeVisible();
    }
    else {
      await slowExpect(this.arrowDown(className), "Verify arrow down button is visible").toBeVisible();
      await this.arrowDown(className).click();
    }

    await expect(this.createHomeworkButton, "verify create homework button is visible").toBeVisible();
    await this.createHomeworkButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  async verifyClassMode(classMode) {
    await this.page.waitForTimeout(2000); // required
    switch (classMode) {
      case 'online':
        await expect(this.liveClassClassMode, "verify live class classmode text is visible").toBeVisible();
        break;
      case 'liveTelecast':
        await expect(this.liveTelecastClassMode, "verify live telecast classmode text is visible").toBeVisible();
        break;
      case 'offline':
        await expect(this.offlineClassClassMode, "verify offline class classmode text is visible").toBeVisible();
        break;
      case 'recordingOnly':
        await expect(this.recordingOnlyClassMode, "verify recording only class classmode text is visible").toBeVisible();
        break;
      case 'zoom':
        await expect(this.zoomClassClassMode, "verify zoom class classmode text is visible").toBeVisible();
        break;
      case 'sip':
        await expect(this.sipClassMode, "verify SIPclass classmode text is visible").toBeVisible();
        break;
      default:
        throw new Error(`Invalid poll type: ${classMode}`);
    }
  }

}