import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { execPath } from 'process';

const ScheduleMentorshipPageUrl = '/course-management'


export class ScheduleMentorshipPage extends ICPage {

    readonly generalSection: Locator;
    readonly unresponsiveSection: Locator;
    readonly needsUpdateSection: Locator;
    readonly attendanceLast30days: Locator;
    readonly lastTestTotalMarks: Locator;
    readonly upcomingMeetingsNullValue: Locator;
    readonly searchByDropdown: Locator;
    readonly dropdownOptionFormId: Locator;
    readonly dropdownOptionBatchCode: Locator;
    readonly searchInput: Locator;
    readonly searchInputButton: Locator;
    readonly formIdCheckbox: (checkboxFormId: string) => Locator;
    readonly scheduleMeetingButton: Locator;
    readonly apiLoader: Locator;
    readonly scheduleMeetingHeader: Locator;
    readonly studentsSelectedText: Locator;
    readonly datetitle: Locator;
    readonly dateinput: Locator;
    readonly timeTitle: Locator;
    readonly startTimeInput: Locator;
    readonly endTimeInput: Locator;
    readonly modeTtile: Locator;
    readonly addguestsTitle: Locator;
    readonly cancelbutton: Locator;
    readonly scheduleButton: Locator;
    readonly meetingScheduledTitle: Locator;
    readonly viewButton: Locator;
    readonly rescheduleButton: Locator;
    readonly joinMeetingButton: Locator;
    readonly addNotesButton: Locator;
    readonly addNotesHeader: Locator;
    readonly scheduledText: Locator;
    readonly notesForStudent: Locator;
    readonly notesForMentor: Locator;
    readonly notesForStudentInput: Locator;
    readonly notesForMentorInput: Locator;
    readonly saveAndCloseButton: Locator;
    readonly rescheduleMeetingHeader: Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, ScheduleMentorshipPageUrl, isMobile);
        this.generalSection = page.getByText('General');
        this.unresponsiveSection = page.getByText('Unresponsive');
        this.needsUpdateSection = page.getByText('Unresponsive');
        this.attendanceLast30days = page.getByText('Attendance (last 30 days)');
        this.lastTestTotalMarks = page.getByText('Last Test Total Marks');
        this.upcomingMeetingsNullValue = page.getByText('-').first();
        this.searchByDropdown = page.getByTestId('search-by-dropdown');
        this.dropdownOptionFormId = page.getByTestId('search-by-dropdown-option-form_id');
        this.dropdownOptionBatchCode = page.getByTestId('search-by-dropdown-option-batch_code');
        this.searchInput = page.getByTestId('search-input');
        this.searchInputButton = page.getByTestId('text-inside-input');
        this.formIdCheckbox = (checkboxFormId: string) => page.locator(`//*[text()='${checkboxFormId}']/ancestor::*[@class='ag-cell-wrapper']//*[@type='checkbox']`);
        this.scheduleMeetingButton = page.getByTestId('sort-search-primary-button').getByText('Schedule Meeting');
        this.scheduleMeetingHeader = page.getByRole('heading', { name: 'Schedule a Meeting' });
        this.studentsSelectedText = page.getByText('Students Selected:');
        this.datetitle = page.getByText('Date', { exact: true });
        this.dateinput = page.locator('input[name="scheduledDate"]');
        this.timeTitle = page.getByText('Time');
        this.startTimeInput = page.locator('input[name="scheduledStartTime"]');
        this.endTimeInput = page.locator('input[name="scheduledEndTime"]');
        this.addguestsTitle = page.getByText('Add Guests');
        this.modeTtile = page.getByText('Mode');
        this.cancelbutton = page.getByRole('button', { name: 'Cancel' });
        this.scheduleButton = page.getByRole('button', { name: 'Schedule' });
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.meetingScheduledTitle = page.getByText('Meeting Scheduled');
        this.viewButton = page.getByTestId('cta-button').getByText('View').first();
        this.addNotesButton = page.getByRole('button', { name: 'Add notes' });
        this.rescheduleButton = page.getByRole('button', { name: 'Reschedule' });
        this.joinMeetingButton = page.getByRole('button', { name: 'Join meeting' });
        this.addNotesHeader = page.getByRole('heading', { name: 'Add Notes' });
        this.scheduledText = page.getByText('Scheduled');
        this.notesForStudent = page.getByText('Notes for student');
        this.notesForMentor = page.getByText('Notes for mentor');
        this.notesForStudentInput = page.locator('textarea[name="notesForStudent"]');
        this.notesForMentorInput = page.locator('textarea[name="notesForMentor"]');
        this.saveAndCloseButton = page.getByRole('button', { name: 'Save & Close' });
        this.rescheduleMeetingHeader = page.getByRole('heading', { name: 'Reschedule this meeting' });
    }

    async validateScheduleMentorshipPage() {
        await slowExpect(this.generalSection, "Verify general section is visible").toBeVisible();
        await expect(this.needsUpdateSection, "Verify needs update section is visible").toBeVisible();
        await expect(this.unresponsiveSection, "Verify unresponsive section is visible").toBeVisible();
        await expect(this.needsUpdateSection, 'Verify needs update section is visible').toBeVisible();
        await expect(this.attendanceLast30days, 'Verify attendance last 30 days is visible').toBeVisible();
        await expect(this.lastTestTotalMarks, 'Verify last test total marks is visible').toBeVisible();
    }

    async validateSearchFeature(searchFormId) {
        await slowExpect(this.searchByDropdown, "Verify search by dropdown is visible").toBeVisible();
        await this.searchByDropdown.click();
        await expect(this.dropdownOptionFormId, "Verify dropdown option form id is visible").toBeVisible();
        await expect(this.dropdownOptionBatchCode, "Verify dropdown option batchcode is visible").toBeVisible();
        await this.dropdownOptionFormId.click();
        await expect(this.searchInput, 'Verify search input field is visible').toBeVisible();
        await this.searchInput.click();
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await this.searchInput.fill(searchFormId);
        await expect(this.searchInputButton, 'Verify search button is visible').toBeVisible();
        await this.searchInputButton.click();
    }

    async validateAndScheduleMeetinginHomepage(dateValue, startTime, endTime) {
        await this.scheduleMeetingButton.click();
        await slowExpect(this.scheduleMeetingHeader, "Verify schedule meeting header is visible").toBeVisible();
        await expect(this.studentsSelectedText, "Verify students selected text is visible").toBeVisible();
        await expect(this.datetitle, "Verify date title is visible").toBeVisible();
        await expect(this.dateinput, "Verify date input is visible").toBeVisible();
        await this.dateinput.fill(dateValue);
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await expect(this.timeTitle, "Verify time title is visible").toBeVisible();
        await expect(this.startTimeInput, "Verify start time input is visible").toBeVisible();
        await this.startTimeInput.fill(startTime);
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await expect(this.endTimeInput, "Verify end time input is visible").toBeVisible();
        await expect(this.addguestsTitle, "Verify add guests title is visible").toBeVisible();
        await expect(this.modeTtile, "Verify mode title is visible").toBeVisible();
        await expect(this.cancelbutton, "Verify cancel button is visible").toBeVisible();
        await expect(this.scheduleButton, "Verify schedule button is disabled").toBeDisabled();
        await this.endTimeInput.fill(endTime);
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await expect(this.scheduleButton, "Verify schedule button is enabled").toBeEnabled();
        await this.scheduleButton.click();
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
    }

    async validateAndClassMeetingByAddingNotes(notesForStudent, notesForMentor) {
        await slowExpect(this.addNotesButton, "Verify add notes button is visible").toBeVisible();
        await expect(this.rescheduleButton, "Verify reschedule button is visible").toBeVisible();
        await expect(this.joinMeetingButton, "Verify join meeting button is visible").toBeVisible();
        await this.addNotesButton.click();
        await slowExpect(this.addNotesHeader, "Verify add notes header is visible").toBeVisible();
        await expect(this.notesForStudent, "Verify add notes for student title is visible").toBeVisible();
        await expect(this.notesForStudentInput, "Verify add notes for student input is visible").toBeVisible();
        await this.notesForStudentInput.fill(notesForStudent);
        await expect(this.notesForMentor, "Verify add notes for mentor title is visible").toBeVisible();
        await expect(this.notesForMentorInput, "Verify add notes for mentor input is visible").toBeVisible();
        await this.notesForMentorInput.fill(notesForMentor);
        await expect(this.scheduledText, "Verify scheduled button is visible").toBeVisible();
        await this.scheduledText.click();
        await expect(this.saveAndCloseButton, "Verify save and close button is visible").toBeVisible();
        await this.saveAndCloseButton.click();
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
    }

    async validateAndRescheduleClass(dateValue, startTime, endTime) {
        await expect(this.rescheduleButton, "Verify reschedule button is visible").toBeVisible();
        await this.rescheduleButton.click();
        await slowExpect(this.rescheduleMeetingHeader, "Verify reschedule header is visible").toBeVisible();
        await expect(this.datetitle, "Verify date title is visible").toBeVisible();
        await expect(this.dateinput, "Verify date input is visible").toBeVisible();
        await this.dateinput.fill(dateValue);
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await expect(this.timeTitle, "Verify time title is visible").toBeVisible();
        await expect(this.startTimeInput, "Verify start time input is visible").toBeVisible();
        await this.startTimeInput.fill(startTime);
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await expect(this.endTimeInput, "Verify end time input is visible").toBeVisible();
        await expect(this.addguestsTitle, "Verify add guests title is visible").toBeVisible();
        await expect(this.modeTtile, "Verify mode title is visible").toBeVisible();
        await expect(this.cancelbutton, "Verify cancel button is visible").toBeVisible();
        await expect(this.scheduleButton, "Verify schedule button is disabled").toBeDisabled();
        await this.endTimeInput.fill(endTime);
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        await expect(this.scheduleButton, "Verify schedule button is enabled").toBeEnabled();
        await this.scheduleButton.click();
        await slowExpect(this.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
    }

}