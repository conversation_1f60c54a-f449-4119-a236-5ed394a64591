import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const PageCreationEditPageUrl = '/page-management/page'

export class PageCreationEditPage extends ICPage {

    readonly apiLoader: Locator;
    readonly pageCreationTitleText: Locator;
    readonly createPageButton: Locator;
    readonly viewPagesText: Locator;
    readonly applyFilterButton: Locator;
    readonly resetFilterButton: Locator;
    readonly pageNameRow: (widgetName: string) => Locator;
    readonly pageConstId: (widgetName: string) => Locator;

    readonly copyToClipBoardWidgetConstId: (widgetConstId: string) => Locator;
    readonly copiedText: Locator;
    readonly pageConstIdFeild: Locator;
    readonly linkedUrlsRowTitleText: Locator;
    readonly formattedBulletedIcon: Locator;

    readonly urlsLinkedToPagePopUpTitle: Locator;
    readonly allTheUrlsLinkedText: Locator;
    readonly linkedUrlText: (urlName: string) => Locator;
    readonly closeIcon: Locator;
    readonly pageEditedSuccessfullyToastMessage: Locator;
    readonly viewPage: Locator;




    constructor(page: Page, isMobile: boolean) {
        super(page, PageCreationEditPageUrl, isMobile);

        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.applyFilterButton = page.getByRole('button', { name: 'Apply Filters' });
        this.resetFilterButton = page.getByRole('button', { name: 'Reset Filters' });
        this.viewPagesText = page.getByRole('heading', { name: 'View Pages' });
        this.pageCreationTitleText = page.getByText('Page Creation & Edits');
        this.viewPage = page.getByRole('heading', { name: 'View Pages' });
        this.createPageButton = page.getByRole('button', { name: 'Create Page' });
        this.pageNameRow = (pageName: string) => page.locator(`xpath=//span[contains(@id,'cell-name') and text()='${pageName}']/ancestor::div[contains(@class, 'ag-row')]/descendant::*[@row-id="0"]`);
        this.pageConstId = (pageName: string) => (this.pageNameRow(pageName)).locator(`xpath=.//a[contains(@class, 'underline cursor-pointer text-blue500')]`);
        this.copyToClipBoardWidgetConstId = (pageConstId: string) => page.locator(`//*[text()='${pageConstId}']/ancestor::*[contains(@id,"cell-page_id")]//following-sibling::*//*[@data-testid="CopyAllIcon"]`);
        this.copiedText = page.getByText('Copied!');
        this.pageConstIdFeild = page.getByPlaceholder('Page const Id');
        this.linkedUrlsRowTitleText = page.getByText('Linked URLs');
        this.formattedBulletedIcon = page.getByTestId('FormatListBulletedIcon');
        this.urlsLinkedToPagePopUpTitle = page.getByText('URLs linked to this page', { exact: true });
        this.allTheUrlsLinkedText = page.getByText('All the URLs linked to this');
        this.linkedUrlText = (urlName: string) => page.getByRole('link', { name: `${urlName}` });
        this.closeIcon = page.getByRole('button', { name: 'delete_icon' });
        this.pageEditedSuccessfullyToastMessage = page.locator(`//*[contains(text(),"Page Updated and Activated Successfully, Active")]`)

    }

    /* verifying to page creation and edits page */
    async verifyPageCreationAndEditsPage() {
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.pageCreationTitleText, "Verify page creation and edits title text is visible").toBeVisible();
        await expect(this.viewPagesText, "Verify view pages text is visible").toBeVisible();
        await expect(this.createPageButton, "Verify create page button is visible").toBeVisible();
        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await expect(this.resetFilterButton, "Verify reset filter button is visible").toBeVisible();
    }

    /* verifying to page creation and edits page */
    async clickOnCreatePageButton() {
        await expect(this.createPageButton, "Verify create page button is visible").toBeVisible();
        await this.createPageButton.click();
    }

    /* verifying page created  */
    async verifyCreatedPage(pageName) {
        await expect(this.pageNameRow(pageName), "Verify created widget name is visible").toBeVisible();
        await expect(this.pageConstId(pageName), "Verify create widget const id is visible").toBeVisible();
        const widgetContsIdLocator = await this.pageConstId(pageName);
        const constId = await widgetContsIdLocator.textContent() ?? "";
        return constId;
    }

    /* verifying page created  */
    async verifyPageConstIdCopyToClipboard(pageConstId) {
        await expect(this.copyToClipBoardWidgetConstId(pageConstId), "Verify copy to clipboard is visible").toBeVisible();
        await this.copyToClipBoardWidgetConstId(pageConstId).click();
        await expect(this.copiedText, "Verify copied text is visible").toBeVisible();
    }

    /* verifying to page linked to created url */
    async verifyPageLinkedToUrl(pageConstId, urlName) {
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.pageConstIdFeild, "Verify page const id feild is visible").toBeVisible();
        await this.pageConstIdFeild.fill(pageConstId);
        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await expect(this.resetFilterButton, "Verify reset filter button is visible").toBeVisible();
        await this.applyFilterButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await slowExpect(this.linkedUrlsRowTitleText, "Verify linked url's row title text is visible").toBeVisible();
        await slowExpect(this.formattedBulletedIcon, "Verify formatted button icon is visible").toBeVisible();
        await this.formattedBulletedIcon.click();
        await expect(this.urlsLinkedToPagePopUpTitle, "Verify urls linked to page title text is visible").toBeVisible();
        await expect(this.allTheUrlsLinkedText, "Verify all the urls linked text is visible").toBeVisible();
        await expect(this.linkedUrlText(urlName), "Verify linked url text is visible").toBeVisible();
        await expect(this.closeIcon, "Verify close icon is visible").toBeVisible();
        await this.closeIcon.click();
    }

    /* click to widget created */
    async clickOnCreatedPagetId(pageName) {
        await expect(this.pageConstId(pageName), "Verify page created const id is visible").toBeVisible();
        await this.pageConstId(pageName).click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    }

    async verifyPageEditedSuccessfully() {
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.pageEditedSuccessfullyToastMessage, "Verify page edited successfully toast message is visible").toBeVisible();
    }

    /* verifying to widget filter with const id */
    async verifyPageFilter(pageConstId) {
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.pageConstIdFeild, "Verify page const id feild is visible").toBeVisible();
        await this.pageConstIdFeild.fill(pageConstId);
        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await expect(this.resetFilterButton, "Verify reset filter button is visible").toBeVisible();
        await this.applyFilterButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    }



}