
import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreateUrlPageUrl = '/page-management/url/create'

export class CreateUrlPage extends ICPage {

    readonly apiLoader: Locator;
    readonly createUrlHederTitle: Locator;
    readonly urlFeildName: Locator;
    readonly urlFeild: Locator;
    readonly pageConstIdFeildName: Locator;
    readonly pageConstIdFeild: Locator;
    readonly getButton: Locator;
    readonly getName: (name: string) => Locator;
    readonly targetText: Locator;
    readonly defaultTargetOption: Locator;
    readonly submitButton: Locator;
    readonly cancelButton: Locator;
    readonly urlCreatedSuccessfullyToastMessage: Locator;
    readonly selectCategory: Locator;
    readonly categorySearch: Locator;
    readonly urlCategoryOption: (category: string) => Locator;


    constructor(page: Page, isMobile: boolean) {
        super(page, CreateUrlPageUrl, isMobile);

        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.createUrlHederTitle = page.getByRole('heading', { name: 'Create URL' });
        this.urlFeildName = page.getByText('URL', { exact: true });
        this.urlFeild = page.getByPlaceholder('Enter URL');
        this.pageConstIdFeildName = page.getByText('Page Const ID');
        this.pageConstIdFeild = page.getByPlaceholder('Enter Page Const Id');
        this.getButton = page.getByRole('button', { name: 'Get' });
        this.getName = (name: string) => page.getByText(`${name}`);
        this.targetText = page.getByRole('heading', { name: 'Targeting' });
        this.defaultTargetOption = page.locator('label').filter({ hasText: 'Default' });
        this.submitButton = page.getByRole('button', { name: 'Submit' });
        this.cancelButton = page.getByRole('button', { name: 'Cancel' });
        this.urlCreatedSuccessfullyToastMessage = page.getByText('URL Created Successfully');
        this.selectCategory = page.getByText('Select Category');
        this.categorySearch = page.getByPlaceholder('Search...');
        this.urlCategoryOption = (category: string) => page.getByText(category);

    }

    /* verifying to url creation and edits page */
    async enterAndVerifyCreateUrlPage(urlName, pageConstId, pageName, pageType, category: string) {
        await expect(this.createUrlHederTitle, "Verify create url herder title is visible").toBeVisible();
        await expect(this.urlFeildName, "Verify url name  feild is visible").toBeVisible();
        await expect(this.urlFeild, "Verify url feild is visible").toBeVisible();
        await this.urlFeild.fill(urlName);

        await expect(this.pageConstIdFeildName, "Verify page const id feild name is visible").toBeVisible();
        await expect(this.pageConstIdFeild, "Verify page const id feild is visible").toBeVisible();
        await this.pageConstIdFeild.fill(pageConstId);

        await expect(this.getButton, "Verify get button is visible").toBeVisible();
        await this.getButton.click();
        await expect(this.getName(pageName), "Verify page name is visible").toBeVisible();
        await expect(this.getName(pageType), "Verify page type is visible").toBeVisible();
        await expect(this.selectCategory, "Verify select category dropdown is visible").toBeVisible();
        await this.selectCategory.click();
        await expect(this.categorySearch, "Verify category search is visible").toBeVisible();
        await this.categorySearch.fill(category);

        await expect(this.urlCategoryOption(category)).toBeVisible();
        await this.urlCategoryOption(category).click();

        await expect(this.targetText, "Verify targetting text is visible").toBeVisible();
        await expect(this.defaultTargetOption, "Verify default target option selected is visible").toBeVisible();
    }


    /* verifying to url creation and edits page */
    async clickOnSubmitButton() {
        await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
        await expect(this.submitButton, "Verify submit button is visible").toBeVisible();
        await this.submitButton.click();
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();

    }

    async verifyUrlCreated() {
        await expect(this.urlCreatedSuccessfullyToastMessage, "Verify url created successfully toast message is visible").toBeVisible();

    }
}