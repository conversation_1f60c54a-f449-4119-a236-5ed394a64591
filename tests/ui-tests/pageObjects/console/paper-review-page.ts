import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const PaperReviewPageeUrl = 'test-management/student-tests/edit'

export class PaperReviewPage extends ICPage {
  readonly enterPaperCode: Locator;
  readonly importPaperTiltle: Locator;
  readonly noDocumentsText: Locator;
  readonly importAndPublishButton: Locator;
  readonly paperPublishedToastMessage: Locator;
  readonly previewQuestionPaperButton: Locator;
  readonly testStatus: Locator;
  readonly createdTestId: Locator;
  readonly sideMenuTestManagement: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, PaperReviewPageeUrl, isMobile);
    this.importPaperTiltle = page.getByRole('heading', { name: 'Import paper' });
    this.enterPaperCode = page.getByTestId('paper_code');
    this.noDocumentsText = page.getByText('No documents');
    this.importAndPublishButton = page.getByText('Import & Publish');
    this.paperPublishedToastMessage = page.getByText('Imported paper & Test Published successfully');
    this.previewQuestionPaperButton = page.getByRole('button', { name: 'preview Show student\'s preview' });
    this.testStatus = page.getByTestId('test-status');
    this.createdTestId = page.getByTestId('test-id');
    this.sideMenuTestManagement = page.getByTestId('sidebar-tab-Test-Management');
  }

  async enterPaperCodeAndPublishTestWithReviewQuestionPaper(paperCode) {
    await customExpect(15000)(this.importPaperTiltle, 'Import paper header title is visible').toBeVisible();  // customExpect required to wait for the page load
    await expect(this.testStatus, 'test status is visible').toBeVisible();
    await customExpect(15000)(this.enterPaperCode, "enter papercode textfield is visible").toBeVisible();// customExpect required to wait for the page load
    await this.enterPaperCode.click();
    await this.enterPaperCode.fill(paperCode);
    await expect(this.noDocumentsText, 'no documents are available text is visible').toBeVisible();
    await expect(this.importAndPublishButton, 'import and publish button is visible').toBeVisible();
    await this.importAndPublishButton.click();
    await slowExpect(this.paperPublishedToastMessage, ' paper Published Toast Message is visible').toBeVisible();
    await expect(this.previewQuestionPaperButton, 'preview Question Paper Button is visible').toBeVisible();
  }
}
