import { expect, type Locator, type Page } from '@playwright/test';
import { BasePage } from '../base-page';

const adminURL = process.env.adminURL || process.env.PROD === '1' ? 'https://astra.allen.in' : 'https://console.allen-stage.in';


export class ICPage extends BasePage {
  readonly googleLogoButton: Locator;
  readonly emailOrPhoneInput: Locator;
  readonly nextButton: Locator;
  readonly enterPassword: Locator;

  constructor(page: Page, url: string, isMobile: boolean) {
    super(page, adminURL + url, isMobile);
    this.googleLogoButton = page.getByRole('button', { name: 'google logo Sign In With' });
    this.emailOrPhoneInput = page.getByLabel('Email or phone');
    this.nextButton = page.getByRole('button', { name: 'Next' });
    this.enterPassword = page.getByLabel('Enter your password');
  }

  /* IC Login with Email and password */
  async icLoginWithEmailAndPassword(emailId, email_password) {
    await expect(this.googleLogoButton, "verify google logo button to sign in").toBeVisible();
    await this.googleLogoButton.click();
    await expect(this.emailOrPhoneInput, "Verify enter email input field").toBeVisible();
    await this.emailOrPhoneInput.click();
    await this.emailOrPhoneInput.fill(emailId);
    await expect(this.nextButton, "verify next button is visible").toBeVisible();
    await this.nextButton.click();
    await expect(this.enterPassword, "verify enter password is visible").toBeVisible();
    await this.enterPassword.fill(email_password);
    await expect(this.nextButton, "verify next button is visible to login gmail").toBeVisible();
    await this.nextButton.click();
  }
}
