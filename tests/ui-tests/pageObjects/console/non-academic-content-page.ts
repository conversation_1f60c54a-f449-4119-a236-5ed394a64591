import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import exp from 'constants';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const NonAcademicContentPageUrl = '/content-management/nac'

export class NonAcademicContentPage extends ICPage {

  readonly nonAcademicContentText: Locator;
  readonly newUploadButton: Locator;
  readonly browseFileButton: Locator;
  readonly fileInput: Locator;
  readonly csvUploadFileInput: Locator;
  readonly uploadCsvText: Locator;
  readonly filesSelectedText: Locator;
  readonly fillFromConsoleButton: Locator;
  readonly completeMetaDataText: Locator;
  readonly pleaseFillCellsText: Locator;
  readonly contentNameInput: Locator;
  readonly newContentInput: Locator;
  readonly contentTypeInput: Locator;
  readonly contentTypeAsEducation: Locator;
  readonly contentSubTypeInput: Locator;
  readonly contentSubTypeInputSelect: Locator;
  readonly contentDescriptionFeild: Locator;
  readonly contentLanguageInput: Locator;
  readonly contentLanguageInputSelect: Locator;
  readonly contentSessionInput: Locator;
  readonly contentSessionInputSelect: Locator;
  readonly selectFilecheckBox: Locator;
  readonly inputEditorFeild: Locator;
  readonly contentUploadButton: Locator;
  readonly uploadedSuccessfullyText: Locator;
  readonly uploadedContent: (contentName: string) => Locator;
  readonly contentTypeTitle: Locator;
  readonly contentSubTypeTitle: Locator;
  readonly contentDescriptionTitle: Locator;
  readonly languageTitle: Locator;
  readonly sessionTitle: Locator;
  readonly centreTitle: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, NonAcademicContentPageUrl, isMobile);
    this.nonAcademicContentText = page.getByText('Non Academic Content');
    this.newUploadButton = page.getByText('New Upload', { exact: true });
    this.browseFileButton = page.getByRole('button', { name: 'Browse Files' });
    this.fileInput = page.getByTestId('file-input');
    this.uploadCsvText = page.getByText('Upload .csv for bulk files');
    this.filesSelectedText = page.getByText('files selected');
    this.csvUploadFileInput = page.getByText('If you have metadata stored');
    this.fillFromConsoleButton = page.getByRole('button', { name: 'Fill from console' });
    this.completeMetaDataText = page.getByText('Complete Meta Data');
    this.pleaseFillCellsText = page.getByText('Please fill the cells that');
    this.contentNameInput = page.locator("(//*[@col-id='name'])[2]");
    this.contentTypeInput = page.locator("(//*[@col-id='type'])[2]");
    // this.contentTypeInput = page.locator('((//*[@col-id="type"]//*[@class="text-textPlaceholder"]/../../../*)[2])/*[@alt="down-arrow"]');
    this.contentTypeAsEducation = page.locator('//*[@id="type_EDUCATION_AND_LEARNING"]');
    this.contentSubTypeInput = page.locator("(//*[@col-id='sub_type'])[2]");
    this.contentSubTypeInputSelect = page.locator('//*[@id="sub_type_FACTS"]');
    this.contentDescriptionFeild = page.locator('(//*[@col-id="description"])[2]');
    this.inputEditorFeild = page.getByLabel('Input Editor');
    this.contentLanguageInput = page.locator("(//*[@col-id='language'])[2]");
    this.contentLanguageInputSelect = page.locator('//*[@id="language_ENGLISH"]');
    this.contentSessionInput = page.locator("(//*[@col-id='sessions'])[2]");
    this.contentSessionInputSelect = page.getByText('04/2024 - 03/2025', { exact: true });
    this.selectFilecheckBox = page.getByLabel('Press Space to toggle row');
    this.contentUploadButton = page.getByRole('button', { name: 'UPLOAD' });
    this.uploadedSuccessfullyText = page.locator("//*[contains(text(),'Uploaded successfully')]");
    this.uploadedContent = (contentName: string) => page.getByText(`${contentName}`);
    this.contentTypeTitle = page.locator("//*[text()='Content Type*']");
    this.contentSubTypeTitle = page.locator("//*[text()='Content Sub Type*']");
    this.contentDescriptionTitle = page.locator("//*[text()='Content Description*']");
    this.languageTitle = page.locator("//*[text()='Language*']");
    this.sessionTitle = page.locator("//*[text()='Session*']");
    this.centreTitle = page.locator("//*[text()='Centre']");
  }

  async uploadNewContent(file) {
    await slowExpect(this.nonAcademicContentText, "veify non academic content text is visible").toBeVisible();
    await expect(this.newUploadButton, "veify new upload button is visible").toBeVisible();
    await this.newUploadButton.click();
    await expect(this.browseFileButton, "veify browse file button is visible").toBeVisible();
    await this.fileInput.setInputFiles(file);
    await expect(this.filesSelectedText, "verify file selected text is visible").toBeVisible();
    await expect(this.uploadCsvText, "verify upload csv text is visible").toBeVisible();
    await expect(this.fillFromConsoleButton, "verify fill from console button is visible").toBeVisible();
    await this.fillFromConsoleButton.click();
  }

  async completeMetaDataUpload(description, newContentName) {
    const waitTime = 25000;
    await this.page.waitForLoadState('networkidle');
    await expect(this.completeMetaDataText, "verify complete meta data text is visible").toBeVisible();
    await expect(this.pleaseFillCellsText, "verify Please fill the cells that text is visible").toBeVisible();
    await expect(this.selectFilecheckBox, "verify file select checkboxis visble").toBeVisible();
    await this.selectFilecheckBox.check();
    await expect(this.selectFilecheckBox, "verify file selected checkbox is visble").toBeChecked();
    await expect(this.contentNameInput, "verify content name feild  is visible").toBeVisible();
    await this.contentNameInput.dblclick();
    await this.inputEditorFeild.fill(newContentName);
    await expect(this.contentTypeTitle, "verify content type title is visible").toBeVisible();
    await this.contentTypeTitle.click();
    await expect(this.contentTypeInput, "verify content type text is visible").toBeVisible();
    await this.contentTypeInput.click();
    await slowExpect(this.contentTypeAsEducation, "verify type educational and learning option is visible").toBeVisible();
    await this.contentTypeAsEducation.click();
    await expect(this.contentSubTypeTitle, "verify content sub type title is visible").toBeVisible();
    await this.contentSubTypeTitle.click();
    await expect(this.contentSubTypeInput, "verify content sub type text  is visible").toBeVisible();
    await this.contentSubTypeInput.click();
    await expect(this.contentSubTypeInputSelect, "verify type facts option is visible").toBeVisible();
    await this.contentSubTypeInputSelect.click();
    await expect(this.contentDescriptionTitle, "verify content description type title is visible").toBeVisible();
    await this.contentDescriptionTitle.click();
    await expect(this.contentDescriptionFeild, "verify content description input field is visble").toBeVisible();
    await this.contentDescriptionFeild.dblclick();
    await this.inputEditorFeild.fill(description);
    await expect(this.languageTitle, "verify language title is visible").toBeVisible();
    await this.languageTitle.click();
    await expect(this.contentLanguageInput, "verify content language text  is visible").toBeVisible();
    await this.contentLanguageInput.click();
    await expect(this.contentLanguageInputSelect, "verify language option is visible").toBeVisible();
    await this.contentLanguageInputSelect.click();
    await expect(this.sessionTitle, "verify session title is visible").toBeVisible();
    await this.sessionTitle.click();
    await expect(this.contentSessionInput, "verify content session text  is visible").toBeVisible();
    await this.contentSessionInput.click();
    await expect(this.contentSessionInputSelect, "verify session option is visible").toBeVisible();
    await this.contentSessionInputSelect.click();
    await expect(this.centreTitle, "verify centre title is visible").toBeVisible();
    await this.centreTitle.click();
    await expect(this.contentUploadButton, "verify content upload button is visible").toBeVisible();
    await this.contentUploadButton.click();
    await customExpect(waitTime)(this.uploadedSuccessfullyText).toBeVisible();
    // await customExpect(waitTime)(this.uploadedContent(newContentName)).toBeVisible();
  }

}


