import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import exp from 'constants';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';


const TaxonamyPageUrl = '/content-management/taxonomy'
const env = process.env.PROD === "1" ? "prod" : "stage";

export class TaxonamyPage extends ICPage {

  readonly taxonamyManagementText: Locator;
  readonly UploadCenterTaxonamyText: Locator;
  readonly centerDropdown: Locator;
  readonly streamDropdown: Locator;
  readonly classDropdown: Locator;
  readonly searchTextFeild: Locator;
  readonly centerName: (centerName: string) => Locator;
  readonly streamName: (streamName: string) => Locator;
  readonly className: (streamName: string) => Locator;
  readonly UploadTaxonamyButton: Locator;
  readonly fileInput: Locator;
  readonly existingTaxonamyToastMessage: Locator;

  readonly viewAndEditTaxonamyButton: Locator;
  readonly editTaxonamyButton: Locator;
  readonly editTaxonamyText: Locator;
  readonly subjectDropdown: Locator;
  readonly subjectName: (subjectName: string) => Locator;
  readonly addIcon: Locator;
  readonly addSubjectTitleText: Locator;
  readonly addButton: Locator;
  readonly addingErrorToastMessage: Locator;
  readonly renameButton: Locator;
  readonly saveButton: Locator;
  readonly sucessfullyUpdatedToastMessage: Locator;
  readonly sessionTitleText: Locator;
  readonly sessionDropDown: Locator;
  readonly sessionValue: (sessionValue: string) => Locator;
  readonly uploadButton: Locator;
  readonly useExsitingButton: Locator;
  readonly centerDropdownTo: Locator;
  readonly streamDropdownTo: Locator;
  readonly sessionDropDownTo: Locator;
  readonly borrowTaxonomyButton: Locator;
  readonly destinationTaxonomyExistToastMessage: Locator;
  readonly allenDigitalNewPlatformText: Locator;
  readonly questionBankText: Locator;



  constructor(page: Page, isMobile: boolean) {
    super(page, TaxonamyPageUrl, isMobile);
    this.taxonamyManagementText = page.getByText('Taxonomy Management');
    this.UploadCenterTaxonamyText = page.getByRole('button', { name: 'Upload Taxonomy' });
    this.centerDropdown = page.getByTestId('center_id-dropdown').first();
    this.streamDropdown = page.getByTestId('stream-dropdown').first();
    this.classDropdown = page.getByTestId('class-dropdown');
    this.searchTextFeild = page.getByPlaceholder('Search...');
    this.centerName = (centerName: string) => page.locator(`//*[contains(@data-testid,"center_id-dropdown-option-") and text()='${centerName}']`);
    this.streamName = (streamName: string) => page.locator(`//*[contains(@data-testid,"stream-dropdown-option-") and text()='${streamName}']`);
    this.className = (className: string) => page.locator(`//*[contains(@data-testid,"class-dropdown-option-") and text()='${className}']`);
    this.UploadTaxonamyButton = page.getByRole('button', { name: 'Upload Taxonomy' });
    this.fileInput = page.getByTestId('metadata-file-input');
    this.existingTaxonamyToastMessage = page.locator('//*[@data-testid="toast_message_text" and contains(text(), "Taxonomy already exists for this center+stream+cla")]');
    this.viewAndEditTaxonamyButton = page.getByText('View & Edit Taxonomy');
    this.editTaxonamyButton = page.getByRole('button', { name: 'Edit Taxonomy' });
    this.editTaxonamyText = page.getByText('Edit Taxonomy');
    this.subjectDropdown = page.locator('div').filter({ hasText: /^SubjectSelect\.\.\.$/ }).getByTestId('dropdown');
    this.subjectName = (subjectName: string) => page.locator(`//*[contains(@data-testid,"dropdown-option-") and text()='${subjectName}']`);
    this.addIcon = page.locator('div').filter({ hasText: /^SubjectChemistry$/ }).getByTestId('shownImage');
    this.addSubjectTitleText = page.getByText('Add Subject');
    this.addButton = page.getByRole('button', { name: 'Add' });
    this.addingErrorToastMessage = page.locator('//*[@data-testid="toast_message_text" and text()="Error in adding the node"]');
    this.renameButton = page.getByText('Rename');
    this.saveButton = page.getByRole('button', { name: 'Save' });
    this.sucessfullyUpdatedToastMessage = page.locator('//*[@data-testid="toast_message_text" and text()="Successfully updated"]');
    this.sessionTitleText = page.getByText('Session').first();
    this.sessionDropDown = page.getByTestId('session-dropdown').first();
    this.sessionValue = (sessionValue: string) => page.getByTestId(`session-dropdown-option-${sessionValue}`);
    this.uploadButton = page.getByRole('button', { name: 'Upload', exact: true });
    this.useExsitingButton = page.getByRole('button', { name: 'Use existing' });

    this.centerDropdownTo = page.getByTestId('center_id-dropdown').nth(1);
    this.streamDropdownTo = page.getByTestId('stream-dropdown').nth(1);
    this.sessionDropDownTo = page.getByTestId('session-dropdown').nth(1);
    this.borrowTaxonomyButton = page.getByRole('button', { name: 'Borrow Taxonomy' });
    this.destinationTaxonomyExistToastMessage = page.locator('//*[@data-testid="toast_message_text" and contains(text(), "BorrowTaxonomy: Destination Taxonomy already exist")]');
    this.allenDigitalNewPlatformText = page.getByText('Allen Digital New Platform');
    this.questionBankText = page.getByText('Question Bank');

  }
  async verifyUploadTaxonamyToCenterPage() {
    await expect(this.taxonamyManagementText, "Verify taxonamy management text is visible").toBeVisible();
    await expect(this.UploadCenterTaxonamyText, "Verify upload center taxonamy text is visible").toBeVisible();
    await expect(this.uploadButton, "Verify upload button is visible").toBeVisible();
    await expect(this.uploadButton, "Verify upload button is enabled").toBeEnabled();
    await expect(this.useExsitingButton, "Verify use exsiting button is visible").toBeVisible();
  }


  async uploadTaxonamyToCenterDetails(centerName, streamName, className, sessionValue) {

    await expect(this.centerDropdown, "Verify center dropdown is visible").toBeVisible();
    await this.centerDropdown.click();
    await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(centerName);
    await this.centerName(centerName).click();


    await expect(this.streamDropdown, "Verify stream dropdown is visible").toBeVisible();
    await this.streamDropdown.click();
    await expect(this.searchTextFeild, "Verify stream search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(streamName);
    await this.streamName(streamName).click();

    await expect(this.classDropdown, "Verify class dropdown is visible").toBeVisible();
    await this.classDropdown.click();
    await expect(this.searchTextFeild, "Verify class search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(className);
    await this.className(className).click();

    await expect(this.sessionTitleText, "Verify session title is visible").toBeVisible();
    await expect(this.sessionDropDown, "Verify session dropdown is visible").toBeVisible();
    await this.sessionDropDown.click();
    await expect(this.sessionValue(sessionValue), "Verify session value is visible").toBeVisible();
    await this.sessionValue(sessionValue).click();

  }

  async verifyUploadTaxonamyToCenter(file) {
    await expect(this.UploadTaxonamyButton, "Verify upload taxonomy button is visible").toBeVisible();
    await this.UploadTaxonamyButton.click();
    await this.fileInput.setInputFiles(file);
    await slowExpect(this.existingTaxonamyToastMessage, "Verify taxonomy already exists for this center+stream+class combination toast message is visible").toBeVisible();
  }

  async clickOnViewAndEditTaxonamy() {
    await expect(this.allenDigitalNewPlatformText, "Verify allen digital new platform text is visible").toBeVisible();
    await expect(this.questionBankText, "Verify question bank text is visible").toBeVisible();
    await expect(this.viewAndEditTaxonamyButton, "Verify view & edit taxonamy button is visible").toBeVisible();
    await this.viewAndEditTaxonamyButton.click();
  }

  async verifyEditTaxonamy(subjectName) {
    await expect(this.editTaxonamyButton, "Verify edit taxonamy button is visible").toBeVisible();
    await this.editTaxonamyButton.click();
    await expect(this.editTaxonamyText, "Verify edit taxonamy text is visible").toBeVisible();
    await expect(this.subjectDropdown, "Verify subject dropdown is visible").toBeVisible();
    await this.subjectDropdown.click();
    await this.searchTextFeild.fill(subjectName);

    await expect(this.subjectName(subjectName), "Verify subject name is visible").toBeVisible();
    await this.subjectName(subjectName).click();

    await expect(this.addIcon, "Verify add icon is visible").toBeVisible();
    await this.addIcon.click();

    await expect(this.addSubjectTitleText, "Verify add subject title text is visible").toBeVisible();
    await expect(this.addButton, "Verify add button is visible").toBeVisible();
    await this.addButton.click();
    await expect(this.addingErrorToastMessage, "Verify error in adding the node toast message is visible").toBeVisible();

    await expect(this.renameButton, "Verify rename button is visible").toBeVisible();
    await this.renameButton.click();

    await expect(this.saveButton, "Verify save button is visible").toBeVisible();
    await this.saveButton.click();
    await expect(this.sucessfullyUpdatedToastMessage, "Verify successfully updated toast message is visible").toBeVisible();
  }

  async selectFromBorrowTaxonamyDetails(centerName, streamName, sessionValue) {

    await expect(this.useExsitingButton, "Verify use exsiting button is visible").toBeVisible();
    await this.useExsitingButton.click();

    await expect(this.centerDropdown, "Verify center dropdown is visible").toBeVisible();
    await this.centerDropdown.click();
    await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(centerName);
    await this.centerName(centerName).click();

    await expect(this.streamDropdown, "Verify stream dropdown is visible").toBeVisible();
    await this.streamDropdown.click();
    await expect(this.searchTextFeild, "Verify stream search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(streamName);
    await this.streamName(streamName).click();

    await expect(this.sessionTitleText, "Verify session title is visible").toBeVisible();
    await expect(this.sessionDropDown, "Verify session dropdown is visible").toBeVisible();
    await this.sessionDropDown.click();
    await expect(this.sessionValue(sessionValue), "Verify session value is visible").toBeVisible();
    await this.sessionValue(sessionValue).click();
  }

  async selectToBorrowTaxonamyDetails(centerName, streamName, sessionValue) {

    await expect(this.centerDropdownTo, "Verify center dropdown is visible").toBeVisible();
    await this.centerDropdownTo.click();
    await expect(this.searchTextFeild, "Verify center search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(centerName);
    await this.centerName(centerName).click();

    await expect(this.streamDropdownTo, "Verify stream dropdown is visible").toBeVisible();
    await this.streamDropdownTo.click();
    await expect(this.searchTextFeild, "Verify stream search text feild is visible").toBeVisible();
    await this.searchTextFeild.fill(streamName);
    await this.streamName(streamName).click();

    await expect(this.sessionDropDownTo, "Verify session dropdown is visible").toBeVisible();
    await this.sessionDropDownTo.click();
    await expect(this.sessionValue(sessionValue), "Verify session value is visible").toBeVisible();
    await this.sessionValue(sessionValue).click();
  }


  async verifyBorrowTaxonamyToCenter() {
    await expect(this.borrowTaxonomyButton, "Verify borrow taxonomy button is visible").toBeVisible();
    await expect(this.borrowTaxonomyButton, "Verify borrow taxonomy button is enabled").toBeEnabled();
    if (env == 'stage') {
      await this.borrowTaxonomyButton.click();
      await slowExpect(this.destinationTaxonomyExistToastMessage, "veify Destination taxonomy already exis toast message is visible").toBeVisible();
    }
  }

  async clickOnQuestionBank() {
    await expect(this.allenDigitalNewPlatformText, "Verify allen digital new platform text is visible").toBeVisible();
    await expect(this.questionBankText, "Verify question bank text is visible").toBeVisible();
    await expect(this.viewAndEditTaxonamyButton, "Verify view & edit taxonamy button is visible").toBeVisible();
    await this.questionBankText.click();
  }
}


