import { expect, type Locator, type Page } from '@playwright/test';
import { ScheduleBatchPage } from './schedule-batch-page';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreateSchedulePageUrl = '/class-management/schedule'

export class ScheduleClassPage extends ICPage {

  readonly searchByCode: Locator;
  readonly searchButton: Locator;
  readonly viewClassesButton: Locator;
  readonly addRowButton: Locator;
  readonly classMode: Locator;
  readonly onlineClassMode: Locator;
  readonly subject: Locator;
  readonly chemistrySubject: Locator;
  readonly classSchedulingTitle: Locator;
  readonly apiLoader: Locator;
  readonly gridCell: Locator;
  readonly actionsMenu: Locator;
  readonly addClassButton: Locator;
  readonly addClassesToScheduleTitle: Locator;
  readonly classTypeDropdown: Locator;
  readonly regularClassType: Locator;
  readonly selectClassMode: Locator;
  readonly OnlineSelectClassMode: Locator;

  readonly selectSubject: Locator;
  readonly selectTopic: Locator;
  readonly selectFirstTopic: Locator;
  readonly selectTeacher: Locator;
  readonly teacherSearch: Locator;
  readonly teacherName: (teacherName: string) => Locator;
  readonly displayClassNameFeild: Locator;
  readonly dateSelect: Locator;
  readonly startTime: Locator;
  readonly endTime: Locator;
  readonly roomSelect: Locator;
  readonly addButton: Locator;
  readonly rowSavedSuccessfully: Locator;

  readonly publishedButton: Locator;
  readonly classDate: Locator;
  readonly startTimeTitle: Locator;
  readonly endTimeTitle: Locator;
  readonly draftCount: Locator;
  readonly draftDropdown: Locator;
  readonly changeDraftClass: Locator;
  readonly draftToPublished: Locator;
  readonly draftToDeleted: Locator;
  readonly classToBePublishedText: Locator;
  readonly saveChangesButton: Locator;
  readonly cancelButton: Locator;
  readonly changeStatusSuccessfully: Locator;
  readonly subjectTitle: Locator;
  readonly displayClassNameFeildTitle: Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, CreateSchedulePageUrl, isMobile);
    this.searchByCode = page.getByTestId('textInput');
    this.searchButton = page.getByTestId('text-inside-input').getByText('Search');
    this.viewClassesButton = page.getByText('View Classes ', { exact: true }).first();
    this.addRowButton = page.getByTestId('cta-button');
    this.classMode = page.getByTestId('classMode_0');
    this.onlineClassMode = page.getByText('ONLINE');
    this.subject = page.getByTestId('subject_0');
    this.chemistrySubject = page.getByText('Chemistry (STREAM_JEE_MAIN_ADVANCED-CLASS_11)');
    this.classSchedulingTitle = page.getByText('Scheduling', { exact: true });
    this.apiLoader = page.getByTestId('api-loader');
    this.gridCell = page.getByRole('gridcell').first();
    this.actionsMenu = page.getByTestId('lecture-plan-action-cell-0').getByTestId('shownImage').first();
    this.addClassButton = page.locator(`//*[@class='tippy-content']//*[text()='Add Class']`);
    this.addClassesToScheduleTitle = page.getByRole('heading', { name: 'Add Classes to schedule' });
    this.classTypeDropdown = page.getByTestId('class-type-dropdown').getByRole('img');
    this.regularClassType = page.getByTestId('class-type-dropdown-option-LIVE_LECTURE');
    this.classMode = page.getByText('Select Class Mode');
    this.OnlineSelectClassMode = page.getByTestId('class-mode-dropdown-option-SCHEDULE_MODE_ONLINE');
    this.selectSubject = page.getByTestId('subject-dropdown');
    this.selectTopic = page.getByTestId('topic-dropdown');
    this.selectFirstTopic = page.getByTestId('topic-dropdown-option-86');

    this.selectTeacher = page.getByTestId('teacher-dropdown');
    this.teacherSearch = page.getByPlaceholder('Search...')
    this.teacherName = (teacherName: string) => page.locator(`//*[contains(text(),'${teacherName}')]`);
    this.selectTeacher = page.getByText('Select Teacher');
    this.displayClassNameFeild = page.getByTestId('class-display-name');

    this.dateSelect = page.getByPlaceholder('Start Date');
    this.startTime = page.getByPlaceholder('Start Timings');
    this.endTime = page.getByPlaceholder('End Timings');
    this.roomSelect = page.getByText('Select Room/Studio');
    this.addButton = page.getByRole('button', { name: 'ADD' });
    this.rowSavedSuccessfully = page.getByText('Row Published Successfully! Page will refresh');
    this.draftDropdown = page.locator(`//*[contains(@id,'cell-Draft')]//*[@class='shimmering-image-container w-full h-full']`);
    this.publishedButton = page.getByTestId('status-cell-0-Published').getByText('Published');
    this.classDate = page.getByText('Class Date *');
    this.startTimeTitle = page.getByText('Start Timing *');
    this.endTimeTitle = page.getByText('End Timing *');
    this.draftCount = page.locator(`//*[@id='cell-Draft-4']//*[text()='1']`);
    this.changeDraftClass = page.getByText('Change these draft classes to:');
    this.draftToPublished = page.getByTestId('status-cell-0-Published').getByText('Published');
    this.draftToDeleted = page.getByTestId('status-cell-0-Deleted').getByText('Deleted');
    this.classToBePublishedText = page.getByText('classes to be published');
    this.saveChangesButton = page.getByRole('button', { name: 'Save Changes' });
    this.cancelButton = page.getByRole('button', { name: 'Cancel' });
    this.changeStatusSuccessfully = page.getByText('Status Changed Successfully, page will refresh');
    this.subjectTitle = page.getByText('Subject *');
    this.displayClassNameFeildTitle = page.getByText('Class Display Name*');
  }

  async validateScheduleClassPage() {
    await slowExpect(this.gridCell).toBeVisible();
    await expect(this.classSchedulingTitle, 'Scheduling header title is visible').toBeVisible();
    await expect(this.searchByCode, "Search Feild is visible").toBeVisible();
    await expect(this.searchButton, "Search Button is visible").toBeVisible();
  }

  async navigateToCreateSchedulePage(batchCode) {
    await customExpect(15000)(this.viewClassesButton, "Verify veiw classes button is visible").toBeVisible();
    await expect(this.searchByCode, "Search Feild is visible").toBeVisible();// custom wait is required to handle the page load
    await this.searchByCode.fill(batchCode);
    await this.searchButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000); //Required 2 sec wait untill page gets load
    await expect(this.page.getByText(batchCode), "Entered Batch code is visible").toBeVisible();
    await expect(this.viewClassesButton, 'basic details title is visible').toBeVisible();
    const newTabPromise = this.page.waitForEvent("popup");
    await this.viewClassesButton.click();
    const newTab = await newTabPromise;
    return new ScheduleBatchPage(newTab, this.isMobile);
  }

  async navigateToCreateSchedulePageV2(batchCode) {
    await slowExpect(this.actionsMenu, "Verify actions menu is visible").toBeVisible();
    await expect(this.searchByCode, "Search Feild is visible").toBeVisible();// custom wait is required to handle the page load
    await this.searchByCode.fill(batchCode);
    await this.searchButton.click();
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000); //Required 2 sec wait untill page gets load
    await expect(this.page.getByText(batchCode), "Entered Batch code is visible").toBeVisible();
    await expect(this.actionsMenu, 'verify actions menu is visible').toBeVisible();
    await this.actionsMenu.click();
    await expect(this.addClassButton, 'verify add class button is visible').toBeVisible();
    await this.addClassButton.click();
  }


  async createSchedulingV2Class(className, teacherName, room, currentDate, startTime, endTime) {
    await expect(this.addClassesToScheduleTitle, "verifyadd classes to schedule title is visible").toBeVisible();
    await slowExpect(this.classTypeDropdown, "verify class type dropdown is visible").toBeVisible();
    await this.classTypeDropdown.click();
    await expect(this.regularClassType, "verify regular class type is visible").toBeVisible();
    await this.regularClassType.click();

    await expect(this.classMode, "verify class Mode is visible").toBeVisible();
    await this.classMode.click();
    await expect(this.onlineClassMode, "verify Online class Mode is visible").toBeVisible();
    await this.onlineClassMode.click();
    await this.page.waitForTimeout(1000); //required as app response wait

    await expect(this.selectSubject, "verify Subject Type is visible").toBeVisible();
    await this.selectSubject.click();
    await expect(this.chemistrySubject, "verify Chemistry Subject is visible").toBeVisible();
    await this.chemistrySubject.click();

    await expect(this.selectTopic, "verify Topic Type is visible").toBeVisible();
    await this.selectTopic.click();
    await expect(this.selectFirstTopic, "Verify Topic is listed").toBeVisible();
    await this.selectFirstTopic.click();

    await expect(this.selectTeacher, "verify Teacher Type is visible").toBeVisible();
    await this.selectTeacher.click();
    await expect(this.teacherSearch, "verify Teacher Search feild is visible").toBeVisible();
    await this.teacherSearch.fill(teacherName);
    await expect(this.teacherName(teacherName), "verify Teacher select checkbox is visible").toBeVisible();
    await this.teacherName(teacherName).click();
    await this.page.waitForTimeout(1000); //required as app response wait

    await this.displayClassNameFeild.click();
    await this.displayClassNameFeild.fill(className);
    await this.page.waitForTimeout(2000); // required as app response wait
    await this.classDate.click();
    await expect(this.dateSelect, "verify Date Type is visible").toBeVisible();
    await this.dateSelect.fill(currentDate);

    await expect(this.startTimeTitle, "verify Start Time Type is visible").toBeVisible();
    await this.startTime.click();
    await this.startTime.fill(await startTime);

    await expect(this.endTimeTitle, "verify End Time Type is visible").toBeVisible();
    await this.endTimeTitle.click();

    await this.endTime.fill(await endTime);
    await expect(this.roomSelect, "verify Room Select Type is visible").toBeVisible();
    await expect(this.addButton, "verify add button is visible").toBeVisible();
    await this.addButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /* click on Publish button for v2 scheduling and verify */
  async clickOnPublishClass() {
    await customExpect(150000)(this.draftCount, "verify draft count is visible").toBeVisible();
    await expect(this.draftDropdown, "verify draft dropdown is visible").toBeVisible();
    await this.draftDropdown.click();
    await expect(this.changeDraftClass, "verify changes draft class text is visible").toBeVisible();
    await expect(this.draftToPublished, "verify draft to published is visible").toBeVisible();
    await expect(this.draftToDeleted, "verify draft to deleted is visible").toBeVisible();
    await this.draftToPublished.click();
    await expect(this.classToBePublishedText, "verify classes to be published text is visible").toBeVisible();
    await expect(this.saveChangesButton, "verify save changes button is visible").toBeVisible();
    await expect(this.cancelButton, "verify cancel button is visible").toBeVisible();
    await this.saveChangesButton.click();
    await this.page.waitForLoadState('networkidle');
    await customExpect(150000)(this.changeStatusSuccessfully, "verify Status Changed Successfully Page will refresh toast message is visible").toBeVisible();
  }
}