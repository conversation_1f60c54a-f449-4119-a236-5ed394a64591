import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreateSchedulePageUrl = '/class-management/schedule'

export class ScheduleBatchPage extends ICPage {

  readonly addRowButton: Locator;
  readonly classModeType: Locator;
  readonly onlineClassMode: Locator;
  readonly subjectType: Locator;
  readonly chemistrySubject: Locator;
  readonly topicSelect: Locator;
  readonly topicSelec: Locator;
  readonly SelectFirstTopic: Locator;
  readonly classNameText: Locator;
  readonly classNameEnter: Locator;
  readonly teacherSelect: Locator;
  readonly teacherSearch: Locator;
  readonly teacherCheckBox: (teacherName: string) => Locator;
  readonly dateSelect: Locator;
  readonly startTime: Locator;
  readonly endTime: Locator;
  readonly roomSelect: Locator;
  readonly studio: (roomName: string) => Locator;
  readonly saveButton: Locator;
  readonly selectCheckBox: Locator;
  readonly draftStatus: Locator;
  readonly changeStatusButton: Locator;
  readonly unSpecifiedStatus: Locator;
  readonly publishedButton: Locator;
  readonly publishedStatus: Locator;
  readonly refreshStatusButton: Locator;
  readonly changeStatusSuccessfully: Locator;
  readonly clashStatus: Locator;
  readonly deleteButton: Locator;
  readonly deleteStatus: Locator;
  readonly rowSavedSuccessfully: Locator;



  constructor(page: Page, isMobile: boolean) {
    super(page, CreateSchedulePageUrl, isMobile);
    this.addRowButton = page.getByRole('button', { name: 'Add Row' });
    this.classModeType = page.getByTestId('classMode_0').first();
    this.onlineClassMode = page.getByRole('option', { name: 'ONLINE' });
    this.subjectType = page.getByTestId('subject_0');
    //changed locator because of Test data
    this.chemistrySubject = page.getByRole('option', { name: 'Chemistry (STREAM_JEE_MAIN_ADVANCED-CLASS_11)' });
    this.topicSelect = page.getByRole('gridcell', { name: 'Select... down-arrow' }).getByTestId('shownImage').first();
    this.SelectFirstTopic = page.getByTestId('topic_0_0_checkbox').first();
    this.classNameText = page.locator('(//div[@col-id="className"])[2]');
    this.classNameEnter = page.getByLabel('Input Editor');
    this.teacherSelect = page.getByText('Select...').first();
    this.teacherSearch = page.getByPlaceholder('Search');
    /* Test id are unique for different Teachers so cannot use test id*/
    this.teacherCheckBox = (teacherName: string) => page.locator(`//div[@class="cursor-pointer px-4 flex items-center mb-2" and contains(text(),'${teacherName}')]//input[@type="checkbox"]`);
    this.dateSelect = page.locator('input[name="date"]').first();
    this.startTime = page.locator('input[name="startTime"]').first();
    this.endTime = page.locator('input[name="endTime"]').first();
    this.roomSelect = page.getByTestId('room_0');
    /*to-Do to figure out parent widget*/
    //changed here for Test data
    this.studio = (roomName: string) => page.locator(`//*[@class="ag-virtual-list-container ag-rich-select-virtual-list-container"]//div[contains(text(),'${roomName}')]`).first();
    this.saveButton = page.getByRole('button', { name: 'Save' });
    this.selectCheckBox = page.getByRole('gridcell', { name: 'Press Space to toggle row selection (unchecked)  1' }).getByLabel('Press Space to toggle row').first();
    this.draftStatus = page.getByText('Draft');
    this.changeStatusButton = page.getByTestId('change-status-button');
    this.unSpecifiedStatus = page.getByText('Unspecified', { exact: true });
    this.publishedButton = page.getByTestId('change-status-button').getByText('Published', { exact: true });
    this.refreshStatusButton = page.getByRole('button', { name: 'Refresh Status' });
    this.changeStatusSuccessfully = page.getByText('Status Changed Successfully Page will refresh');
    this.clashStatus = page.getByTestId('status-cell-1-Clash').getByText('Clash').first();
    this.deleteButton = page.getByTestId('change-status-button').getByText('Deleted', { exact: true });
    this.deleteStatus = page.getByTestId('status-cell-1-Deleted').getByText('Deleted').first();
    this.rowSavedSuccessfully = page.getByText('Row Published Successfully! Page will refresh');
  }
  async validateCreateSchedulePage() {
    await expect(this.addRowButton, "verify Add Row button is visible").toBeVisible();
    await expect(this.changeStatusButton, "verify Change Status button is visible").toBeVisible();
    await this.page.waitForLoadState('networkidle');
  }


  async createSchedulingClass(className, teacherName, room, currentDate, startTime, endTime) {
    /*To do we need to wait for table load*/
    // await this.page.waitForTimeout(2000);
    // await expect(this.apiLoader," Page load should be hidden").toBeHidden();
    await expect(this.addRowButton, "verify Add Row button is visible").toBeVisible();
    await this.page.waitForTimeout(2000);
    await expect(this.refreshStatusButton, "verify Refresh Status button is visible").toBeVisible();
    await expect(this.changeStatusButton, "verify Change Status button is visible").toBeVisible();
    await this.addRowButton.click();
    await slowExpect(this.unSpecifiedStatus, "verify Unspecified Status is visible").toBeVisible();
    await expect(this.classModeType, "verify Class Mode is visible").toBeVisible();
    await this.classModeType.click();
    await expect(this.onlineClassMode, "verify Online class Mode is visible").toBeVisible();
    await this.onlineClassMode.click();
    await expect(this.subjectType, "verify Subject Type is visible").toBeVisible();
    await this.subjectType.click();
    await expect(this.chemistrySubject, "verify Chemistry Subject is visible").toBeVisible();
    await this.chemistrySubject.click();
    await expect(this.topicSelect, "verify Topic Type is visible").toBeVisible();
    await this.topicSelect.click();
    await expect(this.SelectFirstTopic, "Verify Topic is listed").toBeVisible();
    await this.SelectFirstTopic.click();
    await this.classNameText.click();
    await this.classNameEnter.fill(className);
    await expect(this.teacherSelect, "verify Teacher Type is visible").toBeVisible();
    await this.teacherSelect.click();
    await this.teacherSelect.click();
    await expect(this.teacherSearch, "verify Teacher Search feild is visible").toBeVisible();
    await this.teacherSearch.fill(teacherName);
    await expect(this.teacherCheckBox(teacherName), "verify Teacher select checkbox is visible").toBeVisible();
    await this.teacherCheckBox(teacherName).click();
    await expect(this.dateSelect, "verify Date Type is visible").toBeVisible();
    await this.dateSelect.fill(currentDate);
    await expect(this.startTime, "verify Start Time Type is visible").toBeVisible();
    await this.startTime.click();
    await this.startTime.fill(await startTime);
    await expect(this.endTime, "verify End Time Type is visible").toBeVisible();
    await this.endTime.click();
    await this.endTime.fill(await endTime);
    await expect(this.roomSelect, "verify Room Select Type is visible").toBeVisible();
    /* dbClick is not working hence using 2 click*/
    // await this.roomSelect.click();
    // await this.roomSelect.click();
    // await expect(this.studio(room), "verify Studio Type is visible").toBeVisible();
    // await this.studio(room).click();
    await expect(this.selectCheckBox, "verify Row Select checkbox is visible").toBeVisible();
    await this.selectCheckBox.click();
    await expect(this.saveButton, "verify Save button is visible").toBeVisible();
    const requestPromise = this.page.waitForRequest('**/*arrow-up.svg');
    this.saveButton.click();
    await this.page.waitForTimeout(1000);
    await customExpect(15000)(this.rowSavedSuccessfully, "verify row published Successfully Page will refresh toast message is visible").toBeVisible();
    const request = await requestPromise;
  }

  /* click on Publish button and verify */
  async clickOnPublishClass() {
    await expect(this.selectCheckBox, "verify Row Select checkbox is visible").toBeVisible();
    await this.selectCheckBox.click();
    await expect(this.changeStatusButton, "verify change status button is visible").toBeVisible();
    await this.changeStatusButton.click();
    await expect(this.publishedButton, "verify Published button is visible").toBeVisible();
    await this.publishedButton.click();
    await expect(this.changeStatusSuccessfully, "verify Status Changed Successfully Page will refresh toast message is visible").toBeVisible();

  }

  /* verify Class Clash in Internal console*/
  async verifyClassClashAndCancelClass() {
    await expect(this.clashStatus, "verify class clash status is visible").toBeVisible();
    await expect(this.selectCheckBox, "verify Row Select checkbox is visible").toBeVisible();
    await this.selectCheckBox.click();
    await expect(this.changeStatusButton, "verify change status button is visible").toBeVisible();
    await this.changeStatusButton.click();
    await expect(this.deleteButton, "verify Delete button is visible").toBeVisible();
    await this.deleteButton.click();
    await expect(this.changeStatusSuccessfully, "verify Status Changed Successfully Page will refresh toast message is visible").toBeVisible();
    await expect(this.deleteStatus, "verify Delete Status is visible after clash deletion").toBeVisible();
  }

}