import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const WidgetCreationEditPageUrl = '/page-management/widget'

export class WidgetCreationEditPage extends ICPage {

    readonly profileNameButton: Locator;
    readonly widgetCreationTitleText: Locator;
    readonly createWidgetButton: Locator;
    readonly uploadImageButton: Locator;
    readonly applyFilterButton: Locator;
    readonly resetFilterButton: Locator;
    readonly widgetTypeDropDown: Locator;
    readonly widgetIdField: Locator;
    readonly apiLoader: Locator;
    readonly createWidgetTitleText: Locator;
    readonly widgetDetailsTab: Locator;
    readonly widgetConfigurationTab: Locator;
    readonly widgetReviewTab: Locator;
    readonly widgetNameField: Locator;
    readonly widgetContentTypeDropdown: Locator;
    readonly widgetTypeSelectDropdown: Locator;
    readonly searchFeild: Locator;
    readonly selectDropdown: (selectName: string) => Locator;
    readonly widgetNameRow: (widgetName: string) => Locator;
    readonly widgetConstId: (widgetName: string) => Locator;

    readonly copyToClipBoardWidgetConstId: (widgetConstId: string) => Locator;
    readonly copiedText: Locator;
    readonly widgetConstIdFeild: Locator;
    readonly pageIdsRowTitleName: Locator;

    readonly formattedBulletedIcon: Locator;

    readonly pageIdsForWIdgetPopUpTitle: Locator;
    readonly allThePageIdsinkedText: Locator;
    readonly linkedPageIdText: (pageConstId: string) => Locator;
    readonly closeIcon: Locator;
    readonly compareIcon: Locator;
    readonly versionActiveTitleText: Locator;
    readonly versionActiveTextValue: Locator;

    readonly currentVersionText: Locator;
    readonly selectVersionToCompareText: Locator;
    readonly activateSelectedVersionButton: Locator;
    readonly selectVersionToCompareTitleText: Locator;
    readonly selectVersionDropDown: Locator;
    readonly createdByText: Locator;
    readonly updatedByText: Locator;
    readonly confirmActionText: Locator;
    readonly areYouSureYouWantToRevertText: Locator;
    readonly changeWillReflectText: Locator;
    readonly cancelButton: Locator;
    readonly confirmButton: Locator;
    readonly versionActivatedSuccessfully: Locator;
    readonly widgetNameFilter: Locator;
    readonly duplicateIcon: Locator;
    readonly duplicateWidgetPopUpTitleText: Locator;
    readonly areYouSureToduplicateText: Locator;




    constructor(page: Page, isMobile: boolean) {
        super(page, WidgetCreationEditPageUrl, isMobile);
        this.widgetCreationTitleText = page.getByText('Widget Creation & Edits');
        this.createWidgetButton = page.getByTestId('Create Widget');
        this.uploadImageButton = page.getByRole('button', { name: 'Cloudinary Upload' });
        this.applyFilterButton = page.getByRole('button', { name: 'Apply Filters' });
        this.resetFilterButton = page.getByRole('button', { name: 'Reset Filters' });
        this.widgetTypeDropDown = page.getByTestId('dropdown').getByRole('img');
        this.widgetIdField = page.getByPlaceholder('Widget Id');
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.createWidgetTitleText = page.getByRole('heading', { name: 'Create Widget' });
        this.widgetDetailsTab = page.getByText('Step 1: Widget Details');
        this.widgetConfigurationTab = page.getByText('Step 2: Widget Configuration');
        this.widgetReviewTab = page.getByText('Step 3: Widget Review');
        this.widgetReviewTab = page.getByText('Step 3: Widget Review');
        this.widgetNameField = page.locator('div').filter({ hasText: /^Widget Name$/ }).getByTestId('textInput')
        this.widgetContentTypeDropdown = page.locator('div').filter({ hasText: /^Static$/ }).nth(1);
        this.widgetTypeSelectDropdown = page.locator('div').filter({ hasText: /^Widget TypeSelect a Type$/ }).getByTestId('dropdown');
        this.searchFeild = page.getByPlaceholder('Search...');
        this.selectDropdown = (selectName: string) => page.getByText(`${selectName}`);
        this.widgetNameRow = (widgetName: string) => page.locator(`xpath=//span[contains(@id,'cell-name') and text()='${widgetName}']/ancestor::div[contains(@class, 'ag-row')]/descendant::*[@row-id="0"]`);
        this.widgetConstId = (widgetName: string) => (this.widgetNameRow(widgetName)).locator(`xpath=.//a[contains(@class, 'text-blue500 font-bold underline')]`);
        this.copyToClipBoardWidgetConstId = (widgetConstId: string) => page.locator(`//*[text()='${widgetConstId}']/ancestor::*[contains(@id,"cell-const_widget_id")]//following-sibling::*//*[@data-testid="CopyAllIcon"]`);
        this.copiedText = page.getByText('Copied!')
        this.widgetConstIdFeild = page.getByPlaceholder('Widget Const ID');
        this.pageIdsRowTitleName = page.getByText('Page Ids');
        this.formattedBulletedIcon = page.getByTestId('FormatListBulletedIcon');
        this.pageIdsForWIdgetPopUpTitle = page.getByText('Page Ids for Widget', { exact: true });
        this.allThePageIdsinkedText = page.getByText('Page IDs where this widget is');
        this.linkedPageIdText = (pageConstId: string) => page.getByRole('link', { name: `${pageConstId}` });
        this.closeIcon = page.getByRole('button', { name: 'delete_icon' });
        this.compareIcon = page.getByTestId('CompareArrowsIcon');
        this.versionActiveTitleText = page.getByText('Version Management | Active');
        this.versionActiveTextValue = page.locator(`//*[@class='font-PlusJakartaSans cursor-default font-bold text-2xl no-underline text-2xl']`);
        this.currentVersionText = page.getByText('Current Version (optional)');
        this.selectVersionToCompareText = page.getByText('Select Version to Compare and');
        this.activateSelectedVersionButton = page.getByRole('button', { name: 'Activate Selected Version' });
        this.selectVersionToCompareTitleText = page.getByText('Select a version to compare');
        this.selectVersionDropDown = page.getByText('Select a Version', { exact: true });
        this.createdByText = page.locator("p.text-sm:has-text('Created By:')");
        this.updatedByText = page.locator("p.text-sm:has-text('Updated By:')");

        this.confirmActionText = page.getByRole('heading', { name: 'Confirm Action' });
        this.areYouSureYouWantToRevertText = page.getByText('Are you sure you want to');
        this.changeWillReflectText = page.getByText('(this change will reflect in');
        this.cancelButton = page.getByRole('button', { name: 'Cancel' });
        this.confirmButton = page.getByRole('button', { name: 'Confirm' });
        this.versionActivatedSuccessfully = page.getByText('Version successfully activated');
        this.widgetNameFilter = page.getByPlaceholder('Widget Name');
        this.duplicateIcon = page.getByTestId('CopyAllOutlinedIcon').first();
        this.duplicateWidgetPopUpTitleText = page.getByText('Duplicate Widget?');
        this.areYouSureToduplicateText = page.getByText('Are you sure you want to');

    }

    /* verifying to widget creation and edits page */
    async verifyWidgetCreationAndEditsPage() {
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.widgetCreationTitleText, "Verify widget creation and edits title text is visible").toBeVisible();
        await expect(this.createWidgetButton, "Verify create widget button is visible").toBeVisible();
        await expect(this.uploadImageButton, "Verify upload image button is visible").toBeVisible();
        await expect(this.widgetTypeDropDown, "Verify widget type dropdown is visible").toBeVisible();
        await expect(this.widgetIdField, "Verify widget id feild is visible").toBeVisible();
        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await expect(this.resetFilterButton, "Verify reset filter button is visible").toBeVisible();
    }

    /* verifying to widget creation and edits page */
    async clickOnCreateWidgetButton() {
        await expect(this.createWidgetButton, "Verify create widget button is visible").toBeVisible();
        await this.createWidgetButton.click();
    }



    /* verifying widget created  */
    async verifyCreatedWidget(widgetName) {
        await expect(this.widgetNameRow(widgetName), "Verify created widget name is visible").toBeVisible();
        await expect(this.widgetConstId(widgetName), "Verify create widget const id is visible").toBeVisible();
        const widgetContsIdLocator = await this.widgetConstId(widgetName);
        const constId = await widgetContsIdLocator.textContent() ?? "";
        return constId;
    }

    /* verifying widget created  */
    async verifyWidgetConstIdCopyToClipboard(widgetConstId) {
        await expect(this.copyToClipBoardWidgetConstId(widgetConstId), "Verify copy to clipboard is visible").toBeVisible();
        await this.copyToClipBoardWidgetConstId(widgetConstId).click();
    }

    /* verifying to widget linked to created page const id */
    async verifyWidgetLinkedToPageId(pageConstId) {

        await slowExpect(this.pageIdsRowTitleName, "Verify page id's row title text is visible").toBeVisible();
        await slowExpect(this.formattedBulletedIcon, "Verify formatted button icon is visible").toBeVisible();
        await this.formattedBulletedIcon.click();
        await expect(this.pageIdsForWIdgetPopUpTitle, "Verify page id's linked to widget title text is visible").toBeVisible();
        await expect(this.allThePageIdsinkedText, "Verify all the page id's linked text is visible").toBeVisible();
        await expect(this.linkedPageIdText(pageConstId), "Verify linked page const id text is visible").toBeVisible();
        await expect(this.closeIcon, "Verify close icon is visible").toBeVisible();
        await this.closeIcon.click();
    }

    /* click to widget created */
    async clickOnCreatedWidgetId(widgetName) {
        await expect(this.widgetConstId(widgetName), "Verify widget created const id is visible").toBeVisible();
        await this.widgetConstId(widgetName).click();
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
    }


    /* verifying to widget filter with const id */
    async verifyWidgetFilter(widgetConstId) {
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.widgetConstIdFeild, "Verify widget const id feild is visible").toBeVisible();
        await this.widgetConstIdFeild.fill(widgetConstId);
        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await expect(this.resetFilterButton, "Verify reset filter button is visible").toBeVisible();
        await this.applyFilterButton.click();
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
    }

    /* click to compare widget after edit */
    async clickOnCompareWidget() {
        await expect(this.compareIcon, "Verify compare icon is visible").toBeVisible();
        await this.compareIcon.click();

    }

    async verifyVersionManagementWidget() {

        await slowExpect(this.versionActiveTitleText, "Verify version management, activate & total version title text is visible").toBeVisible();

        const versionText = await this.versionActiveTextValue.textContent();
        if (versionText) {
            // Extract Active Version and Total Versions using regex
            const activeVersionMatch = versionText.match(/Active Version\s*"\s*"(\d+)"/);
            const totalVersionsMatch = versionText.match(/Total Versions:\s*"(\d+)"/);

            const activeVersion = activeVersionMatch ? activeVersionMatch[1] : 'Not found';
            const totalVersions = totalVersionsMatch ? totalVersionsMatch[1] : 'Not found';
        }
        await expect(this.currentVersionText, "Verify current version dropdown title text is visible").toBeVisible();
        await expect(this.selectVersionToCompareText, "Verify select version to compare dropdown title text is visible").toBeVisible();
        await expect(this.activateSelectedVersionButton, "Verify activate selected version button is visible").toBeVisible();
        await expect(this.activateSelectedVersionButton, "Verify activate selected version button is disabled").toBeDisabled();

        await expect(this.selectVersionToCompareTitleText, "Verify select version to compare title text is visible").toBeVisible();
    }

    async verifyVersionCompareWidget(versionToCompare) {
        await expect(this.selectVersionDropDown, "Verify select version dropdown is visible").toBeVisible();
        await this.selectVersionDropDown.click();
        await expect(this.searchFeild, "Verify search field is visible").toBeVisible();
        await this.searchFeild.fill(versionToCompare);
        await expect(this.selectDropdown(versionToCompare), "Verify select dropdowm name is visible").toBeVisible();
        await this.selectDropdown(versionToCompare).click();

        await expect(this.createdByText, "Verify created by text is visible").toBeVisible();
        await expect(this.updatedByText, "Verify updated by text is visible").toBeVisible();


        const createdBy = await this.createdByText.textContent();
        const updatedBy = await this.updatedByText.textContent();
        // Extractcreated By and updated by  values
        const createdByValue = createdBy?.replace("Created By:", "").trim();
        const updatedByValue = updatedBy?.replace("Updated By:", "").trim();

        expect(createdByValue, "Verify created by name is not empty").not.toBeFalsy(); // Check Created By is not empty
        expect(updatedByValue, "Verify updated by name is not empty").not.toBeFalsy();// Check updated By is not empty
        await expect(this.activateSelectedVersionButton, "Verify activate selected version button is enabled").toBeEnabled();
    }

    async verifyActivateVersionSelected() {
        await expect(this.activateSelectedVersionButton, "Verify activate selected version button is enabled").toBeEnabled();
        await this.activateSelectedVersionButton.click();

        await expect(this.confirmActionText, "Verify confirm action text is visible").toBeVisible();
        await expect(this.areYouSureYouWantToRevertText, "Verify are you sure you want to revert text is visible").toBeVisible();
        await expect(this.changeWillReflectText, "Verify changes will reflect text  is visible").toBeVisible();
        await expect(this.cancelButton, "Verify cancel button is enabled").toBeVisible();
        await expect(this.confirmButton, "Verify confirm button is enabled").toBeVisible();
        await this.confirmButton.click();
        await expect(this.versionActivatedSuccessfully, "Verify version successfully activated is visible").toBeVisible();
    }

    /* click to compare widget after edit */
    async verifyDuplicateWidget(widgetName) {
        if (widgetName == "Quiz") {
            const widgetId = "1172";
            await expect(this.widgetIdField, "Verify widget name feild is visible").toBeVisible();
            await this.widgetIdField.fill(widgetId);
        }
        else {
            await expect(this.widgetNameFilter, "Verify widget name feild is visible").toBeVisible();
            await this.widgetNameFilter.fill(widgetName);
        }

        await expect(this.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await this.applyFilterButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.duplicateIcon, "Verify duplicate icon is visible").toBeVisible();
        await this.duplicateIcon.click();

        await expect(this.duplicateWidgetPopUpTitleText, "Verify duplicate widget title text is visible").toBeVisible();
        await expect(this.areYouSureToduplicateText, "Verify are you sure to duplicate widget text is visible").toBeVisible();

        await expect(this.confirmButton, "Verify confirm button is visible").toBeVisible();
        await expect(this.cancelButton, "Verify cancel button is visible").toBeVisible();
        await this.confirmButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();

    }
}