import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreateTestPageUrl = '/test-management/student-tests'

export class CreateTestPage extends ICPage {

  readonly createAsButton: Locator;
  readonly singleTestButton: Locator;
  readonly basicDetailsTitle: Locator;
  readonly testDetailsTitle: Locator;
  readonly createdTestId: (testId: string) => Locator;
  readonly testActionButton: (createdTestId: string) => Locator;
  readonly cancelTestText: Locator;
  readonly confirmText: Locator;
  readonly bulkFileInput: Locator;
  readonly testManagerTitle: Locator;
  readonly testsTableView: Locator;
  readonly searchTestInput: Locator;
  readonly bulkTestButton: Locator;
  readonly bulkTestCreationHeader: Locator;
  readonly dragAndDropSubTitle: Locator;
  readonly downloadTemplateOption: Locator;
  readonly browseFileButton: Locator;
  readonly bulkUploadTestTost: Locator;
  readonly apiLoader: Locator;
  readonly columnData: Locator;
  readonly cancelTestButton: Locator;

  constructor(page: Page, isMobile: boolean) {
    super(page, CreateTestPageUrl, isMobile);
    this.createAsButton = page.getByRole('button', { name: 'Create as down icon' });
    this.singleTestButton = page.getByText('Single test', { exact: true });
    this.basicDetailsTitle = page.getByText('Basic Details', { exact: true });
    this.testDetailsTitle = page.getByText('Test Detail', { exact: true });
    this.createdTestId = (testId: string) => page.getByText(`${testId}`);
    this.testActionButton = (createdTestId: string) => page.getByTestId(`test-action-${createdTestId}`);
    this.cancelTestText = page.getByText('Cancel Test', { exact: true });
    this.confirmText = page.getByText('Confirm');
    this.bulkFileInput = page.getByTestId('file-input');
    this.testManagerTitle = page.getByText('Test Manager').locator('visible=true');
    this.testsTableView = page.locator("//*[@id='viewAgGridTable'");
    this.searchTestInput = page.getByTestId('search-input');
    this.bulkTestButton = page.getByTestId('bulkTest-cta');
    this.bulkTestCreationHeader = page.getByText('Bulk Test Creation');
    this.downloadTemplateOption = page.getByText('Download file template');
    this.browseFileButton = page.getByRole('button', { name: 'Browse File' });
    this.bulkUploadTestTost = page.getByText('Bulk upload test');
    this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
    this.columnData = page.locator('//*[@col-id="ag-Grid-AutoColumn"]');
    this.cancelTestButton = page.locator("//*[@class='tippy-content']//*[text()='Cancel Test']").locator('visible=true');
  }

  async createSingleTest() {
    await this.createAsButton.click();
    await slowExpect(this.singleTestButton, "Verify single test button is visible").toBeVisible();
    await this.singleTestButton.click();
    await expect(this.basicDetailsTitle, 'basic details title is not visible').toBeVisible();
    await expect(this.testDetailsTitle, 'test details sub-title is not visible').toBeVisible();
  }

  async verifyTestManagementPage() {
    await slowExpect(this.testManagerTitle, 'Verify test manager is visible').toBeVisible();
    await expect(this.createAsButton, "Verify create as button to create test is visible").toBeVisible();
    await expect(this.searchTestInput, "Verify search test option is visible").toBeVisible();
  }

  async clickAndVerifyBulkTestCreationPopup() {
    await expect(this.createAsButton, "Verify create as button to create test is visible").toBeVisible();
    await this.createAsButton.click();
    await this.page.waitForLoadState('networkidle'); // To handle network calls to load
    await customExpect(15000)(this.bulkTestButton, 'Verify bulk test button is visible').toBeVisible();
    await this.bulkTestButton.click();
    await expect(this.bulkTestCreationHeader, "Verify bulk test creation header button is visible").toBeVisible();
    await expect(this.downloadTemplateOption, "Verify download template option is visible").toBeVisible();
    await expect(this.browseFileButton, "Verify browse file option is visible").toBeVisible();
  }
}

