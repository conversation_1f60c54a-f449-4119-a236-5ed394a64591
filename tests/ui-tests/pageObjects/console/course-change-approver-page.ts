import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CourseChangeApproverUrl = '/student-support/approver-management/course-change-approver'

export class CourseChangeApprover extends ICPage {


    readonly approvedTitle: Locator;
    readonly rejectedTitle: Locator;
    readonly centerIdFilterCross: Locator;
    readonly searchInputFiled: Locator;
    readonly searchButton: Locator;
    readonly userNameOfStudent: (studentName: string) => Locator;
    readonly studentCheckBox: Locator;
    readonly homePageApproveButton: Locator;
    readonly requestApproveHeading: Locator;
    readonly sidepannelApproveButton: Locator;
    readonly apiLoader: Locator;
    readonly approveSuccessfullyToast: Locator;




    constructor(page: Page, isMobile: boolean) {
        super(page, CourseChangeApproverUrl, isMobile);
        this.approvedTitle = page.getByText('Approved');
        this.rejectedTitle = page.getByText('Rejected');
        this.centerIdFilterCross = page.getByTestId('chip-clear-centre_id').getByTestId('shownImage');
        this.searchInputFiled = page.getByTestId('search-input');
        this.searchButton = page.getByTestId('text-inside-input');
        this.userNameOfStudent = (studentName: string) => page.getByRole('link', { name: `${studentName}` });
        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.studentCheckBox = page.getByLabel('Press Space to toggle row');
        this.homePageApproveButton = page.getByTestId('sort-search-primary-button');
        this.requestApproveHeading = page.getByRole('heading', { name: 'Request Approve' });
        this.sidepannelApproveButton = page.getByLabel('Edit Student').getByRole('button', { name: 'Approve' });
        this.approveSuccessfullyToast = page.getByText('Approve Successfully')

    }
    async validateAndApproveCourseChange(StudentFormId, studentName) {
        await expect(this.approvedTitle, 'Verify approved title is visible').toBeVisible();
        await expect(this.rejectedTitle, 'Verify rejected title is visible').toBeVisible();
        await expect(this.centerIdFilterCross, 'Verify center id filter cross is visible').toBeVisible();
        await this.centerIdFilterCross.click();
        await this.page.waitForLoadState('networkidle');
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
        await this.page.waitForTimeout(2000);
        await expect(this.searchInputFiled, 'Verify search input field is visible').toBeVisible();
        await this.searchInputFiled.fill(StudentFormId);
        await expect(this.searchButton, "Verify search button is visible").toBeVisible();
        await this.searchButton.click();
        await this.page.waitForLoadState('networkidle');
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
        await expect(this.userNameOfStudent(studentName), 'Verify student name is visible').toBeVisible();
        await expect(this.studentCheckBox, 'Verify check box is visible').toBeVisible();
        await this.studentCheckBox.check();
        await expect(this.homePageApproveButton, 'Verify home page approved button is enabled').toBeEnabled();
        await this.homePageApproveButton.click();
        await this.page.waitForLoadState('networkidle');
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
        await slowExpect(this.requestApproveHeading, 'Verify request approver heading is visible').toBeVisible();
        await slowExpect(this.sidepannelApproveButton, 'Verify approve button is enabled').toBeEnabled();
        await this.sidepannelApproveButton.click();
        await slowExpect(this.approveSuccessfullyToast, 'Verify approve successfully toast is visible').toBeVisible();
    }


}