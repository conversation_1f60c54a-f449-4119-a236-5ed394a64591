import { APIRequestContext, expect, Page, request } from '@playwright/test'
import { ReportingApi } from '@reportportal/agent-js-playwright'
import * as fs from 'fs'
import * as path from 'path'
import API from '../../commons/api-helpers/api-helper'
import ScheduleManagement, { MeetingInfo } from '../../commons/api-helpers/schedule-management'
import ResourceManagement from '../../commons/api-helpers/resource-management'
import { generateRandomString } from '../../api-tests/helpers/utilities'

import { commonTest, commonWorkerFixtures } from '../../commons/common-fixtures'

import { consolePOMs, webPOMs, initConsolePOMs, initWebPOMs } from './load-pom-classes'
import { initConsoleAPIs, initWebAPIs, webAPIs, consoleAPIs } from './load-api'
import {
  testData,
  testDataSetup,
  cleanUpTestData,
  unlockExpiredTeacherAccounts,
  unlockExpiredStudentAccounts,
} from './load-test-data'
import { stream } from 'xlsx'
import { cleanupRegistry } from '../../commons/cleanup-registry'
import { retryOperation } from '../../commons/api-helpers/api-common-utils'
import { updateExcelFile } from '../../commons/common-functions'
import logger from '../../../logger'

// Use slow expect where some actions are expected to take more than 5 seconds
const slowExpect = expect.configure({ timeout: 10000 })

// Use custom expect where some actions are expected to take more than 10 seconds
// Example: await customExpect(15000)(preHomePage.allenImage).toBeVisible();
const customExpect = (time: number) => expect.configure({ timeout: time })

export { expect, slowExpect, customExpect }

interface StudentPage extends Page {
  login: () => Promise<void>
  pos: webPOMs
  apis: webAPIs
}

interface AdminPage extends Page {
  pos: consolePOMs
  apis: consoleAPIs
}

interface TeacherPage extends Page {
  pos: consolePOMs
  apis: consoleAPIs
}

type testFixtures = {
  // setReportAttributes: void;
  addTags: void
  adminPage: AdminPage
  studentPage: StudentPage
  teacherPage: TeacherPage
  meetingInfo: MeetingInfo
  studentStorageState: string
  lmmTest: {
    lmmContent: Array<{
      id: string
      name: string
      type: string
      subType: string
    }>
    cleanup: () => Promise<void>
  }
  growthTest: {
    growthContent: Array<{
      widgetId: string[]
      pageId: string
      urlName: string
      teacherId: string
    }>
    cleanup: () => Promise<void>
  }
  doubtSetup: (stream: 'JEE' | 'NEET') => Promise<void>
}

type workerFixtures = commonWorkerFixtures & {
  adminStorageState: string
  testData: testData
}

export const test = commonTest.extend<testFixtures, workerFixtures>({
  testData: [
    async ({ browser, bffURL }, use, workerInfo) => {
      const env = process.env.PROD === '1' ? 'prod' : 'stage'
      const api = new API((await browser.newContext()).request, new URL('/internal-bff/api/v1', bffURL).toString());
      api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
      let testData
      const testId = `worker-${workerInfo.parallelIndex}-${Date.now()}`
      let cleanupError: Error | null = null

      try {
        // Create test data
        const setupResult = await testDataSetup(api, env, 'JEE', 'UI')
        if (!setupResult.success) {
          testData = setupResult.data // Save partial data for cleanup
          logger.error("Cleanup failed", setupResult.error || "Unknown error")
          throw new Error(`${setupResult.error}`)
        }
        testData = setupResult.data
        // Register with cleanup registry
        cleanupRegistry.register(testId, testData, api, env, 'JEE')
        // logger.info('CLEANUP - ', `Test Data for worker ${workerInfo.parallelIndex} is: ${testData?.student?.name}, ${testData?.teacher?.name}, ${testData?.batch?.name}, ${testData?.room?.name},${testData?.lmmContent}`,
        // )
        // console.log(
        //   `[INFO] CLEANUP - Test Data for worker ${workerInfo.parallelIndex} is: ${testData?.student?.name}, ${testData?.teacher?.name}, ${testData?.batch?.name}, ${testData?.room?.name},${testData?.lmmContent}`,
        // )

        // Use the test data
        await use(testData)
        // try {
        //   // Normal cleanup
        //   await cleanUpTestData(testData, api, env, 'JEE')
        // } catch (error) {
        //   cleanupError = error as Error
        //   logger.error('cleanup failed:', error)
        //   // console.error('cleanup failed:', error)
        // }
        // if (cleanupError) {
        //   throw new Error(
        //     `Test cleanup failed:\n\n${cleanupError.message}\n\nCleanup failed while tearing down "testData".\n\nTestId: ${testId}`,
        //   )
        // }
      } finally {
        try {
          // Normal cleanup
          await cleanUpTestData(testData, api, env, 'JEE')
        } catch (error) {
          cleanupError = error as Error
          logger.error('cleanup failed:', error)
          // console.error('cleanup failed:', error)
        }
        // Unregister from registry
        cleanupRegistry.unregister(testId)

        // Throw cleanup error after unregistering
        if (cleanupError) {
          throw new Error(
            `Test cleanup failed:\n\n${cleanupError.message}\n\nCleanup failed while tearing down "testData".\n\nTestId: ${testId}`,
          )
        }
      }
    },
    { scope: 'worker' },
  ],

  studentPage: async ({ browser, request, bffURL, testData, isMobile, studentStorageState }, use) => {
    const page = await browser.newPage()
    const api = new API(request, bffURL)
    const studentPage = page as StudentPage
    studentPage.pos = initWebPOMs(page, isMobile)
    studentPage.apis = initWebAPIs(api)

    studentPage.login = async () => {
      const json = require(studentStorageState)
      // Set cookies
      await studentPage.context().addCookies(json.cookies)
      // Set origins
      for (const origin of json.origins) {
        await studentPage.evaluate((origin) => {
          for (const { name, value } of origin.localStorage) {
            localStorage.setItem(name, value)
          }
        }, origin)

        // Set API token for API call
        for (const { name, value } of origin.localStorage) {
          if (name == 'tokens') {
            api.setToken(JSON.parse(value)['x-access-token'])
            break
          }
        }
      }
      await studentPage.goto('/')
      expect(await studentPage.pos.homePage.isLoggedIn()).toBeTruthy()
    }

    await studentPage.goto('/')
    expect(await studentPage.pos.homePage.isLoggedIn()).not.toBeTruthy()
    await studentPage.waitForTimeout(1000) //1 second is required to load the page
    await use(studentPage)
    await studentPage.close()
  },

  adminPage: async ({ browser, request, bffURL, adminURL, adminStorageState, testData, isMobile }, use) => {
    const page = await browser.newPage({ storageState: adminStorageState })
    const api = new API(request, new URL(bffURL).toString());
    const adminPage = page as AdminPage
    adminPage.pos = initConsolePOMs(page, isMobile)
    adminPage.apis = initConsoleAPIs(api)
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)

    await adminPage.goto(adminURL)
    await adminPage.waitForLoadState()
    await expect(adminPage.pos.teacherHomePage.profileNameButton, 'Verify profile name button is visible').toBeVisible()
    await use(adminPage)
    await adminPage.close()
  },

  teacherPage: async ({ browser, request, bffURL, adminURL, testData, isMobile }, use) => {
    const page = await browser.newPage()
    const api = new API(request, new URL('/internal-bff/api/v1', bffURL).toString());

    const teacherPage = page as TeacherPage
    teacherPage.pos = initConsolePOMs(page, isMobile)
    teacherPage.apis = initConsoleAPIs(api)

    api.setToken(testData.teacher.at)
    const accessToken = '"x-access-token":"' + testData.teacher.at + '"'
    const refreshToken = '"x-refresh-token":"' + testData.teacher.rt + '"'
    const tokens = '{' + accessToken + ',' + refreshToken + '}'

    await teacherPage.goto(adminURL)

    await teacherPage.evaluate((tokens) => {
      localStorage.setItem('tokens', tokens)
    }, tokens)
    await teacherPage.goto(adminURL)
    await teacherPage.waitForLoadState()
    await expect(
      teacherPage.pos.teacherHomePage.profileNameButton,
      'Verify profile name button is visible',
    ).toBeVisible()

    await use(teacherPage)
    await teacherPage.close()
  },

  meetingInfo: async ({ browser, bffURL, testData }, use) => {
    const api = new API((await browser.newContext()).request, new URL(bffURL).toString());
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const scheduleManagement = new ScheduleManagement(api)

    const meetingInfo = await scheduleManagement.createMeeting(
      'SCHEDULE_MODE_ONLINE',
      'LIVE',
      testData.batch.course,
      testData.batch.id,
      testData.batch.name,
      testData.room.id,
      testData.teacher.id,
      testData.teacher.name,
      910,
      5,
      `E2E Test ${generateRandomString(4)}`,
    )
    if (meetingInfo.meetingId == null) {
      await scheduleManagement.cancelAllMeeting(meetingInfo.batchId, meetingInfo.startTimeStamp, meetingInfo.endTimeStamp)
      throw new Error('Meeting not created')
    }
    await use(meetingInfo)
    await scheduleManagement.cancelAllMeeting(meetingInfo.batchId, meetingInfo.startTimeStamp, meetingInfo.endTimeStamp)
  },

  // Authenticate admin user once per worker and reuse it for all tests in the worker.
  studentStorageState: async ({ browser, testData, isMobile }, use) => {
    const fileName = path.resolve(test.info().project.outputDir, `.auth/student-${testData.student.id}.json`)
    // console.log('Student used from DB : ' + testData.student.id)

    function isFileRecent(fileName: string, minutes: number): boolean {
      if (!fs.existsSync(fileName)) {
        return false
      }
      const stats = fs.statSync(fileName)
      const fileCreationTime = new Date(stats.mtime) // stats.birthtme for creation time or use stats.mtime for modification time
      const now = new Date()
      const timeDiff = (now.getTime() - fileCreationTime.getTime()) / (1000 * 60) // Difference in minutes
      return timeDiff < minutes
    }

    // Refresh the token every 3 minutes
    if (fs.existsSync(fileName) && isFileRecent(fileName, 3)) {
      await use(fileName)
    } else {
      const page = await browser.newPage({ storageState: undefined })
      const studentPage = page as StudentPage
      const env = process.env.PROD === '1' ? 'prod' : 'stage'
      studentPage.pos = initWebPOMs(page, isMobile)
      await studentPage.goto('/')
      if (env === 'stage') {
        await studentPage.pos.preHomePage.loginWithMobileNumber(testData.student.phone)
      } else {
        await studentPage.pos.preHomePage.loginWithUsername(testData.student.email, testData.student.phone)
      }
      await studentPage.waitForTimeout(2000) //Wait incase the web page is slow in setting local storage
      expect(await studentPage.pos.homePage.isLoggedIn()).toBeTruthy()
      await studentPage.waitForTimeout(2000) //Wait incase the web page is slow in setting local storage
      await slowExpect(studentPage.pos.homePage.sectionLoader, 'Verify section loader should not visible').toBeHidden()
      await page.context().storageState({ path: fileName })
      await page.close()
      await use(fileName)
    }
  },

  // Authenticate admin user once per worker and reuse it for all tests in the worker.
  adminStorageState: [
    async ({ browser, adminURL }, use) => {
      const id = test.info().parallelIndex
      const fileName = path.resolve(test.info().project.outputDir, `.auth/admin-${id}.json`)

      if (fs.existsSync(fileName)) {
        await use(fileName)
      } else {
        const page = await browser.newPage({ storageState: undefined })
        const token =
          process.env.MEETING_ID_TEST === '1' ? process.env.MEETING_ACCESS_TOKEN : process.env.ADMIN_ACCESS_TOKEN
        const accessToken = '"x-access-token":"' + token + '"'
        const refreshToken = '"x-refresh-token":"' + process.env.ADMIN_REFRESH_TOKEN + '"'

        await page.goto(adminURL)
        const tokens = '{' + accessToken + ',' + refreshToken + '}'

        await page.evaluate((tokens) => {
          localStorage.setItem('tokens', tokens)
        }, tokens)

        await page.context().storageState({ path: fileName })
        await page.close()
        await use(fileName)
      }
    },
    { scope: 'worker' },
  ],

  lmmTest: async ({ browser, adminPage, bffURL }, use) => {
    const api = new API((await browser.newContext()).request, bffURL)
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const resourceManagement = new ResourceManagement(api)

    const apiUrl = new API((await browser.newContext()).request, bffURL)
    apiUrl.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const resourceManagementUrl = new ResourceManagement(apiUrl)

    const lmmContent: Array<{
      id: string
      name: string
      type: string
      subType: string
    }> = []


    // Create the fixture object
    const lmmFixture = {
      lmmContent,
      cleanup: async () => {
        for (const content of lmmContent) {
          try {
            await resourceManagement.setContentInactiveStatus(content.id, content.name, content.type, content.subType)
            const nodeId = await adminPage.apis.resourceManagement.filterLearningMaterials(content.id)
            await resourceManagementUrl.removeContentAttach([content.id], nodeId)
          } catch (error) {
            // console.error(`Failed to clean up LMM content ${content.name}:`, error)
          }
        }
      },
    }

    await use(lmmFixture)
    await lmmFixture.cleanup()
  },

  growthTest: async ({ browser, bffURL }, use) => {
    const api = new API((await browser.newContext()).request, bffURL)
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const resourceManagement = new ResourceManagement(api)

    const growthContent: Array<{
      widgetId: string[]
      pageId: string
      urlName: string
      teacherId: string
    }> = []

    const growthFixture = {
      growthContent,
      cleanup: async () => {
        // console.log('Cleaning up Growth content...')
        for (const content of growthContent) {
          try {
            // Check if URL exists
            const urlsList = await resourceManagement.getUrlsList()
            const urlExists = urlsList.some(url => url.name === content.urlName.replace('/', '')) // Remove leading '/' if present
            if (urlExists) {
              await resourceManagement.detachUrl(content.pageId, content.urlName, content.teacherId)
              // console.log(`URL ${content.urlName} detached successfully`)
            }

            // Check if page exists
            const pagesList = await resourceManagement.getPagesList()
            const pageExists = pagesList.some(page => page.id === content.pageId)
            if (pageExists) {
              await resourceManagement.deletePage(content.pageId)
              // console.log(`Page ${content.pageId} deleted successfully`)
            }

            // Check if widgets exist
            const widgetsList = await resourceManagement.getWidgetsList()
            for (const widgetId of content.widgetId) { // Iterate over each widget ID
              const widgetExists = widgetsList.some(widget => widget.id === widgetId)
              if (widgetExists) {
                await resourceManagement.deleteWidget(widgetId)
                // console.log(`Widget ${widgetId} deleted successfully`)
              }
            }
          } catch (error) {
            console.error(`Failed to clean up Growth content: `, error)
          }
        }
      }
    }

    await use(growthFixture)
    await growthFixture.cleanup()
  },

  doubtSetup: async ({ testData, browser, bffURL }, use) => {
    const setupDoubt = async (stream: 'JEE' | 'NEET') => {
      const api = new API((await browser.newContext()).request, bffURL)
      const resourceManagement = new ResourceManagement(api)

      const centerId = process.env.CENTER_ID ? process.env.CENTER_ID : ''
      const courseId = (stream === 'JEE' ? process.env.JEE_BATCH_COURSE_ID : process.env.NEET_BATCH_COURSE_ID) || ''
      api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)

      try {
        if (stream === 'JEE') {
          await updateExcelFile(`${__dirname}/../../../test-data/doubt_template.xlsx`, testData.batch.id, testData.teacher.phone, stream)
          await resourceManagement.uploadDoubtTemplate(`doubt_template.xlsx`, testData.batch.id, courseId, centerId)
        } else {
          await updateExcelFile(`${__dirname}/../../../test-data/doubt_template_neet.xlsx`, testData.batch.id, testData.teacher.phone, stream)
          await resourceManagement.uploadDoubtTemplate(`doubt_template_neet.xlsx`, testData.batch.id, courseId, centerId)
        }
      } catch (error) {
        // console.error('Failed to setup doubt:', error)
        throw error
      }
    }
    await use(setupDoubt)
  }

  // Fixture for reporting portal
  // setReportAttributes: [async ({ browserName }, use, testInfo) => {
  //   let attributes = [{
  //     key: 'browser',
  //     value: browserName.toString(),
  //   },
  //   {
  //     key: 'suite',
  //     value: path.basename(testInfo.file).replace(/(\.spec)?\.ts$/, '')
  //   }];

  //   for (const annotation of testInfo.annotations) {
  //     attributes.push({
  //       key: annotation.type,
  //       value: annotation.description?.toString() || 'no description',
  //     });
  //   }
  //   await use();
  //   ReportingApi.addAttributes(attributes);
  // }, { auto: true }],
})
// export const testNeet = test.extend<testFixtures, workerFixtures>({
//   testData: [async ({ browser, internalBffURL }, use, workerInfo) => {
//     const env = process.env.PROD === "1" ? "prod" : "stage";
//     const api = new API((await browser.newContext()).request, internalBffURL);
//     api.setToken(`${ process.env.ADMIN_ACCESS_TOKEN }`)
//     const testData = await testDataSetup(api, env, 'NEET');
//     console.log(`NEET Test Data for worker ${ workerInfo.parallelIndex }  is: ${ testData?.student?.name }, ${ testData?.teacher?.name }, ${ testData?.batch?.name }, ${ testData?.room?.name } `)
//     await use(testData)
//     await cleanUpTestData(testData, api, env);
//   }, { scope: 'worker' }],
// })

export const testNeet = test.extend<testFixtures, workerFixtures>({
  testData: [
    async ({ browser, bffURL }, use, workerInfo) => {
      const env = process.env.PROD === '1' ? 'prod' : 'stage'
      const api = new API((await browser.newContext()).request, new URL('/internal-bff/api/v1', bffURL).toString());
      api.setToken(`${process.env.ADMIN_ACCESS_TOKEN} `)
      let testData
      const testId = `worker-${workerInfo.parallelIndex}-${Date.now()}`
      let cleanupError: Error | null = null
      try {
        // Create test data
        const setupResult = await testDataSetup(api, env, 'NEET', 'UI')
        if (!setupResult.success) {
          testData = setupResult.data // Save partial data for cleanup
          throw setupResult.error
        }
        testData = setupResult.data

        // Register with cleanup registry
        cleanupRegistry.register(testId, testData, api, env, 'NEET')
        // console.log(
        //   `NEET Test Data for worker ${ workerInfo.parallelIndex } is: ${ testData?.student?.name }, ${ testData?.teacher?.name }, ${ testData?.batch?.name }, ${ testData?.room?.name },${ testData?.lmmContent } `,
        // )

        // Use the test data
        await use(testData)
      } finally {
        try {
          // Normal cleanup
          await cleanUpTestData(testData, api, env, 'NEET')
        } catch (error) {
          cleanupError = error as Error
          // console.error('Normal cleanup failed:', error)
        }
        // Unregister from registry
        cleanupRegistry.unregister(testId)

        // Throw cleanup error after unregistering
        if (cleanupError) {
          throw new Error(
            `Test cleanup failed: \n\n${cleanupError.message} \n\nCleanup failed while tearing down "testData".\n\nTestId: ${testId} `,
          )
        }
      }
    },
    { scope: 'worker' },
  ],
})
