import API from '../../commons/api-helpers/api-helper';
import Auth from '../../commons/api-helpers/auth';
import CourseManagement from '../../commons/api-helpers/course-management';
import ResourceManagement from '../../commons/api-helpers/resource-management';
import ScheduleManagement from '../../commons/api-helpers/schedule-management';
import User from '../../commons/api-helpers/user';
import DoubtsHelper from '../../commons/api-helpers/doubtsHelper';


export type consoleAPIs = {
  resourceManagement: ResourceManagement;
  scheduleManagement: ScheduleManagement;
  courseManagement: CourseManagement;
};

export function initConsoleAPIs(api: API): consoleAPIs {
  return {
    resourceManagement: new ResourceManagement(api),
    scheduleManagement: new ScheduleManagement(api),
    courseManagement: new CourseManagement(api)
  };
}

export type webAPIs = {
  auth: Auth;
  user: User;
  doubtsHelper: DoubtsHelper;
};

export function initWebAPIs(api: API): webAPIs {
  return {
    auth: new Auth(api),
    user: new User(api),
    doubtsHelper: new DoubtsHelper(api)
  };
}

