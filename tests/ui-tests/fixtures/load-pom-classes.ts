import * as fs from 'fs';
import * as path from 'path';
import { Page } from '@playwright/test';



import { PreHomePage } from '../pageObjects/web/pre-home-page';
import { InstructionPage } from '../pageObjects/web/instruction-Page';
import { ExamPage } from '../pageObjects/web/exam-page';
import { ExamSummaryPage } from '../pageObjects/web/exam-summary-page';
import { HomePage } from '../pageObjects/web/home-page';
import { AccountSetupPage } from '../pageObjects/web/account-setup-page';
import { CheckoutPage } from '../pageObjects/web/checkout-page';
import { CoursePage } from '../pageObjects/web/course-page';
import { DoubtsPage } from '../pageObjects/web/doubts-page';
import { OnboardingPage } from '../pageObjects/web/onboarding-page';
import { PaymentPage } from '../pageObjects/web/payment-page';
import { ProfilePage } from '../pageObjects/web/profile-page';
import { StudentEnterClassPage } from '../pageObjects/web/student-enter-class-page';
import { StudentLiveClassPage } from '../pageObjects/web/student-live-class-page';
import { SubjectDetailsPage } from '../pageObjects/web/subject-details-page';
import { CalendarPage } from '../pageObjects/web/calendar-page';
import { CourseDetailsPage } from '../pageObjects/web/course-details-page';
import { OnlineTestSeriesPage } from '../pageObjects/web/online-test-series-page';
import { NeetOltsPage } from '../pageObjects/web/neet-olts-page';
import { QuizHomePage } from '../pageObjects/web/quiz-home-page';
import { QuizCreatePage } from '../pageObjects/web/quiz-create-page';
import { ScheduleACallbackHomePage } from '../pageObjects/web/schedule-a-callback-page';
import { HomeworkPage } from '../pageObjects/web/homework-page';
import { HomeworkSummaryPage } from '../pageObjects/web/homework-summary-page';
import { HomeworkInsightPage } from '../pageObjects/web/homework-insight-page';
import { CourseChangeApprover } from '../pageObjects/console/course-change-approver-page';

import { TeacherHomePage } from '../pageObjects/console/teacher-home-page';
import { InternalUserHomePage } from '../pageObjects/console/internal-user-home-page';
import { BasicDetailsPage } from '../pageObjects/console/basic-details-page';
import { PaperReviewPage } from '../pageObjects/console/paper-review-page';
import { CreateTestPage } from '../pageObjects/console/create-test-page';
import { ScheduleBatchPage } from '../pageObjects/console/schedule-batch-page';
import { TeacherEnterClassPage } from '../pageObjects/console/teacher-enter-class-page';
import { ScheduleClassPage } from '../pageObjects/console/schedule-class-page';
import { TeacherLiveClassPage } from '../pageObjects/console/teacher-live-class-page';
import { TeacherDoubtsPage } from '../pageObjects/console/teacher-doubts-page';
import { LMMPage } from '../pageObjects/console/lmm-page';
import { CourseAndSyllabusPage } from '../pageObjects/console/course-and-syllabus-page';
import { CreateHomeworkPage } from '../pageObjects/console/create-homework-page';
import { TaxonamyPage } from '../pageObjects/console/taxonamy-page';
import { NonAcademicContentPage } from '../pageObjects/console/non-academic-content-page';
import { BroadcastPage } from '../pageObjects/console/broadcast-crm-page';
import { QuestionBankPage } from '../pageObjects/console/question-bank-page';
import { BatchManagementPage } from '../pageObjects/console/batch-managment-page';
import { FlashCardPage } from '../pageObjects/web/flash-card-page';
import { ScheduleMentorshipPage } from '../pageObjects/console/mentorship-schedule-page';
import { StudentManagementPage } from '../pageObjects/console/student-management-page';
import { WidgetCreationEditPage } from '../pageObjects/console/widget-creation-edit-page';
import { CreateWidgetPage } from '../pageObjects/console/create-widget-page';
import { PageCreationEditPage } from '../pageObjects/console/page-creation-edits-page';
import { CreatePagePage } from '../pageObjects/console/create-page-page';
import { CreateUrlPage } from '../pageObjects/console/create-url-page';
import { CreateUrlEditsPage } from '../pageObjects/console/create-url-edits-page';
import { WebMigrationPage } from '../pageObjects/web/web-migration-page';
import { ReferAFriendPage } from '../pageObjects/web/refer-a-friend-page';
import { PreviousTestsResultPage } from '../pageObjects/web/previous-tests-page';
import { TestTabPage } from '../pageObjects/web/test-tab-page';
import { PhaseManagementPage } from '../pageObjects/console/phase-management-page';
import { CourseListingManagement } from '../pageObjects/console/course-listing-management-page';
import { CourseListingApproval } from '../pageObjects/console/course-listing-approval-page';
import { OffersAndDiscountsPage } from '../pageObjects/console/offers-and-discounts-page';
import { RevisionNotesPage } from '../pageObjects/web/revision-notes-page';

export type consolePOMs = {
  basicDetailsPage: BasicDetailsPage;
  createTestPage: CreateTestPage;
  internalUserHomePage: InternalUserHomePage;
  paperReviewPage: PaperReviewPage;
  scheduleBatchPage: ScheduleBatchPage;
  scheduleClassPage: ScheduleClassPage;
  teacherDoubtsPage: TeacherDoubtsPage;
  teacherEnterClassPage: TeacherEnterClassPage;
  teacherHomePage: TeacherHomePage;
  teacherLiveClassPage: TeacherLiveClassPage;
  lmmPage: LMMPage;
  courseAndSyllabusPage: CourseAndSyllabusPage;
  createHomeworkPage: CreateHomeworkPage;
  taxonamyPage: TaxonamyPage;
  nonAcademicContentPage: NonAcademicContentPage;
  broadcastPage: BroadcastPage;
  questionBankPage: QuestionBankPage;
  batchManagementPage: BatchManagementPage;
  scheduleMentorshipPage: ScheduleMentorshipPage;
  studentManagementPage: StudentManagementPage;

  WidgetCreationEditPage: WidgetCreationEditPage;
  CreateWidgetPage: CreateWidgetPage;
  PageCreationEditPage: PageCreationEditPage;
  CreatePagePage: CreatePagePage;
  CreateUrlPage: CreateUrlPage;
  CreateUrlEditsPage: CreateUrlEditsPage;
  courseChangeApprover: CourseChangeApprover;
  phaseManagementPage: PhaseManagementPage;
  CourseListingManagement: CourseListingManagement;
  courseListingApproval: CourseListingApproval;
  offersAndDiscountsPage: OffersAndDiscountsPage;
};


export function initConsolePOMs(page: Page, isMobile: boolean): consolePOMs {
  return {
    basicDetailsPage: new BasicDetailsPage(page, isMobile),
    createTestPage: new CreateTestPage(page, isMobile),
    internalUserHomePage: new InternalUserHomePage(page, isMobile),
    paperReviewPage: new PaperReviewPage(page, isMobile),
    scheduleBatchPage: new ScheduleBatchPage(page, isMobile),
    scheduleClassPage: new ScheduleClassPage(page, isMobile),
    teacherDoubtsPage: new TeacherDoubtsPage(page, isMobile),
    teacherEnterClassPage: new TeacherEnterClassPage(page, isMobile),
    teacherHomePage: new TeacherHomePage(page, isMobile),
    teacherLiveClassPage: new TeacherLiveClassPage(page, isMobile),
    lmmPage: new LMMPage(page, isMobile),
    courseAndSyllabusPage: new CourseAndSyllabusPage(page, isMobile),
    createHomeworkPage: new CreateHomeworkPage(page, isMobile),
    taxonamyPage: new TaxonamyPage(page, isMobile),
    nonAcademicContentPage: new NonAcademicContentPage(page, isMobile),
    broadcastPage: new BroadcastPage(page, isMobile),
    questionBankPage: new QuestionBankPage(page, isMobile),
    batchManagementPage: new BatchManagementPage(page, isMobile),
    scheduleMentorshipPage: new ScheduleMentorshipPage(page, isMobile),
    studentManagementPage: new StudentManagementPage(page, isMobile),
    WidgetCreationEditPage: new WidgetCreationEditPage(page, isMobile),
    CreateWidgetPage: new CreateWidgetPage(page, isMobile),
    PageCreationEditPage: new PageCreationEditPage(page, isMobile),
    CreatePagePage: new CreatePagePage(page, isMobile),
    CreateUrlEditsPage: new CreateUrlEditsPage(page, isMobile),
    CreateUrlPage: new CreateUrlPage(page, isMobile),
    courseChangeApprover: new CourseChangeApprover(page, isMobile),
    phaseManagementPage: new PhaseManagementPage(page, isMobile),
    CourseListingManagement: new CourseListingManagement(page, isMobile),
    courseListingApproval: new CourseListingApproval(page, isMobile),
    offersAndDiscountsPage: new OffersAndDiscountsPage(page, isMobile),
  }
};


export type webPOMs = {
  accountSetupPage: AccountSetupPage;
  checkoutPage: CheckoutPage;
  coursePage: CoursePage;
  doubtsPage: DoubtsPage;
  examPage: ExamPage;
  examSummaryPage: ExamSummaryPage;
  homePage: HomePage;
  instructionPage: InstructionPage;
  onboardingPage: OnboardingPage;
  paymentPage: PaymentPage;
  preHomePage: PreHomePage;
  profilePage: ProfilePage;
  studentEnterClassPage: StudentEnterClassPage;
  studentLiveClassPage: StudentLiveClassPage;
  subjectDetailsPage: SubjectDetailsPage;
  calendarPage: CalendarPage;
  courseDetailsPage: CourseDetailsPage;
  onlineTestSeriesPage: OnlineTestSeriesPage;
  neetOltsPage: NeetOltsPage;
  quizHomePage: QuizHomePage;
  quizCreatePage: QuizCreatePage;
  scheduleACallbackHomePage: ScheduleACallbackHomePage;
  homeworkPage: HomeworkPage;
  homeworkSummaryPage: HomeworkSummaryPage
  homeworkInsightPage: HomeworkInsightPage
  flashCardPage: FlashCardPage
  webMigrationPage: WebMigrationPage
  referAFriendPage: ReferAFriendPage
  previousTestsResultPage: PreviousTestsResultPage
  testTabPage: TestTabPage
  revisionNotesPage: RevisionNotesPage;
};

export function initWebPOMs(page: Page, isMobile: boolean): webPOMs {
  return {
    accountSetupPage: new AccountSetupPage(page, isMobile),
    checkoutPage: new CheckoutPage(page, isMobile),
    coursePage: new CoursePage(page, isMobile),
    doubtsPage: new DoubtsPage(page, isMobile),
    examPage: new ExamPage(page, isMobile),
    examSummaryPage: new ExamSummaryPage(page, isMobile),
    homePage: new HomePage(page, isMobile),
    instructionPage: new InstructionPage(page, isMobile),
    onboardingPage: new OnboardingPage(page, isMobile),
    paymentPage: new PaymentPage(page, isMobile),
    preHomePage: new PreHomePage(page, isMobile),
    profilePage: new ProfilePage(page, isMobile),
    studentEnterClassPage: new StudentEnterClassPage(page, isMobile),
    studentLiveClassPage: new StudentLiveClassPage(page, isMobile),
    subjectDetailsPage: new SubjectDetailsPage(page, isMobile),
    calendarPage: new CalendarPage(page, isMobile),
    courseDetailsPage: new CourseDetailsPage(page, isMobile),
    onlineTestSeriesPage: new OnlineTestSeriesPage(page, isMobile),
    neetOltsPage: new NeetOltsPage(page, isMobile),
    quizHomePage: new QuizHomePage(page, isMobile),
    quizCreatePage: new QuizCreatePage(page, isMobile),
    scheduleACallbackHomePage: new ScheduleACallbackHomePage(page, isMobile),
    homeworkPage: new HomeworkPage(page, isMobile),
    homeworkSummaryPage: new HomeworkSummaryPage(page, isMobile),
    homeworkInsightPage: new HomeworkInsightPage(page, isMobile),
    flashCardPage: new FlashCardPage(page, isMobile),
    webMigrationPage: new WebMigrationPage(page, isMobile),
    referAFriendPage: new ReferAFriendPage(page, isMobile),
    previousTestsResultPage: new PreviousTestsResultPage(page, isMobile),
    testTabPage: new TestTabPage(page, isMobile),
    revisionNotesPage: new RevisionNotesPage(page, isMobile)
  }
};

// Function to dynamically load and instantiate POM classes
// export async function loadPOMClasses(directory: string, page: Page, isMobile: boolean): Promise<POM> {
//   const classes: POM = {};
//   const files = fs.readdirSync(directory);

//   for (const file of files) {
//     if (file.endsWith('.ts') || file.endsWith('.js')) {
//       const filePath = path.resolve(directory, file);
//       console.log(filePath)
//       const module = await import(filePath);

//       // Assuming each module exports only one class
//       for (const className of Object.keys(module)) {
//         const POMClass = module[className];
//         classes[className] = new POMClass(page, isMobile);
//       }
//     }
//   }

//   return classes;
// }