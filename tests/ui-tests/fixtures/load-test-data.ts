import path from 'path'
import { readCSVFile, updateExcelFile } from '../../commons/common-functions'
import mysql from 'mysql2/promise'
import ResourceManagement from '../../commons/api-helpers/resource-management'
import API from '../../commons/api-helpers/api-helper'
import test, { APIRequestContext, APIResponse, Request } from '@playwright/test'
import { ResultSetHeader, RowDataPacket } from 'mysql2/promise'
import CourseManagement from '../../commons/api-helpers/course-management'
import Auth from '../../commons/api-helpers/auth'
import { retryOperation } from '../../commons/api-helpers/api-common-utils'
import { StringUtils } from '../../commons/common-functions'
import { initConsoleAPIs } from './load-api'
import { throws } from 'assert'
import logger from '../../../logger'
interface TeacherAccount extends RowDataPacket {
  id: string
  phone: string
  user_id: string
  access_token: string
}

interface StudentAccount extends RowDataPacket {
  id: string
  phone: string
  user_id: string
  password: string
}

type teacher = {
  phone: string
  id: string
  name: string
  email: string
  extId: string
  at: string
  rt: string
}

type student = {
  phone: string
  id: string
  name: string
  email: string
  login: string
  password: string
}

type batch = {
  id: string
  name: string
  phase: string
  course: string
}

type room = {
  id: string
  name: string
}

type lmmContent = {
  id: string
  name: string
  type: string
  subType: string
}

export type testData = {
  teacher: teacher
  student: student
  batch: batch
  room: room
  lmmContent: lmmContent[]
}

//Db Connection And API's declaration
const writeHost = 'common-rds.allen-internal-stage.in'
const readHost = 'common-rds-ro.allen-internal-stage.in'
// const writeHost = '***********'
// const readHost = '***********'

const dbConfig = {
  user: 'automation_user',
  password: 'automation%40password123',
  database: 'allen_automation_test_db',
}

const writeConnectionString = `mysql://${dbConfig.user}:${dbConfig.password}@${writeHost}/${dbConfig.database}`
const readConnectionString = `mysql://${dbConfig.user}:${dbConfig.password}@${readHost}/${dbConfig.database}`

const writePool = mysql.createPool(writeConnectionString)
const readPool = mysql.createPool(readConnectionString)

// Read test data from CSV file. get data from top for CI run and from bottom for local run
// async function getTestDataEnv(filename: string, workerIndex: number) {
//   const env = process.env.PROD === '1' ? 'prod' : 'stage'
//   const filePath = path.join('test-data', env, filename)
//   const data = await readCSVFile(filePath)
//   const ci = process.env.CI === '1' ? true : false
//   let idx = workerIndex % data.length
//   if (ci) {
//     idx = data.length - 1 - idx
//   }
//   return data[idx]
// }

//Fetch teacher from Db
async function getTeacherAcct(env: 'stage' | 'prod') {
  const readConnection = await readPool.getConnection()
  const writeConnection = await writePool.getConnection()
  let selectedAccount: TeacherAccount | undefined = undefined
  await new Promise((resolve) => setTimeout(resolve, Math.floor(Math.random() * (1000 - 100 + 1)) + 100))
  try {
    await writeConnection.beginTransaction()
    // First try to unlock any expired accounts
    await unlockExpiredTeacherAccounts(env)
    // Select an unused account with proper locking
    const [teacherAccounts] = await writeConnection.execute<TeacherAccount[]>(`
      SELECT id, phone, user_id, access_token, refresh_token
      FROM ${env === 'stage' ? 'test_accounts_teacher' : 'prod_accounts_teacher'}  
      WHERE is_used = 0
      ORDER BY 
        CASE WHEN last_used IS NULL THEN 0 ELSE 1 END, 
        last_used ASC
      LIMIT 1
      FOR UPDATE SKIP LOCKED;
    `)

    if (teacherAccounts.length === 0) {
      await writeConnection.rollback()
      throw new Error('No unused teacher accounts available')
    }

    selectedAccount = teacherAccounts[0]

    // Mark as used
    try {
      await writeConnection.execute(
        `
      UPDATE ${env === 'stage' ? 'test_accounts_teacher' : 'prod_accounts_teacher'}  
      SET 
        is_used = 1,
        locked_at = CURRENT_TIMESTAMP,
        last_used = CURRENT_TIMESTAMP
      WHERE id = ?
    `,
        [selectedAccount.id],
      )
    } catch (error) {
      throw Error('Could not update student user id', error.message)
    }
    await writeConnection.commit()

    return {
      phone: selectedAccount?.phone,
      id: selectedAccount?.id,
      name: `teacher${selectedAccount?.phone}`,
      email: `teacher${selectedAccount?.phone}@allen.in`,
      extId: '',
      at: selectedAccount?.access_token.trim(),
      rt: selectedAccount?.refresh_token.trim(),
    }
  } catch (error) {
    await writeConnection.rollback()
    if (error instanceof Error) {
      if (selectedAccount) {
        await releaseTeacherAccount(selectedAccount.id, env)
      }
    }
    throw new Error(`Failed to get unused teacher account: ${error.message}`)
  } finally {
    readConnection.release()
    writeConnection.release()
  }
}

//Create Dynamically or Fetch Student Account from DB
// export async function getStudentAcct(api: API, env: 'stage' | 'prod') {
//   try {
//     var student = await fetchStudentAccount(env)
//     //Demo details as of now
//     var resourceManagement = new ResourceManagement(api)
//     var courseManagement = new CourseManagement(api)
//     var auth = new Auth(api)

//     let userId
//     try {
//       userId = await resourceManagement.getStudentUserId(student.phone)
//     } catch (error) {
//       logger.error('Failed to fetch student user ID:', error)
//     }

//     const stuId = userId?.data?.students[0]?.user_id ? userId?.data?.students[0]?.user_id : student?.id
//     console.log(stuId, userId)
//     // if (stuId) {
//     var deleteRes = await retryOperation(
//       'deleteStudentAtSetup',
//       async () => {
//         console.log(await (await resourceManagement.deleteTestAccount(stuId)).json())
//       },
//       3,
//     )
//     // console.log(deleteRes) //Delete happened as usual

//     var firstName = student.name.split(' ')[0]
//     var lastName = student.name.split(' ')[1]
//     //Create Student
//     var createdStudentRes

//     await retryOperation(
//       'createStudentAtSetup',
//       async () => {
//         createdStudentRes = await (
//           await resourceManagement.createTestStudent(student.email, student.phone, firstName, lastName)
//         ).json()
//       },
//       5,
//     )
//     // console.log(createdStudentRes)
//     if (createdStudentRes) {
//       //Update db with the id for that phone number
//       const connection = await writePool.getConnection()
//       try {
//         await connection.beginTransaction()
//         await connection.execute(
//           `
//               UPDATE ${env === 'stage' ? 'stage_student_accounts' : 'prod_student_accounts'}
//               SET id = ?
//               WHERE phone = ?
//               `,
//           [createdStudentRes.data.data.id, student.phone],
//         )
//         await connection.commit()
//       } catch (error) {
//         await connection.rollback()
//         console.error('Error updating student ID in database:', error)
//       } finally {
//         connection.release()
//       }
//     }

//     student = createdStudentRes?.data?.data.phone_number
//       ? {
//           phone: createdStudentRes?.data?.data.phone_number ?? '',
//           id: createdStudentRes?.data?.data.id ?? '',
//           name: `${createdStudentRes?.data?.data.first_name ?? ''} ${createdStudentRes?.data?.data.last_name ?? ''}`,
//           email: createdStudentRes?.data?.data.email ?? '',
//           login: createdStudentRes?.data?.data.phone_number ?? '',
//           password: env === 'stage' ? createdStudentRes?.data?.data.phone_number ?? '' : '',
//         }
//       : student //If Create Fails

//     //Mandatory step to reset password ,for first time
//     await auth.resetPasswordOnAcctCreation(student.phone, student.phone)

//     return student
//   } catch (error) {
//     throw error
//   }
// }
//Fetch Student from Db
async function fetchStudentAccount(env: 'stage' | 'prod', stream: string) {
  await unlockExpiredStudentAccounts(env)
  const readConnection = await readPool.getConnection()
  const writeConnection = await writePool.getConnection()
  let studentPhone: undefined | string = undefined
  await new Promise((resolve) => setTimeout(resolve, Math.floor(Math.random() * (1000 - 100 + 1)) + 100))
  console.log(stream)
  try {
    await writeConnection.beginTransaction()

    // Modified query to select the least recently used account
    // We order by locked_at (ascending) with nulls first to get accounts
    // that haven't been used or were released longest ago
    const [studentAccount] = await writeConnection.execute<StudentAccount[]>(
      `
          SELECT id, name, phone, email, password, batch_code, room_code, batch_id, room_id, stream
          FROM ${env === 'stage' ? 'stage_student_accounts_extended' : 'prod_student_accounts_extended'} 
          WHERE stream = ? AND is_used = 0 AND expired != 1
          ORDER BY 
            CASE WHEN last_used IS NULL THEN 0 ELSE 1 END, 
            last_used ASC
          LIMIT 1 
          FOR UPDATE SKIP LOCKED;
      `,
      [stream],
    )

    if (studentAccount.length === 0) {
      await writeConnection.rollback()
      throw new Error('No unused student accounts available')
    }
    const accountId = studentAccount[0].id
    studentPhone = studentAccount[0].phone

    try {
      await writeConnection.execute(
        `
          UPDATE ${env === 'stage' ? 'stage_student_accounts_extended' : 'prod_student_accounts_extended'}  
          SET is_used = 1, 
              locked_at = CURRENT_TIMESTAMP,
              last_used = CURRENT_TIMESTAMP
          WHERE phone = ?;
      `,
        [studentPhone],
      )
    } catch (error) {
      console.error(error.message)
      throw new Error('Error while Updating user id', error.message)
    }

    await writeConnection.commit()
    return {
      phone: studentAccount[0].phone,
      id: studentAccount[0].id,
      name: studentAccount[0].name,
      email: studentAccount[0].email,
      login: studentAccount[0].phone,
      password: studentAccount[0].password,
      batch_code: studentAccount[0].batch_code,
      batch_id: studentAccount[0].batch_id,
      room_code: studentAccount[0].room_code,
      room_id: studentAccount[0].room_id,
    }
  } catch (error) {
    await writeConnection.rollback()
    if (studentPhone) {
      await releaseStudentAccount(studentPhone, env)
    }
    throw Error('Error at fetchStudentAccount db function while setup', error)
  } finally {
    readConnection.release()
    writeConnection.release()
  }
}

//Release Teacher Account After Cleanup
async function releaseTeacherAccount(teacherAccountId, env: 'stage' | 'prod') {
  const connection = await writePool.getConnection()
  try {
    await connection.beginTransaction()
    const [result] = await connection.execute<ResultSetHeader>(
      `UPDATE ${env === 'stage' ? 'test_accounts_teacher' : 'prod_accounts_teacher'} SET is_used = 0, locked_at = NULL WHERE id = ?`,
      [teacherAccountId],
    )
    if (result.affectedRows === 0) {
      throw new Error(`No teacher account found with user_id: ${teacherAccountId}`)
    }
    await connection.commit()
    // console.log(`Teacher Account released: ${teacherAccountId}`)
  } catch (error) {
    await connection.rollback()
    throw error
  } finally {
    connection.release()
  }
}

async function releaseStudentAccount(studentPhone, env: 'stage' | 'prod') {
  const connection = await writePool.getConnection()
  try {
    await connection.beginTransaction()
    const [result] = await connection.execute<ResultSetHeader>(
      `UPDATE ${env === 'stage' ? 'stage_student_accounts_extended' : 'prod_student_accounts_extended'} SET is_used = 0, locked_at = NULL WHERE phone = ?`,
      [studentPhone],
    )
    if (result.affectedRows === 0) {
      throw new Error(`No student account found with id: ${studentPhone}`)
    }
    await connection.commit()
    // console.log(`Student Account released: ${studentPhone}`)
  } catch (error) {
    await connection.rollback()
    throw error
  } finally {
    connection.release()
  }
}

//Unlock Student Accounts in Db
export async function unlockExpiredStudentAccounts(env: 'stage' | 'prod') {
  const connection = await writePool.getConnection()
  try {
    await connection.beginTransaction()
    const [result] = await connection.execute<ResultSetHeader>(`
          UPDATE ${env === 'stage' ? 'stage_student_accounts_extended' : 'prod_student_accounts_extended'} 
          SET is_used = 0, locked_at = NULL
          WHERE is_used = 1
              AND locked_at IS NOT NULL
              AND locked_at <= DATE_SUB(NOW(), INTERVAL 15 MINUTE);
      `)
    await connection.commit()
  } catch (error) {
    await connection.rollback()
    throw Error('Could not Unlock Expired Student Accounts')
  } finally {
    connection.release()
  }
}

export async function unlockExpiredTeacherAccounts(env: 'stage' | 'prod') {
  const connection = await writePool.getConnection()
  try {
    await connection.beginTransaction()
    const [result] = await connection.execute<ResultSetHeader>(`
            UPDATE ${env === 'stage' ? 'test_accounts_teacher' : 'prod_accounts_teacher'} 
            SET is_used = 0, locked_at = NULL
            WHERE is_used = 1
                AND locked_at IS NOT NULL
                AND locked_at <= DATE_SUB(NOW(), INTERVAL 15 MINUTE);
        `)
    await connection.commit()
  } catch (error) {
    await connection.rollback()
    throw Error('Could not Unlock Expired Teacher Accounts')
  } finally {
    connection.release()
  }
}

export async function cleanUpTestData(testData: testData, api: API, env: 'prod' | 'stage', stream: string) {
  const consoleAPIs = initConsoleAPIs(api)
  // console.log('Cleanup starting...')
  const errors: Error[] = []
  try {
    // Teacher cleanup
    if (testData?.teacher) {
      try {
        await releaseTeacherAccount(testData.teacher.id, env)
        logger.info('CLEANUP - Teacher: ', testData.teacher.id)
      } catch (error) {
        logger.error('Failed to release teacher account', testData.teacher.id, error)
        // console.error('Failed to release teacher account', testData.teacher.id, error)
        errors.push(new Error(`Failed to release teacher account: ${error.message}`))
      }
    }

    // Student cleanup
    if (testData?.student) {
      let res
      try {
        await releaseStudentAccount(testData.student.phone, env)
        logger.info('CLEANUP - Student Id: ', testData.student.phone)
        // try {
        //   if (testData?.batch)
        //     await retryOperation(
        //       'moveStudentsBatch',
        //       async () => {
        //         const batchId = stream === 'JEE' ? process.env.DEL_JEE_BATCH_ID : process.env.DEL_NEET_BATCH_ID
        //         res = await consoleAPIs.resourceManagement.moveStudentsBatch(
        //           testData.batch.id,
        //           batchId,
        //           testData.student.id,
        //         )
        //       },
        //       5,
        //     )
        // } catch (error) {
        //   logger.error(
        //     'move students to cleanup batch error response:',
        //     testData.batch.id,
        //     testData.batch.name,
        //     testData.student.id,
        //     res,
        //   )
        // console.error(
        //   'move students to cleanup batch error response:',
        //   testData.batch.id,
        //   testData.batch.name,
        //   testData.student.id,
        //   res,
        // )
        //   errors.push(new Error(`Failed to move student batch: ${error.message}`))
        // }
      } catch (error) {
        errors.push(new Error(`Failed to release student account: ${error.message}`))
      }
    }

    //-------------------LEGACY CODE-----------------
    // Batch cleanup
    // if (testData?.batch) {
    //   let res
    //   try {
    //     await retryOperation(
    //       'deleteBatch',
    //       async () => {
    //         res = await consoleAPIs.courseManagement.deleteBatch(testData.batch.id)
    //       },
    //       5,
    //     )
    //     logger.info('CLEANUP - Batch Id: ', testData.batch.id)
    //   } catch (error) {
    //     logger.error('batch delete error response:', testData.batch.id, testData.batch.name, await res.json())
    //     // console.error('batch delete error response:', testData.batch.id, testData.batch.name, await res.json())
    //     errors.push(new Error(`Failed to delete batch: ${error.message}`))
    //   }
    // }

    // Room cleanup
    // if (testData?.room) {
    //   try {
    //     await retryOperation(
    //       'deleteFacility',
    //       async () => {
    //         const res = await consoleAPIs.resourceManagement.deleteFacility(testData.room.id)
    //       },
    //       5,
    //     )
    //     logger.info('CLEANUP - Room Id: ', testData.room.id)
    //   } catch (error) {
    //     logger.error('room delete error response:', testData.room.id, testData.room.name)
    //     // console.log('room delete error response:', testData.room.id, testData.room.name)
    //     errors.push(new Error(`Failed to delete room: ${error.message}`))
    //   }
    // }

    // console.log('Cleanup done!')

    // If there were any errors, format and throw them in Playwright style
    if (errors.length > 0) {
      const errorMessage = errors.map((error, index) => `${index + 1}) ${error.message}`).join('\n\n')

      throw new Error(
        `Multiple cleanup errors occurred:\n\n${errorMessage}\n\n` +
        `${errors.length} cleanup ${errors.length === 1 ? 'error' : 'errors'} occurred.`,
      )
    }
  } catch (error) {
    // Format single errors in Playwright style
    if (error instanceof Error) {
      throw new Error(`Cleanup failed:\n\n1) ${error.message}\n\n1 error occurred.\n\n${JSON.stringify(testData)}`)
    }
    throw error
  }
}

//For Each Test Run , We create Student , Fetch teacher Account from DB (lock it), create Batch and create Room , to ensure Parallelized execution
interface SetupResult {
  success: boolean
  data: testData
  error: string | null
}

let testData // Declare testData outside

export async function testDataSetup(
  api: API,
  env: 'stage' | 'prod',
  stream: string,
  type: string,
): Promise<SetupResult> {
  // var resourceManagement = new ResourceManagement(api)
  // var courseManagement = new CourseManagement(api)
  // var auth = new Auth(api)
  var teacher, student, room, batch, lmmContent
  try {
    //1. Fetch Available Teacher Account From DB

    teacher = await getTeacherAcct(env)

    logger.info('SETUP - Teacher: ', 'Phone: ' + teacher.phone, 'Id: ' + teacher.id)
    // console.log(teacher)
    //2. Create Student Account Dynamically

    await retryOperation(
      'getStudentAccount',
      async () => {
        student = await fetchStudentAccount(env, stream)
      },
      3,
    )

    batch = {
      id: student!.batch_id,
      name: student!.batch_code,
      course: `${stream == 'JEE' ? process.env.JEE_BATCH_COURSE_ID : process.env.NEET_BATCH_COURSE_ID}`,
      phase: `${stream == 'JEE' ? process.env.JEE_BATCH_PHASE_ID : process.env.NEET_BATCH_PHASE_ID}`,
    }

    room = {
      id: student!.room_id,
      name: student!.room_code,
    }

    // console.log(student)
    logger.info(
      'SETUP - Student: ',
      'Phone: ' + student.phone,
      'Id: ' + student.id,
      'Name: ' + student.name,
      'Email: ' + student.email,
    )
    //3. Create a Room Dynamically
    // try {
    //   room = await retryOperation(
    //     'getRoom',
    //     async () => {
    //       const response = await resourceManagement.getRoom(
    //         { parent_id: process.env.ROOM_PARENT_ID },
    //         'FACILITY_TYPE_ROOM',
    //       )
    //       return response
    //     },
    //     3,
    //   )
    // } catch (error) {
    //   logger.error('Room Creation Failed', error)
    //   throw new Error('Room Creation Failed', error)
    // }

    // console.log(room)
    logger.info('SETUP - Room: ', 'Id: ' + room.id, 'Name: ' + room.name)
    //4. Create a Batch Dynamically for that Room
    // try {
    //   batch = await retryOperation(
    //     `getBatch ${stream == 'JEE' ? process.env.JEE_BATCH_COURSE_ID : process.env.NEET_BATCH_COURSE_ID}`,
    //     async () => {
    //       const response = await courseManagement.getBatch(
    //         {
    //           room_code: process.env.BATCH_ROOM_CODE,
    //           phase_id: stream == 'JEE' ? process.env.JEE_BATCH_PHASE_ID : process.env.NEET_BATCH_PHASE_ID,
    //           course_id: stream == 'JEE' ? process.env.JEE_BATCH_COURSE_ID : process.env.NEET_BATCH_COURSE_ID,
    //           facility_id: process.env.BATCH_FACILITY_ID,
    //         },
    //         type,
    //       )
    //       return response
    //     },
    //   )
    // } catch (error) {
    //   logger.error('Batch Creation Failed', error)
    //   throw new Error('Batch Creation Failed', error)
    // }

    // console.log(batch)
    logger.info('SETUP - Batch: ', 'Name: ' + batch.name, 'Phase: ' + batch.phase, 'Course: ' + batch.course)
    logger.info('SETUP - Phase Id: ', batch.phase)
    logger.info('SETUP - Course Id: ', batch.course)
    //5. Enroll Test Student
    // console.log(student)
    // await retryOperation('enrollStudent', async () => {
    //   await resourceManagement.enrollTestStudent(student.id, batch.course, batch.phase, batch.id)
    // })

    testData = {
      teacher: teacher as teacher,
      student: student! as student,
      batch: batch as batch,
      room: room as room,
      lmmContent: [lmmContent!] as lmmContent[],
    }
    return { success: true, data: testData, error: null }
  } catch (error) {
    const partialTestData: testData = {
      teacher: teacher as teacher,
      student: student! as student,
      batch: batch as batch,
      room: room as room,
      lmmContent: lmmContent ? [lmmContent] : [],
    }
    return {
      success: false,
      data: partialTestData,
      error: `TestData setup failed: ${error} ${JSON.stringify(partialTestData)}`,
    }
  }
}
