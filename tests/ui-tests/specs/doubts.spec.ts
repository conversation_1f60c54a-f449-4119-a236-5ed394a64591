import { test, expect, slowExpect, customExpect, testNeet } from '../fixtures/ui-fixtures'
import { PlaywrightUtils, StringUtils } from '../../commons/common-functions';
import { TeacherHomePage } from '../pageObjects/console/teacher-home-page';
import { InternalUserHomePage } from '../pageObjects/console/internal-user-home-page';
import { DoubtsPage } from '../pageObjects/web/doubts-page';
import { EnvUtils } from '../../commons/env-utilities';
import { generateRandomString } from '../../api-tests/helpers/utilities'

let checker = false;
const academicQue1 = `what is dc voltage? ${StringUtils.getCurrentTimeWithOffset("hh:mm:ss")}`;
let subject = "Physics";
let topic = "Heat and Thermodynamics";
const solutionForAskedDoubt = "It is nothing";
const currentEnv = EnvUtils.getInstance()
test.describe('Doubts UI Tests', {
  tag: ['@doubts', '@ui']
}, () => {
  if (currentEnv.isStage()) {
    test("Verify students can ask doubts and validate from teacher side", async ({ studentPage, testData, teacherPage, doubtSetup }) => {
      // navigating to doubts page
      await test.step("Doubts Mapping", async () => {
        await doubtSetup('JEE');
      });
      console.log(academicQue1, "vfgngfafhmgfemnbbfdsgn")
      await test.step("Navigate to doubts page and ask doubt by text", async () => {
        await studentPage.login();
        await studentPage.waitForLoadState('networkidle');
        await studentPage.waitForLoadState('domcontentloaded');
        await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
        await studentPage.pos.homePage.navigateToDoubtsPage();
        await slowExpect(studentPage.pos.doubtsPage.myDoubtsText.or(studentPage.pos.doubtsPage.askADoubtButton)).toBeVisible();
        if (await studentPage.pos.doubtsPage.myDoubtsText.isVisible()) {
          console.log("My Doubts is visible, proceeding with alternate flow.");
          await studentPage.pos.doubtsPage.navigateToDoubtsIfAlreadyAsked();
        } else {
          await studentPage.pos.doubtsPage.navigateToDoubtsWithFallbackUI();
        }
        await slowExpect(studentPage.pos.doubtsPage.chatArea).toBeVisible();
        await studentPage.pos.doubtsPage.chatArea.fill(academicQue1);
        await expect(studentPage.pos.doubtsPage.sendDoubtButton, "Verify send doubt button is visible").toBeVisible();
        await studentPage.pos.doubtsPage.sendDoubtButton.click();
        await slowExpect(studentPage.pos.doubtsPage.myDoubtsText, "Verify My Doubts text is visible").toBeVisible();
        await expect(studentPage.pos.doubtsPage.studentDoubtText(academicQue1)).toBeVisible();

        await test.step("Verify confirm subject and topic flow", async () => {
          await studentPage.pos.doubtsPage.selectSubjectAndTopicPopUp(topic, "physics", "JEE");
        });
        await studentPage.pos.doubtsPage.transferDoubtToTeacher("JEE");
      })

      await test.step("Verify filter options are working for Student side", async () => {
        await expect(studentPage.pos.doubtsPage.selectFilterOption, "Verify Filter option is visible").toBeVisible();
        studentPage.pos.doubtsPage.selectFilterOption.click();
        await expect(studentPage.pos.doubtsPage.sortByText, "Verify Filter modal is visible").toBeVisible();
        await expect(studentPage.pos.doubtsPage.latestRadioButton, "Verify radio button with text Latest is visble").toBeVisible();
        await studentPage.pos.doubtsPage.latestRadioButton.click();
        await expect(studentPage.pos.doubtsPage.subjectForFilter, "Verify select subject is visible").toBeVisible();
        await studentPage.pos.doubtsPage.subjectForFilter.click();
        await expect(studentPage.pos.doubtsPage.selectTopicText, "Verify select topic is visible").toBeVisible();
        await studentPage.pos.doubtsPage.selectTopicText.click();
        await expect(studentPage.pos.doubtsPage.searchTopicInput, "Verify input for topic search is visible").toBeVisible();
        await studentPage.pos.doubtsPage.searchTopicInput.fill(topic);
        await expect(studentPage.pos.doubtsPage.topicForFilter(topic), "Verify searched topic is visible").toBeVisible();
        await studentPage.pos.doubtsPage.topicForFilter(topic).click();
        await expect(studentPage.pos.doubtsPage.applyFilterButton, "Verify apply filter button is visible").toBeVisible();
        await studentPage.pos.doubtsPage.applyFilterButton.click();
        await customExpect(3000)(studentPage.pos.doubtsPage.appliedFilterResult(academicQue1)).toHaveText(academicQue1);
      })

      /* Doubtnut for JEE will auto respond due to data science issue */
      // await test.step(`Verify ask academic image doubt from student and mark it as resolved`, async () => {
      //   await studentPage.pos.doubtsPage.askAValidDoubtWithImageInDoubtsPage("test-data/doubt-Image.png");
      //   await slowExpect(studentPage.pos.doubtsPage.selectSubjectAndTopic).toBeVisible();
      //   await studentPage.pos.doubtsPage.sendDoubtButton.click();
      //   await studentPage.waitForTimeout(3000);
      //   if(await studentPage.pos.doubtsPage.transferYourDoubtToTeacherText.isVisible()){

      //     await expect(studentPage.pos.doubtsPage.transferYourDoubtToTeacherText).toBeVisible();
      //   }
      //   else{
      //     await studentPage.pos.doubtsPage.verifyDoubtMarkedAsResolved();
      //   }
      // });

      await test.step("Verify teacher is able to see doubts and reply as text", async () => {
        await slowExpect(teacherPage.pos.teacherHomePage.studentHelpButtonText).toBeVisible();
        await teacherPage.pos.teacherHomePage.studentHelpButtonText.click();
        await teacherPage.pos.teacherHomePage.studentsDoubtOption.waitFor();
        await teacherPage.pos.teacherHomePage.studentsDoubtOption.click();
        await slowExpect(teacherPage.pos.teacherDoubtsPage.doubtAssigned, "Verify doubts assigned toast is visible").toBeVisible();
        await expect(teacherPage.pos.teacherDoubtsPage.closeIconDoubtsAssignedToast, "Verify close icon for doubts assign toast").toBeVisible();
        await teacherPage.pos.teacherDoubtsPage.closeIconDoubtsAssignedToast.click();
        topic = await teacherPage.pos.teacherDoubtsPage.doubtTopic.innerText();
        await teacherPage.pos.teacherDoubtsPage.filterAndVerifyDoubt(topic, "JEE");
        await teacherPage.pos.teacherDoubtsPage.verifyStudentRaisedDoubtAndTextReply(academicQue1, solutionForAskedDoubt, "JEE");
      })

      // await test.step("Verify student able to see text reply from teacher and select need more help", async () => {
      //   await slowExpect(studentPage.pos.doubtsPage.needMoreHelp, "verify student able to see no need more help option is visible").toBeVisible();
      //   await studentPage.pos.doubtsPage.gotItTextAfterBotReply.click();
      // })


      await test.step("Verify teacher is able to reply as audio", async () => {
        await teacherPage.pos.teacherDoubtsPage.verifyAndsendAudioReply();
      })


      await test.step("Verify teacher is able to reply as image and doubts are visible on the answered section ", async () => {
        // await slowExpect(teacherPage.pos.teacherHomePage.studentHelpButtonText).toBeVisible();
        // await teacherPage.pos.teacherHomePage.studentHelpButtonText.click();
        // await teacherPage.pos.teacherHomePage.studentsDoubtOption.waitFor();
        // await teacherPage.pos.teacherHomePage.studentsDoubtOption.click();
        // await expect(teacherPage.pos.teacherDoubtsPage.closeIconDoubtsAssignedToast, "Verify close icon for doubts assign toast").toBeVisible();
        // await teacherPage.pos.teacherDoubtsPage.closeIconDoubtsAssignedToast.click();
        // await teacherPage.pos.teacherDoubtsPage.filterAndVerifyDoubt(topic, "JEE");
        // await teacherPage.pos.teacherDoubtsPage.verifyStudentRaisedDoubtAndTextReply(academicQue1, solutionForAskedDoubt, "JEE");
        // await teacherPage.pos.teacherDoubtsPage.verifyAndsendAudioReply();
        await teacherPage.pos.teacherDoubtsPage.sendImageReply();
        await expect(teacherPage.pos.teacherDoubtsPage.closeButton, "Verify close the doubt modal button is visible").toBeVisible();
        await teacherPage.pos.teacherDoubtsPage.closeButton.click();
        await expect(teacherPage.pos.teacherDoubtsPage.answeredDoubts, "Verify answered text is visible").toBeVisible();
        await teacherPage.pos.teacherDoubtsPage.answeredDoubts.click();
        await expect(teacherPage.pos.teacherDoubtsPage.studentDoubt(academicQue1).first(), "Verify student raised doubt is visible in teacher side").toBeVisible();
      })

      await test.step("Verify student is able to see teachers reply and mark it as resolved", async () => {
        await studentPage.reload();
        await studentPage.waitForTimeout(2000);
        await slowExpect(studentPage.pos.doubtsPage.studentAskedDoubt(academicQue1)).toBeVisible();
        await studentPage.pos.doubtsPage.studentAskedDoubt(academicQue1).click();
        await expect(studentPage.pos.doubtsPage.yesGotitText, "Verify yes got it text is visible").toBeVisible();
        await studentPage.pos.doubtsPage.yesGotitText.click();
        await studentPage.pos.doubtsPage.markedAsResolvedText.scrollIntoViewIfNeeded();
        await slowExpect(studentPage.pos.doubtsPage.markedAsResolvedText, "Verify marked as resolved is visible").toBeVisible();
      })
    })

    test("Verify teacher is able to map doubts from one teacher to another", async ({ teacherPage, adminPage, testData, studentPage, doubtSetup }) => {

      const academicQuestionForMapping = `what is dc voltage? ${StringUtils.getCurrentTimeWithOffset("hh:mm:ss")}`;
      await test.step("Doubts Mapping", async () => {
        await doubtSetup('JEE');
      });

      await test.step("Verify and raise a doubt and transfer to teacher", async () => {
        await studentPage.login();
        await studentPage.waitForLoadState('networkidle');
        await studentPage.waitForLoadState('domcontentloaded');
        await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
        await studentPage.pos.homePage.navigateToDoubtsPage();
        await studentPage.waitForTimeout(5000);
        if (await studentPage.pos.doubtsPage.myDoubtsText.isVisible()) {
          await studentPage.pos.doubtsPage.navigateToDoubtsIfAlreadyAsked();
        } else {
          await studentPage.pos.doubtsPage.navigateToDoubtsWithFallbackUI();
        }
        await studentPage.pos.doubtsPage.askARandomDoubtInDoubtsPage(academicQuestionForMapping);
        // await slowExpect(studentPage.pos.doubtsPage.selectSubjectAndTopic).toBeVisible();
        // await studentPage.pos.doubtsPage.sendDoubtButton.click();

        await slowExpect(studentPage.pos.doubtsPage.myDoubtsText, "Verify My Doubts text is visible").toBeVisible();
        await expect(studentPage.pos.doubtsPage.studentDoubtText(academicQuestionForMapping)).toBeVisible();
        await test.step("Verify confirm subject and topic flow", async () => {
          await studentPage.pos.doubtsPage.selectSubjectAndTopicPopUp(topic, "physics", "JEE");
        });
        await studentPage.pos.doubtsPage.transferDoubtToTeacher("JEE");
      });

      await test.step("Verify and transfer the doubt from one teacher to another teacher", async () => {
        await expect(teacherPage.pos.teacherHomePage.profileNameButton).toBeVisible();
        await expect(teacherPage.pos.teacherHomePage.userRolesText).toBeVisible();
        await teacherPage.pos.teacherHomePage.userRolesText.click();
        await teacherPage.waitForLoadState('networkidle');
        await expect(teacherPage.pos.teacherHomePage.internalUserButton).toBeVisible();
        await teacherPage.pos.teacherHomePage.internalUserButton.click();
        await teacherPage.waitForLoadState('networkidle');
        await teacherPage.pos.internalUserHomePage.navigateToDoubtsManagementPage();
        await teacherPage.pos.internalUserHomePage.mapDoubtToAnotherTeacher(testData.teacher.phone, 1234570100);
      });
    });
  }
});


testNeet.describe.skip('Doubts NEET user Tests', {
  tag: ['@doubts', '@ui']
}, () => {
  if (currentEnv.isStage()) {
    testNeet("Verify student can ask doubt, bot responses, validate from teacher side", async ({ studentPage, teacherPage, doubtSetup }) => {

      testNeet.setTimeout(120000)
      const academicNeetQues = "what is photosynthesis?//";
      const topic = "Organism, Population and Ecosystem";
      const subject = "Biology"

      await test.step("Doubts Mapping", async () => {
        await doubtSetup('NEET');
      });

      await testNeet.step("Navigate to doubts page and ask a doubt", async () => {
        await studentPage.login();
        await studentPage.waitForLoadState('networkidle');
        await studentPage.waitForLoadState('domcontentloaded');
        await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
        await studentPage.pos.homePage.navigateToDoubtsPage();

        await studentPage.waitForTimeout(5000);
        // await studentPage.waitForLoadState('networkidle');    
        if (await studentPage.pos.doubtsPage.myDoubtsText.isVisible()) {

          // Student has already asked the doubt but never interacted with the bot.
          console.log("Student has already asked the doubt but never interacted with the bot.");
          if (await studentPage.pos.doubtsPage.skipTextBotIntroduction.isVisible()) {
            checker = true
            await studentPage.pos.doubtsPage.verifyFirstBotExperienceDoubtNotAsked();
          }
          await studentPage.pos.doubtsPage.navigateToDoubtsIfAlreadyAsked();
        }
        else if (await studentPage.pos.doubtsPage.skipTextBotIntroduction.isVisible()) {
          // Student is going to ask the doubt for the first time, also never interacted with the bot
          console.log("Student is going to ask the doubt for the first time, also never interacted with the bot");
          checker = true
          await studentPage.pos.doubtsPage.verifyFirstUserExperienceForNeetUsersAndAskAQuestion();
        }
        else if (await studentPage.pos.doubtsPage.askADoubtButton.isVisible()) {
          // fallback ui of the first time user experience
          await studentPage.pos.doubtsPage.navigateToDoubtsWithFallbackUI();
        }
        else if (await studentPage.pos.doubtsPage.smarterWayToAskText.isVisible()) {
          await studentPage.pos.doubtsPage.smarterWayToAskText.click();
          await slowExpect(studentPage.pos.doubtsPage.letsStartLearningText).toBeVisible();
          await studentPage.pos.doubtsPage.letsStartLearningText.click();
          await slowExpect(studentPage.pos.doubtsPage.askAQuestionButton).toBeVisible();
          await expect(studentPage.pos.doubtsPage.gotItTextFromBot).toBeVisible();
          await studentPage.pos.doubtsPage.gotItTextFromBot.click();
          await slowExpect(studentPage.pos.doubtsPage.gotItTextAfterBotReply).toBeVisible();
          await studentPage.pos.doubtsPage.gotItTextAfterBotReply.click();
          await studentPage.pos.doubtsPage.askAQuestionButton.click();
        }


        await slowExpect(studentPage.pos.doubtsPage.enterYourDoubtInput).toBeVisible();
        await studentPage.pos.doubtsPage.enterYourDoubtInput.fill(academicNeetQues);
        await expect(studentPage.pos.doubtsPage.sendDoubtButton, "Verify send doubt button is visible").toBeVisible();
        await studentPage.pos.doubtsPage.sendDoubtButton.click();

        // await slowExpect(studentPage.pos.doubtsPage.selectSubjectAndTopic, "Verify select subject and topic text is visible").toBeVisible();
        // await slowExpect(studentPage.pos.doubtsPage.askedDoubtSubject, "Verify predicted subject is visible").toBeVisible();
        // await expect(studentPage.pos.doubtsPage.askedDoubtTopic, "Verify predicted topic is visible").toBeVisible();
        // await studentPage.pos.doubtsPage.askedDoubtTopic.click();
        // await expect(studentPage.pos.doubtsPage.searchTopicInput, "Verify search topic input is visible").toBeVisible();
        // await studentPage.pos.doubtsPage.searchTopicInput.fill(topic);
        // await expect(studentPage.pos.doubtsPage.selectedTopicForDoubt, "Verify searched topic is visible").toBeVisible();
        // await studentPage.pos.doubtsPage.selectedTopicForDoubt.click();
        // await studentPage.pos.doubtsPage.sendDoubtButton.click();
      })

      await testNeet.step("Verify asked doubt is visible", async () => {
        await slowExpect(studentPage.pos.doubtsPage.myDoubtsText, "Verify My Doubts text is visible").toBeVisible();
        await expect(studentPage.pos.doubtsPage.studentDoubtText(academicNeetQues)).toBeVisible();
        await test.step("Verify confirm subject and topic flow", async () => {
          await studentPage.pos.doubtsPage.selectSubjectAndTopicPopUp(topic, "biology", "NEET");
        });
      })
      if (!checker && await studentPage.pos.doubtsPage.skipTextBotIntroduction.isVisible({ timeout: 10000 })) {
        await testNeet.step("Veriffy bot introduction for already asked doubts", async () => {
          await studentPage.pos.doubtsPage.verifyFirstBotExperience();
        })
      }

      await testNeet.step("Simplify the doubt and transfer doubt to teacher", async () => {
        await studentPage.pos.doubtsPage.TransferDoubtToteacherFromBot();
      })

      await testNeet.step("Verify Teacher is able to see Doubts and reply in text, image, audio and doubts are visible on the answered section ", async () => {
        await slowExpect(teacherPage.pos.teacherHomePage.studentHelpButtonText).toBeVisible();
        await teacherPage.pos.teacherHomePage.studentHelpButtonText.click();
        await teacherPage.pos.teacherHomePage.studentsDoubtOption.waitFor();
        await teacherPage.pos.teacherHomePage.studentsDoubtOption.click();
        await expect(teacherPage.pos.teacherDoubtsPage.closeIconDoubtsAssignedToast, "Verify close icon for doubts assign toast").toBeVisible();
        await teacherPage.pos.teacherDoubtsPage.closeIconDoubtsAssignedToast.click();
        await teacherPage.pos.teacherDoubtsPage.filterAndVerifyDoubt(topic, "NEET");
        await teacherPage.pos.teacherDoubtsPage.verifyStudentRaisedDoubtAndTextReply(academicNeetQues, solutionForAskedDoubt, "NEET");
        await teacherPage.pos.teacherDoubtsPage.verifyAndsendAudioReply();
        await teacherPage.pos.teacherDoubtsPage.sendImageReply();
        await expect(teacherPage.pos.teacherDoubtsPage.closeButton, "Verify close the doubt modal button is visible").toBeVisible();
        await teacherPage.pos.teacherDoubtsPage.closeButton.click();
        await expect(teacherPage.pos.teacherDoubtsPage.answeredDoubts, "Verify answered text is visible").toBeVisible();
        await teacherPage.pos.teacherDoubtsPage.answeredDoubts.click();
        await expect(teacherPage.pos.teacherDoubtsPage.studentDoubt(academicNeetQues).first(), "Verify student raised doubt is visible in teacher side").toBeVisible();
      })

      await testNeet.step("Verify Student is able to see teachers reply and mark it as resolved", async () => {
        await slowExpect(studentPage.pos.doubtsPage.studentAskedDoubt(academicNeetQues)).toBeVisible();
        await studentPage.pos.doubtsPage.studentAskedDoubt(academicNeetQues).first().click();

        const maxAttempts = 2;
        // const waitBetweenAttempts = 2000;
        let attempts = 0;
        
        while (attempts < maxAttempts) {
          if (await studentPage.pos.doubtsPage.yesGotitText.isVisible({ timeout: 2000 }).catch(() => false)) {
            break;
          }
          await studentPage.reload();
          // await studentPage.waitForTimeout(waitBetweenAttempts);
          attempts++;
        }

        await expect(studentPage.pos.doubtsPage.yesGotitText, "Verify yes got it text is visible").toBeVisible();
        await studentPage.pos.doubtsPage.yesGotitText.click();
        await studentPage.pos.doubtsPage.markedAsResolvedText.scrollIntoViewIfNeeded();
        await slowExpect(studentPage.pos.doubtsPage.markedAsResolvedText, "Verify Marked as resolved is visible").toBeVisible();
      })
    })

    testNeet("Verify Vtag flow, ask image doubt from student side ", async ({ studentPage, doubtSetup }) => {
      await test.step("Doubts Mapping", async () => {
        await doubtSetup('NEET');
      });
      await studentPage.login();
      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.homePage.navigateToDoubtsPage();

      await studentPage.waitForTimeout(5000);
      if (await studentPage.pos.doubtsPage.myDoubtsText.isVisible()) {

        // Student has already asked the doubt but never interacted with the bot.
        if (await studentPage.pos.doubtsPage.skipTextBotIntroduction.isVisible()) {
          checker = true
          await studentPage.pos.doubtsPage.verifyFirstBotExperienceDoubtNotAsked();
        }
        await studentPage.pos.doubtsPage.navigateToDoubtsIfAlreadyAsked();
      }
      else if (await studentPage.pos.doubtsPage.skipTextBotIntroduction.isVisible()) {
        // Student is going to ask the doubt for the first time, also never interacted with the bot
        checker = true
        await studentPage.pos.doubtsPage.verifyFirstUserExperienceForNeetUsersAndAskAQuestion();
      }
      else if (await studentPage.pos.doubtsPage.askADoubtButton.isVisible()) {
        // fallback ui of the first time user experience
        checker = true
        await studentPage.pos.doubtsPage.navigateToDoubtsWithFallbackUI();
        if (!checker && await studentPage.pos.doubtsPage.skipTextBotIntroduction.isVisible({ timeout: 10000 })) {
          await testNeet.step("Veriffy bot introduction for already asked doubts", async () => {
            await studentPage.pos.doubtsPage.verifyFirstBotExperience();
          })
        }
      }
      else if (await studentPage.pos.doubtsPage.smarterWayToAskText.isVisible()) {
        await studentPage.pos.doubtsPage.smarterWayToAskText.click();
        await slowExpect(studentPage.pos.doubtsPage.letsStartLearningText).toBeVisible();
        await studentPage.pos.doubtsPage.letsStartLearningText.click();

        await expect(studentPage.pos.doubtsPage.skipTextBotIntroduction).toBeVisible();
        await studentPage.pos.doubtsPage.skipTextBotIntroduction.click();

        await expect(studentPage.pos.doubtsPage.closeBotIntroduction).toBeVisible();
        await studentPage.pos.doubtsPage.closeBotIntroduction.click();

        await slowExpect(studentPage.pos.doubtsPage.askAQuestionButton).toBeVisible();
        await expect(studentPage.pos.doubtsPage.gotItTextFromBot).toBeVisible();
        await studentPage.pos.doubtsPage.gotItTextFromBot.click();
        await slowExpect(studentPage.pos.doubtsPage.gotItTextAfterBotReply).toBeVisible();
        await studentPage.pos.doubtsPage.gotItTextAfterBotReply.click();
        await studentPage.pos.doubtsPage.askAQuestionButton.click();
      }


      // Step 2: Enter a Vtag into the input
      const vtagValue = "BC0908"; // Example Vtag value
      await studentPage.pos.doubtsPage.vtagInput.fill(vtagValue);

      // Step 3: Click the Q-code search button
      await studentPage.pos.doubtsPage.qcodeSearchButton.click();

      // await slowExpect(studentPage.pos.doubtsPage.selectSubjectAndTopic, "Verify select subject and topic text is visible").toBeVisible();
      // await expect(studentPage.pos.doubtsPage.sendDoubtButton, "Verify send doubts button is visible").toBeVisible();
      // await studentPage.pos.doubtsPage.sendDoubtButton.click();
      const raisedDoubt = vtagValue; // Use the same Vtag value as the raised doubt
      const doubtLocator = studentPage.pos.doubtsPage.studentAskedDoubt(raisedDoubt);

      // Verify the raised doubt is visible
      await expect(doubtLocator.first(), "Verify raised doubt is visible").toBeVisible();
      await doubtLocator.first().click();


      await studentPage.pos.doubtsPage.verifyDoubtMarkedAsResolvedForVtag();
    })
  }
})


if (EnvUtils.getInstance().isStage()) {
  testNeet.describe('Verify image support for bot response', {
    tag: ['@doubts', '@ui']
  }, () => {
    testNeet("Verify bot replies with an image for image-related doubts.", async ({ studentPage, doubtSetup, testData }) => {
      const courseId = process.env.NEET_BATCH_COURSE_ID;
      const batchId = testData.batch.id;
      const imageNeetQues = `what is malphigian body? ${generateRandomString(3)}`;

      await testNeet.step("Doubts Mapping", async () => {
        await doubtSetup('NEET');
      });

      await testNeet.step("Login and navigate to doubt section", async () => {
        await studentPage.login();
        await studentPage.waitForLoadState('networkidle');
        await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
        await studentPage.pos.homePage.navigateToDoubtsPage();
        await studentPage.pos.doubtsPage.page.waitForLoadState('networkidle');
        await studentPage.pos.doubtsPage.page.waitForTimeout(5000);
        const isFTUE = await studentPage.apis.doubtsHelper.doubtAskedOrNot(batchId, courseId);
        await studentPage.pos.doubtsPage.navigateToDoubtsSection(isFTUE);
      });

      await testNeet.step("Ask a doubt", async () => {
        await studentPage.pos.doubtsPage.askARandomDoubtInDoubtsPage(imageNeetQues);
      });

      await testNeet.step("Verify asked doubt is visible", async () => {
        await slowExpect(studentPage.pos.doubtsPage.myDoubtsText, "Verify My Doubts text is visible").toBeVisible();
        await expect(studentPage.pos.doubtsPage.studentDoubtText(imageNeetQues)).toBeVisible();
      });

      await testNeet.step("Verify image solution", async () => {
        await studentPage.pos.doubtsPage.verifyImageSolution();
      });
    });
  });

  testNeet.describe('Verify audio support for bot response', {
    tag: ['@doubts', '@ui']
  }, () => {
    testNeet("Should show audio support for bot reply", async ({ studentPage, doubtSetup, testData }) => {
      const courseId = process.env.NEET_BATCH_COURSE_ID;
      const batchId = testData.batch.id;
      const neetQues = `what is photosynthesis? ${generateRandomString(3)}`;

      await testNeet.step("Doubts Mapping", async () => {
        await doubtSetup('NEET');
      });

      await testNeet.step("Login and navigate to doubt section", async () => {
        await studentPage.login();
        await studentPage.waitForLoadState('networkidle');
        await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
        await studentPage.pos.homePage.navigateToDoubtsPage();
        await studentPage.pos.doubtsPage.page.waitForLoadState('networkidle');
        await studentPage.pos.doubtsPage.page.waitForTimeout(5000);
        const isFTUE = await studentPage.apis.doubtsHelper.doubtAskedOrNot(batchId, courseId);
        await studentPage.pos.doubtsPage.navigateToDoubtsSection(isFTUE);
      });

      await testNeet.step("Ask a doubt", async () => {
        await studentPage.pos.doubtsPage.askARandomDoubtInDoubtsPage(neetQues);
      });

      await testNeet.step("Verify asked doubt is visible", async () => {
        await slowExpect(studentPage.pos.doubtsPage.myDoubtsText, "Verify My Doubts text is visible").toBeVisible();
        await expect(studentPage.pos.doubtsPage.studentDoubtText(neetQues)).toBeVisible();
      });

      await testNeet.step("Verify bot response includes audio support", async () => {
        await studentPage.pos.doubtsPage.verifyBotAudioSupport();
      });
    });
  });
}
