import { test, expect, slowExpect, customExpect } from '../fixtures/ui-fixtures'
import { EnvUtils } from '../../commons/env-utilities';
import { StringUtils } from '../../commons/common-functions';

test.describe('Growth UI Tests', {
    tag: ['@growth', '@ui']
}, () => {
    const currentEnv = EnvUtils.getInstance();
    let referralCode;

    test('Verify that eligible students can view a widget and its page after creation and URL enablement,updating page config & versions and delete url/page/widget.', async ({ adminPage, testData, studentPage, growthTest }) => {
        test.setTimeout(100000);
        const webUrl = process.env.BASE_URL_WEB_UI ? process.env.BASE_URL_WEB_UI : ''
        const webUrlNavigated = "https://web.allen-stage.in/"

        const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
        const widgetName = 'Demo Automation' + StringUtils.generateRandomString(3)
        const widgetType = 'IMAGE_BANNER_V1'
        const criteriaType = 'Enrolled'
        const titleName = "Meet Our champions"
        const urlMobile = "https://res.cloudinary.com/dpzpn3dkw/image/upload/w_1600,f_auto,q_auto/v1737403541/juykhvlse6fazcgb7d4c.png?_upload_ref=ic_img_tool&__ar__=3.33"
        const urlDesktop = "https://res.cloudinary.com/dpzpn3dkw/image/upload/w_1600,f_auto,q_auto/v1737403540/rgjoghuj7v6nbscsqgnk.png?_upload_ref=ic_img_tool&__ar__=16.00"
        const cta1LabelName = 'Enroll'
        const navigationURL = "https://web.allen-stage.in/"
        let widgetconstId;

        const pageName = 'Demo Automation123'
        const pageType = 'LIST'
        let pageconstId;

        const string = StringUtils.generateRandomString(3)
        let urlName = '/automation-demo' + string
        const urlNameProd = 'automation-demo' + string
        const urlCategory = 'AOSAT_REGISTRATION_PAGE';
        let urlConstId;

        const widgetReName = 'Demo AutomationNew'
        const navigationNewURL = "https://Schedule-a-call-back.in/"
        const pageReName = 'Demo Automation Page'
        const urlReName = '/automation-demoPage'


        const version = 'Version 1'
        const pageDescriptionName = 'Page Name'

        await test.step(`Navigate and verify to widget creation and edits page in internal console`, async () => {
            await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
            await adminPage.goto(adminPage.pos.WidgetCreationEditPage.url) //this end point for automation purpose
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetCreationAndEditsPage();
        })

        await test.step(`Verify create widget page in internal console`, async () => {
            await adminPage.pos.WidgetCreationEditPage.clickOnCreateWidgetButton();
            await adminPage.pos.CreateWidgetPage.verifyCreateWidgetPage();
        })

        await test.step(`Verify create widget in internal console`, async () => {
            await adminPage.pos.CreateWidgetPage.enterWidgetDetails(widgetName, widgetType, criteriaType);
            await adminPage.pos.CreateWidgetPage.clickOnNextButton();
            await adminPage.pos.CreateWidgetPage.enterWidgetConfiguration(titleName, urlMobile, urlDesktop, navigationURL, cta1LabelName);
            await adminPage.pos.CreateWidgetPage.clickOnNextButton();
            await adminPage.pos.CreateWidgetPage.verifyWidgetReviewTab();
            await adminPage.pos.CreateWidgetPage.verifyWidgetCreatedSuccessfully();
        })

        await test.step(`Verify created widget and copy widget const id`, async () => {
            widgetconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetName);
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetconstId);
        })

        await test.step(`Navigate and verify to page creation and edits page in internal console`, async () => {
            await adminPage.goto(adminPage.pos.PageCreationEditPage.url) //this end point for automation purpose
            await adminPage.pos.PageCreationEditPage.verifyPageCreationAndEditsPage();
        })

        await test.step(`Verify create page page in internal console`, async () => {
            await adminPage.pos.PageCreationEditPage.clickOnCreatePageButton();
            await adminPage.pos.CreatePagePage.verifyCreatePagePage();
        })

        await test.step(`Verify create page in internal console`, async () => {
            await adminPage.pos.CreatePagePage.enterPageDetails(pageName, pageType);
            await adminPage.pos.CreateWidgetPage.clickOnNextButton();
            await adminPage.pos.CreatePagePage.verifyEnterPageConfiguration();
            await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId);
            await adminPage.pos.CreatePagePage.verifyClickOnPageLaout();
            await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId);
            await adminPage.pos.CreateWidgetPage.clickOnNextButton();
            await adminPage.pos.CreatePagePage.verifyPageReviewTab(pageName);
        })

        await test.step(`Verify created page and copy page const id`, async () => {
            pageconstId = await adminPage.pos.PageCreationEditPage.verifyCreatedPage(pageName);
            console.log(pageconstId + "pageconstId");
            await adminPage.pos.PageCreationEditPage.verifyPageConstIdCopyToClipboard(pageconstId);
        })

        await test.step(`Navigate and verify to url creation and edits page in internal console`, async () => {
            await adminPage.goto(adminPage.pos.CreateUrlEditsPage.url) //this end point for automation purpose
            await adminPage.pos.CreateUrlEditsPage.verifyUrlCreationAndEditsPage();
        })

        await test.step(`Verify create url in internal console`, async () => {
            await adminPage.pos.CreateUrlEditsPage.clickOnCreateUrlButton();
            await adminPage.pos.CreateUrlPage.enterAndVerifyCreateUrlPage(urlName, pageconstId, pageName, pageType, urlCategory);
            await adminPage.pos.CreateUrlPage.clickOnSubmitButton();
            await adminPage.pos.CreateUrlPage.verifyUrlCreated();
        })

        await test.step(`Verify created url and copy page const id`, async () => {
            urlConstId = await adminPage.pos.CreateUrlEditsPage.verifyCreatedUrl(urlName);
            console.log(urlConstId + "urlconstId");
            await adminPage.pos.CreateUrlEditsPage.verifyUrlConstIdCopyToClipboard(urlConstId);
        });

        await test.step(`Store Growth IDs for cleanup`, async () => {
            growthTest.growthContent.push({
                widgetId: widgetconstId,
                pageId: pageconstId,
                urlName: urlName,
                teacherId: testData.teacher.id
            })
        })


        await test.step(`Verify enable critia for url in internal console`, async () => {
            await adminPage.pos.CreateUrlEditsPage.verifyInternalUserCriteria(urlName, pageconstId);
            await adminPage.pos.CreateUrlEditsPage.verifyEnableCriteria(baseURL);
        });

        await test.step(`Store Growth id's for cleanup`, async () => {
            growthTest.growthContent.push({
                widgetId: [widgetconstId],
                pageId: pageconstId,
                urlName: urlName,
                teacherId: testData.teacher.id
            })
        })
        await test.step(`Navigate and verify to page created is linked to created url in internal console`, async () => {
            await adminPage.goto(adminPage.pos.PageCreationEditPage.url) //this end point for automation purpose
            await adminPage.pos.PageCreationEditPage.verifyPageLinkedToUrl(pageconstId, urlName);
        });

        await test.step(`Navigate and verify to widget created is linked to created page id in internal console`, async () => {
            await adminPage.goto(adminPage.pos.WidgetCreationEditPage.url) //this end point for automation purpose
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetFilter(widgetconstId);
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetLinkedToPageId(pageconstId);
        });

        /* Login to the student side with mobile number */
        await test.step(`Navigate to student web page without login`, async () => {
            await expect(studentPage.pos.preHomePage.loginButton, "Verify login button is visible").toBeVisible();
            if (currentEnv.isProd()) {
                await studentPage.goto(`${webUrl}${urlNameProd}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlNameProd}`);
            }
            else {
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
            }
            await studentPage.pos.webMigrationPage.verifyWithOutLogin(titleName);
        });

        /* Login to the student side with mobile number */
        await test.step(`Login to the student side`, async () => {
            await studentPage.login();
            await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
        });

        /* Login to the student side with mobile number */
        await test.step(`Verify the eligible student is able to open the url & see the linked widgets and page as configured`, async () => {
            if (currentEnv.isProd()) {
                await studentPage.goto(`${webUrl}${urlNameProd}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlNameProd}`);
            }
            else {
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
            }
            await studentPage.pos.webMigrationPage.verifyWithLoginAndEnrolled(titleName);
            await studentPage.pos.webMigrationPage.verifyImageClick();
            await slowExpect(studentPage).toHaveURL(`${webUrlNavigated}`);
        });


        await test.step(`Verify widget edit in internal console`, async () => {
            await adminPage.goto(adminPage.pos.WidgetCreationEditPage.url) //this end point for automation purpose
            await customExpect(15000)(adminPage.pos.WidgetCreationEditPage.apiLoader, "Verify page loading").not.toBeVisible();
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetCreationAndEditsPage();
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetFilter(widgetconstId);
            await adminPage.pos.WidgetCreationEditPage.clickOnCreatedWidgetId(widgetName);
            await adminPage.pos.CreateWidgetPage.editWidget(widgetReName, navigationNewURL);
            await adminPage.pos.CreateWidgetPage.verifyWidgetReviewTab();
            await adminPage.pos.CreateWidgetPage.verifyWidgetEditedSuccessfully();
            await adminPage.pos.WidgetCreationEditPage.verifyWidgetFilter(widgetconstId);
        })

        await test.step(`Verify to widget versions in internal console`, async () => {
            await adminPage.pos.WidgetCreationEditPage.clickOnCompareWidget();
            await adminPage.pos.WidgetCreationEditPage.verifyVersionManagementWidget();
            await adminPage.pos.WidgetCreationEditPage.verifyVersionCompareWidget(version);
            await adminPage.pos.WidgetCreationEditPage.verifyActivateVersionSelected();
        })


        await test.step(`Verify page edit in internal console`, async () => {
            await adminPage.goto(adminPage.pos.PageCreationEditPage.url) //this end point for automation purpose
            await customExpect(15000)(adminPage.pos.PageCreationEditPage.apiLoader, "Verify page loading").not.toBeVisible();
            await adminPage.pos.PageCreationEditPage.verifyPageCreationAndEditsPage();
            await adminPage.pos.PageCreationEditPage.verifyPageFilter(pageconstId);
            await adminPage.pos.PageCreationEditPage.clickOnCreatedPagetId(pageName);
            await adminPage.pos.CreatePagePage.verifyEditPage(pageDescriptionName);
            await adminPage.pos.PageCreationEditPage.verifyPageEditedSuccessfully();
            await adminPage.pos.PageCreationEditPage.verifyPageFilter(pageconstId);
        })

        await test.step(`Verify to page versions in internal console`, async () => {
            await adminPage.pos.WidgetCreationEditPage.clickOnCompareWidget();
            await adminPage.pos.WidgetCreationEditPage.verifyVersionManagementWidget();
            await adminPage.pos.WidgetCreationEditPage.verifyVersionCompareWidget(version);
            await adminPage.pos.WidgetCreationEditPage.verifyActivateVersionSelected();
        })

        /* Login to the student side  after widget edit and comparing version */
        await test.step(`Verify the eligible student is  open/refresh the linked url & see the updates made in the widget and page`, async () => {
            if (currentEnv.isProd()) {
                await studentPage.goto(`${webUrl}${urlNameProd}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlNameProd}`);
            }
            else {
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
            }
            await studentPage.pos.webMigrationPage.verifyWithLoginAndEnrolled(titleName);
            await studentPage.pos.webMigrationPage.verifyImageClick();
            await slowExpect(studentPage).toHaveURL(`${webUrlNavigated}`);
        });

        await test.step(`Deleting the url`, async () => {
            await adminPage.apis.resourceManagement.detachUrl(pageconstId, urlName, testData.teacher.id);
        })

        await test.step(`Deleting the page`, async () => {
            await adminPage.apis.resourceManagement.deletePage(pageconstId);
        });

        await test.step(`Deleting the widget`, async () => {
            await adminPage.apis.resourceManagement.deleteWidget(widgetconstId);
        });

        /* Verify the eligible user is able to see content not found error if the page or url is deleted from IC*/
        await test.step(`Verify the eligible user is able to see content not found error if the page or url is deleted from IC`, async () => {
            if (currentEnv.isProd()) {
                await studentPage.goto(`${webUrl}${urlNameProd}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlNameProd}`);
            }
            else {
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
            }
            await studentPage.pos.webMigrationPage.verifyPageUrlAfterDeletion();
        });

    });



    test('Verify enrolled user is able to open the referrals page, see the referral code and share it with their friends', async ({ studentPage, testData }) => {
        const webUrl = process.env.BASE_URL_WEB_UI ? process.env.BASE_URL_WEB_UI : ''
        const referAFriendUrl = '/refer-a-friend/'
        /* Login to the student side with mobile number */
        await test.step(`Login to the student side`, async () => {
            await studentPage.login();
            await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();

        });

        await test.step('Open refer a friend menu from profile dropdown', async () => {
            await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
            await studentPage.pos.homePage.userNameMenu.hover();
            await studentPage.pos.homePage.verifyProfileNavigationMenu(testData.student.name);
            await expect(studentPage.pos.homePage.referAFriendMenu, 'Verify refer a friend menu is visible').toBeVisible();
            await studentPage.pos.homePage.referAFriendMenu.click();
            await expect(studentPage).toHaveURL(/.*refer-a-friend/);
        });

        await test.step('Verify enrolled user is able to see the Referral code with a copy and share CTA on the landing page', async () => {
            referralCode = await studentPage.pos.referAFriendPage.verifyReferAFriendPage();
            await studentPage.pos.referAFriendPage.clickOnTermsAndConditions();
            await expect(studentPage).toHaveURL(/.*referral-tnc/);
            await studentPage.pos.referAFriendPage.verifyReferralProgramTC();
        });

        await test.step('Verify the enrolled user is able to copy the Referral link by clicking on the Copy CTA', async () => {
            await studentPage.goto(`${webUrl}${referAFriendUrl}`);
            await studentPage.pos.referAFriendPage.verifyCodeReferralCodeToClipBoard();
        });


        await test.step('Verify enrolled user is able to see a share popup with different share options by clicking on the Share CTA', async () => {
            await studentPage.pos.referAFriendPage.verifyClickOnShareReferralCode();

        });

        await test.step('Verify enrolled user is able to share the Referral link using any share option available in the share popup', async () => {
            await studentPage.pos.referAFriendPage.verifyShareLink('gmail');
            await studentPage.pos.referAFriendPage.verifyShareLink('copyToClipboard');
            await studentPage.pos.referAFriendPage.verifyCopiedToClipBoardToastMessage();
            await studentPage.pos.referAFriendPage.verifyShareLink('whatsAppIcon');
            let newTabPromise = studentPage.waitForEvent("popup");
            studentPage.pos.referAFriendPage.whatsAppIcon.click();
            const whatsAppPage = await newTabPromise;
            await expect(whatsAppPage).toHaveURL(/.*api.whatsapp.com/);
            await whatsAppPage.close();
            await studentPage.waitForTimeout(500); //wait is requried

            await studentPage.pos.referAFriendPage.verifyShareLink('instIcon');
            newTabPromise = studentPage.waitForEvent("popup");
            studentPage.pos.referAFriendPage.instaIcon.click();
            const instaPage = await newTabPromise;
            await expect(instaPage).toHaveURL(/.*instagram.com/);
            await instaPage.close();
            await studentPage.waitForTimeout(500); //wait is requried

            await studentPage.pos.referAFriendPage.verifyShareLink('telegramIcon');
            newTabPromise = studentPage.waitForEvent("popup");
            studentPage.pos.referAFriendPage.telegramIcon.click();
            const telegramPage = await newTabPromise;
            await expect(telegramPage).toHaveURL(/.*t.me/);
            await telegramPage.close();

        });

    })

    if (currentEnv.isStage()) {
        test('Verify freemium user is able to click on the referral links, explore and purchase a course using a referral code', async ({ studentPage, testData }) => {
            const webUrl = process.env.BASE_URL_WEB_UI ? process.env.BASE_URL_WEB_UI : ''
            const email_Id = process.env.FREEMIUM_USER ? process.env.FREEMIUM_USER : ''
            const mobileNumber = process.env.FREEMIUM_USER_NUMBER ? process.env.FREEMIUM_USER_NUMBER : ''
            const studentMobileNumber = StringUtils.generateRandomMobileNumber();
            const studentName = "user" + StringUtils.generateRandomString(10);

            const invalidReferralCode = 'ABCD'
            const invalidReferralUrl = `/referral?code=${invalidReferralCode}`
            let languageSelectedValue;
            let courseStartDateSelected;

            await test.step(`Login to the student side`, async () => {
                await studentPage.login();
                await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
            });

            await test.step('Open refer a friend menu from profile dropdown', async () => {
                await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
                await studentPage.pos.homePage.userNameMenu.hover();
                await studentPage.pos.homePage.verifyProfileNavigationMenu(testData.student.name);
                await expect(studentPage.pos.homePage.referAFriendMenu, 'Verify refer a friend menu is visible').toBeVisible();
                await studentPage.pos.homePage.referAFriendMenu.click();
                await expect(studentPage).toHaveURL(/.*refer-a-friend/);
            });

            await test.step('Verify enrolled user is able to see the referral code with a copy ', async () => {
                referralCode = await studentPage.pos.referAFriendPage.verifyReferAFriendPage();
                console.log("Referral Code1:", referralCode);
            });

            let referralUrl = `referral?code=${referralCode}`

            /* User logout flow and Forgot password flow  */
            await test.step(`Verify user logout flow`, async () => {
                /* Forgot pass word flow */
                await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
                await studentPage.pos.homePage.userNameMenu.hover();
                await expect(studentPage.pos.homePage.settingsMenu, "Verify settings menu button is visible").toBeVisible();
                await studentPage.pos.homePage.settingsMenu.click();
                await studentPage.waitForTimeout(1000); //wait is requried due to websocket
                await expect(studentPage).toHaveURL(/.*view=settings/);
                await expect(studentPage.pos.profilePage.accountSettingsSubTitleText, 'Verifying "account settings title" should be visible').toBeVisible();
                await expect(studentPage.pos.profilePage.logoutButton, 'Verifying "logout of all devices" text should be visible').toBeVisible();
                await studentPage.pos.profilePage.logoutButton.click();
                await studentPage.waitForTimeout(1000); //wait is requried due to websocket
                await expect(studentPage.pos.profilePage.logoutConfirmText, 'Verify logout confirm text is visible').toBeVisible();
                await expect(studentPage.pos.profilePage.accountLogout, 'Verify logout button is visible').toBeVisible();
                await studentPage.pos.profilePage.accountLogout.click();
            });


            await test.step(`Verify the non-logged in freemium user is able to see the Continue CTA on the landing page`, async () => {
                await expect(studentPage.pos.preHomePage.loginButton, "Verify login button is visible").toBeVisible();
                if (currentEnv.isStage()) {
                    referralUrl = '/' + referralUrl;
                    await studentPage.goto(`${webUrl}${referralUrl}`);
                }
                else {
                    await studentPage.goto(`${webUrl}${referralUrl}`);
                }
                await slowExpect(studentPage).toHaveURL(`${webUrl}${referralUrl} `);
                await studentPage.pos.referAFriendPage.verifyUnEnrolledStudentReferralCodePage();
                if (currentEnv.isStage()) {
                    await studentPage.pos.preHomePage.loginWidget.loginWithMobileNumber(studentMobileNumber, '1111');
                    await studentPage.waitForTimeout(2000); //wait is requried due to websocket
                } else {
                    await studentPage.pos.preHomePage.loginWidget.loginWithUsername(email_Id, mobileNumber);
                }
            })

            if (currentEnv.isStage()) {
                await test.step(`Verify logged -in freemium user onboard`, async () => {
                    await expect(studentPage).toHaveURL(/.*onboarding/);
                    await studentPage.waitForLoadState('domcontentloaded');
                    await expect(studentPage.pos.onboardingPage.onboardingImage, "Verify onboarding image is visible").toBeVisible();
                    await expect(studentPage.pos.onboardingPage.onboardingFormCard, "Verify onboarding form card is visible").toBeVisible();
                    await expect(studentPage.pos.onboardingPage.onboardingChipGroup, "Verify onboarding chip group is visible").toBeVisible();
                    await studentPage.pos.onboardingPage.onBoardToNEET();
                    await studentPage.pos.onboardingPage.onboardingTextInput.fill(studentName);
                    await expect(studentPage.pos.onboardingPage.studentNameSubmitButton, 'Check submit butten is visible').toBeVisible();
                    await studentPage.pos.onboardingPage.studentNameSubmitButton.click();
                })
            }


            await test.step(`Verify logged -in freemium user is able to explore the courses using the explore cta on the landing page`, async () => {
                await studentPage.pos.referAFriendPage.verifyLoggedInStudentReferralCodePage();
                await slowExpect(studentPage).toHaveURL(/.*courses/);
                await studentPage.goto(`${webUrl}`);

            })

            await test.step(`Verify logged -in freemium user is able to see the referral code of the first clicked link pre - applied on the checkout page`, async () => {

                await slowExpect(studentPage.pos.preHomePage.neetButton, "Verify Neet button is visible").toBeVisible();
                await studentPage.pos.preHomePage.neetButton.click();
                await slowExpect(studentPage.pos.preHomePage.selectYourStreamTitle, "Verify select your stream title is visible").toBeVisible();
                await expect(studentPage.pos.preHomePage.selectYourClassTitle, "Verify select your class title is visible").toBeVisible();
                await expect(studentPage.pos.preHomePage.class11Th, "Verify select class 11th title is visible").toBeVisible();
                await studentPage.pos.preHomePage.class11Th.click();
                await expect(studentPage.pos.preHomePage.exploreCourses, "Verify explore courses is visible").toBeVisible();
                await expect(studentPage.pos.preHomePage.exploreCourses, "Verify explore courses is enabled").toBeEnabled();
                await studentPage.pos.preHomePage.exploreCourses.click();
                // checking with neet 11 course
                await expect(studentPage).toHaveURL(/.*online-coaching-class-11/);
                await slowExpect(studentPage.pos.courseDetailsPage.liveProgramsTitle, "Verify live Programs Title is visible").toBeVisible();
                await expect(studentPage.pos.courseDetailsPage.nurtureOnlineProgram, "Verify nurture Online Program card is visible").toBeVisible();
                await studentPage.pos.courseDetailsPage.nurtureOnlineProgram.click();
                await expect(studentPage).toHaveURL(/.*nurture-online-programs-class-11/);
                
                // ✅ Check if popup is visible and close it
                try {
                    await slowExpect(studentPage.pos.courseDetailsPage.closePopupButton, "Verify close popup button is visible").toBeVisible();
                    await studentPage.pos.courseDetailsPage.closePopupButton.click();
                }catch (error) {
                    console.log("Popup not visible or already closed, continuing with the test");
                }

                await studentPage.pos.courseDetailsPage.verifyCourseDetails(); // validating course details page titles
                await expect(studentPage.pos.courseDetailsPage.languagePrefrenceValue.nth(0), "Verify language preference is visible").toBeVisible();
                languageSelectedValue = await studentPage.pos.courseDetailsPage.languagePrefrenceValue.nth(0).textContent();
                courseStartDateSelected = await studentPage.pos.courseDetailsPage.courseStartDateButton.textContent();
                let multipleLanguageCount = await studentPage.pos.courseDetailsPage.languageContainer.count();
                if (multipleLanguageCount > 1) {
                    await expect(studentPage.pos.courseDetailsPage.languageContainer.nth(1), "Verify second language preference is visible").toBeVisible();
                    await studentPage.pos.courseDetailsPage.languageContainer.nth(1).click();
                    await expect(studentPage.pos.courseDetailsPage.courseStartDateButton, "Verify course start date button is visible").toBeVisible();
                    await studentPage.pos.courseDetailsPage.courseStartDateButton.first().click();
                }           
                await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "Verify enroll now button is enabled").toBeEnabled();
                await studentPage.pos.courseDetailsPage.enrollNowButton.click();
                await slowExpect(studentPage).toHaveURL(/.*checkout*/);
                await studentPage.waitForTimeout(1000); //wait is requried due to websocket
                await slowExpect(studentPage.pos.checkoutPage.oneYearText, "Verify one year course duration text is visible").toBeVisible();
                await slowExpect(studentPage.pos.checkoutPage.appliedText, 'Verifying applied text is visible').toBeVisible();
                await studentPage.pos.checkoutPage.verifyReferralCodeCoupon(referralCode);
            })

            await test.step(`Verify logged -in freemium user gets an error message upon applying an invalid code`, async () => {
                await studentPage.goto(`${webUrl}${invalidReferralUrl} `);
                await studentPage.pos.referAFriendPage.verifyLoggedInStudentInvalidReferralCodePage();

            })
        })


        test('Verify create course single filter dimension on internal console and verify plp page on student side', async ({ adminPage, studentPage, testData, growthTest }) => {
            const webUrl = process.env.BASE_URL_WEB_UI ? process.env.BASE_URL_WEB_UI : ''
            const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
            const widgetName = 'Demo Filter Automation' + StringUtils.generateRandomString(3)
            const widgetType = 'COURSE_GRID_WITH_FILTER'
            const criteriaType = 'Enrolled'
            const widgetHeading = "Explore our NEET courses"
            const cardTitleName = 'NEET Nurture Online Program'
            const programName = "LIVE"
            let widgetconstId;
            let ctaLabelName = "View all programs";
            let className11 = "Class 11";
            let className12 = "Class 12";
            let actual_price = "88983";
            let discounted_price = "42372";
            let ctaName = "Know More";
            let className = "Class";

            const neetClass11Url = "/neet/online-coaching-class-11";
            const neetClass12Url = "/neet/online-coaching-class-12";
            const programInfo1 = "Covers the complete syllabus of NEET";
            const programInfo2 = "Study material delivered at home";

            const pageName = 'Demo Automation Single filter'
            const pageType = 'LIST'
            let pageconstId;
            const string = StringUtils.generateRandomString(3)
            const urlName = '/automation-filter' + string
            const urlCategory = 'PLP';
            const urlNameProd = 'automation-demo' + string

            let urlConstId;

            await test.step(`Navigate and verify to widget creation and edits page in internal console`, async () => {
                await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
                await adminPage.goto(adminPage.pos.WidgetCreationEditPage.url) //this end point for automation purpose
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetCreationAndEditsPage();
            })

            await test.step(`Verify create widget page in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.clickOnCreateWidgetButton();
                await adminPage.pos.CreateWidgetPage.verifyCreateWidgetPage();
            })

            await test.step(`Verify create widget in internal console`, async () => {
                await adminPage.pos.CreateWidgetPage.enterWidgetDetails(widgetName, widgetType, criteriaType);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreateWidgetPage.enterSingleFilterDimensionsValues(className11, className12, className);
                await adminPage.pos.CreateWidgetPage.enterCTAsQuickFilterValues(ctaLabelName, neetClass11Url, neetClass12Url);
                await adminPage.pos.CreateWidgetPage.enterFilterPreferences(widgetHeading);
                await adminPage.pos.CreateWidgetPage.addCardDetails(cardTitleName, programName, actual_price, discounted_price, ctaName, programInfo1, programInfo2, neetClass11Url, className, className11);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreateWidgetPage.verifyWidgetReviewTab();
                await adminPage.pos.CreateWidgetPage.verifyWidgetCreatedSuccessfully();
            })

            await test.step(`Verify created widget and copy widget const id`, async () => {
                widgetconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetName);
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetconstId);
            })


            await test.step(`Navigate and verify to page creation and edits page in internal console`, async () => {
                await adminPage.goto(adminPage.pos.PageCreationEditPage.url) //this end point for automation purpose
                await adminPage.pos.PageCreationEditPage.verifyPageCreationAndEditsPage();
            })

            await test.step(`Verify create page page in internal console`, async () => {
                await adminPage.pos.PageCreationEditPage.clickOnCreatePageButton();
                await adminPage.pos.CreatePagePage.verifyCreatePagePage();
            })

            await test.step(`Verify create page in internal console`, async () => {
                await adminPage.pos.CreatePagePage.enterPageDetails(pageName, pageType);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreatePagePage.verifyEnterPageConfiguration();
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId);
                await adminPage.pos.CreatePagePage.verifyClickOnPageLaout();
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreatePagePage.verifyPageReviewTab(pageName);
            })

            await test.step(`Verify created page and copy page const id`, async () => {
                pageconstId = await adminPage.pos.PageCreationEditPage.verifyCreatedPage(pageName);
                await adminPage.pos.PageCreationEditPage.verifyPageConstIdCopyToClipboard(pageconstId);
            })

            await test.step(`Navigate and verify to url creation and edits page in internal console`, async () => {
                await adminPage.goto(adminPage.pos.CreateUrlEditsPage.url) //this end point for automation purpose
                await adminPage.pos.CreateUrlEditsPage.verifyUrlCreationAndEditsPage();
            })

            await test.step(`Verify create url in internal console`, async () => {
                await adminPage.pos.CreateUrlEditsPage.clickOnCreateUrlButton();
                await adminPage.pos.CreateUrlPage.enterAndVerifyCreateUrlPage(urlName, pageconstId, pageName, pageType, urlCategory);
                await adminPage.pos.CreateUrlPage.clickOnSubmitButton();
                await adminPage.pos.CreateUrlPage.verifyUrlCreated();
            })

            await test.step(`Verify created url and copy page const id`, async () => {
                urlConstId = await adminPage.pos.CreateUrlEditsPage.verifyCreatedUrl(urlName);
                await adminPage.pos.CreateUrlEditsPage.verifyUrlConstIdCopyToClipboard(urlConstId);
            });

            await test.step(`Verify enable critia for url in internal console`, async () => {
                await adminPage.pos.CreateUrlEditsPage.verifyInternalUserCriteria(urlName, pageconstId);
                await adminPage.pos.CreateUrlEditsPage.verifyEnableCriteria(baseURL);
            });

            await test.step(`Store Growth id's for cleanup`, async () => {
                growthTest.growthContent.push({
                    widgetId: [widgetconstId],
                    pageId: pageconstId,
                    urlName: urlName,
                    teacherId: testData.teacher.id
                })
            })

            /* Login to the student side with mobile number */
            await test.step(`Login to the student side`, async () => {
                await studentPage.login();
                await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verifying "explore side menu" text should be visible').toBeVisible();
            });

            /* Login to the student side with mobile number */
            await test.step(`Verify the student is able to see filters at the top/row/horizontally in the PLP page`, async () => {

                if (currentEnv.isProd()) {
                    await studentPage.goto(`${webUrl}${urlNameProd}`);
                    await slowExpect(studentPage).toHaveURL(`${webUrl}${urlNameProd}`);
                }
                else {
                    await studentPage.goto(`${webUrl}${urlName}`);
                    await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
                }

                await studentPage.pos.webMigrationPage.verifyWithLoginAndEnrolled(widgetHeading);
                await studentPage.pos.webMigrationPage.verifyFilterCreatedOnPlpPage();
                await studentPage.pos.webMigrationPage.clickOnKnowMoreCtaButton();
                await slowExpect(studentPage).toHaveURL(`${neetClass11Url}`);
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
                await studentPage.pos.webMigrationPage.clickOnVieAllProgramsCtaButton();
                await slowExpect(studentPage).toHaveURL(`${neetClass11Url}`);
            });
        });
    }

    if (currentEnv.isStage()) {
        test('Verify create course multi filter dimension on internal console and verify plp page on student side', async ({ adminPage, studentPage, testData, growthTest }) => {
            const webUrl = process.env.BASE_URL_WEB_UI ? process.env.BASE_URL_WEB_UI : ''
            const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
            let widgetName1 = 'NEET (TagGradBlue)'
            let widgetName2 = 'NEET Image Banner'
            let widgetName3 = 'NEET-11 PLP'
            let widgetconstId;
            let widgetconstId2;
            let widgetconstId3;
            let widgetHeading = 'Live Programs';

            const pageName = 'Demo Automation Multi filter'
            const pageType = 'LIST'
            let pageconstId;

            const urlName = '/automation-multifilter' + StringUtils.generateRandomString(3)
            const urlCategory = 'PLP';
            let urlConstId;

            await test.step(`Navigate and verify to widget creation and edits page in internal console`, async () => {
                await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
                await adminPage.goto(adminPage.pos.WidgetCreationEditPage.url) //this end point for automation purpose
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetCreationAndEditsPage();
            })

            await test.step(`Verify duplicate neet tag blue widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetName1);
                widgetName1 = widgetName1 + " (Copy)";
                widgetconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetName1);
                console.log(widgetconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetconstId);

            })

            await test.step(`Verify duplicate neet imaga banner widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetName2);
                widgetName2 = widgetName2 + " (Copy)";
                widgetconstId2 = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetName2);
                console.log(widgetconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetconstId);

            })

            await test.step(`Verify duplicate neet 11 plp widget and copy widget const id`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetName3);
                widgetName3 = widgetName3 + " (Copy)";
                widgetconstId3 = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetName3);
                console.log(widgetconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetconstId);
            })

            await test.step(`Navigate and verify to page creation and edits page in internal console`, async () => {
                await adminPage.goto(adminPage.pos.PageCreationEditPage.url) //this end point for automation purpose
                await adminPage.pos.PageCreationEditPage.verifyPageCreationAndEditsPage();
            })

            await test.step(`Verify create page page in internal console`, async () => {
                await adminPage.pos.PageCreationEditPage.clickOnCreatePageButton();
                await adminPage.pos.CreatePagePage.verifyCreatePagePage();
            })

            await test.step(`Verify create page in internal console`, async () => {
                await adminPage.pos.CreatePagePage.enterPageDetails(pageName, pageType);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreatePagePage.verifyEnterPageConfiguration();
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId);
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId2);
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetconstId3);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreatePagePage.verifyPageReviewTab(pageName);
            })

            await test.step(`Verify created page and copy page const id`, async () => {
                pageconstId = await adminPage.pos.PageCreationEditPage.verifyCreatedPage(pageName);
                console.log(pageconstId + "pageconstId");
                await adminPage.pos.PageCreationEditPage.verifyPageConstIdCopyToClipboard(pageconstId);
            })

            await test.step(`Navigate and verify to url creation and edits page in internal console`, async () => {
                await adminPage.goto(adminPage.pos.CreateUrlEditsPage.url) //this end point for automation purpose
                await adminPage.pos.CreateUrlEditsPage.verifyUrlCreationAndEditsPage();
            })

            await test.step(`Verify create url in internal console`, async () => {
                await adminPage.pos.CreateUrlEditsPage.clickOnCreateUrlButton();
                await adminPage.pos.CreateUrlPage.enterAndVerifyCreateUrlPage(urlName, pageconstId, pageName, pageType, urlCategory);
                await adminPage.pos.CreateUrlPage.clickOnSubmitButton();
                await adminPage.pos.CreateUrlPage.verifyUrlCreated();
            })

            await test.step(`Verify created url and copy page const id`, async () => {
                urlConstId = await adminPage.pos.CreateUrlEditsPage.verifyCreatedUrl(urlName);
                console.log(urlConstId + "urlconstId");
                await adminPage.pos.CreateUrlEditsPage.verifyUrlConstIdCopyToClipboard(urlConstId);
            });

            await test.step(`Store Growth id's for cleanup`, async () => {

                growthTest.growthContent.push({
                    widgetId: [widgetconstId, widgetconstId2, widgetconstId3], // Store multiple widget IDs in an array
                    pageId: pageconstId,
                    urlName: urlName,
                    teacherId: testData.teacher.id

                });
            })

            await test.step(`Verify enable critia for url in internal console`, async () => {
                await adminPage.pos.CreateUrlEditsPage.verifyInternalUserCriteria(urlName, pageconstId);
                await adminPage.pos.CreateUrlEditsPage.verifyEnableCriteria(baseURL);
            });

            /* Login to the student side with mobile number */
            await test.step(`Login to the student side`, async () => {
                await studentPage.login();
                await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
            });

            /* Login to the student side with mobile number */
            await test.step(`Verify the student is able to see filters at the top/row/horizontally in the PLP page`, async () => {
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);
                await studentPage.pos.webMigrationPage.verifyWithLoginAndEnrolled(widgetHeading);
                await studentPage.pos.webMigrationPage.verifyMultiFilterCreatedOnPlpPage();
                await studentPage.pos.webMigrationPage.clickOnFilterCtaButton();
            });

        })
    }

    if (currentEnv.isStage()) {
        test('Verify the seo page is working properly', async ({ adminPage, studentPage, testData, growthTest }) => {
            test.setTimeout(700000);
            const webUrl = process.env.BASE_URL_WEB_UI ? process.env.BASE_URL_WEB_UI : ''
            const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
            let widgetNavBarName = 'NAVBAR'
            let widgetNavBar = 'Navbar'
            let widgetCBSEName = 'CBSE'
            let widgetTextCarousalName = 'Text Article Carousel'
            let widgetLinksName = 'Trending Topics'

            let widgetTestKnowledgeName = "Quiz"
            let widgetNavBarconstId;
            let widgetCBSEconstId;
            let widgetTextCarousalconstId;
            let widgetLinksconstId;
            let widgetTestKnowledgeconstId;
            const pageName = 'Demo Automation Seo Page'
            const pageType = 'LIST'
            let pageconstId;

            const urlName = '/automation-seopage' + StringUtils.generateRandomString(3)
            const urlCategory = 'SEO_PAGES';
            let urlConstId;

            await test.step(`Navigate and verify to widget creation and edits page in internal console`, async () => {
                await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
                await adminPage.goto(adminPage.pos.WidgetCreationEditPage.url) //this end point for automation purpose
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetCreationAndEditsPage();
            })

            await test.step(`Verify navbar widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetNavBarName);
                widgetNavBarName = widgetNavBar + " (Copy)";
                widgetNavBarconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetNavBarName);
                console.log(widgetNavBarconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetNavBarconstId);

            })

            await test.step(`Verify editor content upload widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetCBSEName);
                widgetCBSEName = widgetCBSEName + " (Copy)";
                widgetCBSEconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetCBSEName);
                console.log(widgetCBSEconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetCBSEconstId);

            })

            await test.step(`Verify article suggestions widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetTextCarousalName);
                widgetTextCarousalName = widgetTextCarousalName + " (Co";
                widgetTextCarousalconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetTextCarousalName);
                console.log(widgetTextCarousalconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetTextCarousalconstId);

            })

            await test.step(`Verify widget links widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetLinksName);
                widgetLinksName = widgetLinksName + " (Copy)";
                widgetLinksconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetLinksName);
                console.log(widgetLinksconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetLinksconstId);

            })

            await test.step(`Verify test knowledge widget in internal console`, async () => {
                await adminPage.pos.WidgetCreationEditPage.verifyDuplicateWidget(widgetTestKnowledgeName);
                widgetTestKnowledgeName = widgetTestKnowledgeName + " (Copy)";
                widgetTestKnowledgeconstId = await adminPage.pos.WidgetCreationEditPage.verifyCreatedWidget(widgetTestKnowledgeName);
                console.log(widgetTestKnowledgeconstId + "widgetconstId");
                await adminPage.pos.WidgetCreationEditPage.verifyWidgetConstIdCopyToClipboard(widgetTestKnowledgeconstId);

            })

            await test.step(`Navigate and verify to page creation and edits page in internal console`, async () => {
                await adminPage.goto(adminPage.pos.PageCreationEditPage.url) //this end point for automation purpose
                await adminPage.pos.PageCreationEditPage.verifyPageCreationAndEditsPage();
            })

            await test.step(`Verify create page page in internal console`, async () => {
                await adminPage.pos.PageCreationEditPage.clickOnCreatePageButton();
                await adminPage.pos.CreatePagePage.verifyCreatePagePage();
            })

            await test.step(`Verify create page in internal console`, async () => {
                await adminPage.pos.CreatePagePage.enterPageDetails(pageName, pageType);
                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreatePagePage.verifyEnterPageConfiguration();
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetNavBarconstId);
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetCBSEconstId);
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetTextCarousalconstId);
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetLinksconstId);
                await adminPage.pos.CreatePagePage.enterWidgetIdAndSaveDetails(widgetTestKnowledgeconstId);

                await adminPage.pos.CreateWidgetPage.clickOnNextButton();
                await adminPage.pos.CreatePagePage.verifyPageReviewTab(pageName);
            })

            await test.step(`Verify created page and copy page const id`, async () => {
                pageconstId = await adminPage.pos.PageCreationEditPage.verifyCreatedPage(pageName);
                console.log(pageconstId + "pageconstId");
                await adminPage.pos.PageCreationEditPage.verifyPageConstIdCopyToClipboard(pageconstId);
            })

            await test.step(`Navigate and verify to url creation and edits page in internal console`, async () => {
                await adminPage.goto(adminPage.pos.CreateUrlEditsPage.url) //this end point for automation purpose
                await adminPage.pos.CreateUrlEditsPage.verifyUrlCreationAndEditsPage();
            })

            await test.step(`Verify create url in internal console`, async () => {
                await adminPage.pos.CreateUrlEditsPage.clickOnCreateUrlButton();
                await adminPage.pos.CreateUrlPage.enterAndVerifyCreateUrlPage(urlName, pageconstId, pageName, pageType, urlCategory);
                await adminPage.pos.CreateUrlPage.clickOnSubmitButton();
                await adminPage.pos.CreateUrlPage.verifyUrlCreated();
            })

            await test.step(`Verify created url and copy page const id`, async () => {
                urlConstId = await adminPage.pos.CreateUrlEditsPage.verifyCreatedUrl(urlName);
                console.log(urlConstId + "urlconstId");
                await adminPage.pos.CreateUrlEditsPage.verifyUrlConstIdCopyToClipboard(urlConstId);
            });

            await test.step(`Store Growth id's for cleanup`, async () => {

                growthTest.growthContent.push({
                    widgetId: [widgetNavBarconstId, widgetCBSEconstId, widgetTextCarousalconstId, widgetLinksconstId, widgetTestKnowledgeconstId], // Store multiple widget IDs in an array
                    pageId: pageconstId,
                    urlName: urlName,
                    teacherId: testData.teacher.id

                });
            })

            await test.step(`Verify enable critia for url in internal console`, async () => {
                await adminPage.pos.CreateUrlEditsPage.verifyInternalUserCriteria(urlName, pageconstId);
                await adminPage.pos.CreateUrlEditsPage.verifyEnableCriteria(baseURL);
            });

            /* Login to the student side with mobile number */
            await test.step(`Login to the student side`, async () => {
                await studentPage.login();
                await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
            });

            await test.step(`Verify the eligible student is able to click on a navbar tab and navigate to the respective page`, async () => {
                await studentPage.goto(`${webUrl}${urlName}`);
                await slowExpect(studentPage).toHaveURL(`${webUrl}${urlName}`);

                await studentPage.pos.webMigrationPage.verifyNavBarTabAndNavigation();
                await expect(studentPage.pos.webMigrationPage.neetNavigationLink, 'Verify neet navigation link is visible').toBeVisible();
                await studentPage.pos.webMigrationPage.neetNavigationLink.click();
                await expect(studentPage).toHaveURL(/.*neet*/);
                await studentPage.goBack();

                await studentPage.pos.webMigrationPage.verifyNavBarTabAndNavigation();
                await expect(studentPage.pos.webMigrationPage.jeeNavigationLink, 'Verify jee navigation link is visible').toBeVisible();
                await studentPage.pos.webMigrationPage.jeeNavigationLink.click();
                await expect(studentPage).toHaveURL(/.*jee*/);
                await studentPage.goBack();

                await studentPage.pos.webMigrationPage.verifyNavBarTabAndNavigation();
                await expect(studentPage.pos.webMigrationPage.pncfNavigationLink, 'Verify pncf navigation link is visible').toBeVisible();
                await studentPage.pos.webMigrationPage.pncfNavigationLink.click();
                await expect(studentPage).toHaveURL(/.*classes-6-10*/);
                await studentPage.goBack();
            });

            await test.step(`Verify the eligible student is able to see the SEO page as configured`, async () => {
                await studentPage.pos.webMigrationPage.verifySeoPage();
            });

            await test.step(`Verify student able to scroll to text carousels horizontally`, async () => {
                await studentPage.pos.webMigrationPage.verifyTextCarousel();
            });

            await test.step(`Verify student links windgets`, async () => {
                await studentPage.pos.webMigrationPage.verifyWidgetLinks();
            });

            await test.step(`Verify student quiz and their quiz score at the last page of the quiz`, async () => {
                await studentPage.pos.webMigrationPage.verifyQuizSection();
                await studentPage.pos.webMigrationPage.verifyQuizScore();
            });

        })
    }
})
