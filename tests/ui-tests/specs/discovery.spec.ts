import { test, expect, slowExpect, customExpect } from '../fixtures/ui-fixtures'
import { StringUtils, PlaywrightUtils } from '../../commons/common-functions';
import { EnvUtils } from '../../commons/env-utilities'

test.describe('Discovery UI Tests', {
  tag: ['@discovery', '@ui']
}, () => {
  const env = process.env.PROD === "1" ? "prod" : "stage";
  if (env == 'stage') {
    test('Verify login, onboarding, your profile,logout flow, forgot password flow & deleting account for freemium user', {
      tag: '@mobile'
    }, async ({ studentPage, isMobile }) => {
      test.setTimeout(180000);
      const studentMobileNumber = StringUtils.generateRandomMobileNumber();
      const new_mobile_number = StringUtils.generateRandomMobileNumber();
      const studentName = "user" + StringUtils.generateRandomString(10);
      const mailID = "auto" + StringUtils.generateRandomString(4) + StringUtils.generateRandomFourDigits() + StringUtils.generateRandomString(4) + "@gmail.com";
      const timezone = await PlaywrightUtils.getBrowserTimezone(studentPage);
      const currentDate = StringUtils.getCurrentTimeWithOffset('DD-MM-YYYY', 0, false, timezone);
      const pincode = "560035"
      const addressText = "kormangala";
      const nineDigitNumber = "*********";
      const numberStartswithFive = "**********";
      const newPassword = "Test@123";

      /** 
       * @info Test Naming convention updated and assertion message
       */
      await test.step(`Verify error message for invalid mobile number less than 5 digits and starting with 5`, async () => {
        await expect(studentPage.pos.preHomePage.loginButton, "Verify login button is visible").toBeVisible();
        await studentPage.pos.preHomePage.loginButton.click();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.click();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.fill(nineDigitNumber);
        await expect(studentPage.pos.preHomePage.loginWidget.sendOtpButton, "Verify send otp button is disabled").toBeDisabled();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.clear();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.fill(numberStartswithFive);
        await expect(studentPage.pos.preHomePage.enterValidNumberText, "Verify please enter a valid number error is visible").toBeVisible();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.clear();
      });

      /** 
       * @info Test Naming convention updated
       */
      await test.step(`Verify the "Edit" button functionality for changing the mobile number during the login process.`, async () => {
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.clear();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.click();
        await studentPage.pos.preHomePage.loginWidget.mobileNumberInput.fill(studentMobileNumber);
        await studentPage.pos.preHomePage.loginWidget.sendOtpButton.click();
        await expect(studentPage.pos.preHomePage.editNumberButton, "verify edit number button is visible").toBeVisible();
        await studentPage.pos.preHomePage.editNumberButton.click();
        await studentPage.pos.preHomePage.loginWidget.loginWithMobileNumber(studentMobileNumber, "1111");
      });

      /* Fill the required student data on onboarding page */
      await test.step(`Onboarding by checking the back button`, async () => {
        await expect(studentPage).toHaveURL(/.*onboarding/);
        // await studentPage.waitForLoadState('networkidle');
        await studentPage.waitForLoadState('domcontentloaded');

        if (!isMobile) {
          await expect(studentPage.pos.onboardingPage.onboardingImage, "verify onboarding image is visible").toBeVisible();
        }
        await expect(studentPage.pos.onboardingPage.onboardingFormCard, "verify onboarding form card is visible").toBeVisible();
        await expect(studentPage.pos.onboardingPage.onboardingChipGroup, "verify onboarding chip group is visible").toBeVisible();
        await studentPage.pos.onboardingPage.onBoardToNEET();
        await expect(studentPage.pos.onboardingPage.onboardingTextInput, "verify name input field is visible").toBeVisible();
        await expect(studentPage.pos.onboardingPage.onboardingBackButton, "verify go back button is visible").toBeVisible();
        await studentPage.pos.onboardingPage.onboardingBackButton.click();
        await expect(studentPage.pos.onboardingPage.letsStartYourNEETText, "verify first click of go back navigated to class selection is visible").toBeVisible();
        await expect(studentPage.pos.onboardingPage.onboardingBackButton, "verify go back button is visible").toBeVisible();
        await studentPage.pos.onboardingPage.onboardingBackButton.click();
        await studentPage.waitForLoadState('domcontentloaded');
        await expect(studentPage.pos.onboardingPage.examPreparingText, "verify second click of go back navigated to stream selection is visible").toBeVisible();
        await expect(studentPage.pos.onboardingPage.onboardingFormCard, "verify onboarding form card is visible").toBeVisible();
        await expect(studentPage.pos.onboardingPage.onboardingChipGroup, "verify onboarding chip group is visible").toBeVisible();
        await studentPage.pos.onboardingPage.onBoardToNEET();
        await studentPage.pos.onboardingPage.onboardingTextInput.fill(studentName);
        await expect(studentPage.pos.onboardingPage.studentNameSubmitButton, 'Check submit butten is visible').toBeVisible();
        await studentPage.pos.onboardingPage.studentNameSubmitButton.click();
      });

      /* Verify your profile page for freemium user */
      await test.step(` Verify your profile page for freemium user`, async () => {
        await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();

        if (isMobile) {
          await studentPage.pos.homePage.userNameMenu.click();
        }
        else {
          await studentPage.pos.homePage.userNameMenu.hover();
        }

        await studentPage.waitForTimeout(1000); //wait is requried due to websocket

        if (isMobile) {
          await studentPage.pos.homePage.verifyProfileMenuTitleFields();
        }

        await expect(studentPage.pos.homePage.profilePageNavigationButton, "profile page navigation button is visible").toBeVisible();
        await studentPage.pos.homePage.profilePageNavigationButton.click();
        await studentPage.waitForTimeout(1000); //wait is requried due to websocket

        if (isMobile) {
          await slowExpect(studentPage.pos.profilePage.yourProfileText, 'verify your profile text is visible').toBeVisible();
        }

        else {
          await studentPage.pos.profilePage.verifyProfilePageTitleFields();
        }

        await test.step(`Verify change stream flow`, async () => {
          await studentPage.pos.profilePage.verifyChangeStream("NEET", "12th");
        })
        await test.step(`Verify edit personal details`, async () => {
          await studentPage.pos.profilePage.verifyEditContactDetailInPersonalDetails(new_mobile_number, "1111", mailID);
          await studentPage.pos.profilePage.verifyEditStudentDetailInPersonalDetails("30-09-2024");
        })
        await test.step('Verify help and support', async () => {

          if (isMobile) {
            await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
            await studentPage.pos.homePage.userNameMenu.click();
            await expect(studentPage.pos.profilePage.helpAndSupportLink, 'Verify help and support link is visible').toBeVisible();
            await studentPage.pos.profilePage.helpAndSupportLink.click();
          }
          else {
            await studentPage.pos.profilePage.helpAndSupportText.click();
          }

          await studentPage.waitForTimeout(1000); //wait is requried due to websocket
          await expect(studentPage).toHaveURL(/.*view=help/);
          await studentPage.pos.profilePage.verifyHelpAndSupportTitles();
        })
        await test.step('Verify allen digital bot', async () => {
          if(env != 'stage'){
            await studentPage.pos.profilePage.verifyAllenDigitalBot();
          }
        })
        await test.step('Verify privacy and policy, tnc', async () => {
          if (isMobile) {
            await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
            await studentPage.pos.homePage.userNameMenu.click();
          }

          await expect(studentPage.pos.profilePage.privacyAndPolicyText, "verify privacy and policy link is visible").toBeVisible();
          await studentPage.pos.profilePage.privacyAndPolicyText.click();
          await studentPage.waitForTimeout(1000); //wait is requried due to websocket
          await expect(studentPage, "verify page navigated to privacy and policy page").toHaveURL(/.*privacy-policy/);
          await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
          await studentPage.pos.homePage.userNameMenu.hover();
          await expect(studentPage.pos.homePage.profilePageNavigationButton, "profile page navigation button is visible").toBeVisible();
          await studentPage.pos.homePage.profilePageNavigationButton.click();
          await slowExpect(studentPage.pos.profilePage.yourProfileText, 'verify your profile text is visible').toBeVisible();

          if (isMobile) {
            await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
            await studentPage.pos.homePage.userNameMenu.click();
          }

          await expect(studentPage.pos.profilePage.termsAndConditionsText, "verify TNC link is visible").toBeVisible();
          await studentPage.pos.profilePage.termsAndConditionsText.click();
          await expect(studentPage, "verify page navigated to privacy and policy page").toHaveURL(/.*tnc/);
        })
      });

      /* User logout flow and Forgot password flow  */
      await test.step(`Verify user logout flow and forgot password flow`, async () => {
        /* Forgot pass word flow */
        await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
        await studentPage.pos.homePage.userNameMenu.hover();
        await expect(studentPage.pos.homePage.settingsMenu, "verify settings menu button is visible").toBeVisible();
        await studentPage.pos.homePage.settingsMenu.click();
        await studentPage.waitForTimeout(1000); //wait is requried due to websocket
        await expect(studentPage).toHaveURL(/.*view=settings/);
        await expect(studentPage.pos.profilePage.accountSettingsSubTitleText, 'verifying "account settings title" should be visible').toBeVisible();
        await expect(studentPage.pos.profilePage.logoutButton, 'verifying "logout of all devices" text should be visible').toBeVisible();
        await studentPage.pos.profilePage.logoutButton.click();
        await studentPage.waitForTimeout(1000); //wait is requried due to websocket
        await expect(studentPage.pos.profilePage.logoutConfirmText, 'verify logout confirm text is visible').toBeVisible();
        await expect(studentPage.pos.profilePage.accountLogout, 'verify logout button is visible').toBeVisible();
        await studentPage.pos.profilePage.accountLogout.click();
        /* Forgot pass word flow */
        await studentPage.pos.preHomePage.forgotPasswordFlow(mailID, newPassword, "1111")
        await studentPage.waitForTimeout(2000); //wait is requried due to websocket
      });

      /* Delete the onboarded freemium user */
      await test.step('Verify delete user flow for freemium user', async () => {
        await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();

        if (isMobile) {
          await studentPage.pos.homePage.userNameMenu.click();
        }
        else {
          await studentPage.pos.homePage.userNameMenu.hover();
        }

        await studentPage.waitForTimeout(1000); //wait is requried due to websocket
        await expect(studentPage.pos.homePage.settingsMenu, "verify settings menu button is visible").toBeVisible();
        await studentPage.pos.homePage.settingsMenu.click();
        await studentPage.waitForTimeout(1000); //wait is requried due to websocket
        await expect(studentPage).toHaveURL(/.*view=settings/);
        await expect(studentPage.pos.profilePage.accountSettingsSubTitleText, 'verifying "account settings title" should be visible').toBeVisible();
        await expect(studentPage.pos.profilePage.logoutButton, 'verifying "logout of all devices" text should be visible').toBeVisible();
        await expect(studentPage.pos.profilePage.deleteAccountButton, 'verifying "delete account" text should be visible').toBeVisible();
        await studentPage.pos.profilePage.deleteAccountButton.click();
        await expect(studentPage.pos.profilePage.deleteAccountConfirmationText, 'verifying "delete account confirmation" text should be visible').toBeVisible();
        await expect(studentPage.pos.profilePage.deletedButton, 'verifying delete button should be visible').toBeVisible();
        await studentPage.pos.profilePage.deletedButton.click();
        await expect(studentPage.pos.preHomePage.loginButton, "verify login button is visible").toBeVisible();
      });

    });
  }
  test('Verify subjects under continue learning', async ({ studentPage, testData }) => {
    const data =
    {
      JEE: {
        Chemistry: ["Physical Chemistry", "Inorganic Chemistry", "Organic Chemistry"],
        Maths: "",
        Physics: ""
      },
      NEET: {
        Biology: "",
        Chemistry: "",
        Physics: ""
      }
    };
    const jeeKeys = Object.keys(data.JEE);
    const chemistrySubject = jeeKeys.find(key => key === "Chemistry");


    /* Login to the student side with mobile number */
    await test.step(`Login to the student side`, async () => {
      await studentPage.login();
      await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();

    });

    /* Verify Maths, chemistry and physics subjects under continue learning once student landed on post home page after login */
    await test.step(`Verify subjects under explore study materials`, async () => {
      if (await studentPage.pos.homePage.neetSubjectsFilter.isVisible()) {
        await studentPage.pos.homePage.neetSubjectsFilter.click();
        await expect(studentPage.pos.homePage.jeeadvancedSubjectsFilter, "verify jee advanced filter is visible").toBeVisible();
        await studentPage.pos.homePage.jeeadvancedSubjectsFilter.click();
        await expect(studentPage.pos.homePage.saveFilter, "verify Save filter is visible").toBeVisible();
        await studentPage.pos.homePage.saveFilter.click();
        await studentPage.pos.homePage.listOfSubjectsUnderContinueLearning(data.JEE);
      } else {
        await studentPage.pos.homePage.listOfSubjectsUnderContinueLearning(data.JEE);
      }
    });
  });

  test('Verify subject details, super topics and sub topics', async ({ studentPage, testData }) => {

    const data =
    {
      JEE: {
        Chemistry: ["Physical Chemistry", "Inorganic Chemistry", "Organic Chemistry"],
        Maths: "",
        Physics: ""
      },
      NEET: {
        Chemistry: "",
        Biology: "",
        Physics: ""
      }
    };
    const jeeKeys = Object.keys(data.JEE);
    const chemistrySubject = jeeKeys.find(key => key === "Chemistry");
    /* Login to the student side with mobile number */
    await test.step(`Login to the student side`, async () => {
      await studentPage.login();
      await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();

    });

    /* validating supertopics */
    await test.step(`Validating supertopics`, async () => {
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.subjectDetailsPage.checkAndClickSubjectUnderContinueLearning(chemistrySubject!);
     // await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await studentPage.pos.subjectDetailsPage.checkForSubjectModuleAndSupertopicsCount();
    });

    /* validating subtopics */
    await test.step(`Validating subtopics`, async () => {
      await studentPage.pos.subjectDetailsPage.checkForSubjectSubtopicsCount();
    });
  });

  test('Verify navigation tabs and profile menus for enrolled Student', async ({ studentPage, testData }) => {

    /* Login to the student side with mobile number */
    await test.step(`Login to the student side`, async () => {
      await studentPage.login();
      await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();

    });

    /* Validate Explore, Doubts And tests tabs in home page */
    await test.step('Validate explore, doubts and tests tabs in home page', async () => {
      await slowExpect(studentPage.pos.homePage.doubtsMenu, 'Check doubts menu is visible').toBeVisible();
      await studentPage.pos.homePage.doubtsMenu.click();
      await expect(studentPage).toHaveURL(/.*doubts/);
      await expect(studentPage.pos.homePage.testsMenu, 'Check Tests menu is visible').toBeVisible();
      await studentPage.pos.homePage.testsMenu.click();
      await expect(studentPage).toHaveURL(/.*test*/);
      await expect(studentPage.pos.homePage.exploreMenu, 'Check explore menu is visible').toBeVisible();
      await studentPage.pos.homePage.exploreMenu.click();
      await expect(studentPage).toHaveURL(/.*explore/);
    });

    /* Validate all items in profile dropdown menu  */
    await test.step('Validate all items in profile dropdown menu', async () => {
      await slowExpect(studentPage.pos.homePage.notificationButton, 'Verify notification button is visble').toBeVisible();
      await expect(studentPage.pos.preHomePage.sectionLoader, 'Verify section loader is hidden').toBeHidden();
      await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
      await studentPage.pos.homePage.userNameMenu.dblclick();
      await studentPage.pos.homePage.verifyProfileNavigationMenu(testData.student.name);
    });

    await test.step('Open noticeboard menu from profile dropdown', async () => {
      await studentPage.pos.homePage.noticeboardMenu.click();
      await expect(studentPage).toHaveURL(/.*view=noticeboard/);
    });
    await test.step('Open orderDetails menu from profile dropdown', async () => {
      await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
      await studentPage.pos.homePage.userNameMenu.hover();
      await studentPage.pos.homePage.verifyProfileNavigationMenu(testData.student.name);
      await expect(studentPage.pos.homePage.orderDetailsMenu, 'Check order details menu is visible').toBeVisible();
      await studentPage.pos.homePage.orderDetailsMenu.click();
      await expect(studentPage).toHaveURL(/.*view=order/);
    });
    await test.step('Open help and support menu from profile dropdown', async () => {
      await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
      await studentPage.pos.homePage.userNameMenu.hover();
      await studentPage.pos.homePage.verifyProfileNavigationMenu(testData.student.name);
      await expect(studentPage.pos.homePage.helpAndsupportMenu, 'Check helpAndsupportMenu menu is visible').toBeVisible();
      await studentPage.pos.homePage.helpAndsupportMenu.click();
      await expect(studentPage).toHaveURL(/.*view=help/);
    });
    
    await test.step('Open settings menu from profile dropdown', async () => {
      await expect(studentPage.pos.homePage.userNameMenu, 'Check user name is visible').toBeVisible();
      await studentPage.pos.homePage.userNameMenu.hover();
      await studentPage.pos.homePage.verifyProfileNavigationMenu(testData.student.name);
      await expect(studentPage.pos.homePage.settingsMenu, 'Check settingsMenu menu is visible').toBeVisible();
      await studentPage.pos.homePage.settingsMenu.click();
      await expect(studentPage).toHaveURL(/.*view=settings/);
    });

  });

  test('Verify profile page for enrolled student', async ({ studentPage, testData }) => {
    test.setTimeout(90000);
    /* Login to the student side with mobile number */
    await test.step(`Login to the student side`, async () => {
      await studentPage.login();
      await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();

    });

    /* Navigate to the profile page from post home page */
    await test.step(`Navigate to profile page from post home page`, async () => {
      await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
      await studentPage.pos.homePage.userNameMenu.hover();
      await expect(studentPage.pos.homePage.profilePageNavigationButton, "profile page navigation button is visible").toBeVisible();
      await studentPage.pos.homePage.profilePageNavigationButton.click();
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.profilePageNavigationButton, "profile page navigation button should not visible").toBeHidden();
      await slowExpect(studentPage.pos.profilePage.contactDetailsSubTitle, 'verify profile page is loaded').toBeVisible();
    });

    /* Validate the presence of header, sidebar items and chat icon */
    await test.step(`Validate the presence of header, sidebar items and chat icon`, async () => {
      await studentPage.pos.profilePage.verifyProfilePageTitleFields();
    });
    await test.step(`Validate profile from sidebar`, async () => {
      await expect(studentPage.pos.profilePage.enrolledStudentText.first(), "verify enrolled text is visible").toBeVisible();
      await studentPage.pos.profilePage.verifySubFieldsUnderPersonalDetails(testData.student.name, testData.student.phone);
    });

    await test.step(`Validate noticeboard from sidebar`, async () => {
      await studentPage.pos.profilePage.noticeBoardText.click();
      await slowExpect(studentPage).toHaveURL(/.*noticeboard/);
    });

    await test.step(`Validate order details from sidebar`, async () => {
      await studentPage.pos.profilePage.orderDetailsText.click();
      await slowExpect(studentPage).toHaveURL(/.*view=order/);
    });

    await test.step(`Validate help and support from sidebar`, async () => {
      await studentPage.pos.profilePage.verifySubFieldsUnderHelpAndSupport();
    });

    await test.step(`Validate settings from sidebar`, async () => {
      await studentPage.pos.profilePage.verifySubFieldsUnderSettings();
    });

  });
  /** 
   * @info updated assertion message
  */
  test('Verify the course details page for freemium user', {
    tag: '@mobile'
  }, async ({ studentPage, isMobile }) => {
    const studentMobileNumber = "6333399006";
    const prodUer = "8840425863";
    const course_details =
    {
      NEET: {
        class_11: [
          {
            program_header: "NEET programs for",
            course_card_title: "Multi Course NEET 11",
            program_duration: "1 year programs",
            course_name: "JEE",
            course_language: "Hindi + English",
            course_standard: "Class 11",
            course_duration: "1 Year | 2024-2025"
          }
        ]
        ,
        class_12: {
          class_name: "Starting Class 11"
        }
      }
    };
    const neetCourseDetailKeys = Object.keys(course_details);
    const NEETCourse = neetCourseDetailKeys.find(key => key === "NEET");

    if (env == 'stage') {
      /* Login with mobile number */
      await test.step(`Login with mobile number`, async () => {
        await studentPage.pos.preHomePage.loginWithMobileNumber(studentMobileNumber);
        await studentPage.waitForLoadState('networkidle');
        await studentPage.waitForTimeout(2000); //wait is requried until get to handle reload
      });
    } else {
      /* Login username and password using for prod login */
      await test.step(`Login with username and password`, async () => {
        await studentPage.pos.preHomePage.loginWithUsername(prodUer, prodUer);
        await studentPage.waitForLoadState('networkidle');
        await studentPage.waitForTimeout(2000); //wait is requried until get to handle reload
      });
    }

    /* Verify profile page details and click on explore courses under order details */
    await test.step('Verify profile page details and click on explore courses under order details', async () => {
      await slowExpect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();

      if (isMobile) {
        await studentPage.pos.homePage.userNameMenu.click();
      }
      else {
        await studentPage.pos.homePage.userNameMenu.hover();
      }

      await expect(studentPage.pos.homePage.orderDetailsButton, 'Verify order details button is visible').toBeVisible();
      await studentPage.pos.homePage.orderDetailsButton.click();
      await expect(studentPage).toHaveURL(/.*view=order/);
      await slowExpect(studentPage.pos.profilePage.exploreCoursesButton, 'Verifying "explore button" should be visible').toBeVisible();
      await studentPage.pos.profilePage.exploreCoursesButton.click();
    });

    /* Verify and click on the course name to navigate to the available course details page */
    await test.step(`navigate to course course details page by clicking course card`, async () => {
      await expect(studentPage).toHaveURL(/.*courses/);
      await slowExpect(studentPage.pos.coursePage.programHeadingText(course_details.NEET.class_11[0].program_header), "Verify program heading text is visible").toBeVisible();
      await expect(studentPage.pos.coursePage.programDurationText(course_details.NEET.class_11[0].program_duration), "Verify program duration text is visible").toBeVisible();
      await studentPage.pos.coursePage.verifyCourseCardDetails();
      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
    });

    /* Verify course details page */
    await test.step(`Verify the fields in course Details Page`, async () => {
      await expect(studentPage).toHaveURL(/.*course-details*/);
      await studentPage.pos.courseDetailsPage.verifyCourseDetails();
      await studentPage.pos.courseDetailsPage.selectBatchAndEnroll();
    });
  });

  test('Verify class recordings, notes and practise sections under subject', async ({ studentPage, testData }) => {
    const data =
    {
      JEE: {
        Chemistry: ["Physical Chemistry", "Inorganic Chemistry", "Organic Chemistry"],
        Maths: "",
        Physics: ""
      },
      NEET: {
        Chemistry: "",
        Biology: "",
        Physics: ""
      }
    };
    const jeeKeys = Object.keys(data.JEE);
    const chemistrySubject = jeeKeys.find(key => key === "Chemistry");


    /* Login to the student side with mobile number */
    await test.step(`Login to the student side`, async () => {
      await studentPage.login();
      await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
    });

    /* Navigate to the subject post login */
    await test.step(`Navigate to the subject from home page`, async () => {
      await slowExpect(studentPage.pos.homePage.subjectText(chemistrySubject!).first(), "subject should be available under continue learning").toBeVisible();
      await studentPage.pos.homePage.subjectText(chemistrySubject!).first().click();
    });

    /* verify class recordings, Notes and Practise under subject */
    await test.step(`Verify class recordings, Notes and Practise under subject`, async () => {
      await slowExpect(studentPage.pos.subjectDetailsPage.listOfSuperTopics.first(), "verify the super topic is visible").toBeVisible();
      if (await studentPage.pos.subjectDetailsPage.listOfSubTopics.first().isHidden()) {
        await studentPage.pos.subjectDetailsPage.listOfSuperTopics.first().click();
        await studentPage.waitForLoadState('domcontentloaded');
      }
      await expect(studentPage.pos.subjectDetailsPage.listOfSubTopics.first(), "verify the subtopics are visible").toBeVisible();
      await studentPage.pos.subjectDetailsPage.listOfSubTopics.first().click();
      await studentPage.waitForLoadState('domcontentloaded');
      await studentPage.pos.subjectDetailsPage.validateClassesNotesAndPracticeUnderSubTopic();
    });
  });

  test('Verify study planner card under quick actions', async ({ studentPage, testData }) => {
    const currentDate = new Date();
    const WeekDate = new Date(currentDate);
    const nextWeek = WeekDate.setDate(WeekDate.getDate() + 7);
    const locale = 'en-US';
    const currentMonthName = new Intl.DateTimeFormat(locale, { month: 'long' }).format(currentDate);
    const nextWeekDateString = new Intl.DateTimeFormat(locale, { day: 'numeric' }).format(nextWeek);
    let currentDay = currentDate.getDate().toString();

    /* Login to the student side with mobile number */
    await test.step(`Login to the student side`, async () => {
      await studentPage.login();
      await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();

    });

    /* Navigate to the study planner post login */
    await test.step(`Navigate to the study planner from home page under quick actions`, async () => {
      await slowExpect(studentPage.pos.homePage.quickActionsTitleText, "verify quick actions title is visible").toBeVisible();
      await expect(studentPage.pos.homePage.studyPlannerTitleText, "verify study planner title is visible").toBeVisible();
      await studentPage.pos.homePage.studyPlannerTitleText.click();
    });

    /* verify calender dates and switching between dates */
    await test.step(`Verify calender dates and switching between dates inside of study planner`, async () => {
      await expect(studentPage.pos.calendarPage.currentMonthText(currentMonthName), 'verify calendar text is visible').toBeVisible();
      if (env == 'stage') {
        await slowExpect(studentPage.pos.calendarPage.calendarText, 'verify calendar text is visible').toBeVisible();
      }
      await expect(studentPage.pos.calendarPage.nextWeekButton, 'verify next week button text is visible').toBeVisible();
      await expect(studentPage.pos.calendarPage.weekText, 'verify next week text is visible').toBeVisible();
      await expect(studentPage.pos.calendarPage.dateText(currentDay), 'verify next week text is visible').toBeVisible();
      await studentPage.pos.calendarPage.nextWeekButton.click();
      await slowExpect(studentPage.pos.calendarPage.dateText(nextWeekDateString), 'verify next week date is visible').toBeVisible();
    });
  });

  test('Verify login flow with email and password', async ({ studentPage, testData }) => {
    const invalidPassword = "auto@1";
    await test.step(`Login with invalid credentials`, async () => {
      await studentPage.pos.preHomePage.loginWithUsername(testData.student.email, invalidPassword);
      await slowExpect(studentPage.pos.preHomePage.invalidLoginText, 'verify invalid login credentials text is visible').toBeVisible();
    });
    // await test.step(`verify login information at login page`, async () => {
    //   await slowExpect(studentPage.pos.preHomePage.iSymbolIcon, 'verify i symbol icon is visible').toBeVisible();
    //   await studentPage.pos.preHomePage.iSymbolIcon.click();
    //   await expect(studentPage.pos.preHomePage.usernameInfoText, 'verify username info text is visible').toBeVisible();
    //   await expect(studentPage.pos.preHomePage.userInfoDetails, 'verify username detail text is visible').toBeVisible();
    //   await expect(studentPage.pos.preHomePage.gotItButton, 'verify got it button is visible').toBeVisible();
    //   await studentPage.pos.preHomePage.gotItButton.click();

    // });
    await test.step(`Login with valid credentials`, async () => {
      await expect(studentPage.pos.preHomePage.loginWidget.passwordInput, "verify password input is visible").toBeVisible();
      await studentPage.pos.preHomePage.loginWidget.passwordInput.clear();
      await studentPage.pos.preHomePage.loginWidget.passwordInput.fill(testData.student.phone);
      await expect(studentPage.pos.preHomePage.loginWidget.loginButton, "verify login button is visible").toBeVisible();
      await studentPage.pos.preHomePage.loginWidget.loginButton.click();
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
    });
  });

  /* This scenario will cover in Static page validations
  // test('Verify user able to purchase any OLTS program', async ({ studentPage }) => {
  //   const studentMobileNumber = StringUtils.generateRandomMobileNumber();
  //   const studentName = "user" + StringUtils.generateRandomString(10);
  //   const randomString = StringUtils.generateRandomString(10);
  //   const streamName = "NEET";
  //   const className = "11";
  //   const courseEnroll = "Online test series";
  //   const timezone = await PlaywrightUtils.getBrowserTimezone(studentPage);
  //   const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
  //   const mailID = "Automation" + StringUtils.generateRandomString(3) + "@gmail.com";
  //   const marksheet = 'test-data/marksheet.png';
  //   const city = 'Bangalore';
  //   const pincode = '560035';
  //   const waitTime = 20000;
   
  //   /* Login with mobile number */
  //   await test.step(`Login with mobile number`, async () => {
  //     await studentPage.pos.preHomePage.loginWithMobileNumber(studentMobileNumber);
  //   });

  //   /* Fill the required student data on onboarding page */
  //   await test.step(`Fill the required student data on onboarding page`, async () => {
  //     await expect(studentPage).toHaveURL(/.*onboarding/);
  //     await studentPage.waitForLoadState('networkidle');
  //     await studentPage.pos.onboardingPage.onBoardToJEE();
  //     await studentPage.pos.onboardingPage.onboardingTextInput.fill(studentName);
  //     await expect(studentPage.pos.onboardingPage.studentNameSubmitButton, 'Check submit butten is visible').toBeVisible();
  //     await studentPage.pos.onboardingPage.studentNameSubmitButton.click();
  //   });

  //   /* Navigate to online-test-series page and click on course*/
  //   await test.step('Navigate to online test series page and click on course', async () => {
  //     await expect(studentPage.pos.homePage.userName, 'Check user name is visible').toBeVisible();
  //     await expect(studentPage.pos.homePage.testSeriesLink, 'verify test series link is visible').toBeVisible();
  //     await studentPage.pos.homePage.testSeriesLink.click();
  //     await expect(studentPage).toHaveURL(/.*online-test-series/);
  //     await studentPage.pos.onlineTestSeriesPage.verifyOltsPageAndClickOnStream(streamName);
  //   });

  //   /* Navigate to enrollment by clicking class based course */
  //   await test.step('Navigate to enrollment by clicking class based course', async () => {
  //     await studentPage.pos.neetOltsPage.verifyCourseNameAndClickToEnroll(streamName, className, courseEnroll);
  //   });

  //   /* Verify course page details */
  //   await test.step('Verify course page details', async () => {
  //     await expect(studentPage).toHaveURL(/.*course-details*/);
  //     await studentPage.pos.courseDetailsPage.verifyCourseDetails();
  //     await expect(studentPage.pos.coursePage.StartingDateRadioButton, 'verifying "Select Starting Date" radio button should be visible').toBeVisible();
  //     await studentPage.pos.coursePage.StartingDateRadioButton.check();
  //     await expect(studentPage.pos.coursePage.EnrollNowButton, 'verifying "Enroll Now" button should be visible').toBeVisible();
  //     await studentPage.pos.coursePage.EnrollNowButton.click();
  //   });

  //   /* Verify and fill checkout page details */
  //   await test.step('Verify and fill checkout page details', async () => {
  //     await expect(studentPage).toHaveURL(/.*checkout*/);
  //     await studentPage.pos.checkoutPage.EmailIDInputBox.fill(mailID);
  //     await studentPage.pos.checkoutPage.ParentName.click();
  //     await studentPage.pos.checkoutPage.ParentName.fill(randomString);
  //     await studentPage.pos.checkoutPage.Pincode.fill(pincode);
  //     //field removed
  //     // await studentPage.pos.checkoutPage.cityName.fill(city);
  //     await studentPage.pos.checkoutPage.ContinueButton.click();
  //   });

  //   /* ReVerify checkout page final details before move to payment page */
  //   await test.step('Re-verify checkout page details at review order page', async () => {
  // await expect(studentPage.pos.checkoutPage.selectPaymentMethod, 'verifying "select payment method" text should be visible').toBeVisible();
  // await expect(studentPage.pos.checkoutPage.paymentModeFull, 'verifying "payment Mode Full" button should be visible').toBeVisible();
  // await expect(studentPage.pos.checkoutPage.paymentModeLoan, 'verifying "payment Mode Loan" button should be visible').toBeVisible();
  // await studentPage.pos.checkoutPage.paymentModeFull.click();
  // await expect(studentPage.pos.checkoutPage.ContinueToPayButton, 'verifying "Continue to pay" button should be visible').toBeVisible();
  // await expect(studentPage.pos.checkoutPage.ContinueToPayButton, 'verifying "Continue to pay" button should be enabled').toBeEnabled();
  //     await studentPage.pos.checkoutPage.ContinueToPayButton.click();
  //   });

  //   /* Verify payment page details */
  //   await test.step('Verify payment page details', async () => {
  //     await expect(studentPage).toHaveURL(/.*payment-page*/);
  //     await studentPage.pos.paymentPage.paymentSuccess();
  //     await customExpect(waitTime)(studentPage.pos.paymentPage.PaymentSuccessful, 'verifying "PAYMENT SUCCESSFUL" text should be visible').toBeVisible();
  //     await customExpect(waitTime)(studentPage.pos.paymentPage.SetupYourAccount, 'verifying "Setup Your Account" text should be visible').toBeVisible();
  //   });

  //   /* Verify Account page details */
  //   await test.step('Verify account page details', async () => {
  //     await studentPage.pos.accountSetupPage.validateAndFillStudentAndEmergencyContactDetails(currentDate, studentMobileNumber);
  //     await studentPage.pos.accountSetupPage.verifyAndFillAddressDetailsOnAccountSetupPage(city, pincode);
  //     await studentPage.pos.accountSetupPage.verifyAndUploadMarksheet(marksheet);
  //     await slowExpect(studentPage.pos.homePage.ContinueLearningText.first(), 'verifying "Continue Learning" text should be visible').toBeVisible();
  //   });
  // });
  //*** form validations are commenting due to new enhancement basic details and review order details page merged as one page -Script changes are required */


  test('Verify form validations', {
    tag: '@mobile'
  }, async ({ studentPage, testData, isMobile }) => {
    test.setTimeout(90000);
    const studentMobileNumber = StringUtils.generateRandomMobileNumber();
    const nameStartsWithDot = "." + StringUtils.generateRandomString(3);
    const nameStartsWithSpecialCharacter = "@" + StringUtils.generateRandomString(3);
    const nameStartsWithNumber = "1" + StringUtils.generateRandomString(3);
    const twoSpacesBetweenNameToName = StringUtils.generateRandomString(3) + "  " + StringUtils.generateRandomString(3);
    const doubleDotWithSpacesBetweenNameToName = StringUtils.generateRandomString(3) + ".. " + StringUtils.generateRandomString(3);
    const spaceAtEndOfName = StringUtils.generateRandomString(3) + " ";
    const doubleDotAtEndOfName = StringUtils.generateRandomString(3) + "..";
    const studentName = "user name " + StringUtils.generateRandomString(3);
    const fiveDigitPincode = "56000"
    const parentName = "parent name " + StringUtils.generateRandomString(3);
    const addressLineWithSpecialCharacter = "@"
    const marksheetWithSixMb = "test-data/SampleFile.pdf";
    const randomString = StringUtils.generateRandomString(10);
    const timezone = await PlaywrightUtils.getBrowserTimezone(studentPage);
    const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
    const existingEmail = "<EMAIL>";
    const mailID = "auto" + StringUtils.generateRandomString(4) + StringUtils.generateRandomFourDigits() + StringUtils.generateRandomString(4) + "@gmail.com";
    const marksheet = 'test-data/marksheet.png';
    const city = 'Bangalore';
    const pincode = '560035';
    const MultipleCity_pincode = '401101';
    const waitTime = 20000;
    let languageSelectedValue;
    let courseStartDateSelected;
    let courseStandard;
    let courseDuration;

    /* Login with mobile number */
    await test.step(`Enrolling without onboarding with course details page validations`, async () => {
      await test.step(`Navigate to course details page from pre-home page`, async () => {
        await studentPage.reload(); // added this to handle page load

        if (EnvUtils.getInstance().isProd()) {
          await studentPage.goto('neet/online-coaching-class-11');
        }

        if (EnvUtils.getInstance().isStage()) {
          await slowExpect(studentPage.pos.preHomePage.neetButton, "verify Neet Course Button is Visible").toBeVisible();
          await studentPage.pos.preHomePage.neetButton.click();
          await slowExpect(studentPage.pos.preHomePage.selectYourStreamTitle, "verify select your stream title is visible").toBeVisible();
          await slowExpect(studentPage.pos.preHomePage.selectYourClassTitle, "verify select your class title is visible").toBeVisible();
          await slowExpect(studentPage.pos.preHomePage.class11Th, "verify select class 11th title is visible").toBeVisible();
          await studentPage.pos.preHomePage.class11Th.click();
          await expect(studentPage.pos.preHomePage.exploreCourses, "verify explore courses is visible").toBeVisible();
          await expect(studentPage.pos.preHomePage.exploreCourses, "verify explore courses is visible").toBeEnabled();
          await studentPage.pos.preHomePage.exploreCourses.click();
        }

        // checking with neet 12 course
        await expect(studentPage).toHaveURL(/.*online-coaching-class-11/);
        await slowExpect(studentPage.pos.courseDetailsPage.liveProgramsTitle, "verify live Programs Title is visible").toBeVisible();
        await expect(studentPage.pos.courseDetailsPage.nurtureOnlineProgram, "verify nurture Online Program card is visible").toBeVisible();
        await studentPage.pos.courseDetailsPage.nurtureOnlineProgram.click();
        await expect(studentPage).toHaveURL(/.*nurture-online-programs-class-11/);

        // await slowExpect(studentPage.pos.preHomePage.class11Link, "verify class 11 link is visible").toBeVisible();
        // await studentPage.pos.preHomePage.class11Link.click();
        // await expect(studentPage).toHaveURL(/.*course-details*/); //url is frequently changing in stage
      })
      await test.step(`Verify course details page`, async () => {
        await studentPage.pos.courseDetailsPage.verifyCourseDetails(); // validating course details page titles

        if (isMobile) {
          if (EnvUtils.getInstance().isStage()) {
            await studentPage.pos.preHomePage.loginWidget.loginWithMobileNumber(studentMobileNumber, '1111');
          } else {
            await studentPage.pos.preHomePage.loginWidget.loginWithUsername(testData.student.phone, testData.student.phone);
            await studentPage.waitForTimeout(2000); //wait is requried due to websocket
          }
          await expect(studentPage.pos.courseDetailsPage.selectBatchAndEnrollButton, "Verify select batch and enroll button is visible").toBeVisible()
          await studentPage.pos.courseDetailsPage.selectBatchAndEnrollButton.click();
        }

        await studentPage.pos.courseDetailsPage.selectBatchAndEnroll();
        // courseStandard = await studentPage.pos.courseDetailsPage.courseStandardValue.textContent();//Commenting temporarily due to test-id removed //storing the course standard value to validate in basic details page
        // courseDuration = await studentPage.pos.courseDetailsPage.courseDurationValue.textContent();//Commenting temporarily due to test-id removed //storing the course standard value to validate in basic details page
        // let courseLanguage = await studentPage.pos.courseDetailsPage.courseLanguageValue.textContent(); //language feature removed
        // await expect(studentPage.pos.courseDetailsPage.languageContainer.nth(0), "verify course title under language and preference language is same in course details page").toContainText(courseLanguage!); // language feature removed
        await expect(studentPage.pos.courseDetailsPage.languagePrefrenceValue.nth(0), "verify language preference is visible").toBeVisible();
        languageSelectedValue = await studentPage.pos.courseDetailsPage.languagePrefrenceValue.nth(0).textContent();
        courseStartDateSelected = await studentPage.pos.courseDetailsPage.courseStartDateButton.textContent();
        let multipleLanguageCount = await studentPage.pos.courseDetailsPage.languageContainer.count();
        if (multipleLanguageCount > 1) {
          await expect(studentPage.pos.courseDetailsPage.languageContainer.nth(1), "verify second language preference is visible").toBeVisible();
          await studentPage.pos.courseDetailsPage.languageContainer.nth(1).click();
          await expect(studentPage.pos.courseDetailsPage.courseStartDateButton, "verify course start date button is visible").toBeVisible();
        }

        if (!isMobile) {
          await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "verify enroll now button is enabled").toBeEnabled();
          await studentPage.pos.courseDetailsPage.enrollNowButton.click();
          if (EnvUtils.getInstance().isStage()) {
            await studentPage.pos.preHomePage.loginWidget.loginWithMobileNumber(studentMobileNumber, '1111');
          } else {
            await studentPage.pos.preHomePage.loginWidget.loginWithUsername(testData.student.phone, testData.student.phone);
            await studentPage.waitForTimeout(2000); //wait is requried due to websocket
          }
        }

        await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "verify enroll now button is visible").toBeVisible();
        await expect(studentPage.pos.courseDetailsPage.courseStartDateButton, "verify course start date button is visible").toBeVisible();
        await studentPage.pos.courseDetailsPage.courseStartDateButton.click();
        await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "verify enroll now button is enabled").toBeEnabled();
        await studentPage.pos.courseDetailsPage.enrollNowButton.click();
        await slowExpect(studentPage).toHaveURL(/.*checkout*/);
      })
    });

    /* Validate fields at student details section in checkout page */
    await test.step('Validate fields at student details section in checkout page', async () => {
      await slowExpect(studentPage.pos.checkoutPage.oneYearText, "verify one year course duration text is visible").toBeVisible();
      if (env == 'stage') {
        /* validating name field */
        await test.step('Validate name field at student details section', async () => {
          await expect(studentPage.pos.checkoutPage.FullNameInputBox, "verify full name input box is empty in student details page").toBeEmpty();
          await expect(studentPage.pos.checkoutPage.FullNameInputBox, "verify full name input box is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(nameStartsWithDot);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "name starts with dot - invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(nameStartsWithSpecialCharacter);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "name starts with special character - invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(nameStartsWithNumber);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "name starts with number - Invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(twoSpacesBetweenNameToName);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "two Spaces Between NameToName - Invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(doubleDotWithSpacesBetweenNameToName);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "double Dot With Spaces Between NameToName - Invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(spaceAtEndOfName);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "space At End Of Name - Invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(doubleDotAtEndOfName);
          await expect(studentPage.pos.checkoutPage.invalidUserNameText, "double Dot At End Of Name - Invalid name is visible").toBeVisible();
          await studentPage.pos.checkoutPage.FullNameInputBox.clear();
          await studentPage.pos.checkoutPage.FullNameInputBox.fill(studentName);
        })
      }
      /* selecting gender and verifying phone number */
      await test.step('Validate phone number is auto-populated, gender selection at student details section', async () => {
        await expect(studentPage.pos.checkoutPage.PhoneNumberInputBox, 'onboarded phone number is auto-populated').toHaveValue(new RegExp(`^(${studentMobileNumber}|${testData.student.phone})$`));
        if (env == 'stage') {
          await expect(studentPage.pos.checkoutPage.ContinueButton, 'verifying "Continue" button should be disabled first at student details').toBeDisabled(); // the pre-set of test data we are creating with all mandatory fields and hence continue button is pre-enabled in prod
        }
        await expect(studentPage.pos.checkoutPage.Gender, 'verifying "Gender" text should be visible').toBeVisible();
        await expect(studentPage.pos.checkoutPage.GenderMale, 'verifying "Male" text should be visible').toBeVisible();
        await studentPage.pos.checkoutPage.GenderMale.click();
      })

      if (env == 'stage') {
        /* verify and filling dob and email fields */
        await test.step('Validate and fill dob and email fields at student details section', async () => {
          await expect(studentPage.pos.checkoutPage.DateOfBirth, 'verifying "Date Of Birth" text should be visible').toBeVisible();
          await studentPage.pos.checkoutPage.DateOfBirthInputBox.fill(currentDate);
          await expect(studentPage.pos.checkoutPage.EmailIDInputBox, 'verify email id field is visible').toBeVisible();
          await studentPage.pos.checkoutPage.EmailIDInputBox.click();
          await studentPage.pos.checkoutPage.EmailIDInputBox.fill(existingEmail);
          await expect(studentPage.pos.checkoutPage.ContinueButton, 'verifying "Continue" button should be visible at student details').toBeVisible();
          await expect(studentPage.pos.checkoutPage.ContinueButton, 'verifying "Continue" button should be enabled at student details').toBeEnabled();
          await studentPage.pos.checkoutPage.ContinueButton.click();
          await expect(studentPage.pos.checkoutPage.emailErrorText, 'veify existing email error is visible').toBeVisible();
          await studentPage.pos.checkoutPage.EmailIDInputBox.clear();
          await studentPage.pos.checkoutPage.EmailIDInputBox.fill(mailID);
        })
      }
      await expect(studentPage.pos.checkoutPage.ContinueButton, 'verifying "Continue" button should be visible at student details').toBeVisible();
      await studentPage.pos.checkoutPage.ContinueButton.click();
      await slowExpect(studentPage.pos.checkoutPage.filledText, 'verifying "FILLED" Text should be visible').toBeVisible();
    });

    /* Validate parents fields at parent details section */
    await test.step('Validate parent field and fill number and gender details at parent details section', async () => {
      // codition is added due to some of the test data already been used for enrollment and causing the issue at checkout page to validate parent details
      if (await studentPage.pos.checkoutPage.parentDetailsContinueButton.isEnabled()) {
        await studentPage.pos.checkoutPage.parentDetailsContinueButton.click();
        await studentPage.waitForLoadState('networkidle');
      } else {
        await slowExpect(studentPage.pos.checkoutPage.parentNameInputBox, "verify parent name input box is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(nameStartsWithDot);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name starts with dot - invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(nameStartsWithSpecialCharacter);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name starts with special character - invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(nameStartsWithNumber);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name starts with number - Invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(twoSpacesBetweenNameToName);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name with two Spaces Between NameToName - Invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(doubleDotWithSpacesBetweenNameToName);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name with double Dot With Spaces Between NameToName - Invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(spaceAtEndOfName);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name with space At End Of Name - Invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(doubleDotAtEndOfName);
        await expect(studentPage.pos.checkoutPage.invalidParentNameText, "Parent name with double Dot At End Of Name - Invalid parent name is visible").toBeVisible();
        await studentPage.pos.checkoutPage.parentNameInputBox.clear();
        await studentPage.pos.checkoutPage.parentNameInputBox.fill(parentName);
        await expect(studentPage.pos.checkoutPage.Gender, 'verifying "Gender" text should be visible').toBeVisible();
        await expect(studentPage.pos.checkoutPage.GenderMale, 'verifying "Male" text should be visible').toBeVisible();
        await studentPage.pos.checkoutPage.GenderMale.click();
        await expect(studentPage.pos.checkoutPage.ParentsNumbers, 'verifying "Parents numbers" text should be visible').toBeVisible();
        await expect(studentPage.pos.checkoutPage.ParentsNumberInputBox, 'verifying "Parents Number" input box should be visible').toBeVisible();
        await studentPage.pos.checkoutPage.ParentsNumberInputBox.fill(studentMobileNumber);
        await expect(studentPage.pos.checkoutPage.parentDetailsContinueButton, 'verifying "Continue" button should be visible at parent details').toBeVisible();
        await expect(studentPage.pos.checkoutPage.parentDetailsContinueButton, 'verifying "Continue" button should be enabled at parent details').toBeEnabled();
        await studentPage.pos.checkoutPage.parentDetailsContinueButton.click();
      }
    });

    /* Validate and fill the billing address at checkout */
    await test.step('Validate and fill the billing address at checkout', async () => {
      await slowExpect(studentPage.pos.checkoutPage.billingAddressTtile, 'verifying "Billing address title" should be visible at student details').toBeVisible();
      await slowExpect(studentPage.pos.checkoutPage.PinCode, 'verifying "Pincode" input box should be visible').toBeVisible();
      await studentPage.pos.checkoutPage.PinCode.fill(pincode);
      await expect(studentPage.pos.checkoutPage.addressLineOne, 'verifying "Address" input box should be visible').toBeVisible();
      await studentPage.pos.checkoutPage.addressLineOne.fill(addressLineWithSpecialCharacter + city);
      await expect(studentPage.pos.checkoutPage.invalidValueText, 'verify "Address line 1" with invalid value error is visible').toBeVisible();
      await studentPage.pos.checkoutPage.addressLineOne.clear();
      await studentPage.pos.checkoutPage.addressLineOne.fill(city);
      await expect(studentPage.pos.checkoutPage.addressLineTwo, 'verifying "Locality" input box should be visible').toBeVisible();
      await studentPage.pos.checkoutPage.addressLineTwo.fill(nameStartsWithSpecialCharacter);
      await expect(studentPage.pos.checkoutPage.invalidValueText, "Locality name starts with special character - invalid value is visible").toBeVisible();
      await studentPage.pos.checkoutPage.addressLineTwo.clear();
      await studentPage.pos.checkoutPage.addressLineTwo.fill(city);
      await expect(studentPage.pos.checkoutPage.sameAsBuildingAddress, 'verifying "Same As Building Address" checkbox should be visible').toBeVisible();
      await studentPage.pos.checkoutPage.sameAsBuildingAddressCheckbox.check();
      await expect(studentPage.pos.checkoutPage.addressDetailsContinueButton, 'verifying "Continue" button should be visible').toBeVisible();
      await slowExpect(studentPage.pos.checkoutPage.addressDetailsContinueButton, 'verifying "Continue" button should be enabled').toBeEnabled();
      await studentPage.pos.checkoutPage.addressDetailsContinueButton.click();
    });

    /* select the payment details in checkout page */
    await test.step('Select the payment details in checkout page', async () => {
      const coursePrice = parseInt(await studentPage.pos.checkoutPage.coursePrice.innerText());

      await expect(studentPage.pos.checkoutPage.selectPaymentMethod, 'verifying "select payment method" text should be visible').toBeVisible();
      await expect(studentPage.pos.checkoutPage.paymentModeFull, 'verifying "payment Mode Full" button should be visible').toBeVisible();

      if (coursePrice > 20000) {
        await expect(studentPage.pos.checkoutPage.paymentModeLoan, 'verifying "payment Mode Loan" button should be visible').toBeVisible();
      }

      await studentPage.pos.checkoutPage.paymentModeFull.click();
      await expect(studentPage.pos.checkoutPage.justPayOption, 'verifying "just pay option" button should be visible').toBeVisible();
      await studentPage.pos.checkoutPage.justPayOption.click();
      await expect(studentPage.pos.checkoutPage.paymentContinueButton, 'verifying "Continue" button should be visible').toBeVisible();
      await slowExpect(studentPage.pos.checkoutPage.paymentContinueButton, 'verifying "Continue" button should be enabled').toBeEnabled();
    });

    if (env == 'stage') {
      /* Verify payment page details */
      await test.step('Verify payment page details', async () => {
        await studentPage.pos.checkoutPage.paymentContinueButton.click();
        await expect(studentPage).toHaveURL(/.*payment-page*/);
        await studentPage.pos.paymentPage.paymentSuccess();
        await customExpect(waitTime)(studentPage.pos.paymentPage.PaymentSuccessful, 'verifying "PAYMENT SUCCESSFUL" text should be visible').toBeVisible();
      });

      /* Perform account setup post purchase */
      await test.step('Verify account setup post purchase with marks sheet validation', async () => {
        await customExpect(waitTime)(studentPage.pos.paymentPage.SetupYourAccount, 'verifying "Setup Your Account" text should be visible').toBeVisible();
        await studentPage.pos.paymentPage.AccountSetupButton.click()
        await expect(studentPage.pos.accountSetupPage.UploadAcademicFilesText, 'verifying "Upload Academic Files" text should be visible').toBeVisible();
        await expect(studentPage.pos.accountSetupPage.LatestMarksheetText, 'verifying "Latest Marksheet" input box should be visible').toBeVisible();
        await studentPage.pos.accountSetupPage.UploadMarksheetButton.setInputFiles(marksheetWithSixMb);
        await expect(studentPage.pos.accountSetupPage.marksheetUploadErrorText, "verify File size exceeding more than 5MB error is visible").toBeVisible();
        await studentPage.pos.accountSetupPage.UploadMarksheetButton.setInputFiles(marksheet);
        await slowExpect(studentPage.pos.accountSetupPage.SuccessIcon, 'verifying "Success icon" should be visible').toBeVisible();
        await slowExpect(studentPage.pos.accountSetupPage.SubmitButton, 'verifying "Submit" button should be visible').toBeEnabled();
        await studentPage.pos.accountSetupPage.SubmitButton.click();
        await expect(studentPage).toHaveURL(studentPage.pos.homePage.url);

        if (!isMobile) {
          await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
        }
      });

      // *******  Post Enrolment validations are commented due to SP-699 Bug & will redo once it fixed *******//

      // /* Verify invoice validations in oder details page */
      // await test.step('Verify open invoice, invoice id post open and before of invoice', async () => {
      //   // await expect(studentPage.pos.homePage.userName, 'Check user name is visible').toBeVisible();
      //   await studentPage.pos.homePage.userNameMenu.hover();
      //   await studentPage.pos.homePage.orderDetailsButton.click();
      //   await expect(studentPage).toHaveURL(/.*view=order/);
      //   await expect(studentPage.pos.profilePage.orderDetailsStatus, "verify purchase complete status is visible").toBeVisible();
      //   await expect(studentPage.pos.profilePage.orderDetailstatusButton, "verify order details status button is visible").toBeVisible();
      //   await studentPage.pos.profilePage.orderDetailstatusButton.click();
      //   await expect(studentPage.pos.profilePage.viewInvoicesButton, "verify view invoices button is visible").toBeVisible();
      //   await expect(studentPage.pos.profilePage.orderIdValue, "verify orderId is not empty value").not.toBeEmpty();
      //   const invoiceId = await studentPage.pos.profilePage.orderIdValue.textContent();
      //   await studentPage.pos.profilePage.viewInvoicesButton.click();
      //   await expect(studentPage.pos.profilePage.downloadInvoice, "verify download invoice button is visible").toBeVisible();
      //   await expect(studentPage.pos.profilePage.orderIdText(invoiceId!.substring(0)), "verify order details orderid and invoice order id is same").toBeVisible();
      // });

      // /* verifing confirmation message for account deletion for enrolled user */
      // await test.step('Verify confirmation message for account deletion for enrolled user', async () => {
      //   // await expect(studentPage.pos.homePage.userName, 'Check user name is visible').toBeVisible();
      //   await studentPage.pos.homePage.userNameMenu.hover();
      //   await expect(studentPage.pos.homePage.settingsMenu, "verify settings menu button is visible").toBeVisible();
      //   await studentPage.pos.homePage.settingsMenu.click();
      //   await expect(studentPage).toHaveURL(/.*view=settings/);
      //   await expect(studentPage.pos.profilePage.accountSettingsSubTitleText, 'verifying "account settings title" should be visible').toBeVisible();
      //   await expect(studentPage.pos.profilePage.deleteAccountButton, 'verifying "delete account" text should be visible').toBeVisible();
      //   await studentPage.pos.profilePage.deleteAccountButton.click();
      //   await expect(studentPage.pos.profilePage.deleteAccountConfirmationText, 'verifying "delete account confirmation" text should be visible').toBeVisible();
      //   await expect(studentPage.pos.profilePage.deletedButton, 'verifying delete button should be visible').toBeVisible();
      //   await studentPage.pos.profilePage.deletedButton.click();
      //   await expect(studentPage.pos.profilePage.deleteAccountMessageOfEnrolledUser, 'verifying enrolled user account deletion message is visible').toBeVisible();
      //   await expect(studentPage.pos.profilePage.cancelButton, 'verifying enrolled user account deletion cancel button is visible').toBeVisible();
      //   await studentPage.pos.profilePage.cancelButton.click();
      // });

      // /* verify account logout flow */
      // await test.step('Verify account logout flow', async () => {
      //   await expect(studentPage.pos.profilePage.logoutButton, 'verify logout from all devices button is visible').toBeVisible();
      //   await studentPage.pos.profilePage.logoutButton.click();
      //   await expect(studentPage.pos.profilePage.accountLogout, 'verify logout button is visible').toBeVisible();
      //   await studentPage.pos.profilePage.accountLogout.click();
      //   await slowExpect(studentPage.pos.preHomePage.loginButton, "verify login button is visible").toBeVisible();
      // });

      // await test.step(`verify second time enrolment error validation`, async () => {
      //   await slowExpect(studentPage.pos.preHomePage.neetButton, "verify Neet button is visible").toBeVisible();
      //   // await studentPage.pos.preHomePage.jeeButton.click(); // switching to neet due to jee config is down
      //   await studentPage.pos.preHomePage.neetButton.click();
      //   await slowExpect(studentPage.pos.preHomePage.selectYourStreamTitle, "verify select your stream title is visible").toBeVisible();
      //   await slowExpect(studentPage.pos.preHomePage.selectYourClassTitle, "verify select your class title is visible").toBeVisible();
      //   await slowExpect(studentPage.pos.preHomePage.class11Th, "verify select class 11th title is visible").toBeVisible();
      //   await studentPage.pos.preHomePage.class11Th.click();
      //   await expect(studentPage.pos.preHomePage.exploreCourses, "verify explore courses is visible").toBeVisible();
      //   await expect(studentPage.pos.preHomePage.exploreCourses, "verify explore courses is visible").toBeEnabled();
      //   await studentPage.pos.preHomePage.exploreCourses.click();
      //   // checking with neet 12 course
      //   await expect(studentPage).toHaveURL(/.*online-coaching-class-11/);
      //   await slowExpect(studentPage.pos.courseDetailsPage.liveProgramsTitle, "verify live Programs Title is visible").toBeVisible();
      //   await expect(studentPage.pos.courseDetailsPage.nurtureOnlineProgram, "verify nurture Online Program card is visible").toBeVisible();
      //   await studentPage.pos.courseDetailsPage.nurtureOnlineProgram.click();
      //   await expect(studentPage).toHaveURL(/.*nurture-online-programs-class-11/);
      //   // await slowExpect(studentPage.pos.preHomePage.jeeButton, "verify Jee button is visible").toBeVisible();
      //   // await studentPage.pos.preHomePage.jeeButton.click();
      //   // await slowExpect(studentPage.pos.preHomePage.class11Link, "verify class 11 link is visible").toBeVisible();
      //   // await studentPage.pos.preHomePage.class11Link.click();
      //   // await expect(studentPage).toHaveURL(/.*course-details*/); //url is frequently changing in stage
      //   await slowExpect(studentPage.pos.courseDetailsPage.courseNameText, " verify course name text is visible").toBeVisible();
      //   await slowExpect(studentPage.pos.courseDetailsPage.courseStartDateButton, "verify course start date button is visible").toBeVisible();
      //   await studentPage.pos.courseDetailsPage.courseStartDateButton.click();
      //   await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "verify enroll now button is enabled").toBeEnabled();
      //   await studentPage.pos.courseDetailsPage.enrollNowButton.click();
      //   await studentPage.pos.preHomePage.loginWidget.loginWithMobileNumber(studentMobileNumber, '1111');
      //   await expect(studentPage.pos.courseDetailsPage.courseStartDateButton, "verify course start date button is visible").toBeVisible();
      //   await studentPage.pos.courseDetailsPage.courseStartDateButton.click();
      //   await expect(studentPage.pos.courseDetailsPage.enrollNowButton, "verify enroll now button is enabled").toBeEnabled();
      //   await studentPage.pos.courseDetailsPage.enrollNowButton.click();
      //   await expect(studentPage.pos.coursePage.alreadyPurchasedCourseText, "verify already purchased text is visible").toBeVisible();
      // })
    }

  });

  /* Talk to us is commenting, since it was based on dynamic page configuration */
  // test('Verify talk to us', async ({ studentPage }) => {
  //   let studentMobileNumber: string;
  //   if (env == 'stage') {
  //     studentMobileNumber = StringUtils.generateRandomMobileNumber();
  //   } else {
  //     studentMobileNumber = "9032130595"; //check with Prabhakaran either need to submit the call or not and decide this condition
  //   }
  //   console.log("checking number " + studentMobileNumber)
  //   const name = "testing";
  //   const emailId = "<EMAIL>";
  //   /* verify first name with invalid data */
  //   await test.step(`Navigate to talk to us option and verify with invalid first name`, async () => {
  //     await slowExpect(studentPage.pos.preHomePage.talkToUsButton, "verify talk to us button is visble").toBeVisible();
  //     await studentPage.pos.preHomePage.talkToUsButton.click();
  //     await expect(studentPage).toHaveURL(/.*schedule-a-call-back/);
  //     await expect(studentPage.pos.scheduleACallbackHomePage.firstNameInput, "verify Enter first name input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.firstNameInput.fill("aa");
  //     await expect(studentPage.pos.scheduleACallbackHomePage.lastNameInput, "verify enter last name input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.lastNameInput.fill(name);
  //     await expect(studentPage.pos.scheduleACallbackHomePage.mobileNumberInput, "verify Enter mobile number input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.fill(studentMobileNumber);
  //     await expect(studentPage.pos.scheduleACallbackHomePage.emailIdInput, "verify Enter emai id input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.emailIdInput.fill(emailId);
  //     await expect(studentPage.pos.scheduleACallbackHomePage.selectClass, "verify select class dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.selectClass.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.class6thOption, "verify class 6th dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.class6thOption.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.selectGoal, "verify select goal dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.selectGoal.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.goalNeetOption, "verify Neet Goal dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.goalNeetOption.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.selectMode, "verify preferred programs dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.selectMode.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.onlineProgramsOption, "verify online programs dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.onlineProgramsOption.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.selectState, "verify select state dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.selectState.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.andhraPradeshOption, "verify Andhara pradesh state dropdown is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.andhraPradeshOption.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.tncCheckbox.or(studentPage.pos.scheduleACallbackHomePage.termsCheckbox), "verify TNC checkbox is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.tncCheckbox.or(studentPage.pos.scheduleACallbackHomePage.termsCheckbox).check();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.authoriseCheckbox, "verify authorize checkbox is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.authoriseCheckbox.check();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.submitButton, "verify submit button is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.submitButton.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.nameErrorMessage, "verify enter first name field thorughs error with 2 alphabets").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.firstNameInput.fill(name);
  //   });

  //   /* verify last name with invalid data */
  //   await test.step(`Verify last name with invalid data`, async () => {
  //     await expect(studentPage.pos.scheduleACallbackHomePage.lastNameInput, "verify enter last name input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.lastNameInput.clear();
  //     await studentPage.pos.scheduleACallbackHomePage.lastNameInput.fill("aa");
  //     await studentPage.pos.scheduleACallbackHomePage.submitButton.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.nameErrorMessage, "verify enter first name field thorughs error with 2 alphabets").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.lastNameInput.fill(name);
  //   });

  //   /* verify mobile number with invalid data */
  //   await test.step(`Verify mobile number with invalid data`, async () => {
  //     await expect(studentPage.pos.scheduleACallbackHomePage.mobileNumberInput, "verify enter mobile number input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.clear();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.fill("**********");
  //     await expect(studentPage.pos.scheduleACallbackHomePage.mobileNumberErrorMessage, "verify mobile number starts with 5 error message is visible").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.clear();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.fill("612345678");
  //     await studentPage.pos.scheduleACallbackHomePage.submitButton.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.mobileNumberErrorMessage, "verify mobile number with less than 10 digit error message is visible").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.clear();
  //     await studentPage.pos.scheduleACallbackHomePage.mobileNumberInput.fill(studentMobileNumber);
  //   });

  //   /* verify email-id with invalid data */
  //   await test.step(`Verify email-id with invalid data`, async () => {
  //     await expect(studentPage.pos.scheduleACallbackHomePage.emailIdInput, "verify enter email-id input is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.emailIdInput.clear();
  //     await studentPage.pos.scheduleACallbackHomePage.emailIdInput.fill("testing@gmail.");
  //     await studentPage.pos.scheduleACallbackHomePage.submitButton.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.emailErrorMessage, "verify invalid email error message is visible").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.emailIdInput.clear();
  //     await studentPage.pos.scheduleACallbackHomePage.emailIdInput.fill(emailId);
  //   });

  //   /* verify without check boxs check-in */
  //   await test.step(`Verify without check-in the check boxs`, async () => {
  //     await expect(studentPage.pos.scheduleACallbackHomePage.tncCheckbox.or(studentPage.pos.scheduleACallbackHomePage.termsCheckbox), "verify TNC checkbox is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.tncCheckbox.or(studentPage.pos.scheduleACallbackHomePage.termsCheckbox).uncheck();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.authoriseCheckbox, "verify authorize checkbox is visble").toBeVisible();
  //     await studentPage.pos.scheduleACallbackHomePage.authoriseCheckbox.uncheck();
  //     await studentPage.pos.scheduleACallbackHomePage.submitButton.click();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.checkBoxErrorMessage, "verify invalid email error message is visible").toBeVisible();
  //   });

  //   /* Apply check boxs of tnc and authorise */
  //   await test.step(`Apply check boxs of tnc and authorise`, async () => {
  //     await studentPage.pos.scheduleACallbackHomePage.tncCheckbox.or(studentPage.pos.scheduleACallbackHomePage.termsCheckbox).check();
  //     await studentPage.pos.scheduleACallbackHomePage.authoriseCheckbox.check();
  //     await expect(studentPage.pos.scheduleACallbackHomePage.authoriseCheckbox, "verify authorise check box is checked").toBeChecked();
  //   });

  //   if (env == "stage") {
  //     /* Schedule a call with valid data */
  //     await test.step(`Schedule a call and verify response`, async () => {
  //       await studentPage.pos.scheduleACallbackHomePage.submitButton.click();
  //       await slowExpect(studentPage).toHaveURL(/.*thank-you/);
  //       await slowExpect(studentPage.pos.scheduleACallbackHomePage.teamWillContactText, "verify post submit our team will contact text is visible").toBeVisible();
  //       await expect(studentPage.pos.scheduleACallbackHomePage.goToAllenButton, "verify go to allen button is visible").toBeVisible();
  //     });
  //   }
  // });
});