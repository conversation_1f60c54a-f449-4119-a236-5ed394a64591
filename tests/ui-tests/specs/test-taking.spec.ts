import { test, expect, customExpect, slowExpect } from '../fixtures/ui-fixtures';
import { StringUtils, PlaywrightUtils } from '../../commons/common-functions';
import { format } from 'path';
import ResourceManagement from '../../commons/api-helpers/resource-management';
import { EnvUtils } from '../../commons/env-utilities'

test.describe('Test Taking UI Tests', {
  tag: ['@test-taking', '@ui']
}, () => {
  /** 
   * @info Test Naming convention updated and assertion message
   */
  test('Verify scheduling and publishing a test from the console, and validate student access to take and submit the test.', async ({ adminPage, testData, studentPage }) => {
    test.setTimeout(180000);

    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDateAndTime = StringUtils.getCurrentTimeWithOffset("YYYY-MM-DD hh:mm:ss tt", 40, true, timezone);

    //data for creating the test 
    const testName = "ALLEN DIGITAL";
    const SyllabusPublicURL = process.env.TEST_SYLLABUS_LINK ? process.env.TEST_SYLLABUS_LINK : "";
    const batchId = testData.batch.name;
    const centerId = process.env.CENTER_ID;
    const language = EnvUtils.getInstance().isProd() ? "ENGLISH" : "HINGLISH"
    const courseMode = EnvUtils.getInstance().isProd() ? "LIVE" : "LIVE_RECORDED"
    const courseId = process.env.JEE_BATCH_COURSE_ID;
    const courseName = process.env.COURSE_NAME;
    const facilityId = process.env.BATCH_FACILITY_ID;
    const testDuration = "1"
    const windowLoginDuration = "2"
    const paperCode = "9610WJAPRANUR24001"
    const testDisplayFirstName = "FULL TEST";
    const randomFourDigit = StringUtils.generateRandomFourDigits();
    const testDisplayNamePlusNumber = testDisplayFirstName + " " + randomFourDigit;
    const qustionNumber1 = "1";
    const qustionNumber2 = "2";
    const qustionNumber3 = "3";
    const qustionNumber4 = "4";
    const qustionNumber5 = "5";
    const uploadSyllabusInputFilePath = "test-data/stage/test-taking-syllabus.pdf";


    //navigate to internal user home page
    await test.step(`Navigate from teacher home page to internal user home page`, async () => {
      await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
      // await adminPage.pos.teacherHomePage.navigateToInternalUser();
    });
    /* The below commented script will delete in further observation runs */
    // //navigate to create test page 
    // await test.step(`Verify the test managment page details`, async () => {
    //   await adminPage.pos.internalUserHomePage.navigateToCreateTestPage();
    // });
    //create the single test
    await test.step(`Create the single test`, async () => {
      // await adminPage.pos.createTestPage.createSingleTest();
      await adminPage.goto(adminPage.pos.createTestPage.url + '/create?type=e2e-automation'); //this end point for automation purpose
      await expect(adminPage).toHaveURL(/.*e2e-automation/);
      await adminPage.waitForLoadState('networkidle');
    });
    //enter the vaild data for creation of test
    await test.step(`Enter the basic details for creation of test`, async () => {
      await adminPage.pos.basicDetailsPage.enterBasicDetailsForTestCreation(testName, testDisplayFirstName, randomFourDigit, SyllabusPublicURL, batchId, centerId, language, courseMode, courseId, courseName, facilityId, currentDateAndTime, testDuration, windowLoginDuration, "objective", "onlineBatch");
      await expect(adminPage.pos.basicDetailsPage.createAndNextButton, "verify create and next button is visible").toBeVisible();
      await adminPage.pos.basicDetailsPage.createAndNextButton.click();
    });
    //import the paper code and published the test
    await test.step(`Enter the paper code and publish the test`, async () => {
      await adminPage.pos.paperReviewPage.enterPaperCodeAndPublishTestWithReviewQuestionPaper(paperCode);
    });
    /** 
    * @info Test Naming convention updated
   */
    //login for the student page
    await test.step(`Verify login to the student side`, async () => {
      await studentPage.waitForTimeout(15000);
      await studentPage.login()
    });
    //Validate test card widgets and start the test
    await test.step(`Validate test card widgets and start the test`, async () => {
      await studentPage.pos.homePage.validateUpcomingTestCard(testDisplayNamePlusNumber);
      await studentPage.waitForLoadState('networkidle');
      await studentPage.goBack();
      await studentPage.waitForTimeout(20000); //hard wait is required according to business logic for the test taking module
      await studentPage.reload(); // reload of the page is requried because to get the start button to start test
      await studentPage.pos.homePage.startTest(testDisplayNamePlusNumber);
    });
    /** 
    * @info Test Naming convention updated and assertion message
    */
    // verify user is accept the instructions that terms and conditions
    await test.step(`Verify user accept of terms and conditions in the instructions and click on proceed test`, async () => {
      await studentPage.pos.instructionPage.verifyTheInstructionPageAndProceedTest();
    });

    // verify after test started student can see the countdown of the timer
    await test.step(`Verify test taking countdown timer`, async () => {
      await studentPage.pos.examPage.validatingTestTimerCountDown();
    });

    // verify before and after marking the question student can see it is answered and not visited
    await test.step(`Verify student can see the question is answered once it marked`, async () => {
      await studentPage.pos.examPage.rightArrowButton.click();
      await studentPage.pos.examPage.validatingQuestionIsAnswered(qustionNumber1);
    });

    // verify the question is not answered by clicking save and next
    await test.step(`Verify student can see the question is not answered by clicking save and next`, async () => {
      await studentPage.pos.examPage.validatingQuestionIsNotAnswered(qustionNumber2);
    });

    // verify the question is answered but marked for review
    await test.step(`Verify student can see the question is answered but marked for review`, async () => {
      await studentPage.pos.examPage.validatingQuestionIsAnsweredAndMarkedForReview(qustionNumber3);
    });

    // verify the question is not answered but marked for review
    await test.step(`Verify student can see the question is not answered but marked for review`, async () => {
      await studentPage.pos.examPage.validatingQuestionIsNotAnsweredAndMarkedForReview(qustionNumber4);
    });

    // verify the user is able to take the test
    await test.step(`Verify clear response and back functionality`, async () => {
      await studentPage.pos.examPage.clearResponseAndAgainAnswerTheQuestion(qustionNumber5);
      await studentPage.pos.examPage.verifyTheBackButtonFunctionality(qustionNumber4);
    });
    // verify user is able to submit the test
    await test.step(`Verify user is able to submit the test`, async () => {
      await expect(studentPage.pos.examPage.submitButton, "verify submit button is visible").toBeVisible();
      await studentPage.waitForTimeout(2000); //wait is required to load the answers
      await studentPage.pos.examPage.submitButton.click();
      await studentPage.waitForTimeout(2000); //wait is required to load the answers
    });
    // verify user is successfully submitted the test with toast message
    await test.step(`Verify user is successfully submitted the test with toast message`, async () => {
      await studentPage.pos.examSummaryPage.verifySuccessToasMessageAfterTestSubmission();
    });
  });

  test('verify create custom practice/ start quiz/ validate timer and other titles/ force submit', async ({ studentPage, testData }) => {
    test.setTimeout(180000);

    //login for the student page
    await test.step(`Validate mobile number login`, async () => {
      await studentPage.login();
    });

    //create custom practice quiz by navigating to custom practice under study planner
    await test.step(`Create custom practice quiz`, async () => {
      await expect(studentPage.pos.homePage.quickActionsTitleText, "verify quick actions title is visible").toBeVisible();
      await expect(studentPage.pos.homePage.customPracticeButton, "verify custom practice button is visible").toBeVisible();
      await studentPage.pos.homePage.customPracticeButton.click();
      await slowExpect(studentPage).toHaveURL(/.*quiz*/);
      await studentPage.pos.quizHomePage.selectTheSubjectToCreateQuiz();
      await studentPage.pos.quizCreatePage.createCustomQuiz();
      await expect(studentPage).toHaveURL(/.*questions*/);
    });

    // In Stage due to question paper configuration issue we are not able to answer the question properly
    //verify questions are visible and do force submit
    await test.step(`Verify questions are visible / force submit`, async () => {
      await studentPage.pos.quizCreatePage.startQuizAndForceSubmit();
      await expect(studentPage).toHaveURL(/.*result*/);
    });

  });

  test('Verify user is able to schedule and publish the subjective test and able to see in listing page and cancel created test', async ({ adminPage, testData }) => {
    test.setTimeout(180000);

    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDateAndTime = StringUtils.getCurrentTimeWithOffset("YYYY-MM-DD hh:mm:ss tt", 50, true, timezone);

    //data for creating the test 
    const testName = "ALLEN DIGITAL";
    const SyllabusPublicURL = process.env.TEST_SYLLABUS_LINK ? process.env.TEST_SYLLABUS_LINK : "";
    const batchId = testData.batch.name;
    const centerId = process.env.CENTER_ID;
    const language = EnvUtils.getInstance().isProd() ? "ENGLISH" : "HINGLISH"
    const courseMode = EnvUtils.getInstance().isProd() ? "LIVE" : "LIVE_RECORDED"
    const courseId = process.env.JEE_BATCH_COURSE_ID;
    const courseName = process.env.COURSE_NAME;
    const facilityId = process.env.BATCH_FACILITY_ID;
    const testDuration = "1"
    const windowLoginDuration = "2"
    const paperCode = "9610WJAPRANUR24001"
    const testDisplayFirstName = "FULL TEST";
    const randomFourDigit = StringUtils.generateRandomFourDigits();
    const testDisplayNamePlusNumber = testDisplayFirstName + " " + randomFourDigit;
    const qustionNumber1 = "1";
    const qustionNumber2 = "2";
    const qustionNumber3 = "3";
    const qustionNumber4 = "4";
    const uploadSyllabusInputFilePath = "test-data/stage/test-taking-syllabus.pdf";


    //navigate to internal user home page
    await test.step(`Navigate from teacher home page to internal user home page`, async () => {
      await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
    });

    //create the single test
    await test.step(`Create the single test`, async () => {
      await adminPage.goto(adminPage.pos.createTestPage.url + '/create?type=e2e-automation'); //this end point for automation purpose
      await expect(adminPage).toHaveURL(/.*e2e-automation/);
    });
    //enter the vaild data for creation of test
    await test.step(`Enter the basic details for creation of test`, async () => {
      await adminPage.pos.basicDetailsPage.enterBasicDetailsForTestCreation(testName, testDisplayFirstName, randomFourDigit, SyllabusPublicURL, batchId, centerId, language, courseMode, courseId, courseName, facilityId, currentDateAndTime, testDuration, windowLoginDuration, "objective", "onlineBatch");
      await expect(adminPage.pos.basicDetailsPage.createAndNextButton, "verify create and next button is visible").toBeVisible();
      await adminPage.pos.basicDetailsPage.createAndNextButton.click();
      await adminPage.waitForLoadState('networkidle');
    });
    //import the paper code and published the test
    await test.step(`Enter the paper code and publish the test`, async () => {
      await adminPage.pos.paperReviewPage.enterPaperCodeAndPublishTestWithReviewQuestionPaper(paperCode);
    });


    //import the paper code and published the test
    await test.step(`verify the created test is present in listing page`, async () => {
      await slowExpect(adminPage.pos.paperReviewPage.createdTestId, 'created test id  is visible').toBeVisible();
      const test_ID = await adminPage.pos.paperReviewPage.createdTestId.textContent();
      await expect(adminPage.pos.paperReviewPage.sideMenuTestManagement, "test management side menu icon is visible").toBeVisible();
      await adminPage.pos.paperReviewPage.sideMenuTestManagement.click();
      await expect(adminPage.pos.internalUserHomePage.studentTestButton, "student tests button is visible").toBeVisible();
      await adminPage.pos.internalUserHomePage.studentTestButton.click();
      await adminPage.waitForTimeout(3000);
      await slowExpect(adminPage.pos.createTestPage.createAsButton, "creat test button is visible").toBeVisible();
      await slowExpect(adminPage.pos.createTestPage.createdTestId(test_ID!), "created test id is visible in listing page").toBeVisible();
      await expect(adminPage.pos.createTestPage.testActionButton(test_ID!)).toBeVisible();
      await adminPage.pos.createTestPage.testActionButton(test_ID!).click();
      await expect(adminPage.pos.createTestPage.cancelTestText, "verify cancel test is visible").toBeVisible();
      await adminPage.pos.createTestPage.cancelTestText.click();
      await expect(adminPage.pos.createTestPage.confirmText, "verify confirm test is visible").toBeVisible();
      await adminPage.pos.createTestPage.confirmText.click();

    });


  });

  test('Verify admin able to navigate to test management and able to see tests', async ({ adminPage }) => {

    //navigate to internal user home page
    await test.step(`Navigate from teacher home page to internal user home page`, async () => {
      await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
      await adminPage.pos.teacherHomePage.navigateToInternalUser();
      await slowExpect(adminPage, "Verify after navigating to internal user page url").toHaveURL(/.*internal-user/);
      await adminPage.waitForLoadState('networkidle');
      await slowExpect(adminPage.pos.createTestPage.apiLoader, "Verify api loader which fetch the data should not visible").toBeHidden();
    });

    //navigate to create test page 
    await test.step(`Verify the test managment page details`, async () => {
      await adminPage.pos.internalUserHomePage.navigateToCreateTestPage();
      await slowExpect(adminPage, "Verify after navigating to test management page url").toHaveURL(/.*student-tests/);
      await adminPage.pos.createTestPage.verifyTestManagementPage();
      await slowExpect(adminPage.pos.createTestPage.apiLoader, "Verify api loader which fetch the data should not visible").toBeHidden();
      await slowExpect(adminPage.pos.createTestPage.columnData.nth(5)).toBeAttached();
      expect(await adminPage.pos.createTestPage.columnData.count(), "Verify tests are present and showing in test page list").toBeGreaterThan(20); //Verifying the tests count should be present and greater than 20 atleat
      await adminPage.pos.createTestPage.createSingleTest();
    });
  });

});


