import { test, expect, slowExpect, customExpect } from '../fixtures/ui-fixtures'
import { StringUtils, PlaywrightUtils, getStartAndEndTimestamps } from '../../commons/common-functions';
import { EnvUtils } from '../../commons/env-utilities'

let studentCount;

test.describe('Live UI Tests', {
  tag: ['@live', '@ui']
}, () => {
  const env = process.env.PROD === "1" ? "prod" : "stage";
  // test('Validate class scheduling creation and cancelling', async ({ adminPage, teacherPage, studentPage, testData }) => {
  //   test.setTimeout(80000);
  //   const className = "Automation " + StringUtils.generateRandomString(3);
  //   const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
  //   const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
  //   const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 180, false, timezone);
  //   const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 300, false, timezone);
  //
  //   /*Cancelling all the classes*/
  //   await test.step(`Cancelling all the classes`, async () => {
  //     await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
  //   });
  //
  //   /*Navigate to Scheduling page from Internal page*/
  //   await test.step(`Navigate from teacher home page to internal user home page`, async () => {
  //     await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
  //     await adminPage.pos.teacherHomePage.navigateToInternalUser();
  //   });
  //
  //   /*Navigate to class Scheduling page and validate page details from Internal page*/
  //   await test.step('Verify the class scheduling page details', async () => {
  //     await adminPage.pos.internalUserHomePage.navigateToSchedulingPage();
  //     await expect(adminPage).toHaveURL(/.*class-management*/);
  //     await adminPage.pos.scheduleClassPage.validateScheduleClassPage();
  //   });
  //   //* Below code to switch page context to new tab *//
  //   const scheduleClassBatchPage = await adminPage.pos.scheduleClassPage.navigateToCreateSchedulePage(testData.batch.name);
  //
  //   /*Enter required feild and create class scheduling*/
  //   await test.step(`Verify the create scheduling page details ${testData.teacher}`, async () => {
  //     await scheduleClassBatchPage.validateCreateSchedulePage();
  //     await scheduleClassBatchPage.createSchedulingClass(className, testData.teacher.name, testData.room.name, currentDate, startTime, endTime);
  //     await scheduleClassBatchPage.clickOnPublishClass();
  //     await teacherPage.pos.teacherHomePage.goto();
  //     await teacherPage.pos.teacherHomePage.validateTeacherCalender();
  //     await teacherPage.pos.teacherHomePage.clickOnClassScheduled(className);
  //   });
  //   /*Cancelling class scheduling*/
  //   await test.step(`Cancelling the created schedule class ${testData.teacher}`, async () => {
  //     await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
  //   });
  //
  //   // //untill issue resolves schedule card https:/ / acikota.atlassian.net / browse / CDEN - 2590
  //   // await test.step(`Student joining the meeting`, async () => {
  //   //   await studentPage.login();
  //   //   await studentPage.reload();
  //   //   await studentPage.pos.homePage.verifyLiveClassCancelled(className);
  //   // });
  // });
  //
  // /*Verify class joining/leaving by teacher / student*/
  // test('Verify class joining/leaving by teacher/student', async ({ meetingInfo, studentPage, teacherPage, testData }) => {
  //   test.setTimeout(80000);
  //   console.log('Meeting Id - ', meetingInfo.meetingId, ' with class name ', meetingInfo.className)
  //
  //   await test.step(`Teacher joining the meeting`, async () => {
  //     await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
  //     await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
  //     await expect(teacherPage.pos.teacherLiveClassPage.studentYetToJoinText, "verify student yet to join text is visible").toBeVisible();
  //     studentCount = await teacherPage.pos.teacherLiveClassPage.getStudentCount();
  //   });
  //
  //   /* login for the student page*/
  //   await test.step(`Student joining the meeting`, async () => {
  //     await studentPage.login();
  //     await studentPage.waitForLoadState('networkidle');
  //     await studentPage.waitForLoadState('domcontentloaded');
  //     await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
  //     await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
  //   });
  //
  //   //verify the teacher live class page after student joins the class
  //   await test.step(`Validate teacher page after student joined`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPageAfterStudentJoins(testData.student.name);
  //     const studentCountJoins = await teacherPage.pos.teacherLiveClassPage.getStudentCount();
  //     expect(parseInt(studentCountJoins!)).toBeGreaterThan(parseInt(studentCount));
  //   });
  //
  //   //click  and verify student leave class
  //   await test.step(`Student leaving the meeting`, async () => {
  //     await studentPage.pos.studentLiveClassPage.clickOnLeaveClassButton();
  //     await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText.first(), 'verifying "explore study materials" text should be visible').toBeVisible();
  //   });
  //
  //   //verify student leave class in Teacher live class page
  //   await test.step(`Validate teacher page after student left`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPageAfterStudentLeavesClass(testData.student.name);
  //   });
  //
  //   await test.step(`Student joining meeting again`, async () => {
  //     await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
  //   });
  //
  //   //click and verify teacher end class in Teacher live class page
  //   await test.step(`Teacher ends the meeting`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.clickAndValidateTeacherEndClass();
  //   });
  //
  //   //verify class ended pop up once teacher ended class in student live class page
  //   await test.step(`Validate class ended popup`, async () => {
  //     await studentPage.pos.studentLiveClassPage.verifyClassEndedPopUp();
  //   });
  // });


  // Verify chat- Move to Query, Block/Unblock and Bring on/off Stage
  test('Verify chat(move to query, block/unblock & bring on/off stage) ', async ({ meetingInfo, studentPage, teacherPage, testData }) => {
    test.setTimeout(160000);

    const messageTeacher = "Test";
    const messageStudent = "Testing";
    const urlLink = "www.gmail.com"
    console.log('Meeting Id - ', meetingInfo.meetingId, ' with class name ', meetingInfo.className)
    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();

      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();
    });

    //Start Teacher chat and verify
    await test.step(`Verify teacher enable chat and send to student`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickEnableChatAndSelectEveryoneOption();
      await teacherPage.pos.teacherLiveClassPage.teacherChatWithStudent(messageTeacher);
    });

    //verify the student is able verify chat and send chat to teacher
    await test.step(`Verify teacher chat and send chat to teacher from student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnChatButton();
      await studentPage.pos.studentLiveClassPage.verifyChatFromTeacherToStudent(messageTeacher);
      await studentPage.pos.studentLiveClassPage.studentChatWithTeacher(messageStudent);
    });
    /*
    // Teacher perform chat as move to query and verify.
    await test.step(`Verify chat is moved to query from teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnChatMoveToQuery(messageStudent);
      await teacherPage.pos.teacherLiveClassPage.verifyChatIsMovedToQuery(messageStudent);
    });

    //verify the student is able verify moved to query chat and like from Student live class
    await test.step(`Verify chat is moved to query in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyChatIsMovedToQueryByTeacher(messageStudent);
    });

    // Teacher resolved chat query and verify from teacher live class.
    await test.step(`Verify query is resolved by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnLikeAndResolveQueryInQueryTab(messageStudent);
    });

    // Teacher resolved chat query and verify from student live class.
    await test.step(`Verify query is resolved by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyQueryIsResolvedByTeacher();
    });
    */

    // verify emoji feature
    await test.step(`Verify student can send a emoji in chat and teacher can see`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyStudentChatEmojiFeature();
      await slowExpect(teacherPage.pos.teacherLiveClassPage.studentSentThumsupEmoji, "veify student sent emoji is visible to teacher").toBeVisible();
    });

    // verify URL link feature
    await test.step(`Verify student can send & open url link and the teacher can see & open link`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyStudentChatUrlLinkFeature(urlLink);
      await slowExpect(teacherPage.pos.teacherLiveClassPage.studentSentUrlLink(urlLink), "veify student sent emoji is visible to teacher").toBeVisible();
      const newTabPromise = teacherPage.pos.teacherLiveClassPage.page.waitForEvent("popup");
      await teacherPage.pos.teacherLiveClassPage.studentSentUrlLink(urlLink).click();
      const newTab = await newTabPromise;
      await expect(newTab).toHaveURL(/.*google.com*/);
      await newTab.close();
    });

    // Teacher click on chat block from teacher live class.
    await test.step(`Verify chat is blocked from teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnBlockChatFromTeacher(testData.student.name);
    });

    //verify chat is blocke from teacher live class
    await test.step(`Verify chat blocked teacher from student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyChatIsBlockedByTeacher();
    });

    //verify chat is blocked from Teacher live class
    await test.step(`Verify chat blocked from teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBlockByTeacher(testData.student.name);
    });


    //verify raise hand is blocked from Student side when teacher blocks student
    await test.step(`Verify raise hand is blocked from Student side when teacher blocks student`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyRaiseHandWhenTeacherBlocksStudent();
    });

    // Teacher unblocks chat from teacher live class.
    await test.step(`Verify chat is unblocked by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnUnBlockChatFromTeacher(testData.student.name);
    });

    // Teacher unblocks chat and verify from student live class.
    await test.step(`Verify unblocks by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyChatIsUnBlockedByTeacher();
    });

    // Teacher Bring on stage chat from teacher live class.
    await test.step(`Verify chat bring on stage by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnBringOnStageChatFromTeacher(testData.student.name);
    });

    // Teacher bring on stage chat and verify from student live class.
    await test.step(`Verify bring on stage by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnUnMuteVideoForBringOnStage();
    });

    // Teacher bring off stage chat and verify from teacher live class.
    await test.step(`Verify bring off stage by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBringOnStage();
      await teacherPage.pos.teacherLiveClassPage.verifyBringOffStageFromTeacher();
    });

    // Verify select teacher only and send to student`
    await test.step(`Verify select teacher only and chat send to student`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnTeacherOnlyAndVerify();
      await teacherPage.pos.teacherLiveClassPage.teacherChatWithStudent(messageTeacher);
    });

    //Verify teacher only option selected from student
    await test.step(`Verify teacher only option selected from student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyChatFromTeacherToStudent(messageTeacher);
      await studentPage.pos.studentLiveClassPage.verifyTeacherOnlyOption();
      await studentPage.pos.studentLiveClassPage.studentChatWithTeacher(messageStudent);
      await teacherPage.pos.teacherLiveClassPage.verifyChatFromStudentToTeacher(messageStudent);
    });

    // Verify select No one option and send to student`
    await test.step(`Verify select no one and chat send to student`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnNoOneOptionAndVerify();
      await expect(teacherPage.pos.teacherLiveClassPage.enableChatButton, "Verify enable chat button is visible").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.enableChatButton.click();
      await teacherPage.pos.teacherLiveClassPage.teacherChatWithStudent(messageTeacher);
    });
  });

  /*Verify Poll for 4 & 5 options*/
  test('Verify poll for four & five options', async ({ meetingInfo, teacherPage, studentPage, testData }) => {
    PlaywrightUtils.listenWebsockets(studentPage, "student");
    PlaywrightUtils.listenWebsockets(teacherPage, "teacher");
    test.setTimeout(180000);

    const fourPollOption = '1';
    const fivePollOption = '1';
    //Navigate to Teacher live class using meetingId
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);   // wait for class to start
      }
      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);
    });

    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await teacherPage.waitForTimeout(2000);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();
    });

    //create poll as a no limits until class duration
    await test.step(`Create polls[4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("noLimits");
    });


    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit[4 polls - no limit]`, async () => {
      await studentPage.pos.studentLiveClassPage.submitThePoll(fourPollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls from the teacher side and end poll [4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(fourPollOption);
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is ended [4 polls - no limit]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });

    //close the current poll by clicking close poll popup
    await test.step(`Verify and close the current poll by clicking close poll popup [4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.closePollPopUp.click();
      await teacherPage.waitForTimeout(1000); // wait required to end the poll websocket
    });

    //create poll as a no limits until class duration
    await test.step(`Create polls [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("oneMin");
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit poll[4 polls -1 min]`, async () => {
      await studentPage.pos.studentLiveClassPage.submitThePoll(fourPollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls from the teacher side and end poll [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(fourPollOption);
    });

    //close the current poll by clicking close poll popup
    await test.step(`Verify and close the current poll by clicking close poll popup [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.closePollPopUp.click();
      await teacherPage.waitForTimeout(1000); // wait required to end the poll websocket
    });

    //verify the student is end the poll
    await test.step(`Verify the student polls is ended [4 polls -1 min]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });

    //create poll as a no limits until class duration
    await test.step(`Create polls [4 polls -30 sec]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("thirtySec");
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit poll[4 polls -30 sec]`, async () => {
      await studentPage.pos.studentLiveClassPage.submitThePoll(fourPollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls from the teacher side and end poll[4 polls -30 sec] `, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(fourPollOption);
    });

    //verify the student is end the poll
    await test.step(`Verify the student polls is ended  poll[4 polls -30 sec]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });

    //close the current poll by clicking close poll popup
    await test.step(`Verify and close the current poll by clicking close poll popup [4 polls -30 sec]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.closePollPopUp.click();
      await teacherPage.waitForTimeout(1000); // wait required to end the poll websocket
    });

    //create five poll as a no limits until class duration
    await test.step(`Create polls [5 polls -no limit]`, async () => {
      // await teacherPage.pos.teacherLiveClassPage.closePollsPopUp();
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectFivePollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("noLimits");
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit poll[5 polls -no limit]`, async () => {
      // await studentPage.pos.studentLiveClassPage.closePollPopUp.click();
      await studentPage.pos.studentLiveClassPage.submitThePoll(fivePollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls from the teacher side and end poll [5 polls -no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(fivePollOption);
    });

    //verify the student is end the poll
    await test.step(`Verify the student polls is endeded [5 polls -no limit]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });

    //close the current poll by clicking close poll popup
    await test.step(`Verify and close the current poll by clicking close poll popup [5 polls -no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.closePollPopUp.click();
      await teacherPage.waitForTimeout(1000); // wait required to end the poll websocket
    });

    //create five poll as a no limits until class duration
    await test.step(`Create polls [5 polls -1 min]`, async () => {
      // await teacherPage.pos.teacherLiveClassPage.closePollsPopUp();
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectFivePollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("oneMin");
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit poll[5 polls -1 min]`, async () => {
      // await studentPage.pos.studentLiveClassPage.closePollPopUp.click();
      await studentPage.pos.studentLiveClassPage.submitThePoll(fivePollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered  polls from the teacher side and end poll [5 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(fivePollOption);
    });

    //verify the student is end the poll
    await test.step(`Verify the student polls is ended [5 polls -1 min]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });

    //close the current poll by clicking close poll popup
    await test.step(`Verify and close the current poll by clicking close poll popup [5 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.closePollPopUp.click();
      await teacherPage.waitForTimeout(1000); // wait required to end the poll websocket
    });

    //create five poll as a no limits until class duration
    await test.step(`Create polls [5 polls -30 sec]`, async () => {
      // await teacherPage.pos.teacherLiveClassPage.closePollsPopUp();
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectFivePollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("thirtySec");
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit poll[5 polls -30 sec]`, async () => {
      // await studentPage.pos.studentLiveClassPage.closePollPopUp.click();
      await studentPage.pos.studentLiveClassPage.submitThePoll(fivePollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered  polls from the teacher side and end poll [5 polls -30 sec]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(fivePollOption);
    });

    //verify the student is end the poll
    await test.step(`Verify the student polls is ended [5 polls -30 sec]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });
  });

  /* schedule clash and cancelling */
  /*
  test('Validate schedule clash and cancelling', async ({ adminPage, testData }) => {
    const className = "Automation " + StringUtils.generateRandomString(3);
    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
    const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 120, false, timezone);
    const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 240, false, timezone);
    let scheduleClassBatchPage;
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_ONLINE";
    let teacherInfo;

    //Cancelling the created schedule class
    await test.step(`creating live class `, async () => {
      teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 0, 5, 'E2E Test');

    });

    //Navigate to Scheduling page from Internal page
    await test.step(`Navigate from teacher home page to internal user home page`, async () => {
      await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url);
      await adminPage.pos.teacherHomePage.navigateToInternalUser();
    });

    //Navigate to class Scheduling page and validate page details from Internal page
    await test.step('Verify the class scheduling page details,', async () => {
      await adminPage.pos.internalUserHomePage.navigateToSchedulingPage();
      await adminPage.pos.scheduleClassPage.validateScheduleClassPage();
    });
    //Below code to switch page context to new tab
    //Navigate to class Scheduling page and validate page details from Internal page
    await test.step('Navigate to class scheduling page,', async () => {
      scheduleClassBatchPage = await adminPage.pos.scheduleClassPage.navigateToCreateSchedulePage(testData.batch.name);
    });

    //Enter required feild and create class scheduling
    await test.step(`Verify clash and cancel the class`, async () => {
      await scheduleClassBatchPage.validateCreateSchedulePage();
      await scheduleClassBatchPage.createSchedulingClass(className, testData.teacher.name, testData.room.name, currentDate, startTime, endTime);
      await scheduleClassBatchPage.verifyClassClashAndCancelClass();
    });

    //Cancelling the created schedule class
    await test.step(`Cancelling the created schedule class `, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });
  });
*/

  /*Verify handouts upload/download/delete */
  test('Verify handouts(upload/download/delete) by teacher', async ({ meetingInfo, adminPage, studentPage, teacherPage, testData }) => {
    test.setTimeout(140000);

    const className = "Automation " + StringUtils.generateRandomString(3);
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_ONLINE";

    await test.step(`Verify teacher Upload self material on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
      await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(meetingInfo.className);
      await teacherPage.pos.teacherHomePage.validateScheduledClassDetailsOnTeacherSide(meetingInfo.className, testData.batch.name, testData.room.name);
      await teacherPage.pos.teacherHomePage.uploadSelfMaterial();
      await teacherPage.pos.teacherHomePage.verifyDownloadDeleteIcon();
    });
    await test.step(`Verify teacher Upload materials for student on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.uploadMaterialsForStudent();
      await teacherPage.pos.teacherHomePage.verifyDownloadDeleteIcon();
    });
    await test.step(`Verify teacher Upload again materials for self & student on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.uploadSelfMaterial();
      await teacherPage.pos.teacherHomePage.uploadMaterialsForStudent();
    });
    await test.step(`Verify file uploaded on pre-joining teacher page`, async () => {
      await teacherPage.pos.teacherEnterClassPage.verifyUploadFileInPreClasPage(meetingInfo.meetingId, meetingInfo.className);
    });
    await test.step("Verify Teacher joining the meeting", async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherClickOnGoLiveButton();
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await expect(teacherPage.pos.teacherLiveClassPage.studentYetToJoinText, "verify student yet to join text is visible").toBeVisible();
      studentCount = await teacherPage.pos.teacherLiveClassPage.getStudentCount();
    })
    await test.step(`Verify file uploaded on pre-joining student page`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.homePage.clickOnJoinNowButton(meetingInfo.className, testData.room.name);
      await studentPage.pos.homePage.verifyFileUploaded(meetingInfo.className);
    });

    //student joinig the meet
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.pos.homePage.clickOnJoinClassButton(meetingInfo.className);
      await studentPage.pos.studentEnterClassPage.navigateToLiveClass(meetingInfo.className);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);

    });

    await test.step(`Validate teacher page after student joined`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPageAfterStudentJoins(testData.student.name);
      const studentCountJoins = await teacherPage.pos.teacherLiveClassPage.getStudentCount();
      expect(parseInt(studentCountJoins!)).toBeGreaterThan(parseInt(studentCount));
    });

    await test.step("Verify teacher is able to share screen", async () => {
      await expect(teacherPage.pos.teacherLiveClassPage.shareScreenOption, "Verify share screen buttno is visible").toBeVisible();
      await expect(teacherPage.pos.teacherLiveClassPage.studentCard, "Verify joined student is visible").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.shareScreenOption.click();


      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("noLimits");
      await teacherPage.pos.teacherLiveClassPage.verifyEndPoll();

      await teacherPage.pos.teacherLiveClassPage.clickOnStudentListButton(testData.student.name);

      // // Navigate to attendance view page
      // await teacherPage.pos.teacherLiveClassPage.clickOnAttendanceViewOptionAfterScreenShare();
      // await teacherPage.pos.teacherLiveClassPage.veriyAttendanceViewPage();


      // // Navigate to grid view page
      // await teacherPage.pos.teacherLiveClassPage.verifyAndClickGridViewOptionAfterScreenShare();



      // toggle prenseter and gird view
      await expect(teacherPage.pos.teacherLiveClassPage.togglePresenterAndGridViewInput, "Verify toggle presenter and grid view").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.togglePresenterAndGridViewInput.check();
      await expect(teacherPage.pos.teacherLiveClassPage.studentCard, "Verify student is visible").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.togglePresenterAndGridViewInput.check();

      await expect(teacherPage.pos.teacherLiveClassPage.stopSharingText, "Verify stop sharing button is visible").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.stopSharingText.click();
    })
  });

  /*Verify attendance view page*/
  // test('Verify attendance insights & grid view page on layout option', async ({ meetingInfo, teacherPage }) => {

  //   //Navigate to Teacher live class using meetingId
  //   await test.step(`Teacher joining the meeting`, async () => {
  //     await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
  //   });

  //   //validate teacher class page
  //   await test.step(`Verify teacher live class after joining`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
  //   });

  //   //Navigate to attendance view page
  //   await test.step(`verify attendance view page`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.clickOnAttendanceViewOption();
  //     await teacherPage.pos.teacherLiveClassPage.veriyAttendanceViewPage();
  //   });
  //   //Navigate to grid view page
  //   await test.step(`Teacher select back grid view`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.verifyAndClickGridViewOption();
  //     await teacherPage.pos.teacherLiveClassPage.veriyGridViewPage();
  //   });

    //Navigate to grid view page
  //   await test.step(`Teacher switching to attendance/grid view using key`, async () => {
  //     await teacherPage.pos.teacherLiveClassPage.veriySwitchingFronGridViewToAttendanceView();
  //     await teacherPage.pos.teacherLiveClassPage.veriyAttendanceViewPage();
  //     await teacherPage.pos.teacherLiveClassPage.veriySwitchingFronAttendanceViewToGridView();
  //     await teacherPage.pos.teacherLiveClassPage.veriyGridViewPage();
  //   });


  // });

  /*Verify mic, video on/off from teacher and student side */
  test('Verify mic, video on/off from teacher and student side', async ({ meetingInfo, teacherPage, studentPage, testData }) => {
    test.setTimeout(140000);
    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }
      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
    });

    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();
    });

    //check mic, video on/off from teacher side
    await test.step(`Verify mic, video on/off from teacher side`, async () => {
      await teacherPage.pos.teacherLiveClassPage.veriyMicVideoOnAndOffFromTeacher();
    });

    // Teacher Bring on stage Grid view from teacher live class.
    await test.step(`Verify bring on stage Grid view by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnBringOnStage(testData.student.name);
    });

    //check mic, video on/off from Student side
    await test.step(`verify mic, video on/off from student side`, async () => {
      await studentPage.pos.studentLiveClassPage.veriyMicVideoOnAndOffFromStudent();
    });
  });

  test('Verify admin is able to create offline class and student able to see the schedule class', async ({ adminPage, studentPage, teacherPage, testData }) => {
    await test.setTimeout(100000);
    const className = "Offline Class " + StringUtils.generateRandomString(5);
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_OFFLINE";
    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
    const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 180, false, timezone);
    const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 300, false, timezone);

    /*Cancelling all the classes*/
    await test.step(`Cancelling all the classes`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });

    let teacherInfo;
    await test.step(`Schedule class `, async () => {
      teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 920, 5, className);
      console.log("meetingid" + teacherInfo.meetingId);
    });

    await test.step(`Verify offline class from teacher on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
      await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);
      await teacherPage.pos.teacherHomePage.verifyClassMode("offline");
      await teacherPage.pos.teacherHomePage.validateScheduledClassDetailsOnTeacherSide(teacherInfo.className, testData.batch.name, testData.room.name);
      await expect(teacherPage.pos.teacherHomePage.joinClassButton, "verify join class button is not present for offline class").toBeHidden();
    });

    await test.step(`Verify offline class from student page`, async () => {
      await studentPage.login();
      await studentPage.reload();
      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.homePage.verifyJoinNowButtonNotPresent(teacherInfo.className, testData.room.name);
    });


    /*Cancelling class scheduling*/
    await test.step(`Cancelling the created schedule class ${testData.teacher}`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });
  });

  test('Verify admin is able to create live telecast class and student able to see the schedule class', async ({ adminPage, studentPage, teacherPage, testData }) => {
    test.setTimeout(140000);

    const className = "Live-Telecast Class " + StringUtils.generateRandomString(5);
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_LIVE_TELECAST";
    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
    const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 180, false, timezone);
    const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 300, false, timezone);

    /*Cancelling all the classes*/
    await test.step(`Cancelling all the classes`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });

    let teacherInfo;
    await test.step(`Schedule class `, async () => {
      teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 920, 5, className);
    });

    await test.step(`Verify live telecast class joining from teacher on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
      await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);
      await teacherPage.pos.teacherHomePage.verifyClassMode("liveTelecast");
      await teacherPage.pos.teacherHomePage.validateScheduledClassDetailsOnTeacherSide(teacherInfo.className, testData.batch.name, testData.room.name);
      await expect(teacherPage.pos.teacherHomePage.joinClassButton, "verify join class button is present for live telecast class").toBeVisible();
      await teacherPage.pos.teacherHomePage.clickOnJoinClassButton();
      await teacherPage.pos.teacherEnterClassPage.naviageToLiveClass(teacherInfo.className);
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(teacherInfo.className);
    });

    await test.step(`Verify live telecast class joining from student page`, async () => {
      await studentPage.login();
      await studentPage.reload();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.homePage.clickOnJoinNowButton(teacherInfo.className, testData.room.name);
      await studentPage.pos.homePage.clickOnJoinClassButton(teacherInfo.className);
      await studentPage.pos.studentEnterClassPage.navigateToLiveClass(teacherInfo.className);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(teacherInfo.className);
    });

    /*Cancelling class scheduling*/
    await test.step(`Cancelling the created schedule class ${testData.teacher}`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });
  });

  test('Verify admin is able to create recording only class and student able to see the schedule class', async ({ adminPage, studentPage, teacherPage, testData }) => {
    const className = "Recording Class " + StringUtils.generateRandomString(5);
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_RECORDING_ONLY";
    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
    const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 180, false, timezone);
    const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 300, false, timezone);

    /*Cancelling all the classes*/
    await test.step(`Cancelling all the classes`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });

    let teacherInfo;
    await test.step(`Schedule class `, async () => {
      teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 920, 5, className);
    });

    await test.step(`Verify recording only class joining from teacher on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
      await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);
      await teacherPage.pos.teacherHomePage.verifyClassMode("recordingOnly");
      await teacherPage.pos.teacherHomePage.validateScheduledClassDetailsOnTeacherSide(teacherInfo.className, testData.batch.name, testData.room.name);
      await expect(teacherPage.pos.teacherHomePage.joinClassButton, "verify join class button is present for recording only class").toBeVisible();
      await teacherPage.pos.teacherHomePage.clickOnJoinClassButton();
      await teacherPage.pos.teacherEnterClassPage.naviageToLiveClass(teacherInfo.className);
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(teacherInfo.className);
    });

    await test.step(`Verify recording only class from student page`, async () => {
      await studentPage.login();
      await studentPage.reload();
      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.homePage.verifyJoinNowButtonNotPresent(teacherInfo.className, testData.room.name);
    });

    /*Cancelling class scheduling*/
    await test.step(`Cancelling the created schedule class ${testData.teacher}`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });
  });

  /*Verify Bring ON/OFF Stage Grid view*/
  test('Verify bring on/off stage grid view and search view- bring on stage & hand raise', async ({ meetingInfo, studentPage, teacherPage, testData }) => {
    PlaywrightUtils.listenWebsockets(studentPage, "student");
    PlaywrightUtils.listenWebsockets(teacherPage, "teacher");
    test.setTimeout(190000);

    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();
    });

    // // Teacher bring on stage Grid view and verify from student live class.
    // await test.step(`Verify teacher is joined in student live class`, async () => {
    //   await studentPage.pos.studentLiveClassPage.verifyTeacherJoinClassInStudent();
    // });

    // Teacher Bring on stage Grid view from teacher live class.
    await test.step(`Verify bring on stage Grid view by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnBringOnStage(testData.student.name);
    });

    // Teacher bring on stage Grid view and verify from student live class.
    await test.step(`Verify bring on stage grid view by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnUnMuteVideoForBringOnStage();
    });

    // Teacher bring off stage grid view and verify from teacher live class.
    await test.step(`Verify bring off stage by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBringOnStage();
      await teacherPage.pos.teacherLiveClassPage.verifyBringOffStageFromTeacher();
    });

    // Teacher in search view with student and verify from teacher live class.
    await test.step(`Verify search view bring on stage in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.searchStudent(testData.student.name);
      await teacherPage.pos.teacherLiveClassPage.searchStudentBringOnstage();
    });

    // Teacher bring on stage search view and verify from student live class.
    await test.step(`Verify bring on stage search view by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnUnMuteVideoForBringOnStage();
    });

    // Teacher bring off stage search view and verify from teacher live class.
    await test.step(`Verify bring off stage search view by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBringOnStage();
      await teacherPage.pos.teacherLiveClassPage.verifyBringOffStageFromTeacher();
    });

    // Click and verify raise hand from Student live class.
    await test.step(`Verify raise hand for search view in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnRaiseHandFromStudent();
    });

    // verify search student hand from teacher live class.
    await test.step(`Verify search view hand raise by student in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.searchStudent(testData.student.name);
      await teacherPage.pos.teacherLiveClassPage.searchStudentLowerHand();
    });

    // verify lowered hand by teacher in Student live class.
    await test.step(`Verify raise hand in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyHandRaised();
    });
  });


  /*Verify Haind Raise*/
  test('Verify hand raise grid view', async ({ meetingInfo, studentPage, testData, teacherPage }) => {
    test.setTimeout(140000);

    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();

    });

    // // Teacher bring on stage Grid view and verify from student live class.
    // await test.step(`Verify teacher is joined in student live class`, async () => {
    //   await studentPage.pos.studentLiveClassPage.verifyTeacherJoinClassInStudent();
    // });

    // Click and verify raise hand from Student live class.
    await test.step(`Verify raise hand in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnRaiseHandFromStudent();
    });

    // Lower All Hand and verify from teacher live class.
    await test.step(`Verify lower all hands by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnLowerHandButton();
    });

    // verify lowered hand by teacher in Student live class.
    await test.step(`Verify lowered hand in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyHandRaised();
    });

    // // verify full/exit screen in Student live class.
    // await test.step(`Verify full/exit screen in student live class`, async () => {
    //   await studentPage.pos.studentLiveClassPage.verifyFullScreenLiveClass();
    //   await studentPage.pos.studentLiveClassPage.verifyExitFullScreenLiveClass();
    // });


  });

  // if (env == 'stage') {
  test('Create Home work and verify from teacher side', {
    tag: '@mobile'
  }, async ({ adminPage, teacherPage, testData, isMobile }) => {
    test.setTimeout(80000);
    const className = "Automation " + StringUtils.generateRandomString(5);
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_ONLINE";
    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
    const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 180, false, timezone);
    const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 300, false, timezone);


    /*Cancelling all the classes*/
    await test.step(`Cancelling all the classes`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });

    let teacherInfo;
    await test.step(`Schedule class `, async () => {
      teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 920, 5, className);
    });

    /*Create homework and publish from teacher side*/
    await test.step(`Create homework and publish from teacher side and verify homework is created`, async () => {
      await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();

      if (!isMobile) {
        await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      }

      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);
      await teacherPage.pos.teacherHomePage.navigateToCreateHomeWorkPage(teacherInfo.className);
      await expect(teacherPage).toHaveURL(/.*edit*/);
      await teacherPage.pos.createHomeworkPage.createHomework();
      await teacherPage.pos.createHomeworkPage.publishTheHomework();
      await expect(teacherPage).toHaveURL(teacherPage.pos.teacherHomePage.url);

      if (!isMobile) {
        await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      }

      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);

      if (isMobile) {
        await slowExpect(teacherPage.pos.teacherHomePage.arrowDown(teacherInfo.className), "Verify arrow down button is visible").toBeVisible();
        await teacherPage.pos.teacherHomePage.arrowDown(teacherInfo.className).click();
      }
      await expect(teacherPage.pos.teacherHomePage.viewHomeworkButton, "verify view homework button is visible").toBeVisible();
    });

    /*Cancelling class scheduling*/
    await test.step(`Cancelling the created schedule class ${testData.teacher}`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });

  });

  if (env == 'stage') {
    test('Validate v2 class scheduling creation', async ({ adminPage, teacherPage, studentPage, testData }) => {
      test.setTimeout(80000);
      const className = "Automation " + StringUtils.generateRandomString(3);
      const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
      const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
      const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm tt", 1500, true, timezone);  // 25 minutes (25 * 60 = 1500 seconds)
      const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm tt", 1800, true, timezone);    // 30 minutes (30 * 60 = 1800 seconds)

      /*Cancelling all the classes*/
      await test.step(`Cancelling all the classes`, async () => {
        await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
      });

      /*Navigate to Scheduling page from Internal page*/
      await test.step(`Navigate from teacher home page to internal user home page`, async () => {
        await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.pos.teacherHomePage.navigateToInternalUser();
      });

      /*Navigate to class Scheduling page and validate page details from Internal page*/
      await test.step('Verify the class scheduling page details', async () => {
        await adminPage.pos.internalUserHomePage.navigateToV2SchedulingPage();
        await expect(adminPage).toHaveURL(/.*class-management*/);
        await adminPage.pos.scheduleClassPage.validateScheduleClassPage();
      });
      //* Below code to switch page context to new tab *//
      const scheduleClassBatchPage = await adminPage.pos.scheduleClassPage.navigateToCreateSchedulePageV2(testData.batch.name);

      /*Enter required feild and create class scheduling*/
      await test.step(`Verify the create scheduling page details ${testData.teacher}`, async () => {
        await adminPage.pos.scheduleClassPage.createSchedulingV2Class(className, testData.teacher.name, testData.room.name, currentDate, startTime, endTime);
        await adminPage.pos.scheduleClassPage.clickOnPublishClass();
        await teacherPage.reload();
        await teacherPage.pos.teacherHomePage.goto();
        await teacherPage.pos.teacherHomePage.validateTeacherCalender();
        await expect(teacherPage.pos.teacherHomePage.scheduledClassName(className), "verify Schedule className is visible").toBeVisible();
        await teacherPage.pos.teacherHomePage.clickOnClassScheduled(className);
      });
      /*Cancelling class scheduling*/
      await test.step(`Cancelling the created schedule class ${testData.teacher}`, async () => {
        await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
      });
    });
  }

  // }

  // //  * schdeule clash and cancelling * /
  // test('Validate schedule class with multiple batch', async ({ adminPage, testData, teacherPage }) => {
  //   const className = "Multiple Batch " + StringUtils.generateRandomString(3);
  //   const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
  //   const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
  //   const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 120, false, timezone);
  //   const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 240, false, timezone);
  //   let scheduleClassBatchPage;
  //   const classType = "LIVE";
  //   const classMode = "SCHEDULE_MODE_ONLINE";
  //   const batchCode = "01MN-55P-AA1";

  //   let teacherInfo;

  //   /*Cancelling the created schedule class */
  //   await test.step(`creating live class `, async () => {
  //     //schedule class before 20 mins to starts
  //     teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 20, 25, className);
  //   });

  //   /*Navigate to Scheduling page from Internal page*/
  //   await test.step(`Navigate from teacher home page to internal user home page`, async () => {
  //     await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url);
  //     await adminPage.pos.teacherHomePage.navigateToInternalUser();
  //   });

  //   /*Navigate to class Scheduling page and validate page details from Internal page*/
  //   await test.step('Verify the class scheduling page details,', async () => {
  //     await adminPage.pos.internalUserHomePage.navigateToSchedulingPage();
  //     await adminPage.pos.scheduleClassPage.validateScheduleClassPage();
  //   });
  //   //* Below code to switch page context to new tab *//
  //   /*Navigate to class Scheduling page and validate page details from Internal page*/
  //   await test.step('Navigate to class scheduling page,', async () => {
  //     scheduleClassBatchPage = await adminPage.pos.scheduleClassPage.navigateToCreateSchedulePage(testData.batch.name);
  //   });

  //   /*Enter required feild and create class scheduling*/
  //   await test.step(`Verify multiple batch class creation`, async () => {
  //     await scheduleClassBatchPage.validateCreateSchedulePage();
  //     await scheduleClassBatchPage.addMutipleBatch(batchCode);
  //   });

  //   await test.step(`Verify added batch to a scheduled class from teacher on calender page`, async () => {
  //     await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
  //     await teacherPage.pos.teacherHomePage.validateTeacherCalender();
  //     await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);
  //     await teacherPage.pos.teacherHomePage.verifyClassMode("online");
  //     await teacherPage.pos.teacherHomePage.validateScheduledClassDetailsOnTeacherSide(teacherInfo.className, testData.batch.name, testData.room.name);
  //     await expect(teacherPage.pos.teacherHomePage.scheduledBatchCode(batchCode), "verify added batch code is present for online class").toBeVisible();
  //   });
  //   /*Cancelling the created schedule class */
  //   await test.step(`Cancelling the created schedule class `, async () => {
  //     await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
  //   });

  // });

  /**
   * @info  corrected spelling
   */
  /*Verify Poll student responses*/
  test('Verify poll student responses', async ({ meetingInfo, teacherPage, studentPage, testData }) => {
    PlaywrightUtils.listenWebsockets(studentPage, "student");
    PlaywrightUtils.listenWebsockets(teacherPage, "teacher");
    test.setTimeout(180000);
    const pollOption = '1';
    const pollType = '1 option';
    //Navigate to Teacher live class using meetingId
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.waitForLoadState('networkidle');
      await studentPage.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);
    });

    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await teacherPage.waitForTimeout(2000);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();
    });

    //create poll as a no limits until class duration
    await test.step(`Create polls[4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("noLimits");
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify teacher end poll [4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyEndPoll();
    });

    //verify student responses tab
    await test.step(`Verify student responses tab[4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyStudentResponsesTab();
    });

    //verify student responses tab
    await test.step(`Verify student not responses tab and bos on/off[4 polls - no limit]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyStudentNotResponses(testData.student.name);
      await teacherPage.pos.teacherLiveClassPage.verifyStudentNotResponsesBOS(testData.student.name);
    });

    // Teacher bring on stage chat and verify from student live class.
    await test.step(`Verify bring on stage by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnUnMuteVideoForBringOnStage();
    });

    // Teacher bring off stage chat and verify from teacher live class.
    await test.step(`Verify bring off stage by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBringOnStage();
      await teacherPage.pos.teacherLiveClassPage.verifyBringOffStageFromTeacher();
      await teacherPage.pos.teacherLiveClassPage.responsesClosePollsPopUp();
      await teacherPage.pos.teacherLiveClassPage.closePollsPopUp();
      await teacherPage.waitForTimeout(1000) // wait is required to close the poll web socket
    });


    //create poll as a no limits until class duration
    await test.step(`Create polls [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("oneMin");
    });

    //verify the student is able to submit the poll
    await test.step(`Verify the student polls is able to submit poll[4 polls -1 min]`, async () => {
      await studentPage.pos.studentLiveClassPage.submitThePoll(pollOption);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls from the teacher side and end poll [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(pollOption);
    });

    //verify the student is end the poll
    await test.step(`Verify the student polls is ended [4 polls -1 min]`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls from the teacher side and end poll [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyStudentResponses(testData.student.name, pollType);
    });

    //verify student answered poll from the teacher side and end poll
    await test.step(`Verify student answered polls bring on stage from the teacher side [4 polls -1 min]`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyStudentResponsesBOS(testData.student.name, pollType);
    });

    // Teacher bring on stage chat and verify from student live class.
    await test.step(`Verify bring on stage by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnUnMuteVideoForBringOnStage();
    });

    // Teacher bring off stage chat and verify from teacher live class.
    await test.step(`Verify bring off stage by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBringOnStage();
      await teacherPage.pos.teacherLiveClassPage.verifyBringOffStageFromTeacher();
    });
  });

  /*Verify Student list*/
  test('Verify teacher is able to see and validate the student list of BOS', async ({ meetingInfo, studentPage, testData, teacherPage }) => {
    test.setTimeout(140000);
    const invalidName = "AutomationInvalid";

    //Navigate to Teacher live class using meetingId
    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
      await studentPage.pos.studentLiveClassPage.validateStudentLiveClassPage(meetingInfo.className);
    });

    //validate teacher class page
    await test.step(`Verify teacher live class after joining`, async () => {
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(meetingInfo.className);
      await customExpect(20000)(teacherPage.pos.teacherLiveClassPage.studentNameGridView(testData.student.name), "verify student name is visible on Gride view").toBeVisible();
    });

    //validate student list tab
    await test.step(`Verify student list tab once clicked on student button`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnStudentListButton(testData.student.name);
    });

      //validate raised hand student list tab
      await test.step(`Verify student list bos `, async () => {
        await teacherPage.pos.teacherLiveClassPage.verifyStudentListBOS(testData.student.name);
      });

    // Teacher bring on stage from student list and verify from student live class.
    await test.step(`Verify bring on stage by teacher in student live class`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnUnMuteVideoForBringOnStage();
    });

    // Teacher bring off stage from student list and verify from teacher live class.
    await test.step(`Verify bring off stage by teacher in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyBringOnStage();
      await teacherPage.pos.teacherLiveClassPage.verifyStudentListBOffStage(testData.student.name);
    });

    // Teachersearch participants from student list and verify from teacher live class.
    await test.step(`Verify search participants from student list in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyStudentListSearch(testData.student.name);
      await teacherPage.pos.teacherLiveClassPage.verifyStudentListInvalidSearch(invalidName);
    });


    //click  and verify student leave class
    await test.step(`Student leaving the meeting`, async () => {
      await studentPage.pos.studentLiveClassPage.clickOnLeaveClassButton();
      await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText.first(), 'verifying "explore study materials" text should be visible').toBeVisible();
    });

    // Teacher offline from student list and verify from teacher live class.
    await test.step(`Verify student offline from student list in teacher live class`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyStudentListOffLine(testData.student.name);
    });



  });

  // test('Verify teacher able create homework for past classes and students able attempt it and view pending and review sections', async ({ adminPage, studentPage, teacherPage, testData }) => {
  //   test.setTimeout(180000);
  //   const className = "Automation " + StringUtils.generateRandomString(5);
  //   const classType = "LIVE";
  //   const classMode = "SCHEDULE_MODE_ONLINE";
  //   /* Date creation to schedule a class for yesterday */
  //   const { startTimestamp, endTimestamp } = getStartAndEndTimestamps();
  //   console.log('Start Timestamp:', startTimestamp);
  //   console.log('End Timestamp:', endTimestamp);
  //   const today = new Date(); // Get today's date
  //   const dayOfWeek = today.getDay(); // Get the day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  //
  //   /*Cancelling all the classes*/
  //   await test.step(`Cancelling all existing classes to avoid clash with API`, async () => {
  //     await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.name, startTimestamp, endTimestamp);
  //   });
  //
  //   let teacherInfo;
  //   await test.step(`Schedule class with past date using API `, async () => {
  //     teacherInfo = await adminPage.apis.scheduleManagement.createPastDateMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, "", testData.teacher.id, testData.teacher.name, 2, 5, className);
  //   });
  //
  //   /*Create homework and publish from teacher side*/
  //   await test.step(`Create homework and publish from teacher side and verify homework is created`, async () => {
  //     await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
  //     await teacherPage.waitForLoadState('networkidle'); // to handle network calls to load
  //     await teacherPage.waitForLoadState('domcontentloaded');
  //     await teacherPage.pos.teacherHomePage.validateTeacherCalender();
  //     /* If condition added to handle if teacher calander starts with sunday */
  //     if (dayOfWeek === 0) {
  //       // Execute this block if today is Sunday
  //       await teacherPage.pos.teacherHomePage.prevWeekButton.click();
  //       await teacherPage.waitForLoadState('networkidle'); // to handle network calls to load
  //       await teacherPage.waitForLoadState('domcontentloaded');
  //       await slowExpect(teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className), "verify Schedule className is visible").toBeVisible();
  //       await teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className).click();
  //     } else {
  //       await slowExpect(teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className), "verify Schedule className is visible").toBeVisible();
  //       await teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className).click();
  //     }
  //     await teacherPage.pos.teacherHomePage.navigateToCreateHomeWorkPage(teacherInfo.className);
  //     await teacherPage.pos.createHomeworkPage.createHomework();
  //     await teacherPage.pos.createHomeworkPage.publishTheHomeworkForPastSchedule();
  //     await expect(teacherPage).toHaveURL(teacherPage.pos.teacherHomePage.url);
  //     await teacherPage.waitForLoadState('networkidle'); // to handle network calls to load
  //     await teacherPage.waitForLoadState('domcontentloaded');
  //     await teacherPage.pos.teacherHomePage.validateTeacherCalender();
  //     /* If condition added to handle post homework creation to validate view homework in calendar */
  //     if (dayOfWeek === 0) {
  //       // Execute this block if today is Sunday
  //       await teacherPage.pos.teacherHomePage.prevWeekButton.click();
  //       await teacherPage.waitForLoadState('networkidle'); // to handle network calls to load
  //       await teacherPage.waitForLoadState('domcontentloaded');
  //       await slowExpect(teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className), "verify Schedule className is visible").toBeVisible();
  //       await teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className).click();
  //     } else {
  //       await slowExpect(teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className), "verify Schedule className is visible").toBeVisible();
  //       await teacherPage.pos.teacherHomePage.scheduledClassName(teacherInfo.className).click();
  //     }
  //     await slowExpect(teacherPage.pos.teacherHomePage.viewHomeworkButton, "Verify view homework button is visible").toBeVisible();
  //   });
  //
  //   await test.step(`Verify student is able to login and navigate to homework section and validate`, async () => {
  //     await studentPage.login();
  //     await studentPage.waitForTimeout(3000); // to handle homework should reflect to student
  //     await studentPage.pos.homePage.navigateToHomeworkPage();
  //     await studentPage.waitForLoadState('networkidle') // wait to load the network calls to complete
  //     await studentPage.pos.homeworkPage.validateHomeworkPage();
  //   });
  //
  //   await test.step(`Verify student is able to attempt homework and submit test with available options`, async () => {
  //     await test.step(`Verify homework instructions page and questions homepage`, async () => {
  //       await studentPage.pos.homeworkPage.startHomeworkWithInstructionsPage();
  //       await studentPage.waitForLoadState('networkidle') // wait to load the network calls to complete
  //       await studentPage.waitForLoadState('domcontentloaded')
  //       await studentPage.pos.homeworkPage.VerifyQuestionsPage();
  //     });
  //
  //     await test.step(`Verify question answered and marked for review state and answered state`, async () => {
  //       await expect(studentPage.pos.homeworkPage.question1, "Verify question 1 is visible").toBeVisible();
  //       /* If condition added to handle, if homework first option is fill in the blank instead of option */
  //       if (await studentPage.pos.homeworkPage.integerQuestionType.isVisible()) {
  //         await expect(studentPage.pos.homeworkPage.markForReviewButton, "Verify mark For Review Button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.markForReviewButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question2, "verify question 2 is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.question1BrickMarkedForReview, "Verify question 1 is marked for reivew is visible").toBeVisible();
  //         const option1Element = studentPage.pos.homeworkPage.option1RadioButton.or(studentPage.pos.homeworkPage.hwprodOption1).locator('visible=true').first();
  //         await expect(option1Element, "Verify question 1 option is visible").toBeVisible();
  //         await option1Element.click();
  //         await expect(studentPage.pos.homeworkPage.saveAndNextButton, "Verify save and next button is visible").toBeVisible();
  //         await studentPage.pos.homeworkPage.saveAndNextButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question3, "Verify question 3 is visible").toBeVisible();
  //         await slowExpect(studentPage.pos.homeworkPage.question2BrickAnswered, "Verify side menu question 2 answered is visible").toBeVisible();
  //       } else {
  //         const option1Element = studentPage.pos.homeworkPage.option1RadioButton.or(studentPage.pos.homeworkPage.hwprodOption1).locator('visible=true').first();
  //         await expect(option1Element, "Verify question 1 option is visible").toBeVisible();
  //         await option1Element.click();
  //         await expect(studentPage.pos.homeworkPage.markForReviewButton, "Verify mark For Review Button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.markForReviewButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question2, "Verify question 2 is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.question1BrickAnsweredMarkedForReview, "Verify question 1 is answered and mark for reivew is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.questionBackButton, "Verify question back button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.questionBackButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question1, "Verify question 1 is visible").toBeVisible();
  //         await expect(option1Element, "Verify question 1 option is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.saveAndNextButton, "Verify save and next button is visible").toBeVisible();
  //         await studentPage.pos.homeworkPage.saveAndNextButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question2, "Verify question 2 is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.question1BrickAnswered, "Verify side menu question 1 answered is visible").toBeVisible();
  //       }
  //     });
  //
  //     await test.step(`Verify question marked for review state and not answered state`, async () => {
  //       if (await studentPage.pos.homeworkPage.question3.isVisible()) {
  //         await expect(studentPage.pos.homeworkPage.questionBackButton, "Verify question back button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.questionBackButton.click();
  //         await expect(studentPage.pos.homeworkPage.clearResponseButton, "Verify clear response button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.clearResponseButton.click();
  //         await expect(studentPage.pos.homeworkPage.saveAndNextButton, "Verify save and next button is visible").toBeVisible();
  //         await studentPage.pos.homeworkPage.saveAndNextButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question3, "Verify question 3 is visible").toBeVisible();
  //         await slowExpect(studentPage.pos.homeworkPage.question2BrickNotAnswered, "Verify side menu question 2 not answered is visible").toBeVisible();
  //       } else {
  //         await expect(studentPage.pos.homeworkPage.question2, "Verify question 2 is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.markForReviewButton, "Verify mark For Review Button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.markForReviewButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question3, "Verify question 3 is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.question2BrickMarkedForReview, "Verify question 2 is marked for review is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.questionBackButton, "Verify question back button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.questionBackButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question2, "Verify question 2 is visible").toBeVisible();
  //         await expect(studentPage.pos.homeworkPage.clearResponseButton, "Verify clear response button is enabled").toBeEnabled();
  //         await studentPage.pos.homeworkPage.clearResponseButton.click();
  //         await expect(studentPage.pos.homeworkPage.saveAndNextButton, "Verify save and next button is visible").toBeVisible();
  //         await studentPage.pos.homeworkPage.saveAndNextButton.click();
  //         await slowExpect(studentPage.pos.homeworkPage.question3, "Verify question 3 is visible").toBeVisible();
  //         await slowExpect(studentPage.pos.homeworkPage.question2BrickNotAnswered, "Verify side menu question 2 not answered is visible").toBeVisible();
  //       }
  //     });
  //
  //     await test.step(`Verify question marked for review and submit test`, async () => {
  //       await expect(studentPage.pos.homeworkPage.question3, "Verify question 3 is visible").toBeVisible();
  //       await expect(studentPage.pos.homeworkPage.markForReviewButton, "Verify mark For Review Button is enabled").toBeEnabled();
  //       await studentPage.pos.homeworkPage.markForReviewButton.click();
  //       await expect(studentPage.pos.homeworkPage.question3BrickMarkedForReview, "Verify question 3 is marked for review is visible").toBeVisible();
  //       await expect(studentPage.pos.homeworkPage.sideMenuSubmitTestButton, "Verify submit homework button is enabled").toBeEnabled();
  //       await studentPage.pos.homeworkPage.sideMenuSubmitTestButton.click();
  //     });
  //
  //     await test.step(`Verify homework summary and do final submit`, async () => {
  //       await studentPage.pos.homeworkSummaryPage.verifyHomeWorkSummaryPageAndFinalSubmit();
  //     });
  //   });
  //
  //   await test.step(`Verify student is able to view report and review answers`, async () => {
  //     await studentPage.pos.homeworkSummaryPage.viewReportButton.click();
  //     await studentPage.waitForLoadState('networkidle') // wait to load the network calls to complete
  //     await slowExpect(studentPage, "Verify after clicking view report navigated to homework insight url").toHaveURL(/.*homework-insight*/);
  //     await studentPage.pos.homeworkInsightPage.verifyHomeWorkSummaryPageAndFinalSubmit();
  //     await studentPage.pos.homeworkInsightPage.seeAnswersButton.click();
  //
  //     await slowExpect(studentPage, "Verify after clicking see answers navigated to homework review answers url").toHaveURL(/.*review-answers*/);
  //
  //     await studentPage.waitForLoadState('networkidle') // wait to load the network calls to complete
  //     await studentPage.pos.homeworkInsightPage.verifyReviewAnswers();
  //     await slowExpect(studentPage, "Verify after clicking back button navigated to homework insight url").toHaveURL(/.*homework-insight*/);
  //   });
  //
  //   await test.step(`Verify student is able to navigate to pending side menu and validate all options`, async () => {
  //     await slowExpect(studentPage.pos.homeworkPage.pendingHeading, "Verify pending heading in side menu is visible").toBeVisible();
  //     await studentPage.pos.homeworkPage.pendingHeading.click();
  //     await studentPage.waitForLoadState('networkidle') // wait to load the network calls to complete
  //     await slowExpect(studentPage, "Verify after clicking pending heading navigated to pending homework url").toHaveURL(/.*homework-pending/);
  //     await studentPage.pos.homeworkInsightPage.verifyPendingHomeworkExpolre();
  //       // await studentPage.pos.homeworkInsightPage.verifybacklogHomework();
  //
  //   });
  //
  // });

  if (env == 'prod') {
    test('Verify pop up for early join restriction for teacher and student', async ({ adminPage, studentPage, teacherPage, testData }) => {
      const className = "Automation " + StringUtils.generateRandomString(5);
      const classType = "LIVE";
      const classMode = "SCHEDULE_MODE_ONLINE";

      let teacherInfo;
      await test.step(`Schedule class with future date using API `, async () => {
        teacherInfo = await adminPage.apis.scheduleManagement.createNextDateMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, "", testData.teacher.id, testData.teacher.name, 2, 5, className);
      });

       //Navigate to Teacher live class using meetingId
       await test.step(`Teacher joining the meeting`, async () => {
        await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(teacherInfo.meetingId);
      });

      await test.step('Validate the popup on teacher page', async () => {
        await teacherPage.pos.teacherEnterClassPage.verifyEarlyJoinPopUp();
      })

      /* login for the student page*/
      await test.step(`Student joining the meeting`, async () => {
        await studentPage.login();
        await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(teacherInfo.meetingId);
      });

      await test.step('Validate the popup on student page', async () => {
        await studentPage.pos.studentEnterClassPage.verifyEarlyJoinPopUp();
      })
    });
  }

  test("Ensure doubts feature is enabled inside class (teacher's side)", async ({ studentPage, teacherPage, meetingInfo }) => {
    test.setTimeout(120000);
    const doubtMessage = "What is electron affinity?"
    const voteCount = 0

    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
    });

    await test.step(`Verify teacher can enable/disable the doubts `, async () => {
      await teacherPage.pos.teacherLiveClassPage.enableDisableDoubts();
      await expect(teacherPage.pos.teacherLiveClassPage.openDoubtsTab, "Verify open doubts tab is visible").toBeVisible();
      await expect(teacherPage.pos.teacherLiveClassPage.resolvedDoubtsTab, "Verify resolve doubts tab is visible").toBeVisible();
    });

    await test.step(`Verify the doubt is reflected on teacher's side`, async () => {
      await studentPage.pos.studentLiveClassPage.navigateToDoubtsSection();
      await studentPage.pos.studentLiveClassPage.studentSendsDoubt(doubtMessage);
      await teacherPage.pos.teacherLiveClassPage.doubtVisibleToTeacher(doubtMessage);
    });

    await test.step(`Verify teacher is able to block/unblock student from doubts section`, async () => {
      await teacherPage.pos.teacherLiveClassPage.blockStudentDoubts();
      await teacherPage.pos.teacherLiveClassPage.unblockStudentDoubts();
    });

    // await test.step(`Verify teacher is able to upvote any student's doubt`, async () => {
    //   await teacherPage.pos.teacherLiveClassPage.upvoteDoubt(voteCount);
    // });

    await test.step(`Verify teacher can resolve any student's doubt and it goes to resolved tab`, async () => {
      await teacherPage.pos.teacherLiveClassPage.resolveDoubt(doubtMessage);
    });
  });

  test("Ensure doubts feature is enabled inside class (student's side)", async ({ studentPage, teacherPage, meetingInfo }) => {
    await test.setTimeout(140000);
    const invalidDoubtMessage = "Lorem ipsum dolor sit amet.";
    const doubtMessage = "What is electron affinity?";
    const chatMessage = "What is gibbs free energy?";
    const voteCount = 0;

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
    });

    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    await test.step(`Verify doubts are disabled for student`, async () => {
      await studentPage.pos.studentLiveClassPage.navigateToDoubtsSection();
      await slowExpect(studentPage.pos.studentLiveClassPage.doubtsDisabledText, "verify doubts disabled text is visible").toBeVisible();
    })

    await test.step(`Verify doubts UI from student side`, async () => {
      await teacherPage.pos.teacherLiveClassPage.enableDisableDoubts();
      await studentPage.pos.studentLiveClassPage.verifyDoubtsUI();
    });

    await test.step(`Verify toast message when student sends an invalid doubt`, async () => {
      await studentPage.pos.studentLiveClassPage.studentSendsDoubt(invalidDoubtMessage);
      await slowExpect(studentPage.pos.studentLiveClassPage.invalidDoubtToast, "Verify invalid doubt toast is visible").toBeVisible();
    });

    await test.step(`Verify student can send a valid doubt`, async () => {
      await studentPage.pos.studentLiveClassPage.studentSendsDoubt(doubtMessage);
    });

    await test.step(`Verify teacher is able to upvote any student's doubt`, async () => {
      await studentPage.pos.studentLiveClassPage.upvoteDoubt(voteCount);
    });

    await test.step(`Verify when teacher blocks the student then student cannot use the doubts feature`, async () => {
      await teacherPage.pos.teacherLiveClassPage.blockStudentDoubts();
      await slowExpect(studentPage.pos.studentLiveClassPage.blockedByTeacherText, "Verify blocked by teacher text is visible").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.unblockStudentDoubts();
    });

    await test.step(`Verify student can resolve their own doubts and it goes to resolved tab`, async () => {
      await studentPage.pos.studentLiveClassPage.resolveDoubt(doubtMessage);
    })

    await test.step('Verify asked doubt is present in My doubts tab', async () => {
      await studentPage.pos.studentLiveClassPage.myDoubts(doubtMessage);
    })

    await test.step('Verify when a student post any chat then that chat should not be shown on doubts tab and vice-versa', async () => {
      await teacherPage.pos.teacherLiveClassPage.enableChat();
      await studentPage.pos.studentLiveClassPage.verifyChatAndDoubtSeparation(chatMessage, doubtMessage);
    })
  });

  test('Verify admin is able to schedule class with SIP as class mode', async ({ adminPage, studentPage, teacherPage, testData }) => {
    test.setTimeout(140000);
    const className = "SIP" + StringUtils.generateRandomString(5);
    const classType = "LIVE";
    const classMode = "SCHEDULE_MODE_SIP";
    const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
    const startTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 180, false, timezone);
    const endTime = StringUtils.getCurrentTimeWithOffset("hh:mm", 300, false, timezone);
    const messageTeacher = "Test";
    const messageStudent = "Testing";
    const pollOption = '1';

    await test.step(`Cancelling all the classes`, async () => {
      await adminPage.apis.scheduleManagement.cancelAllMeeting(testData.batch.id, startTime, endTime);
    });

    let teacherInfo;
    await test.step(`Schedule class `, async () => {
      teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(classMode, classType, testData.batch.course, testData.batch.id, testData.batch.name, testData.room.id, testData.teacher.id, testData.teacher.name, 920, 5, className);
    });

    await test.step(`Verify live SIP class joining from teacher on calender page`, async () => {
      await teacherPage.pos.teacherHomePage.teacherNavigatingToCalenderPage();
      await teacherPage.pos.teacherHomePage.validateTeacherCalender();
      await teacherPage.pos.teacherHomePage.clickOnClassScheduled(teacherInfo.className);
      await teacherPage.pos.teacherHomePage.verifyClassMode("sip");
      await teacherPage.pos.teacherHomePage.validateScheduledClassDetailsOnTeacherSide(teacherInfo.className, testData.batch.name, testData.room.name);
      await expect(teacherPage.pos.teacherHomePage.joinClassButton, "verify join class button is present for SIP class").toBeVisible();
      await teacherPage.pos.teacherHomePage.clickOnJoinClassButton();
      await teacherPage.pos.teacherEnterClassPage.naviageToLiveClass(teacherInfo.className);
      await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(teacherInfo.className);
    });

    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(teacherInfo.meetingId);
    });

    await test.step('Verify teacher is able to pin/unpin student', async () => {
      await teacherPage.pos.teacherLiveClassPage.pinUnpinStudent(testData.student.name);
    })

    await test.step(`Verify teacher is able to send and receive chat`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickEnableChatAndSelectEveryoneOption();
      await teacherPage.pos.teacherLiveClassPage.teacherChatWithStudent(messageTeacher);
      await studentPage.pos.studentLiveClassPage.clickOnChatButton();
      await studentPage.pos.studentLiveClassPage.verifyChatFromTeacherToStudent(messageTeacher);
      await studentPage.pos.studentLiveClassPage.studentChatWithTeacher(messageStudent);
      await teacherPage.pos.teacherLiveClassPage.verifyChatFromStudentToTeacher(messageStudent);
      await expect(teacherPage.pos.teacherLiveClassPage.chatButton, "Verify chat button is visible").toBeVisible();
      await teacherPage.pos.teacherLiveClassPage.chatButton.click();
    });

    await test.step(`Create poll from teacher side`, async () => {
      await teacherPage.pos.teacherLiveClassPage.clickOnPolls();
      await teacherPage.pos.teacherLiveClassPage.selectPollOptions.click();
      await teacherPage.pos.teacherLiveClassPage.verifyPollsTimeOptions();
      await teacherPage.pos.teacherLiveClassPage.createPollsUsingTimeOption("noLimits");
    });


    await test.step(`Verify the student is able to submit the poll`, async () => {
      await studentPage.pos.studentLiveClassPage.submitThePoll(pollOption);
    });

    await test.step(`Verify student answered polls from the teacher side and end poll`, async () => {
      await teacherPage.pos.teacherLiveClassPage.verifyTheAnsweredPollAndEndPoll(pollOption);
    });

    await test.step(`Verify the student's poll is ended`, async () => {
      await studentPage.pos.studentLiveClassPage.verifyEndPoll();
    });
  })

  test("Ensure teacher is able to delete the doubts asked by student", async ({ studentPage, teacherPage, meetingInfo }) => {
    const doubtMessage = "What is electron affinity?";

    /* login for the student page*/
    await test.step(`Student joining the meeting`, async () => {
      await studentPage.login();
      // wait for class to start
      if (EnvUtils.getInstance().isProd()) {
        await studentPage.waitForTimeout(10000);
      }

      await studentPage.pos.studentEnterClassPage.studentJoiningTheClass(meetingInfo.meetingId);
    });

    await test.step(`Teacher joining the meeting`, async () => {
      await teacherPage.pos.teacherEnterClassPage.teacherJoingTheClass(meetingInfo.meetingId);
    });

    await test.step(`Verify doubts are disabled for student`, async () => {
      await studentPage.pos.studentLiveClassPage.navigateToDoubtsSection();
      await slowExpect(studentPage.pos.studentLiveClassPage.doubtsDisabledText, "verify doubts disabled text is visible").toBeVisible();
    })

    await test.step(`Verify doubts UI from student side`, async () => {
      await teacherPage.pos.teacherLiveClassPage.enableDisableDoubts();
      await studentPage.pos.studentLiveClassPage.verifyDoubtsUI();
    });

    await test.step(`Verify student can send a valid doubt`, async () => {
      await studentPage.pos.studentLiveClassPage.studentSendsDoubt(doubtMessage);
    });

    await test.step(`Verify teacher is able to delete any student's doubt`, async () => {
      await teacherPage.pos.teacherLiveClassPage.deleteStudentDoubt(doubtMessage);
    })

    await test.step(`Verify doubt deleted pop up from student side`, async () => {
      await studentPage.pos.studentLiveClassPage.page.waitForLoadState('networkidle');
      await slowExpect(studentPage.pos.studentLiveClassPage.doubtDeletedPopUp, "Verify doubt deleted pop up is visible").toBeVisible();
    })

  });

});