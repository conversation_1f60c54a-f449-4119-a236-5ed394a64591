import exp from 'constants';
import { test, expect } from '../fixtures/ui-fixtures'
import { PlaywrightUtils, StringUtils } from '../../commons/common-functions';

test.describe('E2E Sample Tests', {
  tag: ['@sample']
}, () => {
  // Sample test for web frontend
  test('Validate home page widgets', async ({ studentPage }) => {
    await test.step('Check if its not loggedIn', async () => {
      await expect(await studentPage.pos.preHomePage.isLoggedIn()).not.toBeTruthy();
    });
    await test.step('Check navigation menu', async () => {
      await expect(studentPage.pos.preHomePage.allenImage).toBeVisible();
    });
  });

  // Sample test for login with student account
  test('Validate mobile number login', async ({ studentPage, testData }) => {
    await test.step(`Login with mobile number ${testData.student.phone}`, async () => {
      await studentPage.pos.preHomePage.loginWithMobileNumber(testData.student.phone);
    });
  });

  // Sample test for login with login id and password if available
  test('Validate username login', async ({ studentPage, testData }) => {
    test.skip(testData.student.login === '', 'Skipping because login id is empty');
    await test.step(`Login with username ${testData.student.login}`, async () => {
      await studentPage.pos.preHomePage.loginWithUsername(testData.student.login, testData.student.password);
    });
  });

  // By default, student won't be signed in at the start of each test case whereas admin and teacher will always be signed in.
  // If you want to navigate web as a logged in student, use studentPage.login(), it will login via API and set the tokens for frontend
  test('Validate Username and Title', async ({ studentPage, testData }) => {
    await studentPage.login()
  });

  // Sample test for IC page
  test('Validate Admin home page', async ({ adminPage, testData }) => {
    await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
  });

  // Sample test that involve both user page and IC page
  test('Validate User and Admin home page', async ({ studentPage, adminPage, testData }) => {
    await studentPage.login()
    await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
  });

  // Sample test that needs live meeting to be tested
  test('Validate Meeting', async ({ meetingInfo }) => {
    await expect(meetingInfo.meetingId).not.toBeNull();
  });

  // Sample test for API calls
  test('Validate API response', async ({ studentPage }) => {
    await studentPage.login()
    const studentInfo = await studentPage.apis.user.studentInfo()
    expect(studentInfo.status()).toBe(200)
    expect(await studentInfo.json()).not.toBeNull()
  });

  test('Timestamp sample', async ({ page }) => {
    console.log(`Browser's Timezone:` + await page.evaluate(() => Intl.DateTimeFormat().resolvedOptions().timeZone));
    console.log(`System Timezone:` + Intl.DateTimeFormat().resolvedOptions().timeZone)

    // Get the browser's timezone using PlaywrightUtils and the current date and time using StringUtils
    const timezone = await PlaywrightUtils.getBrowserTimezone(page);
    const currentDateAndTime = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD hh:mm tt', 0, true, timezone);
  });
});

