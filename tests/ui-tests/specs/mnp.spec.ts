import { test, expect, slowExpect, customExpect } from '../fixtures/ui-fixtures'
import { StringUtils, PlaywrightUtils } from '../../commons/common-functions';
import { EnvUtils } from '../../commons/env-utilities';

test.describe('MNP UI Tests', {
    tag: ['@mnp', '@ui']
}, () => {
    // const currentEnv = EnvUtils.getInstance()
    // if (currentEnv.isStage()) {
    test('Verify user able to create and cancel the mnp schedule', async ({ adminPage }) => {
        test.setTimeout(180000);
        const studentMobileNumber = EnvUtils.getInstance().isStage() ? "1234526101" : "1234519008";
        const notesForStudent = "testing";
        const notesForMentor = "tester";

        /** 
         * @info Verify and navigate to mentorship schgedule page
         */
        await test.step(`Verify admin is able to navigate and validate the mentorship schedule page`, async () => {
            await adminPage.pos.teacherHomePage.navigateToInternalUser();
            await adminPage.pos.internalUserHomePage.navigateToMentorshipSchedulePage();
            await slowExpect(adminPage).toHaveURL(/.*schedule/);
        });

        /** 
         * @info Verify mentorship page and search feature
         */
        await test.step(`Verify mentorship page and search feature`, async () => {
            await adminPage.pos.scheduleMentorshipPage.validateScheduleMentorshipPage();
            await adminPage.pos.scheduleMentorshipPage.validateSearchFeature(studentMobileNumber);
            await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
        });

        /** 
         * @info Verify schedule meeting button in homepage
         */
        await test.step(`Verify schedule meeting button in homepage`, async () => {
            await expect(adminPage.pos.scheduleMentorshipPage.scheduleMeetingButton, 'Verify schedule meeting button is disabled').toBeDisabled();
            await adminPage.pos.scheduleMentorshipPage.formIdCheckbox(studentMobileNumber).check();
            await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
            await expect(adminPage.pos.scheduleMentorshipPage.scheduleMeetingButton, 'Verify schedule meeting button is enabled').toBeEnabled();
        });

        /** 
         * @info Verify and schedule meeting
         */
        await test.step(`Verify and schedule meeting in home page`, async () => {
            const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
            const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone);
            const startTimeForSchedule = StringUtils.getCurrentTimeWithOffset("hh:mm", 240, false, timezone);
            const endTimeForSchedule = StringUtils.getCurrentTimeWithOffset("hh:mm", 600, false, timezone);
            const startTime = Date.now();
            const timeout = 3000; // 3 seconds
            while (Date.now() - startTime < timeout) {
                if (!(await adminPage.pos.scheduleMentorshipPage.upcomingMeetingsNullValue.isVisible())) {
                    await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
                    await slowExpect(adminPage.pos.scheduleMentorshipPage.viewButton, "Verify view student button is visible").toBeVisible();
                    await adminPage.pos.scheduleMentorshipPage.viewButton.click();
                    await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
                    if (await adminPage.pos.scheduleMentorshipPage.addNotesButton.isDisabled()) {
                        await adminPage.pos.scheduleMentorshipPage.validateAndRescheduleClass(currentDate, startTimeForSchedule, endTimeForSchedule);
                        await adminPage.reload();
                        await slowExpect(adminPage.pos.scheduleMentorshipPage.viewButton, "Verify view student button is visible").toBeVisible();
                        await adminPage.pos.scheduleMentorshipPage.viewButton.click();
                        await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
                    }
                    await adminPage.pos.scheduleMentorshipPage.validateAndClassMeetingByAddingNotes(notesForStudent, notesForMentor);
                    await adminPage.reload();
                    await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
                    await adminPage.pos.scheduleMentorshipPage.formIdCheckbox(studentMobileNumber).check();
                    break; // Exit the loop if the condition is met
                }
                await new Promise(resolve => setTimeout(resolve, 200)); // Wait for 200ms before checking again
            }
            await adminPage.pos.scheduleMentorshipPage.validateAndScheduleMeetinginHomepage(currentDate, startTimeForSchedule, endTimeForSchedule);
            await slowExpect(adminPage.pos.scheduleMentorshipPage.meetingScheduledTitle, "Verify meeting scheduled title is visible").toBeVisible();
        });

        /** 
         * @info Validate and cancel meeting by adding notes
         */
        await test.step(`Verify and cancel meeting by adding notes`, async () => {
            await adminPage.reload();
            await slowExpect(adminPage.pos.scheduleMentorshipPage.upcomingMeetingsNullValue, "Verify upcoming class has value").not.toBeVisible();
            await slowExpect(adminPage.pos.scheduleMentorshipPage.viewButton, "Verify view student button is visible").toBeVisible();
            await adminPage.pos.scheduleMentorshipPage.viewButton.click();
            await slowExpect(adminPage.pos.scheduleMentorshipPage.apiLoader, 'Verify api loader is not visible').not.toBeVisible();
            await adminPage.pos.scheduleMentorshipPage.validateAndClassMeetingByAddingNotes(notesForStudent, notesForMentor);
        });

    });
    // }

    
    test('Verify teacher accessisible to mentorship class', async ({ adminPage, teacherPage, testData }) => {
        test.setTimeout(180000)
        const classType = 'MENTORSHIP'
        const classMode = 'SCHEDULE_MODE_ONLINE'
        let teacherInfo
        /*Cancelling the created schedule class */
        await test.step(`Create mentorship class `, async () => {
          teacherInfo = await adminPage.apis.scheduleManagement.createMeeting(
            classMode,
            classType,
            testData.batch.course,
            testData.batch.id,
            testData.batch.name,
            testData.room.id,
            testData.teacher.id,
            testData.teacher.name,
            920,
            5,
            'E2E Test',
          )
        })
  
        await test.step(`Teacher joining the meeting`, async () => {
          await teacherPage.pos.teacherEnterClassPage.teacherJoiningMentorshipSession(teacherInfo.meetingId)
          await teacherPage.pos.teacherLiveClassPage.validateTeacherLiveClassPage(teacherInfo.className)
        })
  
        /*Cancelling the created schedule class */
        await test.step(`Cancelling the created schedule class `, async () => {
          await adminPage.apis.scheduleManagement.cancelAllMeeting(teacherInfo.batchId, teacherInfo.startTimeStamp, teacherInfo.endTimeStamp)
        })
    })
});