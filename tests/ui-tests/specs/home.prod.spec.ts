// import { test, expect, Page, Locator } from '@playwright/test';
// import { assert } from 'console';

// // Define the base URL
// // const BASE_URL = 'https://web.allen-stage.in';
// // const BASE_URL = 'https://allen.in';

// test.describe('Home page UI Tests', {
//   tag: ['@home', '@ui']
// }, () => {
//   test('Allen footer links navigation', async ({ page, context }) => {

//     // Set test timeout to 3 minutes (180000 ms)
//     test.setTimeout(180000);

//     interface LinkInfo {
//       text: string;
//       href: string;
//       section: string;
//       index: number;
//     }

//     interface TestResult {
//       status: 'Passed' | 'Failed' | 'Error';
//       link: LinkInfo;
//       url?: string;
//       message?: string;
//     }

//     const normalizeText = (text: string): string => {
//       return text.toLowerCase().replace(/[^a-z0-9\s]+/g, '').trim().replace(/\s+/g, ' ');
//     };

//     // Validate if the link is valid, if the url contains the link text or section title
//     // Ex: Valid links:
//     // sectionTitle - "About", linkText - "MyExam EduBlogs", url - https://myexam.allen.in/ 

//     const isValidUrl = (url: string, linkText: string, sectionTitle: string): boolean => {
//       const urlLower = url.toLowerCase();
//       const linkTextNormalized = normalizeText(linkText);
//       const sectionTitleNormalized = normalizeText(sectionTitle);

//       // For general cases, check if any significant word from the link text or section title is in the URL
//       const words = [...linkTextNormalized.split(' '), ...sectionTitleNormalized.split(' ')];
//       const significantWords = words.filter(word => word.length > 1); // Consider words longer than 1 character

//       // Function to check if a word matches the URL, ignoring special characters
//       const wordMatchesUrl = (word: string, url: string) => {
//         return url.includes(word);
//       };

//       // Check if any significant word is in the URL
//       if (significantWords.some(word => wordMatchesUrl(word, urlLower))) {
//         return true;
//       }

//       // Special cases mapping with section and UI text as keys
//       const specialLinks: { [key: string]: (url: string) => boolean } = {
//         'About|Careers': (url) => url.includes('https://allendigital.hire.trakstar.com/'),
//         'About|Public notice': (url) => url.includes('https://cdn.digital.allen.ac.in/fe-bsite/ALLEN-Material-Copyright-Trademark-Public-Notice.pdf'),
//         'Help & Support|Terms & Conditions ': (url) => url.includes('tnc'),
//         'Popular goals|6th to 10th': (url) => url.includes('classes-6-10'),
//         'Courses|Distance learning': (url) => url.includes('dlp.allen.ac.in'),
//         'Centers|Kota': (url) => url.includes('https://www.allen.ac.in/'),
//         'Centers|Bangalore': (url) => url.includes('bengaluru'),
//         'Centers|More centres': (url) => url.includes('allen-centers'),
//         'Popular goals|JEE Mains': (url) => url.includes('jee-mains'),
//         'Exam information|JEE Mains': (url) => url.includes('jee-main/exam'),
//         'Exam information|Boards 10th': (url) => url.includes('cbse/class-10'),
//         'Exam information|Boards 12th': (url) => url.includes('cbse/class-12'),
//         'Exam information|JEE Mains Percentile Predictor': (url) => url.includes('jee-main-percentile-predictor')
//       };

//       // Check special cases
//       const specialCaseKey = `${sectionTitle}|${linkText}`;
//       const normalizedSpecialCaseKey = normalizeText(specialCaseKey);


//       for (const [caseText, caseCheck] of Object.entries(specialLinks)) {
//         if (normalizeText(caseText) === normalizedSpecialCaseKey) {
//           return caseCheck(urlLower);
//         }
//       }

//       return false;
//     };


//     await page.goto('/');

//     const footerLinks = await page.evaluate(() => {
//       const links: LinkInfo[] = [];
//       const sectionElements = document.querySelectorAll('footer > ul > li');
//       sectionElements.forEach((section) => {
//         const sectionTitle = section.querySelector('h3')?.textContent || '';
//         const sectionLinks = section.querySelectorAll('ul > li > a');
//         sectionLinks.forEach((a, index) => {
//           links.push({
//             text: (a as HTMLAnchorElement).textContent || '',
//             href: (a as HTMLAnchorElement).href,
//             section: sectionTitle,
//             index: index
//           });
//         });
//       });
//       return links;
//     });

//     let results: TestResult[] = [];
//     let totalChecked = 0;

//     for (const link of footerLinks) {
//       totalChecked++;

//       // Wrap each iteration in a step
//       await test.step(`Checking footer: "${link.section}" -> "${link.text}"`, async () => {
//         console.log(`Checking footer: "${link.section}" -> "${link.text}"`);

//         try {

//           await page.waitForTimeout(1000)

//           const newPagePromise = context.waitForEvent('page', { timeout: 10000 }).catch(() => null);
//           const currentUrl = page.url();

//           await page.click(`footer > ul > li:has(h3:text-is("${link.section}")) > ul > li:nth-child(${link.index + 1}) > a`);

//           const urlChangePromise = page.waitForURL(url => url.toString() !== currentUrl.toString(), { timeout: 10000 }).catch(() => null);


//           let newPage: Page | null = null;
//           let pdfDetected = false;
//           let finalUrl = '';
//           let pageContent = '';

//           try {
//             const result = await Promise.race([newPagePromise, urlChangePromise]);

//             // Case 1: Handle a new page(could be a web page or a PDF in a new tab)
//             // TODO: PDF page does not work in headless mode
//             if (result && 'url' in result && typeof result.url === 'function') {
//               newPage = result as Page;
//             }
//           } catch (error) {
//             console.log(error)
//           }

//           const targetPage = newPage || page;

//           await targetPage.waitForLoadState('networkidle', { timeout: 20000 });
//           pageContent = await targetPage.content();
//           const newUrl = targetPage.url();
//           const hasContentNotFound = pageContent.toLowerCase().includes('content not found');

//           // expect.soft(hasContentNotFound, 'Content not found').toBeFalsy();

//           if (isValidUrl(newUrl, link.text, link.section) && (pdfDetected || !hasContentNotFound)) {
//             results.push({ status: 'Passed', link, url: newUrl });
//           } else {
//             let failureReason = '';
//             if (!isValidUrl(newUrl, link.text, link.section)) {
//               failureReason = 'Invalid URL';
//             }
//             if (hasContentNotFound) {
//               failureReason += (failureReason ? ' and ' : '') + 'Content not found';
//             }
//             results.push({ status: 'Failed', link, url: newUrl, message: failureReason });
//             expect.soft(false, `Error occurred: ${failureReason}`).toBeTruthy();
//           }

//           // If a new page was opened, close it
//           if (newPage) {
//             await newPage.close();
//           }
//         } catch (error) {
//           results.push({ status: 'Error', link, message: (error as Error).message });
//           // Use soft assertion instead of test.step.fail()
//           expect.soft(false, `Error occurred: ${(error as Error).message}`).toBeTruthy();
//         }

//         // Always navigate back to the home page for the next iteration
//         await page.goto("/");
//       });
//     }

//     console.log("\n--- Test Summary ---");
//     console.log(`Total links checked: ${totalChecked}`);
//     console.log(`Number of passed links: ${results.filter(r => r.status === 'Passed').length}`);
//     console.log(`Number of failed links: ${results.filter(r => r.status !== 'Passed').length}`);

//     console.log("\nDetailed Results:");
//     results.forEach((result, index) => {
//       if (result.status === 'Passed') {
//         console.log(`${index + 1}. PASSED: "${result.link.text}" (${result.link.section}) -> ${result.url}`);
//       } else if (result.status === 'Failed') {
//         console.log(`${index + 1}. FAILED: "${result.link.text}" (${result.link.section}) -> ${result.url} (Reason: ${result.message})`);
//       } else {
//         console.log(`${index + 1}. ERROR: "${result.link.text}" (${result.link.section}) - ${result.message}`);
//       }
//     });

//     const failures = results.filter(r => r.status !== 'Passed');
//     expect(failures.length, `${failures.length} link(s) failed the check`).toBe(0);
//   });


//   test('Static page validation', async ({ page, context }) => {
//     test.setTimeout(1200000);

//     // Results array for overall validation (stream, class, section, and course assertions)
//     let results: Array<{
//       stream: string,
//       className: string,
//       section: string,
//       status: string,
//       url?: string,
//       message?: string,
//       assertionResults?: Array<{ status: string, message: string }>
//     }> = [];

//     async function logResult(stream: string, className: string, section: string, status: string, url?: string, message?: string, assertionResults?: Array<{ status: string, message: string }>) {
//       results.push({ stream, className, section, status, url, message, assertionResults });
//     }

//     async function getTextFromElem(element: Locator) {
//       return (await element.innerText()).replace(/\s+/g, ' ').trim();
//     }

//     async function waitForPageLoad() {
//       await page.waitForLoadState('domcontentloaded');
//       await page.waitForTimeout(2000);
//     }

//     async function clickAndWait(clickable: Locator) {
//       await test.step('Click and wait for page load', async () => {
//         await Promise.all([
//           clickable.click(),
//           page.waitForRequest(request => request.url() !== page.url()),
//         ]);
//         await waitForPageLoad();
//       });
//     }

//     async function goBackAndWait() {
//       await test.step('Navigate back and wait for page load', async () => {
//         await Promise.all([
//           page.goBack(),
//           page.waitForRequest(request => request.url() !== page.url()),
//         ]);
//         await waitForPageLoad();
//       });
//     }

//     async function getCardsBySection(sectionText: string) {
//       let cards: Locator[] = [];
//       // Check if the section exists and is visible, but don't fail the test if it's not
//       try {
//         await page.locator('section').filter({ hasText: sectionText }).getByTestId('card').first().waitFor({ state: 'visible', timeout: 5000 });
//         cards = await page.locator('section')
//           .filter({ hasText: sectionText })
//           .getByTestId('card')
//           .all();
//       } catch (error) {
//         // If the section or cards are not found within the timeout, handle the error
//         console.log(`No cards found for section: ${sectionText}`);
//       }
//       return cards;
//     }

//     // Function to validate course details and log soft assertion results
//     async function validateCourseDetails(classValue: string) {
//       let softResults: Array<{ status: string, message: string }> = [];
//       try {
//         await expect.soft(page.getByTestId('productInfo-title'), "verify course name title is visible").toBeVisible();
//         softResults.push({ status: 'Passed', message: 'Course name title is visible' });

//         await expect.soft(page.getByTestId('course-details-0').nth(0), "verify course standard value is visible").toBeVisible();
//         softResults.push({ status: 'Passed', message: 'Course standard value is visible' });

//         const courseStandardValue = await page.getByTestId('course-details-0').nth(0).textContent();
//         if (courseStandardValue) {
//           const standardValue = courseStandardValue.split(' ');
//           expect.soft(classValue, "verify course class value should match at course details page class value").toEqual(standardValue[1]);
//           softResults.push({ status: 'Passed', message: 'Course class value matches course details page class value' });
//         } else {
//           softResults.push({ status: 'Failed', message: 'Course standard value is missing' });
//         }

//         await expect.soft(page.getByTestId('course-details-0').nth(1), "verify course duration is visible").toBeVisible();
//         softResults.push({ status: 'Passed', message: 'Course duration is visible' });

//         await expect.soft(page.getByTestId('preferencesWidget'), "verify preference program widget is visible").toBeVisible();
//         softResults.push({ status: 'Passed', message: 'Preference program widget is visible' });

//         await expect.soft(page.getByTestId('productInfo-paymentPrice'), "verify course fee value is visible").toBeVisible();
//         softResults.push({ status: 'Passed', message: 'Course fee value is visible' });

//         let courseValueText = await page.getByTestId('productInfo-paymentPrice').textContent();
//         if (courseValueText) {
//           let courseValue = courseValueText.split(' ')[1];
//           expect.soft(parseInt(courseValue), "validating course fee value is greater than zero").toBeGreaterThan(0);
//           softResults.push({ status: 'Passed', message: 'Course fee value is greater than zero' });
//         } else {
//           softResults.push({ status: 'Failed', message: 'Course fee value is missing' });
//         }
//       } catch (error) {
//         softResults.push({ status: 'Failed', message: `An error occurred: ${error.message}` });
//       }
//       return softResults;
//     }

//     // Main page navigation and validation
//     await page.goto("/");

//     const streams = ['NEET', 'JEE', 'Class 6-10'];

//     for (const stream of streams) {
//       await test.step(`Validating ${stream}`, async () => {
//         await test.step(`Navigating to ${stream} and getting class cards`, async () => {
//           await page.getByTestId('Exams').click();
//           await page.getByRole('link', { name: stream }).click();
//           await page.waitForRequest(request => request.url() !== page.url());
//         });
//         // const classCards = await getCardsBySection('Online Programs') || await getCardsBySection('Online Coaching');
//         let classCards = await getCardsBySection('Online Programs');
//         if (classCards.length === 0) {
//           classCards = await getCardsBySection('Online Coaching');
//         }

//         for (let i = 0; i < classCards.length; i++) {
//           const classCard = (classCards)[i];
//           const classCardName = await getTextFromElem(classCard);

//           await test.step(`Validating Class ${classCardName}`, async () => {
//             let classValue;
//             if (classCardName.includes('For')) {
//               const regex = /For\s(\d+)(?:th|st|nd|rd)?\s/;
//               const classValueString = classCardName.match(regex);
//               if (classValueString![1]) {
//                 classValue = classValueString![1];
//               }
//             }

//             await clickAndWait(classCard);

//             const sections = await page.$$eval('section:has([data-testid="card"])', sections =>
//               sections.map(section => section.querySelector('h2')?.textContent?.trim()).filter(Boolean)
//             );

//             for (const section of sections) {
//               await test.step(`Validating ${section}`, async () => {
//                 const programs = await getCardsBySection(section!);
//                 if (programs.length > 0) {
//                   for (let j = 0; j < programs.length; j++) {
//                     const program = (await getCardsBySection(section!))[j];
//                     let softResults: Array<{ status: string, message: string }> = [];
//                     try {
//                       await clickAndWait(program);

//                       // Perform detailed course validation and log soft assertion results
//                       softResults = await validateCourseDetails(classValue);

//                       // Log the result for the stream, class, and section with soft assertion results
//                       logResult(stream, classCardName, section!, 'Passed', page.url(), undefined, softResults);
//                     } catch (error) {
//                       // Log failure for the stream, class, and section
//                       logResult(stream, classCardName, section!, 'Failed', page.url(), error.message, softResults);
//                     }

//                     await goBackAndWait();
//                   }
//                 }
//               });
//             }

//             if (i < classCards.length - 1) {
//               await goBackAndWait();
//             }
//           });
//         }

//         if (stream !== streams[streams.length - 1]) {
//           await page.goto("/");
//         }
//       });
//     }

//     // Log final merged summary
//     console.log("\n--- Test Summary ---");
//     console.log(`Total streams checked: ${streams.length}`);
//     console.log(`Total courses checked: ${results.length}`);
//     console.log(`Number of passed validations: ${results.filter(r => r.status === 'Passed').length}`);
//     console.log(`Number of failed validations: ${results.filter(r => r.status === 'Failed').length}`);

//     console.log("\nDetailed Results:");
//     results.forEach((result, index) => {
//       if (result.status === 'Passed') {
//         console.log(`${index + 1}. PASSED: "${result.stream}" : "${result.className}" - "${result.section}" -> ${result.url}`);
//         result.assertionResults?.forEach((assertion, assertionIndex) => {
//           console.log(`    ${assertionIndex + 1}. ${assertion.status.toUpperCase()}: ${assertion.message}`);
//         });
//       }
//       else if (result.status === 'Failed') {
//         console.log(`${index + 1}. FAILED: "${result.stream}" : "${result.className}" - "${result.section}" -> ${result.url} (Reason: ${result.message})`);

//         // Log soft assertion failures if any
//         if (result.assertionResults?.some(a => a.status === 'Failed')) {
//           console.log("    Soft assertion failures:");
//           result.assertionResults?.forEach((assertion, assertionIndex) => {
//             if (assertion.status === 'Failed') {
//               console.log(`    ${assertionIndex + 1}. FAILED: ${assertion.message}`);
//             }
//           });
//         }
//       }
//     });

//     // Optionally, fail the test if any soft assertions failed
//     if (results.some(result => result.assertionResults?.some(assertion => assertion.status === 'Failed'))) {
//       throw new Error("Some soft assertions failed");
//     }
//   });


//   test('Allen header links navigation', async ({ page, context }) => {
//     // test.skip()
//     // Set test timeout to 3 minutes (180000 ms)
//     test.setTimeout(180000);

//     interface LinkInfo {
//       text: string;
//       href: string;
//       section: string;
//       index: number;
//     }

//     interface TestResult {
//       status: 'Passed' | 'Failed' | 'Error';
//       link: LinkInfo;
//       url?: string;
//       message?: string;
//     }

//     const normalizeText = (text: string): string => {
//       return text.toLowerCase().replace(/[^a-z0-9\s]+/g, '').trim().replace(/\s+/g, ' ');
//     };

//     // Validate if the link is valid, if the url contains the link text or section title
//     // Ex: Valid links:
//     // sectionTitle - "About", linkText - "MyExam EduBlogs", url - https://myexam.allen.in/ 

//     const isValidUrl = (url: string, linkText: string, sectionTitle: string): boolean => {
//       const urlLower = url.toLowerCase();
//       const linkTextNormalized = normalizeText(linkText);
//       const sectionTitleNormalized = normalizeText(sectionTitle);

//       // For general cases, check if any significant word from the link text or section title is in the URL
//       const words = [...linkTextNormalized.split(' '), ...sectionTitleNormalized.split(' ')];
//       const significantWords = words.filter(word => word.length > 1); // Consider words longer than 1 character

//       // Function to check if a word matches the URL, ignoring special characters
//       const wordMatchesUrl = (word: string, url: string) => {
//         return url.includes(word);
//       };

//       // Check if any significant word is in the URL
//       if (significantWords.some(word => wordMatchesUrl(word, urlLower))) {
//         return true;
//       }

//       // Special cases mapping with section and UI text as keys
//       const specialLinks: { [key: string]: (url: string) => boolean } = {
//         'Programs|Online Programs': (url) => url.includes('ultimate-program-live-courses'),
//         'Programs|Classroom Programs': (url) => url.includes('https://www.allen.ac.in/'),
//         'Programs|Distance learning': (url) => url.includes('dlp.allen.ac.in'),
//         'Scholarships|ADSAT': (url) => url.includes('https://web.allen-stage.in/adsat-register'),
//         'Scholarships|TALLENTEX': (url) => url.includes('https://studyonline.tallentex.com/' || 'https://www.tallentex.com/'),
//       };

//       // Check special cases
//       const specialCaseKey = `${sectionTitle}|${linkText}`;
//       const normalizedSpecialCaseKey = normalizeText(specialCaseKey);

//       for (const [caseText, caseCheck] of Object.entries(specialLinks)) {
//         if (normalizeText(caseText) === normalizedSpecialCaseKey) {
//           return caseCheck(urlLower);
//         }
//       }
//       return false;
//     };


//     await page.goto('/');

//     const headerLinks = await page.evaluate(async () => {
//       const links: LinkInfo[] = [];
//       const sectionElements = document.querySelectorAll('header nav > div');
//       console.log(sectionElements + "= sectionElements");
//       for (const section of sectionElements) {
//         const sectionTitle = section.querySelector('button')?.textContent || '';
//         console.log(sectionTitle + "= sectionTitle");

//         const examsButton = section.querySelector(`button[data-testid="${sectionTitle}"]`);

//         if (examsButton) {
//           console.log(sectionTitle + "= sectionTitle click");
//           const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
//           await sleep(1000);
//           // Use Playwright's click instead of dispatchEvent for better interaction
//           // await page.locator(`button[data - testid= "${sectionTitle}"]`).click();
//           examsButton.dispatchEvent(new Event('click', { bubbles: true }));
//           await sleep(100);

//           const sectionLinks = document.querySelectorAll('div > div > button > div > ul[data-testid="webHeaderNav"] li a');
//           console.log(sectionLinks + "= section Links ");
//           console.log(`Found ${sectionLinks.length} links in the section: ${sectionTitle}`);
//           // Check if sectionLinks is empty
//           if (sectionLinks.length === 0) {
//             console.log(`No links found in the section: ${sectionTitle}`);
//             continue;  // Move to the next section if no links are found
//           }

//           sectionLinks.forEach((a, index) => {
//             console.log(" section Links Individual Inside");
//             const anchorElement = a as HTMLAnchorElement; // Typecast the element to HTMLAnchorElement
//             const linkText = anchorElement.textContent || ''; // Get the text inside the link
//             const linkHref = anchorElement.href; // Get the href attribute

//             console.log(`Link ${index + 1}: ${linkText} (${linkHref})`); // Print individual links

//             links.push({
//               text: linkText,
//               href: linkHref,
//               section: sectionTitle,
//               index: index
//             });
//           });
//           if (examsButton) {
//             examsButton.dispatchEvent(new Event('click', { bubbles: true }));
//           }
//         }
//       }

//       const additionalLink = document.querySelector('header nav > a');
//       if (additionalLink) {
//         const linkText = additionalLink.textContent || '';
//         // const linkHref = additionalLink.href;
//         console.log(`Found additional link: ${linkText}`);

//         links.push({
//           text: linkText,
//           section: "Test Series",
//           index: links.length,
//           href: ''
//         });
//       }
//       return links;
//     });


//     console.log();


//     let results: TestResult[] = [];
//     let totalChecked = 0;

//     for (const link of headerLinks) {
//       totalChecked++;

//       // Wrap each iteration in a step
//       await test.step(`Checking header: "${link.section}" -> "${link.text}"`, async () => {
//         console.log(`Checking header: "${link.section}" -> "${link.text}"`);

//         try {

//           await page.waitForTimeout(1000)

//           const newPagePromise = context.waitForEvent('page', { timeout: 10000 }).catch(() => null);
//           const currentUrl = page.url();
//           if (link.section !== "Test Series") {

//             await page.click(`header nav > div:has(button[data-testid="${link.section}"])`);
//             await page.waitForTimeout(500)
//             await page.click(`ul[data-testid="webHeaderNav"] li:nth-child(${link.index + 1}) a`);
//           }
//           else {
//             await page.click('header nav > a');
//             await page.waitForTimeout(500)
//           }

//           const urlChangePromise = page.waitForURL(url => url.toString() !== currentUrl.toString(), { timeout: 10000 }).catch(() => null);

//           let newPage: Page | null = null;
//           let pdfDetected = false;
//           let finalUrl = '';
//           let pageContent = '';

//           try {
//             const result = await Promise.race([newPagePromise, urlChangePromise]);

//             // Case 1: Handle a new page(could be a web page or a PDF in a new tab)
//             // TODO: PDF page does not work in headless mode
//             if (result && 'url' in result && typeof result.url === 'function') {
//               newPage = result as Page;
//             }
//           } catch (error) {
//             console.log(error)
//           }

//           const targetPage = newPage || page;

//           await targetPage.waitForLoadState('networkidle', { timeout: 20000 });
//           pageContent = await targetPage.content();
//           const newUrl = targetPage.url();
//           const hasContentNotFound = pageContent.toLowerCase().includes('content not found');

//           // expect.soft(hasContentNotFound, 'Content not found').toBeFalsy();

//           if (isValidUrl(newUrl, link.text, link.section) && (pdfDetected || !hasContentNotFound)) {
//             results.push({ status: 'Passed', link, url: newUrl });
//           } else {
//             let failureReason = '';
//             if (!isValidUrl(newUrl, link.text, link.section)) {
//               failureReason = 'Invalid URL';
//             }
//             if (hasContentNotFound) {
//               failureReason += (failureReason ? ' and ' : '') + 'Content not found';
//             }
//             results.push({ status: 'Failed', link, url: newUrl, message: failureReason });
//             expect.soft(false, `Error occurred: ${failureReason} `).toBeTruthy();
//           }

//           // If a new page was opened, close it
//           if (newPage) {
//             await newPage.close();
//           }
//         } catch (error) {
//           results.push({ status: 'Error', link, message: (error as Error).message });
//           // Use soft assertion instead of test.step.fail()
//           expect.soft(false, `Error occurred: ${(error as Error).message} `).toBeTruthy();
//         }

//         // Always navigate back to the home page for the next iteration
//         await page.goto("/");
//       });
//     }

//     console.log("\n--- Test Summary ---");
//     console.log(`Total links checked: ${totalChecked} `);
//     console.log(`Number of passed links: ${results.filter(r => r.status === 'Passed').length} `);
//     console.log(`Number of failed links: ${results.filter(r => r.status !== 'Passed').length} `);

//     console.log("\nDetailed Results:");
//     results.forEach((result, index) => {
//       if (result.status === 'Passed') {
//         console.log(`${index + 1}.PASSED: "${result.link.text}"(${result.link.section}) -> ${result.url} `);
//       } else if (result.status === 'Failed') {
//         console.log(`${index + 1}.FAILED: "${result.link.text}"(${result.link.section}) -> ${result.url} (Reason: ${result.message})`);
//       } else {
//         console.log(`${index + 1}.ERROR: "${result.link.text}"(${result.link.section}) - ${result.message} `);
//       }
//     });

//     const failures = results.filter(r => r.status !== 'Passed');
//     expect(failures.length, `${failures.length} link(s) failed the check`).toBe(0);
//   });
// });