import { expect } from '@playwright/test'
import { APIRequestContext } from '@playwright/test'
import { commonTest, commonWorkerFixtures } from '../../commons/common-fixtures'
import API from '../../commons/api-helpers/api-helper'
import { cleanUpTestData, getStudentAcct, testDataSetup } from '../../ui-tests/fixtures/load-test-data'
import { workerData } from 'worker_threads'
import ResourceManagement from '../../commons/api-helpers/resource-management'
import { retryOperation } from '../../commons/api-helpers/api-common-utils'
import { generateRandomNameEmailPhone } from '../helpers/utilities'
import { updateExcelFile } from '../../commons/common-functions'
import Auth from '../../commons/api-helpers/auth'
import { cleanupRegistry } from '../../commons/cleanup-registry'
import { EnvUtils } from '../../commons/env-utilities'

// Use EnvUtils instead of direct environment variable check
const envUtils = EnvUtils.getInstance();

const fixtures = {
  // Remove adminURL and bffURL from here as they're now in the common fixture
  testData: [
    async ({ browser, bffURL }, use, workerInfo) => {
      const env = envUtils.getEnv();
      const api = new API((await browser.newContext()).request, new URL('/internal-bff/api/v1', bffURL).toString());
      api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
      let testData
      const testId = `worker-${workerInfo.parallelIndex}-${Date.now()}`
      let cleanupError: Error | null = null
      const syllabusConfig = process.env.SYLLABUS_CONFIG ? JSON.parse(process.env.SYLLABUS_CONFIG) : {}

      try {
        // Create test data
        const setupResult = await testDataSetup(api, env, 'JEE', 'API')
        if (!setupResult.success) {
          testData = setupResult.data
          throw new Error(`${setupResult.error}`)
        }
        testData = setupResult.data

        // Register with cleanup registry
        cleanupRegistry.register(testId, testData, api, env, 'JEE')
        // console.log(
        //   `JEE Test Data for worker ${workerInfo.parallelIndex} is: ${testData?.student?.name}, ${testData?.teacher?.name}, ${testData?.batch?.name}, ${testData?.room?.name},${testData?.lmmContent}`,
        // )

        // Use the test data
        await use({
          env: process.env,
          ...testData,
          syllabusConfig,
        })
      } finally {
        try {
          // Normal cleanup
          await cleanUpTestData(testData, api, env, 'JEE')
        } catch (error) {
          cleanupError = error as Error
          console.error('Normal cleanup failed:', error)
        }
        // Unregister from registry
        cleanupRegistry.unregister(testId)

        // Throw cleanup error after unregistering
        if (cleanupError) {
          throw new Error(
            `Test cleanup failed:\n\n${cleanupError.message}\n\nCleanup failed while tearing down "testData".\n\nTestId: ${testId}`,
          )
        }
      }
    },
    { scope: 'worker' },
  ],
  neetTestData: [
    async ({ browser, bffURL }, use, workerInfo) => {
      const env = envUtils.getEnv();
      const api = new API((await browser.newContext()).request, new URL('/internal-bff/api/v1', bffURL).toString());
      api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
      let testData
      const testId = `worker-${workerInfo.parallelIndex}-${Date.now()}`
      let cleanupError: Error | null = null
      const syllabusConfig = process.env.SYLLABUS_CONFIG ? JSON.parse(process.env.SYLLABUS_CONFIG) : {}

      try {
        // Create test data specifically for NEET
        console.log('Setting up NEET test data for API tests...');
        const setupResult = await testDataSetup(api, env, 'NEET', 'API')
        if (!setupResult.success) {
          testData = setupResult.data
          throw new Error(`${setupResult.error}`)
        }
        testData = setupResult.data
        console.log('NEET test data setup successful:', {
          studentPhone: testData?.student?.phone,
          batchId: testData?.batch?.id
        });

        // Register with cleanup registry
        cleanupRegistry.register(testId, testData, api, env, 'NEET')
        
        // Use the test data
        await use({
          env: process.env,
          ...testData,
          syllabusConfig,
        })
      } finally {
        try {
          // Normal cleanup - only if testData exists
          if (testData) {
            console.log('Cleaning up NEET test data...');
            await cleanUpTestData(testData, api, env, 'NEET')
            console.log('NEET test data cleanup successful');
          }
        } catch (error) {
          cleanupError = error as Error
          console.error('NEET test data cleanup failed:', error)
        }
        // Unregister from registry
        cleanupRegistry.unregister(testId)

        // Throw cleanup error after unregistering
        if (cleanupError) {
          throw new Error(
            `Test cleanup failed:\n\n${cleanupError.message}\n\nCleanup failed while tearing down "neetTestData".\n\nTestId: ${testId}`,
          )
        }
      }
    },
    { scope: 'worker' },
  ],


  login: async ({ browser, bffURL, request, testData }, use, workerInfo) => {
    // Generate test data (create batches, create test account, etc.)
    // TODO: Add test data generation logic here
    const api = new API(request, bffURL)
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const auth = new Auth(api)
    const userDetails = await auth.loginWithUsername(testData.student.phone)
    await use(userDetails)
  },

  getUnusedStudentNumber: async ({ browser, bffURL, request }, use, workerInfo) => {
    // Generate test data (create batches, create test account, etc.)
    // TODO: Add test data generation logic here
    const api = new API(request, new URL('/internal-bff/api/v1', bffURL).toString());
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const resourceManagement = new ResourceManagement(api)
    var newStudent = await retryOperation(
      //We are getting a Unused Student Number here , for onboarding process
      //To ensure that we are first creating a new student (showing its availability as a non registered user) and then deleting it afterwards.
      'getUnusedStudentNumber',
      async () => {
        const studentDetails = generateRandomNameEmailPhone()
        const studentCreated = await resourceManagement.createTestStudent(
          studentDetails.email,
          studentDetails.phone,
          studentDetails.Fname,
          studentDetails.Lname,
        )
        resourceManagement.deleteTestAccount((await studentCreated.json()).data.data.id)
        return studentCreated
      },
      8,
    )
    newStudent = (await newStudent.json()).data.data
    await use(newStudent)
  },

  setUpDoubtMapping: async ({ browser, bffURL, request, testData }, use, workerInfo) => {
    console.log('Doubt Setup')
    const centerId = process.env.CENTER_ID ? process.env.CENTER_ID : ''
    const courseId = process.env.BATCH_COURSE_ID ? process.env.BATCH_COURSE_ID : ''
    const api = new API(request, new URL('/internal-bff/api/v1', bffURL).toString());
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const resourceManagement = new ResourceManagement(api)
    //Add Syllabus To Batch
    await resourceManagement.addSyllabusToBatch(testData.batch.id)
    //Update Excel File with Batch and current teacher
    await updateExcelFile(
      `${__dirname}/../../../test-data/doubt_template.xlsx`,
      testData.batch.id,
      testData.teacher.phone,
    )
    //Upload and make the Doubt Mapping
    await resourceManagement.uploadDoubtTemplate(`doubt_template.xlsx`, testData.batch.id, courseId, centerId)
    await use()
  },

  neetLogin: async ({ browser, bffURL, request, neetTestData }, use) => {
    console.log('Setting up NEET login with phone:', neetTestData.student.phone);
    const api = new API(request, bffURL)
    api.setToken(`${process.env.ADMIN_ACCESS_TOKEN}`)
    const auth = new Auth(api)
    
    try {
      const userDetails = await auth.loginWithUsername(neetTestData.student.phone)
      console.log('NEET login successful');
      await use(userDetails)
    } catch (error) {
      console.error('NEET login failed:', error);
      throw error;
    }
  },
}

type TestFixtures = {
  [key: string]: any
}

export type ApiWorkerFixtures = commonWorkerFixtures & {
  // Add any API-specific worker fixtures here
}

const test = commonTest.extend<TestFixtures, ApiWorkerFixtures>(fixtures)

export { test, expect, fixtures }
