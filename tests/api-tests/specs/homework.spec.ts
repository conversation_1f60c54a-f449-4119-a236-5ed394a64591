import { runYamlTests } from '../helpers/yaml-test-runner';
import path from 'path';
import * as yaml from 'js-yaml';
import fs from 'fs';
const config = yaml.load(fs.readFileSync('tests/api-tests/yamls/homework/homework-creation.yml', 'utf8'));

// Assume that dates.tomorrowDate is passed as a string in the YAML file
const expiryString = config.config.variables.expiry;

// Convert the expiry string to a number (assuming it's a timestamp string)
const expiryNumber = Number(expiryString);

// Custom functions specific to this test suite
const customFunctions = {};

const yamlPath = path.join(__dirname, '../yamls/homework/');
runYamlTests(yamlPath, customFunctions);
