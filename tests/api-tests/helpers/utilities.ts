// tests/api/helpers/common-functions.ts
import { expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import FormData from 'form-data';
import axios from 'axios';

export function generateRandomEmail() {
  return `user${Math.floor(Math.random() * 10000)}@example.com`;
}

export function validateEmailFormat(response: any) {
  const body = response.json();
  expect(body.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
}

export function getFormattedDateTime(format: string): string {
  const now = new Date();

  const map: { [key: string]: string } = {
    YYYY: now.getFullYear().toString(),
    MM: (now.getMonth() + 1).toString().padStart(2, '0'),
    DD: now.getDate().toString().padStart(2, '0'),
    HH: now.getHours().toString().padStart(2, '0'),
    mm: now.getMinutes().toString().padStart(2, '0'),
    ss: now.getSeconds().toString().padStart(2, '0'),
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, matched => map[matched]);
}

export function getDates() {
  // Create date 
  const now = new Date();
  const startTime = now.getTime() + 60000;
  const endTime = now.getTime() + 120000;
  const startDate = now.getTime();

  // Calculate start of the week (Monday at 00:00:00)
  var startOfWeek = new Date(now);
  startOfWeek.setDate(
    now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1)
  );
  startOfWeek.setHours(0, 0, 0, 0);

  // Calculate end of the week (Sunday at 23:59:59)
  var endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);

  // Convert to epoch time (milliseconds)
  const start_of_week = startOfWeek.getTime();
  const end_of_week = endOfWeek.getTime();

  // Add 1 minute
  now.setMinutes(now.getMinutes() + 1);
  const currentDate = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
  currentDate.setMinutes(currentDate.getMinutes() + 1);
  // Get the components of the current time
  const hours = currentDate.getHours();
  const minutes = currentDate.getMinutes();
  const seconds = currentDate.getSeconds();

  // Format the date to "YYYY-MM-DD HH:mm:ss"
  const formattedNowDate = `${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}-${currentDate.getDate().toString().padStart(2, '0')} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

  // Create date with 5 seconds added
  const exactDate = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
  exactDate.setSeconds(exactDate.getSeconds()+5);
  const exactHours = exactDate.getHours();
  const exactMinutes = exactDate.getMinutes();
  const exactSeconds = exactDate.getSeconds();
  const FormattedNowDateTime = `${exactDate.getFullYear()}-${(exactDate.getMonth() + 1).toString().padStart(2, '0')}-${exactDate.getDate().toString().padStart(2, '0')} ${exactHours.toString().padStart(2, '0')}:${exactMinutes.toString().padStart(2, '0')}:${exactSeconds.toString().padStart(2, '0')}`;

  // Create a new Date object for the current date
  const futureDate = new Date();
  // Add a random number of days (0-28) to the current date
  futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 29));
  // Format the date as "DD-MM-YYYY"
  const formatedDate: string = futureDate.toISOString().split('T')[0].split('-').reverse().join('-');
  // Get tomorrow's date and convert to epoch time
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);
  const tomorrowDate = tomorrow.getTime();

  var endOfToday = new Date(now);
  endOfToday.setHours(23, 59, 59, 999);
  const end_of_today = endOfToday.getTime();

  const today_epoch_date = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' })).setHours(0, 0, 0, 0).toString();
  const currentTime_in_secs = Math.floor(Date.now() / 1000);

  const start_epoch = Math.floor((Date.now() + 10 * 60 * 1000) / 1000); // 10 minutes from now
  const end_epoch = Math.floor((Date.now() + 15 * 60 * 1000) / 1000); // 15 minutes from now

  const meeting_start_epoch = Math.floor((Date.now() + (15 * 60 + 20) * 1000) / 1000); // 15 minutes and 20 seconds from now
  const meeting_end_epoch = Math.floor((Date.now() + 25 * 60 * 1000) / 1000); // 25 minutes from now

  const start_of_week_in_seconds = Math.floor(start_of_week / 1000);
  const end_of_week_in_seconds = Math.floor(end_of_week / 1000);


  return {
    start_of_week,
    end_of_week,
    startTime,
    endTime,
    startDate,
    formattedNowDate,
    tomorrowDate,
    end_of_today,
    today_epoch_date,
    currentTime_in_secs,
    formatedDate,
    start_epoch,
    end_epoch,
    start_of_week_in_seconds,
    end_of_week_in_seconds,
    meeting_start_epoch,
    meeting_end_epoch,
    FormattedNowDateTime,
  }
}

// Function to generate a random name, email, and phone number
export function generateRandomNameEmailPhone() {
  const Fname = `student`;
  const Lname = `test${generateRandomString(4)}`;
  const randomNum = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
  return {
    Fname: Fname,
    Lname: Lname,
    name: `${Fname} ${Lname}`,
    email: `user${Fname}${Lname}@allen.in`,
    phone: `12345${randomNum}`,
  };
}

export function generateRandomString(length: number, prefix: string = '') {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  let randomString = '';
  for (let i = 0; i < length; i++) {
    const index = Math.floor(Math.random() * characters.length);
    const randomChar = characters.charAt(index);
    randomString += randomChar;
  }
  return prefix + randomString;
}

// Function to generate a random number with a given length
export function generateRandomNumber(length) {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function getTimeAfterMinutes(minutes: number): number {
  return Date.now() + minutes * 60 * 1000;
}

export function getTimeAfterSeconds(seconds: number): number {
  return Date.now() + seconds * 1000;
}

export async function uploadExcelFile(url: string, token: string, filePath: string, fieldName: string = 'file', additionalFields?: Record<string, string>) {
  try {
    console.log(`Uploading Excel file ${filePath} to ${url}`);
    
    // Create form data
    const form = new FormData();
    
    // Add file to form data
    const file = fs.readFileSync(path.resolve(process.cwd(), filePath));
    form.append(fieldName, file, {
      filename: path.basename(filePath),
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    // Add additional fields if provided
    if (additionalFields) {
      Object.entries(additionalFields).forEach(([key, value]) => {
        form.append(key, value);
      });
    }
    
    // Send request
    const response = await axios.post(url, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': `Bearer ${token}`,
        'x-device-id': '969c6d64-33ed-4fd7-a96e-208104d4812f',
        'x-client-type': 'web'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error uploading Excel file:', error);
    throw error;
  }
}

export function generateRandomMobileNumber() {
  const randomNumber1 = '6' + Math.floor(100000000 + Math.random() * 900000000);
  return randomNumber1.toString().substring(0, 10);
}
