// import { test as base, expect } from '@playwright/test';
import * as fs from 'fs';
import * as yaml from 'js-yaml';
import jsonpath from 'jsonpath'; // Change this import
import * as path from 'path';

// Import common functions and fixtures
import { test, expect, fixtures } from '../fixtures/api-fixtures';
import * as commonFunctions from './utilities';

// type TestFixtures = {
//   [key: string]: any;
// };

// const test = base.extend<TestFixtures>(fixtures);
// const test = base;

function parseYamlFile(filePath: string) {
  try {
    const fileContents = fs.readFileSync(filePath, 'utf8');
    return yaml.load(fileContents) as any;
  } catch (e) {
    console.error('Error parsing YAML file:', e);
    return null;
  }
}

function interpolateString(str: string, context: any): string | number | boolean {
  // If input is not a string, return as is
  if (typeof str !== 'string') {
    return str;
  }

  // Handle template syntax {{variable}}
  let result = str;
  const matches = str.match(/{{([^}]+)}}/g) || [];

  for (const match of matches) {
    const p1 = match.slice(2, -2).trim();
    const keys = p1.split('.');
    let value = context;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        value = match;
        break;
      }
    }

    // If the entire string is just a boolean template, return the boolean value
    if (str === `{{${p1}}}` && typeof value === 'boolean') {
      return value;
    }

    // Ensure value remains a string if the template contains phone_number
    if (p1.includes('phone_number')) {
      value = String(value);
      result = result.replace(match, value);
      continue;
    }

    // Convert to number if the template starts with {{dates.}} or is exactly content_expiry
    if ((p1 === 'content_expiry') && !isNaN(Number(value))) {
      value = Number(value);
      // If this is the only template in the string, return the number directly
      if (str === match) {
        return value;
      }
    }

    // Otherwise convert to string for replacement
    result = result.replace(match, String(value));
  }

  // Only convert to number if it's not a phone number
  return !str.includes('phone_number') && /^\d{11,}$/.test(result) ? Number(result) : result;
}

function interpolateObject(obj, context) {
  if (typeof obj === 'string') {
    return interpolateString(obj, context);
  }
  if (Array.isArray(obj)) {
    return obj.map(item => interpolateObject(item, context));
  }
  if (typeof obj === 'object' && obj !== null) {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [
        key,
        interpolateObject(value, context)
      ])
    );
  }
  // Handle non-string primitives (including booleans and numbers)
  if (typeof obj === 'boolean' || typeof obj === "number" || typeof obj === 'string') {
    return obj;
  }
  // If it's undefined or null, return as is
  return obj;
}

// function interpolateString(str: string, context: any): string {
//   if (typeof str !== 'string') {
//     return JSON.stringify(str, null, 2);
//   }
//   return str.replace(/{{([^}]+)}}/g, (match, p1) => {
//     const keys = p1.trim().split('.');
//     let value = context;
//     for (const key of keys) {
//       if (value && typeof value === 'object' && key in value) {
//         value = value[key];
//       } else {
//         return match; // Return original match if the key is not found
//       }
//     }
//     return typeof value === 'object' ? JSON.stringify(value, null, 2) : value !== undefined ? value : match;
//   });
// }

// function interpolateObject(obj: any, context: any): any {
//   if (typeof obj === 'string') return interpolateString(obj, context);
//   if (Array.isArray(obj)) return obj.map(item => interpolateObject(item, context));
//   if (typeof obj === 'object' && obj !== null) {
//     return Object.fromEntries(
//       Object.entries(obj).map(([key, value]) => [
//         key,
//         interpolateObject(value, context)
//       ])
//     );
//   }
//   return obj;
// }

// Add this function to handle equality checks
function areEqual(actual: any, expected: any): boolean {
  if (typeof actual === 'string' && typeof expected === 'string') {
    return actual.trim() === expected.trim();
  }
  return actual === expected;
}

// Add this helper function at the top with other functions
async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function runYamlTests(
  yamlPath: string,
  customFunctions: Record<string, Function> = {},
) {
  let yamlFiles: string[];
  if (fs.statSync(yamlPath).isDirectory()) {
    yamlFiles = fs.readdirSync(yamlPath).filter(file => file.endsWith('.yml') || file.endsWith('.yaml'));
  } else {
    yamlFiles = [path.basename(yamlPath)];
    yamlPath = path.dirname(yamlPath);
  }

  for (const file of yamlFiles) {
    const filePath = path.join(yamlPath, file);
    const yamlContent = parseYamlFile(filePath);

    // Create a file for storing curl commands
    //const curlLogPath = path.join(yamlPath, `${path.basename(file, path.extname(file))}_curls.txt`);
    const testResultsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(testResultsDir)) {
      fs.mkdirSync(testResultsDir);
    }

    // Use fixed path for curl logs
    const curlLogPath = path.join(testResultsDir, 'curls.txt');

    if (!yamlContent || !yamlContent.scenarios) {
      console.error(`No valid scenarios found in ${file}`);
      continue;
    }

    const allFunctions = { ...commonFunctions, ...customFunctions };

    for (const scenario of yamlContent.scenarios) {
      // Skip if environment is prod and skipInProd is true
      const env = process.env.PROD === "1" ? "prod" : "stage";
      if (env === 'prod' && scenario.skipInProd) {
        test.skip(`${file} > ${scenario.name}`, () => { });
        continue;
      }
      const scenarioFixtures = scenario.fixtures || yamlContent.config.fixtures || [];

      // Setting other fixtures to null if not present in the scenario. 
      // This is to avoid passing unused fixtures to the test. 
      // Playwright doesn't allow dynamic fixtures list in the test.extend function
      const fixtureArgs = Object.fromEntries(
        Object.keys(fixtures).map(fixture => [
          fixture,
          scenarioFixtures.includes(fixture) ? fixtures[fixture] : null
        ])
      );
      const folderName = path.basename(path.dirname(filePath));
      const testWithFixtures = test.extend(fixtureArgs);
      // Prepend the YAML file name to the test description
      const testDescription = `${file} > ${scenario.name}`;

      testWithFixtures(testDescription, {
        tag: ['@api', `@${folderName}`],
      }, async ({
        request,
        adminURL,
        bffURL,
        testData,
        getUnusedStudentNumber,
        setUpDoubtMapping,
        login,
        neetTestData,
        neetLogin,
      }) => {
        const context = {
          ...yamlContent.config.variables,
          adminURL,
          bffURL,
          testData,
          getUnusedStudentNumber,
          setUpDoubtMapping,
          login,
          neetTestData,
          neetLogin,
        };

        // Remove undefined values from context
        Object.keys(context).forEach(key => context[key] === undefined && delete context[key]);

        // console.log('Context:', JSON.stringify(context, null, 2));

        // Get default headers from YAML config
        const defaultHeaders = yamlContent.config.defaults?.headers || {};

        for (const step of scenario.flow) {
          if (step.sleep) {
            await test.step(`Sleep for ${step.sleep}ms`, async () => {
              const duration = interpolateObject(step.sleep, context);
              await sleep(Number(duration));
            });
          } else if (step.function) {
            // Prepend the YAML file name to the function step description
            await test.step(`Executing function: ${step.function.name}`, async () => {
              const funcName = step.function.name;
              const args = (step.function.args || []).map(arg => interpolateObject(arg, context));

              if (allFunctions[funcName]) {
                const result = await allFunctions[funcName](...args);
                if (step.function.capture) {
                  context[step.function.capture] = result;
                }
              } else {
                throw new Error(`Function '${funcName}' not found`);
              }
            });
          } else if (step.log) {
            await test.step(`Logging: ${step.log}`, async () => {
              let interpolatedLog;
              if (typeof step.log === 'string') {
                // Extract the variable name and path from {{variableName.path.to.property}}
                const match = step.log.match(/{{(.+?)}}/)?.[1];
                if (match) {
                  const paths = match.split('.');
                  let value = context;

                  // Navigate through the object path
                  for (const path of paths) {
                    value = value?.[path];
                    if (value === undefined) break;
                  }

                  interpolatedLog = typeof value === 'object'
                    ? JSON.stringify(value, null, 2)
                    : value;
                } else {
                  interpolatedLog = step.log;
                }
              } else {
                interpolatedLog = JSON.stringify(step.log, null, 2);
              }

              console.log(`[${file}] ${step.log.replace(/[{}]/g, '').trim()}:`, interpolatedLog);
            });
          } else {
            const method = Object.keys(step)[0] as 'get' | 'post' | 'put' | 'patch' | 'delete';
            const stepDetails = step[method];

            // Prepend the YAML file name to the request step description
            const stepDescription = stepDetails.name
              ? `${stepDetails.name}`
              : `${method.toUpperCase()} ${stepDetails.url}`;

            await test.step(stepDescription, async () => {
              const url = `${interpolateString(stepDetails.url, context)}`;
              // Merge default headers with step-specific headers
              const headers = {
                ...interpolateObject(defaultHeaders, context),
                ...interpolateObject(stepDetails.headers || {}, context)
              };
              const data = interpolateObject(stepDetails.json, context);

              if (process.env.GENERATE_CURL_COMMANDS === 'true') {
                const curlCmd = generateCurlCommand(stepDescription,method, url, headers, data);
                fs.appendFileSync(curlLogPath, curlCmd);
                //console.log(curlCmd); 
              }
              
              let response;
              switch (method) {
                case 'get':
                  response = await request.get(url, { headers });
                  break;
                case 'post':
                  response = await request.post(url, { headers, data });
                  break;
                case 'put':
                  response = await request.put(url, { headers, data });
                  break;
                case 'patch':
                  response = await request.patch(url, { headers, data });
                  break;
                case 'delete':
                  response = await request.delete(url, { headers });
                  break;
              }

              let responseBody;
              try {
                responseBody = await response.json();
              } catch (error) {
                console.error(`Failed to parse JSON response from ${url}:`, error);
                console.error(`Response status: ${response.status()}`);
                console.error(`Response body: ${await response.text()}`);
                // Set responseBody to null or an empty object to avoid further errors
                responseBody = null; // or {} if you prefer
              }

              if (stepDetails.capture) {
                for (const captureItem of stepDetails.capture) {
                  if (captureItem.json) {
                    // Use jsonpath.query instead of jsonpath.value
                    const value = jsonpath.query(responseBody, captureItem.json)[0];
                    if (value !== undefined) {
                      context[captureItem.as] = value;
                    }
                  } else if (captureItem.header) {
                    context[captureItem.as] = response.headers()[captureItem.header.toLowerCase()];
                  }
                }
              }

              if (stepDetails.expect) {
                for (const expectation of stepDetails.expect) {
                  if (expectation.statusCode) {
                    expect(response.status(), `Expected status code to be ${expectation.statusCode}, and received ${response.status()}`).toBe(expectation.statusCode);
                  }
                  if (expectation.hasProperty) {
                    expect(responseBody).toHaveProperty(expectation.hasProperty);
                  }
                  if (expectation.equals) {
                    const [actual, expected] = expectation.equals.map((value: string) => interpolateObject(value, context));
                    if (!areEqual(actual, expected)) {
                      throw new Error(`Expected ${actual} to equal ${expected}`);
                    }
                  }
                  if (expectation.greaterThanZero) {
                    const actualValue = interpolateObject(expectation.greaterThanZero, context).trim();
                    const numericValue = Number(actualValue);
                    expect(numericValue, `Expected value should be greater than zero. received value - ${numericValue}`).toBeGreaterThan(0);
                  }
                }
              }
            });
          }
        }
      });
    }
  }
}

function generateCurlCommand(stepDescription: string, method: string, url: string, headers: any, data?: any): string {
  let curlLogs = `### ${stepDescription} - ${new Date().toLocaleString()}\n `;

  curlLogs += `curl --location --request ${method.toUpperCase()} '${url}'`;

  curlLogs += ` \\\n  -x "$BURP_PROXY"`
  curlLogs += ` \\\n  --cacert "$CACERT_PATH"`
  
  // Add headers
  for (const [key, value] of Object.entries(headers)) {
    curlLogs += ` \\\n  --header '${key}: ${value}'`;
  }

  curlLogs += ` \\\n  --header 'Content-Type: application/json'`;

  // Add data if present
  if (data) {
    curlLogs += ` \\\n  --data '${JSON.stringify(data, null, 2)}'`;
  }
  curlLogs += '\n\n';
  
  return curlLogs;
}