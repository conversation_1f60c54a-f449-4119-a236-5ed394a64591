# refund-policy.yml
config:
  target: https://bff.allen-stage.in

  defaults:
    headers:
      accept: application/json
      content-type: application/json

  fixtures:
    - bffURL
    - testData

scenarios:
  - name: "verify refund policy"
    flow:
      - function:
          name: getDates
          capture: dates
      # student previewing refind policy 
      - post:
          name: "refund policy"
          url: "{{bffURL}}/api/v1/pages/getPage"
          json:
            page_url: /refund-policy
          capture:
            - json: $.data
              as: refund_policy_response
          expect:
            - statusCode: 200
            - hasProperty: "data.page_info.name"
            - hasProperty: "data.page_info.data.title"
      - log: " refund policy response : - {{ bffURL }}"

     
