# with-onboarding-ecommerce.yml
# verify course details till checkout for freemium user with onboarding
config:
  target: https://bff.allen-stage.in

  variables:
    country_code: "91"
    persona_type: STUDENT
    otp: 1111
    stream: "STREAM_PRE_MEDICAL"
    class: "CLASS_11"
    full_name: "testUser"
    parent_name: "tester"
    pincode: "560035"
    city: "BENGALURU"
    state: "Karnataka"
    streamUrl_param: "/neet/online-coaching-class-11"
    course_url: "/jee-mains/nurture-online-programs-class-11"
    dob: "12-11-2024"
    parent_number: "**********"
    gender: "MALE"


  defaults:
    headers:
      accept: application/json
      # content-type: application/json
      x-client-type: web
      # x-device-id: 252d78fc-8581-4e95-874c-7f63c1f7b688

  
  fixtures:
    - bffURL
    - testData

scenarios:
  - name: "verify user onboarding and course checkout"
    skipInProd: true
    flow:
    # below function is using to generate ransom string for random email
      - function:
          name: generateRandomString
          args:
            - 6
            - "testmail"
          capture: randomEmail
      - function:
          name: generateRandomMobileNumber
          capture: testuser
      # send otp for a new freemium user
      - post:
          name: "send otp"
          url: "{{bffURL}}/api/v1/auth/sendOtp"
          headers:
            x-device-id: 252d78fc-8581-4e95-874c-7f63c1f7b688
          json:
            country_code: "{{country_code}}"
            phone_number: "{{testuser}}"
            persona_type: "{{persona_type}}"
          capture:
            - json: $.data.transaction_id
              as: data_transaction_id
          expect:
            - statusCode: 200
            - hasProperty: "data.transaction_id"

        # verfying  the otp  and generating student token
      - post:
          name: "verify otp"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          headers:
            x-device-id: 252d78fc-8581-4e95-874c-7f63c1f7b688
          json:
            otp: 1111
            transaction_id: "{{data_transaction_id}}"
          capture:
            - header: x-access-token
              as: student_access_token
          expect:
            - statusCode: 200
      - sleep: 1000
      # student details before onboarding
      - get:
          name: "get student details"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            Authorization: "Bearer {{student_access_token}}"
          capture:
            - json: $.data
              as: preOnboarding_student_detail_response
          expect:
            - statusCode: 200
            - hasProperty: "data.student_detail.status"

      # onboarding a new user
      - put:
          name: "onboarding user"
          url: "{{bffURL}}/api/v1/users"
          headers:
            Authorization: "Bearer {{student_access_token}}"
          json:
            stream: "{{stream}}"
            class: "{{class}}"
            full_name: "{{full_name}}"
          capture:
            - json: $.data
              as: onboarding_response
          expect:
            - statusCode: 200
            - hasProperty: "data.student_info.stream"
            - hasProperty: "data.student_info.stream_display_name"
            - equals:
                - "{{onboarding_response.student_info.current_class}}"
                - "{{class}}"

      # student details post onboarding
      - get:
          name: "get student details after onboarding"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: "Bearer {{student_access_token}}"
          capture:
            - json: $.data
              as: postOnboarding_student_detail_response
          expect:
            - statusCode: 200
            - hasProperty: "data.student_detail.status"
            - equals: 
                - "{{postOnboarding_student_detail_response.student_detail.status}}"
                - "ONBOARDED"

      # validating pre-homepage for freemium user login
      - post:
          name: "verify pre-home page"
          url: "{{bffURL}}/api/v1/pages/getPage"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            page_url: /
          capture:
            - json: $.data
              as: pre_home_page_response
          expect:
            - statusCode: 200
            - hasProperty: "data.page_info.data.description"
            - hasProperty: "data.page_info.data.title"
      - log: "prehome-page response : - {{pre_home_page_response}}"

        # student navigation to the stream card
      - post:
          name: "verify PLP page with course card"
          url: "{{bffURL}}/api/v1/pages/getPage"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            page_url: "{{streamUrl_param}}"
          capture:
            - json: $.data
              as: stream_details_info
          expect:
            - statusCode: 200
            - hasProperty: "data.page_info.name"
            - hasProperty: "data.page_info.data.title"
      - log: "stream details page information :  -  {{stream_details_info}}"

      # student navigating with particular class course
      - post:
          name: "verify course details page"
          url: "{{bffURL}}/api/v1/pages/getPage"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            page_url: "{{course_url}}"
          capture:
            - json: $.data.page_content.widgets[0].data.payment.listing_details.languages[0].id
              as: course_listing_id
            - json: $.data..page_content.widgets[0].data.payment.listing_details.languages[0].preferences[0].values[0].id
              as: course_phase_id
          expect:
            - statusCode: 200
            - hasProperty: "data.page_info.data.description"
            # - hasProperty: "data.page_info.data.title"
            # - hasProperty: "data.page_content.widgets[1].data.title"
            # - hasProperty: "data.page_content.widgets[1].data.cards[0].title"

      - log: "course description : - {{course_details_response}}"

      #  course enroll to cart api 
      - post:
          name: "verify course enroll cart"
          url: "{{bffURL}}/api/v1/v2/cart"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            listings:
              - id: "{{course_listing_id}}"
                preference:
                  start_date: "{{course_phase_id}}"
          capture:
            - json: $.data.cart_info.id
              as: cart_orderId
            
            - json: $.data.cart_info.user_id
              as: cart_user_id

            - json: $.data.cart_info.cart_items[0].listing_details.meta.id
              as: data_cart_info_cart_items_0_listing_details_id

            - json: $.data.cart_info.cart_items[0].listing_details.content.title
              as: data_cart_info_cart_items_0_listing_details_title

          expect:
            - statusCode: 200
            - hasProperty: "data.cart_info.id"
            - hasProperty: "data.cart_info.cart_items[0].listing_details.meta.id"
            - hasProperty: "data.cart_info.cart_items[0].listing_details.content.title"
      - log: "cart response : - {{data_cart_info_cart_items_0_listing_details_title}}"

      # add student details
      - put:
          name: "add student details"
          url: "{{bffURL}}/api/v1/v2/cart/{{cart_orderId}}"
          headers:
            authorization: "Bearer {{student_access_token}}"
            content-type: "application/json"
          json:
            user_details:
              full_name: "{{full_name}}"
              phone: "{{testuser}}"
              gender: "{{gender}}"
              dob: "{{dob}}"
              email: "{{randomEmail}}@gmail.com"
              id: "{{cart_user_id}}"
              parent_details:
                name: "{{parent_name}}"
                phone: "{{parent_number}}"
                gender: "{{gender}}"

          capture:
            - json: $.data
              as: studentDetails_response
          expect:
            - statusCode: 200
            - hasProperty: "data.cart_info.user_details.email"
            - hasProperty: "data.cart_info.user_details.dob"
            - equals: 
                - "{{studentDetails_response.cart_info.user_details.dob}}"
                - "{{dob}}"
      - log: "student details : - {{studentDetails_response}}"

      # add billing address
      - put:
          name: "add billing details"
          url: "{{bffURL}}/api/v1/v2/cart/{{cart_orderId}}"
          headers:
            authorization: "Bearer {{student_access_token}}"
            content-type: "application/json"
          json:
            billing_address_details:
              address_line_1: 'tetingrg'
              address_line_2: 'rtrgfrefe'
              city: "{{city}}"
              state: "{{state}}"
              pinCode: "{{pincode}}"
            shipping_address_details:
              address_line_1: 'tetingrg'
              address_line_2: 'rtrgfrefe'
              city: "{{city}}"
              state: "{{state}}"
              pinCode: "{{pincode}}"
          capture:
            - json: $.data
              as: add_address_response
          expect:
            - statusCode: 200
            - hasProperty: "data.cart_info.id"
            - hasProperty: "data.cart_info.user_id"
            - hasProperty: "data.cart_info.user_address_details.billing_address_details.city"
            - hasProperty: "data.cart_info.user_address_details.billing_address_details.address_line_1"

      # delete the user Account
      - delete:
          name: "delete created user"
          url: "{{bffURL}}/api/v1/users"
          headers:
            authorization: "Bearer {{student_access_token}}"
          capture:
            - json: $.data.message
              as: data_message
          expect:
            - statusCode: 200
            - hasProperty: "data.message"
            - equals: 
                - "{{data_message}}"
                - "User deleted successfully"