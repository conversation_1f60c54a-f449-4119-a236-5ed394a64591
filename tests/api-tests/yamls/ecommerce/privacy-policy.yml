# refund-policy.yml
config:
  target: https://bff.allen-stage.in

  defaults:
    headers:
      accept: application/json
      content-type: application/json

  fixtures:
    - bffURL
    - testData

scenarios:
  - name: "verify privacy and policy"
    flow:
      # student previewing privacy policy 
      - post:
          name: "privacy policy"
          url: "{{bffURL}}/api/v1/pages/getPage"
          json:
            page_url: /privacy-policy
          capture:
            - json: $.data
              as: privacy_policy_response
          expect:
            - statusCode: 200
            - hasProperty: "data.page_info.name"
            - hasProperty: "data.page_info.data.title"
      - log: " privacy and policy response : - {{ bffURL }}"

     
