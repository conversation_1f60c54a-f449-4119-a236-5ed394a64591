# create-broadcast-teacher.yml
# "Create Broadcast by the teacher"
config:
  target: https://bff.allen-stage.in
  variables:
    expression: ""
    country_code: "91"
    persona_type: STUDENT
    center_id: fa_npghQRtILotkeoZyLmfAO
    course_id: cr_WhO1B61BXTumqJvtEWz9F
    stream: STREAM_JEE_MAIN_ADVANCED
    class: CLASS_11
    tenant_id: aUSsW8GI03dyQ0AIFVn92
    description: https://www.google.co.in/
    category: General
    batch: bt_8oiUejORPLLwn7qk11lsv
    phase: ph_FnUDbr1OgwWEvfWKrtZjP
    campus: fa_npghQRtILotkeoZyLmfAO
    priority: Medium
    sender_type: NOTICE_SENDER_TEACHER
    sender: dCQzI6EERqe2Is0QthjCM
    expiry: 1735476573
    fileId: "87801f27-2ba5-4ed8-99ca-362cee995024.png"

  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web

  fixtures:
    - adminURL
    - bffURL
    - testData
    - login

scenarios:
  - name: "Create notification by the teacher and verify the status of noticeboard"
    flow:
      - function:
          name: generateRandomString
          args:
            - 4
            - "api_"
          capture: random_crm_name
      - function: 
          name: getDates
          capture: dates
        # Step 1: create a notice board notication as teacher using send api
      - post:
          name: "Send notice board notification as teacher"
          url: "{{bffURL}}/api/v1/notice-board/notice/send"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            notice_id: ""
            title: "{{random_crm_name}}"
            description: "{{description}}"
            category: "{{category}}"
            media:
              - file_id: "{{fileId}}"
                file_type: png
            expiry: "{{dates.tomorrowDate}}"
            recipient: RECIPIENT_STUDENT
            student_attribute:
              center:
                - "{{testData.env.CENTER_ID}}"
              course:
                - "{{testData.batch.course}}"
              batch:
                - "{{testData.batch.id}}"
              phase:
                - "{{testData.batch.phase}}"
              campus:
                - "{{testData.env.CENTER_ID}}"
              stream:
                - "{{testData.syllabusConfig.group_node_details.0.group_node.meta_data.stream}}"
              class:
                - "{{testData.syllabusConfig.group_node_details.0.group_node.meta_data.class}}"
            priority: "{{priority}}"
            sender_info:
              sender_type: "{{sender_type}}"
              sender: "{{sender}}"
          capture:
            - json: $.data
              as: send_notice_response
          expect:
            - statusCode: 200
            - hasProperty: "data.notice_id"
            - hasProperty: "data.title"
            - hasProperty: "data.description"
            - or:
                - hasProperty: "data.created_by_id"
                - hasProperty: "data.created_by"
            - hasProperty: "data.status"
            - equals:
                - "{{send_notice_response.status}}"
                - "NOTICE_STATUS_SEND"
            - hasProperty: "data.sender_info.sender_type"
            - equals:
                - "{{send_notice_response.sender_info.sender_type}}"
                - "NOTICE_SENDER_TEACHER"

      - log: "response of send notice {{send_notice_response}} "
      # - log: "created by  {{data_created_by}}  and data status is {{data_status}} "

      # step 2: verify the details of notice
      - get:
          name: "verify the details of sent notice"
          url: "{{bffURL}}/api/v1/notice-board/notice/{{send_notice_response.notice_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: sent_notice_response
          expect:
            - statusCode: 200
            - hasProperty: "data.recipient"

      # refresh the Page of Notce board
      - get:
          name: "verify notification from student side"
          url: "{{bffURL}}/api/v1/discovery/user-notices?entity_id={{send_notice_response.notice_id}}"
          headers:
            Authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data
              as: student_response
          expect:
            - statusCode: 200
      - log: "student response : - {{student_response}}"

      # DELETE THE BROADCAST From Admin Side
      # - delete:
      #     name: "delete notice after verification"
      #     url: "https://bff.allen-stage.in/internal-bff/api/v1/notice-board/notice/{{send_notice_response.notice_id}}"
      #     headers:
      #       authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
      #       x-device-id: "fe73d42a-c300-4ffb-b4b4-12618d31219d"
      #     capture:
      #       - json: $.data
      #         as: delete_notice_response
      #     expect:
      #       - statusCode: 200
      # - log: "delete response : - {{delete_notice_response}}"
