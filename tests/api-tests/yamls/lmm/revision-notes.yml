# revision-notes.yml
# Flow - Student accesses revision notes, views subjects and topics, views content, and tracks progress
config:
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web
  fixtures:
    - bffURL
    - testData
    - login

scenarios:
  - name: "Verify student can access and view revision notes"
    skipInProd: true
    flow:
      - function: 
          name: getDates
          capture: dates
          
      # Step 1: Get subjects and topics for revision notes
      - get:
          name: "Get revision notes subjects and topics"
          url: "{{bffURL}}/api/v1/revision-notes/subjects-and-topics"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data.heading
              as: heading
            - json: $.data.lists[0].taxonomy_id
              as: taxonomy_id
            - json: $.data.lists[0].node_id
              as: subject_node_id
            - json: $.data.lists[0].name
              as: subject_name
            - json: $.data.lists[0].lists[0].taxonomy_id
              as: topic_taxonomy_id
            - json: $.data.lists[0].lists[0].node_id
              as: topic_node_id
            - json: $.data.lists[0].lists[0].name
              as: topic_name
          expect:
            - statusCode: 200
            - hasProperty: "data.lists"
            - hasProperty: "data.heading"
            - equals:
                - "{{heading}}"
                - "Revision Notes"
      
      # Step 2: Get revision notes collection to get a valid session ID
      - post:
          name: "Get revision notes collection"
          url: "{{bffURL}}/v1/revision-notes/collection"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            user_id: "{{testData.student.id}}"
            taxonomy:
              taxonomy_id: "{{taxonomy_id}}"
              node_id: "{{topic_node_id}}"
          capture:
            - json: $.collections[0].collectionId
              as: material_id
            - json: $.collections[0].sessionId
              as: session_id
            - json: $.collections[0].presignedUrl
              as: presigned_url
          expect:
            - statusCode: 200
            - hasProperty: "collections"
            - hasProperty: "collections[0].presignedUrl"
      
      # Step 3: Track progress for revision notes using the session ID
      - post:
          name: "Get revision notes progress"
          url: "{{bffURL}}/v1/revision-notes/progress"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            session_id: "{{session_id}}"
            user_id: "{{testData.student.id}}"
          capture:
            - json: $.contentProgresses[0].contentId
              as: content_element_id
            - json: $.contentProgresses
              as: content_progresses
          expect:
            - statusCode: 200
            - hasProperty: "sessionId"
            - hasProperty: "contentProgresses"
      
      # Step 4: Set progress for the revision notes content
      - post:
          name: "Set progress for revision notes"
          url: "{{bffURL}}/v1/revision-notes/set-progress"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            progress_infos:
              - content_id: "{{content_element_id}}"
                progress: 1
            session_id: "{{session_id}}"
            user_id: "{{testData.student.id}}"
            total_documents: 1
            finished_documents: 0
          capture:
            - json: $.status
              as: status
          expect:
            - statusCode: 200
            - hasProperty: "status"
            - hasProperty: "message"
            - equals:
                - "{{status}}"
                - "200"
      
      # Step 5: Verify progress has been updated
      - post:
          name: "Verify revision notes progress"
          url: "{{bffURL}}/v1/revision-notes/progress"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            session_id: "{{session_id}}"
            user_id: "{{testData.student.id}}"
          capture:
            - json: $.contentProgresses[0].progress
              as: updated_progress
          expect:
            - statusCode: 200
            - hasProperty: "contentProgresses"
            - equals:
                - "{{updated_progress}}"
                - "CURRENTLY_READING"
      
      - log: "Successfully verified revision notes flow for subject '{{subject_name}}' and topic '{{topic_name}}'"
