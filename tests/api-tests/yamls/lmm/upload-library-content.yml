# upload-library-content.yml
# Flow - Uploading 1 video and 1 pdf to Library
config:
  variables:
    type_1: RECORDED_CONTENT
    content_sub_type: RECORDED_LECTURES
    video_filename: "countDownVideo.mp4"
    pdf_type: STUDY_MATERIALS
    pdf_sub_type: STUDY_MODULE
    pdf_filename: uploadfile.pdf
    language: ENGLISH
    session: "04_2025__03_2026"
    reviewed_on: 1727776548790
    content_expiry: *********
    learning_category: INSTRUCTED
    taxonomy_name: ADPL_STREAM_JEE_MAIN_ADVANCED_2024_2025
    taxonomy_center_id: ADPL
    class: Class 11
    subject: Chemistry
    super_topic: Algebra
    topic: Binomial Theorem
    taxonomy_id: 1701181887VZ
    node_id: "2"
    facility_id: facility_j1IwGFcbbdl3LSALAsGIX
    course_id: course_1InxzrPx4IAV03IyVCc84
    phase_id: ph_lPny3mCqJcOe6WXFiYM4M
    release_date: 01-10-2024
    rowId_1: 91891457-7778-46fe-b618-586f8c5c11fd
    rowId_2: 1a7a289c-745a-4202-8348-ca0701384ae8
    file_format_1: mp4
    file_format_2: pdf

  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web
  fixtures:
    - testData
    - bffURL

scenarios:
  - name: "upload bulk files 1 vedio and 1 pdf"
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "automation_video_"
          capture: random_content_name
      - function:
          name: generateRandomString
          args:
            - 6
            - "automation_pdf_"
          capture: random_pdf_name
      - function: 
          name: getDates
          capture: dates
      # step-1 : validate the content of the uploading files
      - post:
          name: "validate the content of the uploading files"
          url: "{{bffURL}}/api/v1/learningMaterials/validate/bulk"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            requests:
              - name: "{{random_content_name}}"
                description: ""
                type: "{{type_1}}"
                sub_type: "{{content_sub_type}}"
                language: "{{language}}"
                content_stakeholders:
                  created_by: some_ops
                  faculty_emp_id: ""
                client_id: console
                tenant_id: string
                material_id: ""
                session: "{{session}}"
                center_id: "{{testData.env.CENTER_NAME}}"
                review_details:
                  reviewed_on: "{{dates.startTime}}"
                content_expiry: "{{content_expiry}}"
                duration_minutes: 0
                hashtags: []
                learning_category: "{{learning_category}}"
                master_course: ""
                taxonomy_name: "{{taxonomy_name}}"
                taxonomy_attributes:
                  taxonomy_id: string
                  class: "{{class}}"
                  subject: ""
                  super_topic: ""
                  topic: ""
                  sub_topic: []
                filename: "{{video_filename}}"
              - name: "{{random_pdf_name}}"
                description: ""
                type: "{{pdf_type}}"
                sub_type: "{{pdf_sub_type}}"
                language: "{{language}}"
                content_stakeholders:
                  created_by: some_ops
                  faculty_emp_id: ""
                client_id: console
                tenant_id: string
                material_id: ""
                session: "{{session}}"
                center_id: "{{testData.env.CENTER_NAME}}"
                review_details:
                  reviewed_on: "{{dates.startTime}}"
                content_expiry: "{{content_expiry}}"
                duration_minutes: 0
                hashtags: []
                learning_category: "{{learning_category}}"
                master_course: ""
                taxonomy_name: "{{taxonomy_name}}"
                taxonomy_attributes:
                  taxonomy_id: string
                  class: "{{class}}"
                  subject: ""
                  super_topic: ""
                  topic: ""
                  sub_topic: []
                filename: "{{pdf_filename}}"
          capture:
            - json: $.data
              as: content_validate_response
          expect:
            - statusCode: 200
      - log: "content_validate_response: {{content_validate_response}} "

      #step 2: bulk create API
      - post:
          name: "creating the files post validated"
          url: "{{bffURL}}/api/v1/learningMaterials/bulk_create"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            requests:
              - learning_material:
                  rowId: "{{rowId_1}}"
                  name: "{{random_content_name}}"
                  type: "{{type_1}}"
                  taxonomies:
                    - taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
                      node_id: "{{node_id}}"
                  language: "{{language}}"
                  content_stakeholders:
                    faculty_name: ""
                    created_by: " "
                    faculty_emp_id: ""
                  taxonomy_name: "{{taxonomy_name}}"
                  duration_minutes: 0
                  client_id: Console
                  tenant_id: ALLEN
                  session: "{{session}}"
                  center: "{{testData.env.CENTER_NAME}}"
                  center_id: "{{testData.env.CENTER_ID}}"
                  master_course: ""
                  sub_type: "{{content_sub_type}}"
                  learning_category: "{{learning_category}}"
                  hashtags: []
                filename: "{{video_filename}}"
              - learning_material:
                  rowId: "{{rowId_2}}"
                  name: "{{random_pdf_name}}"
                  type: "{{pdf_type}}"
                  taxonomies:
                    - taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
                      node_id: "{{node_id}}"
                  language: "{{language}}"
                  content_stakeholders:
                    faculty_name: ""
                    created_by: " "
                    faculty_emp_id: ""
                  taxonomy_name: "{{taxonomy_name}}"
                  duration_minutes: 0
                  client_id: Console
                  tenant_id: ALLEN
                  session: "{{session}}"
                  center: "{{testData.env.CENTER_NAME}}"
                  center_id: "{{testData.env.CENTER_ID}}"
                  master_course: ""
                  sub_type: "{{pdf_sub_type}}"
                  learning_category: "{{learning_category}}"
                  hashtags: []
                filename: "{{pdf_filename}}"
            draft: false
          capture:
            - json: $.data.materials[0].id
              as: create_api_material_id
            - json: $.data.materials[1].id
              as: create_api_second_material_id
          expect:
            - statusCode: 200
            - hasProperty: "data.materials[0].id"
            - hasProperty: "data.materials[0].material_id"
            - hasProperty: "data.materials[0].name"
      - log: "create_api_post_validation : - {{create_api_material_id}}"

      #step 3: upload the multiple file it create the upload Id for file 1
      - post:
          name: "init multipart upload for first file"
          url: "{{bffURL}}/api/v1/learningMaterials/{{create_api_material_id}}/init_multipart_upload"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            file_format: "{{file_format_1}}"
          capture:
            - json: $.data.upload_data.upload_id
              as: captured_data_upload_data_upload_id
          expect:
            - statusCode: 200
            # - hasProperty: "captured_data_upload_data_upload_id"
      - log: "first file upload id : - {{captured_data_upload_data_upload_id}}"

      #step 4: first file upload part
      - post:
          name: "preassigned url for first file"
          url: "{{bffURL}}/api/v1/learningMaterials/{{create_api_material_id}}/upload_part"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            part_number: 1
            upload_id: "{{captured_data_upload_data_upload_id}}"
          capture:
            - json: $.data.upload_data.presigned_url
              as: captured_data_upload_data_presigned_url
          expect:
            - statusCode: 200
      - log: "first file preassigned url  : - {{captured_data_upload_data_presigned_url}}"

      # Step 5: upload the video file using the presigned URL
      - put:
          name: "upload video file to S3"
          url: '{{captured_data_upload_data_presigned_url}}'
          headers:
            accept: application/json, text/plain, */*
            accept-language: en-GB,en-US;q=0.9,en;q=0.8
            connection: keep-alive
            content-type: application/x-www-form-urlencoded
            sec-fetch-dest: empty
            sec-fetch-mode: cors
            sec-fetch-site: cross-site
            sec-ch-ua-mobile: '?0'
            sec-ch-ua-platform: '"macOS"'
          data: "test-data/countDownVideo.mp4"
          capture:
            - header: ETag # Capture the ETag header from the response
              as: etag_response1

      # Step 6: complete the multipart upload
      - post:
            name: "complete multipart upload"
            url: '{{bffURL}}/api/v1/learningMaterials/{{create_api_material_id}}/complete_multipart_upload'
            headers:
              accept: application/json
              accept-language: en-GB,en-US;q=0.9,en;q=0.8
              authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
              content-type: application/json
              priority: u=1, i
              sec-ch-ua-mobile: '?0'
              sec-ch-ua-platform: '"macOS"'
              sec-fetch-dest: empty
              sec-fetch-mode: cors
              sec-fetch-site: same-site
              x-client-type: web
              x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
            json: 
              upload_id: "{{captured_data_upload_data_upload_id}}"
              parts:
                - part_number: 1
                  etag: "{{etag_response1}}"
            capture:
              - json: $.data # Assuming the response contains relevant data
                as: complete_upload_response
            expect:
              - statusCode: 200 # Expecting a 200 status code
      - log: "Multipart upload completed successfully: {{complete_upload_response}}"

      - sleep: 2000 #sleep required to load the content

      #step 7: upload the multiple file it create the upload Id for file 2
      - post:
          name: "init multipart upload for second file"
          url: "{{bffURL}}/api/v1/learningMaterials/{{create_api_second_material_id}}/init_multipart_upload"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            file_format: "{{file_format_2}}"
          capture:
            - json: $.data.upload_data.upload_id
              as: captured_data_upload_data_upload_id
          expect:
            - statusCode: 200
      - log: "second file upload id : - {{captured_data_upload_data_upload_id}}"

      #step 8: second  file preassigned url
      - post:
          name: "preassigned url for second file"
          url: "{{bffURL}}/api/v1/learningMaterials/{{create_api_second_material_id}}/upload_part"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            part_number: 1
            upload_id: "{{captured_data_upload_data_upload_id}}"
          capture:
            - json: $.data.upload_data.presigned_url
              as: presigned_url_second_content
          expect:
            - statusCode: 200
      - log: "second file preassigned url  : - {{presigned_url_second_content}}"

      # Step 9: upload the video file using the presigned URL for second content
      - put:
          name: "upload video file to S3 for second content"
          url: '{{presigned_url_second_content}}'
          headers:
            accept: application/json, text/plain, */*
            accept-language: en-GB,en-US;q=0.9,en;q=0.8
            connection: keep-alive
            content-type: application/x-www-form-urlencoded
            sec-fetch-dest: empty
            sec-fetch-mode: cors
            sec-fetch-site: cross-site
            sec-ch-ua-mobile: '?0'
            sec-ch-ua-platform: '"macOS"'
          data: "test-data/uploadfile.pdf"
          capture:
            - header: ETag # Capture the ETag header from the response
              as: etag_response2

      # Step 10: complete the multipart upload
      - post:
            name: "complete multipart upload for second content"
            url: '{{bffURL}}/api/v1/learningMaterials/{{create_api_second_material_id}}/complete_multipart_upload'
            headers:
              accept: application/json
              accept-language: en-GB,en-US;q=0.9,en;q=0.8
              authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
              content-type: application/json
              priority: u=1, i
              sec-ch-ua: '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"'
              sec-ch-ua-mobile: '?0'
              sec-ch-ua-platform: '"macOS"'
              sec-fetch-dest: empty
              sec-fetch-mode: cors
              sec-fetch-site: same-site
              x-client-type: web
              x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
            json: 
              upload_id: "{{captured_data_upload_data_upload_id}}"
              parts:
                - part_number: 1
                  etag: "{{etag_response2}}"
            capture:
              - json: $.data # Assuming the response contains relevant data
                as: complete_upload_response
            expect:
              - statusCode: 200 # Expecting a 200 status code
      - log: "Multipart upload completed successfully: {{complete_upload_response}}"

      - sleep: 2000 #sleep required to load the content

      # Step 11: fetch the uploaded video content in lmm page using search api
      - post:
            name: "fetch the uploaded video content in lmm page using search api"
            url: '{{bffURL}}/api/v1/learningMaterials/filter/v2/0'
            headers:
              accept: application/json
              authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
              content-type: application/json
              x-client-type: web
              x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
            json: 
              name: "{{random_content_name}}"
              page_size: 25
            capture:
              - json: $.data.Materials[0].name
                as: uploaded_content_name
            expect:
              - statusCode: 200 # Expecting a 200 status code
              - equals:
                  - "{{uploaded_content_name}}"
                  - "{{random_content_name}}"

      # Step 12: fetch the uploaded pdf content in lmm page using search api
      - post:
            name: "fetch the uploaded pdf content in lmm page using search api"
            url: '{{bffURL}}/api/v1/learningMaterials/filter/v2/0'
            headers:
              accept: application/json
              authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
              content-type: application/json
              x-client-type: web
              x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
            json: 
              name: "{{random_pdf_name}}"
              page_size: 25
            capture:
              - json: $.data.Materials[0].name
                as: uploaded_pdf_name
            expect:
              - statusCode: 200 # Expecting a 200 status code
              - equals:
                  - "{{uploaded_pdf_name}}"
                  - "{{random_pdf_name}}"

      # Step 13: Inactive video content
      - patch:
            name: "Inactive video content"
            url: '{{bffURL}}/api/v1/learningMaterials/{{create_api_material_id}}'
            headers:
              accept: application/json
              authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
              content-type: application/json
              x-client-type: web
              x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
            json: 
              learning_material:
                name: "{{random_content_name}}"
                type: "{{type_1}}"
                taxonomies:
                  - taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
                    center_id: ''
                    node_id: "{{node_id}}"
                language: "{{language}}"
                content_stakeholders:
                  created_by: '-'
                active: false
                client_id: 'Console'
                tenant_id: 'ALLEN'
                session: "{{session}}"
                center_id: "{{testData.env.CENTER_ID}}"
                master_course: ''
                sub_type: "{{content_sub_type}}"
                learning_category: "{{learning_category}}"
                hashtags:
                  - '-'
                duration_minutes: 0
            capture:
              - json: $.data.material.active
                as: content_status
            expect:
              - statusCode: 200 # Expecting a 200 status code
              - equals:
                  - "{{content_status}}"
                  - false
      - log: "Content Status: {{content_status}}"

      # Step 14: Inactive pdf content
      - patch:
            name: "Inactive pdf content"
            url: '{{bffURL}}/api/v1/learningMaterials/{{create_api_second_material_id}}'
            headers:
              accept: application/json
              authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
              content-type: application/json
              x-client-type: web
              x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
            json: 
              learning_material:
                name: "{{random_pdf_name}}"
                type: "{{pdf_type}}"
                taxonomies:
                  - taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
                    center_id: ''
                    node_id: "{{node_id}}"
                language: "{{language}}"
                content_stakeholders:
                  created_by: '-'
                active: false
                client_id: 'Console'
                tenant_id: 'ALLEN'
                session: "{{session}}"
                center_id: "{{testData.env.CENTER_ID}}"
                master_course: ''
                sub_type: "{{pdf_sub_type}}"
                learning_category: "{{learning_category}}"
                hashtags:
                  - '-'
                duration_minutes: 0
            capture:
              - json: $.data.material.active
                as: pdf_status
            expect:
              - statusCode: 200 # Expecting a 200 status code
              - equals:
                  - "{{pdf_status}}"
                  - false
      - log: "Pdf Status: {{pdf_status}}"