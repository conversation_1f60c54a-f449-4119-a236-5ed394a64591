# view-edit-taxonomy.yml
# Flow - View and Edit Taxonomy Center
config:
  variables:
    stream: "STREAM_JEE_MAIN_ADVANCED"
    class: "Class 11"
    subject_name: "Chemistry"

  defaults:
    headers:
      accept: application/json
      x-client-type: web

  fixtures:
    - bffURL
    - testData

scenarios:
  - name: "Verify user is able to view and edit center taxonomy"
    flow:
      # Step 1: Get taxonomy ID for the center
      - get:
          name: "Get taxonomy ID for center"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/getNodeID?center={{testData.env.CENTER_NAME}}&stream={{stream}}&class={{class}}&taxonomy_session={{testData.env.CENTER_SESSION}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.taxonomy_id
              as: taxonomy_id
            - json: $.data.class
              as: class_id
          expect:
            - statusCode: 200
            - hasProperty: "data.taxonomy_id"
      
      # Step 2: Get root node children to view subjects
      - get:
          name: "Get root node children (subjects)"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/{{taxonomy_id}}/node/{{class_id}}/children?ignoreCache=true"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.nodes
              as: subject_nodes
            - json: $.data.nodes[?(@.name=='Chemistry')].id
              as: subject_id
          expect:
            - statusCode: 200
            - hasProperty: "data.nodes"
      # Step 3: Verify validation for empty node name
      - patch:
          name: "Attempt to add node with empty name"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/addNode"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          json:
            taxonomyId: "{{taxonomy_id}}"
            node:
              name: ""
              parent_node: "1"
          capture:
            - json: $.reason
              as: error_reason
          expect:
            - statusCode: 400
            - equals:
                - "{{error_reason}}"
                - "Bad Request"

      
      # Step 3: Update node name (edit taxonomy)
      - patch:
          name: "Update Chemistry node name"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/updateNode"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          json:
            taxonomyId: "{{taxonomy_id}}"
            node:
              Id: "{{subject_id}}"
              name: "{{subject_name}}"
          expect:
            - statusCode: 200
      
      # Step 4: Verify the node was updated by fetching children again
      - get:
          name: "Verify node update by fetching children again"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/{{taxonomy_id}}/node/{{class_id}}/children?ignoreCache=true"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.nodes
              as: updated_subject_nodes
          expect:
            - statusCode: 200
            - hasProperty: "data.nodes"
            - contains:
                - "{{updated_subject_nodes}}"
                - "Chemistry"
                - "name"
      
      # Step 5: Get children of Chemistry subject to verify its structure
      - get:
          name: "Get children of Chemistry subject"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/{{taxonomy_id}}/node/{{subject_id}}/children?ignoreCache=true"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.nodes
              as: updated_chemistry_nodes
          expect:
            - statusCode: 200
            - hasProperty: "data.nodes"