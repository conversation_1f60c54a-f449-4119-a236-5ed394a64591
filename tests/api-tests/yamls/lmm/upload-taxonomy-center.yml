# # upload-taxonomy-center.yml
# # Flow - Upload CSV to Taxonomy Center
# config:
#   target: "https://bff.allen-stage.in"

#   variables:
#     stream: "STREAM_JEE_MAIN_ADVANCED"
#     class: "Class 12"
#     center: "Automation"
#     urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"
#     sessionValue: "2025_2026"
#   defaults:
#     headers:
#       accept: "application/json, text/plain, */*"
#       user-agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36"
#       # x-device-id: "2a788369-47f8-4556-bd2e-95210450349b"
#       sec-ch-ua-platform: '"macOS"'
#   fixtures:
#     - adminURL
#     - testData

# scenarios:
#   - name: "Upload CSV File to Allen Stage"
#     flow:
#       - post:
#           url: "{{urlIc}}/taxonomy/uploadCSV?center=Automation&stream=STREAM_JEE_MAIN_ADVANCED&class=Class%2012&taxonomy_session=2025_2026"
#           headers:
#             Authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#             Content-Type: "multipart/form-data; boundary=----WebKitFormBoundarySlpPpjdzj5K30bna"
#           body: |
#             ------WebKitFormBoundaryUoaSKKYpTDDBKaMY
#             Content-Disposition: form-data; name="file"; filename="export.csv"
#             Content-Type: text/csv

#             id,name,age
#             1,John Doe,25
#             2,Jane Doe,30

#             ------WebKitFormBoundaryUoaSKKYpTDDBKaMY--
#           capture:
#             - json: $.data
#               as: r
#           expect:
#             - statusCode: 400
#             - equals:
#                 - "{{r.reason}}"
#                 - "Taxonomy already exists for this center+stream+class combination"
