# # upload-nac-content.yml
# # Flow - Uploading Non Academic Content and then Preview it
# config:
#   target: https://bff.allen-stage.in
#   variables:
#     description: nac_testing
#     type: EDUCATION_AND_LEARNING
#     sub_type: FACTS
#     language: ENGLISH
#     filename: countDownVideo.mp4
#     file_format: mp4
#     session: "04_2024__03_2025"
#     sortBy: "updated_at"
#     urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"

#   defaults:
#     headers:
#       accept: application/json
#       content-type: application/json
#       connection: "keep-alive"
#       x-client-id: "console"
#       sec-fetch-dest: "empty"
#       sec-fetch-mode: "cors"
#       sec-fetch-site: "same-site"
#       x-client-type: web
#   fixtures:
#     - adminURL
#     - testData

# scenarios:
#   - name: "Non Accedmic Content upload"
#     flow:
#       - function:
#           name: generateRandomString
#           args:
#             - 6
#             - "automation_api_"
#           capture: random_nac_name
#       # Step 1: upload a video file in noc acadmic content
#       - post:
#           name: "nac bulk create"
#           url: "{{bffURL}}/api/v1/nac/bulkCreate"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             requests:
#               - content:
#                   name: "{{random_nac_name}}"
#                   description: "{{description}}"
#                   faculty_ids: []
#                   center_id: "-"
#                   type: "{{type}}"
#                   sub_type: "{{sub_type}}"
#                   language: "{{language}}"
#                   active: false
#                   duration_minutes: 0
#                   sessions:
#                     - "{{session}}"
#                   center: "-"
#                   hashtags: []
#                   subjects:
#                     - "-"
#                   streams:
#                     - "-"
#                   classes:
#                     - "-"
#                 filename: "{{filename}}"
#           capture:
#             - json: $.data.contents[0].id
#               as: captured_data_contents_0_id
#             - json: $.data.contents[0].name
#               as: nac_content_name
#           expect:
#             - statusCode: 200 # Expecting a 200 status code
#             - hasProperty: "data.contents[0].name"
#       - log: " create nac content_id  : {{captured_data_contents_0_id}}"
#       - log: " create nac content name  : {{nac_content_name}}"

#       # Step 2: upload a  MP4 video file  and generating upload ID
#       - post:
#           name: "initilize multipart for nac file"
#           url: "{{bffURL}}/api/v1/nac/{{captured_data_contents_0_id}}/initMultipart"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             file_format: "{{file_format}}"
#           capture:
#             - json: $.data.upload_id
#               as: captured_data_upload_id
#           expect:
#             - statusCode: 200 # Expecting a 200 status code
#       - log: "upload_id  : {{captured_data_upload_id}}"

#       # Step 3: by using upload Id generating preassigned url
#       - post:
#           name: "generating preassigned url for nac content"
#           url: "{{bffURL}}/api/v1/nac/{{captured_data_contents_0_id}}/partUploadPresignedURL"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             part_number: 1
#             upload_id: "{{captured_data_upload_id}}"
#           capture:
#             - json: $.data.presigned_url
#               as: captured_data_presigned_url
#           expect:
#             - statusCode: 200 # Expecting a 200 status code
#       - log: "preassigned_url  : {{captured_data_presigned_url}}"

    
#       # Step 4: upload the video file using the presigned URL
#       - put:
#           name: "upload video file to S3"
#           url: '{{captured_data_presigned_url}}'
#           headers:
#             accept: application/json, text/plain, */*
#             accept-language: en-GB,en-US;q=0.9,en;q=0.8
#             connection: keep-alive
#             content-type: application/x-www-form-urlencoded
#             sec-fetch-dest: empty
#             sec-fetch-mode: cors
#             sec-fetch-site: cross-site
#             sec-ch-ua-platform: '"macOS"'
#           data: "test-data/countDownVideo.mp4"
#           capture:
#             - header: ETag # Capture the ETag header from the response
#               as: etag_response

#       # Step 5: complete the multipart upload
#       - post:
#             name: "complete multipart upload"
#             url: '{{bffURL}}/api/v1/nac/{{captured_data_contents_0_id}}/completeMultipartUpload'
#             headers:
#               accept: application/json
#               accept-language: en-GB,en-US;q=0.9,en;q=0.8
#               authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#               content-type: application/json
#               sec-fetch-dest: empty
#               sec-fetch-mode: cors
#               sec-fetch-site: same-site
#               x-client-type: web
#               x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
#             json: 
#               upload_id: "{{captured_data_upload_id}}"
#               parts:
#                 - part_number: 1
#                   etag: "{{etag_response}}"
#             capture:
#               - json: $.data # Assuming the response contains relevant data
#                 as: complete_upload_response
#             expect:
#               - statusCode: 200 # Expecting a 200 status code
#       - log: "Multipart upload completed successfully: {{complete_upload_response}}"

#       - sleep: 2000 #sleep required to load the content

#       # Step 6 :  verify uploaded content from the lmm dashboard
#       - post:
#           name: "verify uploaded nac content in list"
#           url: "{{bffURL}}/api/v1/nac/filter/0"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             sort_by: "{{sortBy}}"
#             filter: {}
#             page_size: 25
#           capture:
#             - json: $.data.contents[0].name
#               as: uploaded_content_name
#           expect:
#             - statusCode: 200 # Expecting a 200 status code
#             - hasProperty: "data.contents[0].name"
#             - equals:
#                 - "{{uploaded_content_name}}"
#                 - "{{nac_content_name}}"
#       - log: "uploaded video content: {{uploaded_content_name}}"

#       # Step 7: Inactive nac content
#       - patch:
#             name: "Inactive nac content"
#             url: '{{bffURL}}/api/v1/nac/{{captured_data_contents_0_id}}'
#             headers:
#               accept: application/json
#               authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#               content-type: application/json
#               x-client-type: web
#               x-device-id: 080a5686-2725-4a97-a3dd-705ae81060b5
#             json: 
#               nac:
#                 name: "{{random_nac_name}}"
#                 type: "{{type}}"
#                 sub_type: "{{sub_type}}"
#                 description: "{{description}}"
#                 language: "{{language}}"
#                 sessions:
#                   - "{{session}}"
#                 center_id: "-"
#                 streams:
#                   - "-"
#                 classes:
#                   - "-"
#                 subjects:
#                   - "-"
#                 duration_minutes: 0
#                 hashtags: []
#                 faculty_ids:
#                   - ""
#                 active: false
#             capture:
#               - json: $.data.info.active
#                 as: nac_status
#             expect:
#               - statusCode: 200 # Expecting a 200 status code
#               - equals:
#                   - "{{nac_status}}"
#                   - false
#       - log: "Pdf Status: {{pdf_status}}"
