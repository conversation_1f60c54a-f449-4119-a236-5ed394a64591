# personal-details.yml
# Flow - User changes personal details like email and other personal details like date of birth , blood group and gender.
config:
  target: https://bff.allen-stage.in
  variables:
    phone_number: "1234519009"
    dob: "20-12-2004"
    blood_group: "B-"
    gender: "MALE"
    country_code: "91"
    persona_type: STUDENT
    otp: 1111
    stream: STREAM_JEE_MAIN_ADVANCED
    class: CLASS_11
    full_name: test
  defaults:
    headers:
      content-type: application/json
      x-client-type: web
      x-device-id: b074d2cc-0951-44fc-9540-3dc169c032ce

  fixtures:
    - bffURL
    - testData
    - login

scenarios:
  - name: Verify student is able to update the EMAIL in personal details for freemium
    skipInProd: true
    flow:
      - sleep: 3000
      - function:
          name: generateRandomEmail
          capture: email
      # send otp for a new freemium user
      - post:
          name: "Verify student is able to send the otp using phone number"
          url: "{{bffURL}}/api/v1/auth/sendOtp"
          headers:
            x-device-id: 252d78fc-8581-4e95-874c-7f63c1f7b688
          json:
            country_code: "{{country_code}}"
            phone_number: "{{phone_number}}"
            persona_type: "{{persona_type}}"
          capture:
            - json: $.data.transaction_id
              as: data_transaction_id
          expect:
            - statusCode: 200
            - hasProperty: "data.transaction_id"

        # verfying  the otp  and generating student token
      - post:
          name: "Verify student is able to login with valid otp"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          headers:
            x-device-id: 252d78fc-8581-4e95-874c-7f63c1f7b688
          json:
            otp: 1111
            transaction_id: "{{data_transaction_id}}"
          capture:
            - header: x-access-token
              as: student_access_token
          expect:
            - statusCode: 200
      - sleep: 2000
      - put:
          name: "onboarding user"
          url: "{{bffURL}}/api/v1/users"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            stream: "{{stream}}"
            class: "{{class}}"
            full_name: "{{full_name}}"
          capture:
            - json: $.data
              as: onboarding_response
          expect:
            - statusCode: 200
            - equals:
                - "{{onboarding_response.student_info.current_class}}"
                - "{{class}}"
      - sleep: 3000
      # Step 2: Send OTP for email update
      - post:
          name: "Verify send otp is called while updating email id"
          url: "{{bffURL}}/api/v1/auth/sendOtp"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            channel_type: EMAIL
            channel_value: "{{email}}"
            persona_type: STUDENT
            otp_type: PII_UPDATE
          capture:
            - json: $.data.transaction_id
              as: captured_data_email_transaction_id
          expect:
            - statusCode: 200
            - hasProperty: "data.transaction_id"

      # Step 3: Verify OTP for email update
      - post:
          name: "updating email id with valid otp"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          headers:
            authorization: "Bearer {{student_access_token}}"
          json:
            otp: 1111
            transaction_id: "{{captured_data_email_transaction_id}}"
            otp_type: PII_UPDATE
          capture:
            - json: $.data
              as: res
          expect:
            - statusCode: 200

      # Step 4: Get student info
      - get:
          name: "Verify student details after updating email id"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: "Bearer {{student_access_token}}"

          expect:
            - statusCode: 200
            - hasProperty: data.student_detail
            - hasProperty: data.parent_detail
            - hasProperty: data.student_batch_detail

      # delete the user Account
      # - delete:
      #     name: "Verify freemium student can delete his account"
      #     url: "{{bffURL}}/users"
      #     headers:
      #       authorization: "Bearer {{student_access_token}}"
      #     capture:
      #       - json: $.data.message
      #         as: data_message
      #     expect:
      #       - statusCode: 200
      #       - hasProperty: "data.message"
      #       - equals: 
      #           - "{{data_message}}"
      #           - "User deleted successfully"

  - name: Verify student is able to update the DOB in personal details for freemium
    flow:
      - sleep: 2000

      # Step 1: Get student info with token
      - get:
          name: "Verify student details after login with token"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: "Bearer {{login.studentToken}}"

          expect:
            - statusCode: 200
            - hasProperty: data.student_detail
            - hasProperty: data.parent_detail
            - hasProperty: data.student_batch_detail

      # Step 2: Update user profile
      - put:
          name: "Verify student can update the DOB details"
          url: "{{bffURL}}/api/v1/user/profile"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            date_of_birth: "{{dob}}"
            blood_group: "{{blood_group}}"
            gender: "{{gender}}"
          expect:
            - statusCode: 200
            - hasProperty: "data.student_info"

      - sleep: 2000 #sleep required to update the dob in user level

      # Step 4: Get student info after update
      - get:
          name: "Verify student details after updating DOB details"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          expect:
            - statusCode: 200
            - hasProperty: "data.student_detail.gender" # Ensure updated gender is reflected
            - hasProperty: "data.student_detail.blood_group" # Ensure updated blood group is reflected