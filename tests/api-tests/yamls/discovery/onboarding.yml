# onboarding.yml
# Flow - Onboarding a New Student User after login
config:
  target: https://bff.allen-stage.in
  variables:
    country_code: "91"
    persona_type: STUDENT
    stream: STREAM_JEE_MAIN_ADVANCED
    class: CLASS_11
    full_name: Rohit

  fixtures:
    - bffURL
    - testData
    - login

  defaults:
    headers:
      accept: application/json
      x-device-id: b074d2cc-0951-44fc-9540-3dc169c032ce
      ontent-type: application/json
      x-client-type: web
scenarios:
  - name: "Onboard a student in a particular stream"
    flow:
      # - post:
      #     name: "Login with Credentials"
      #     url: "{{bffURL}}/auth/username"
      #     json:
      #       username: "{{testData.student.phone}}"
      #       password: "{{testData.student.phone}}"
      #       persona_type: STUDENT
      #     capture:
      #       - header: x-access-token
      #         as: studentToken
      #       - json: $.data.user_data.id
      #         as: user_details_id
      #     expect:
      #       - statusCode: 200
      #       - equals:
      #           - "{{user_details_id}}"
      #           - "{{testData.student.id}}"
      # # step -1 generate the transaction id by sending otp
      # - post:
      #     name: "send otp"
      #     url: "{{bffURL}}/auth/sendOtp"
      #     json:
      #       country_code: "{{country_code}}"
      #       phone_number: "{{testData.student.phone}}"
      #       persona_type: "{{persona_type}}"
      #     capture:
      #       - json: $.data
      #         as: sendOTP_Response
      #     expect:
      #       - statusCode: 200
      #       - hasProperty: "data.transaction_id"
      #       - equals:
      #           - "{{sendOTP_Response.message}}"
      #           - "otp sent successfully"

      # # step - 2 verifying the student and generate token
      # - post:
      #     name: "verify otp"
      #     url: "{{bffURL}}/auth/verifyOtp"
      #     json:
      #       otp: 1111
      #       transaction_id: "{{sendOTP_Response.transaction_id}}"
      #     capture:
      #       - header: x-access-token
      #         as: studentToken
      #       - json: $.data.user_data.id
      #         as: user_details_id
      #     expect:
      #       - statusCode: 200
      #       - equals:
      #           - "{{user_details_id}}"
      #           - "{{testData.student.id}}"

      # step - 3  enter the student details for onboarding
      - put:
          name: "onboarding user"
          url: "{{bffURL}}/internal-bff/api/v1/users"
          headers:
            authorization: >-
              Bearer {{login.studentToken}}
          json:
            stream: "{{stream}}"
            class: "{{class}}"
            full_name: "{{full_name}}"
          capture:
            - json: $.data
              as: onboarding_response
          expect:
            - statusCode: 200
            - equals:
                - "{{onboarding_response.student_info.current_class}}"
                - "{{class}}"
