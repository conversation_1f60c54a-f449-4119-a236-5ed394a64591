#login.yml
#Flow Description - In this flow , we check the login apis and check for maximum limit reached condition , on 4 wrong attempts
config:
  target: https://bff.allen-stage.in
  variables:
    explorePageURL: /
    Name: abhishek
  defaults:
    headers:
      content-type: application/json
      x-client-type: web
      x-device-id: b074d2cc-0951-44fc-9540-3dc169c032ce

  fixtures:
    - bffURL
    - testData

scenarios:
  - name: Verify student is able to successfully login with valid OTP for enrolled student
    skipInProd: true
    flow:
      # Step 1: Send OTP for phone number
      - post:
          name: Verify student can send the OTP using mobile number to login
          url: "{{bffURL}}/api/v1/auth/sendOtp"
          json:
            country_code: "91"
            phone_number: "{{testData.student.phone}}"
            persona_type: STUDENT
          capture:
            - json: $.data.transaction_id
              as: captured_data_transaction_id
          expect:
            - statusCode: 200
            - hasProperty: "data.transaction_id"
      # Step 2: verify OTP for phone number
      - post:
          name: "Verify student is able to login with valid OTP"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 1111
            transaction_id: "{{captured_data_transaction_id}}"
          capture:
            - json: $.data.session_id
              as: captured_data_session_id
            - header: x-access-token
              as: studentToken
            - json: $.data.user_data.id
              as: user_details_id
          expect:
            - statusCode: 200
            - equals:
                - "{{user_details_id}}"
                - "{{testData.student.id}}"

      # Step 3: verify student details post login
      - get:
          name: "Verify student details after login"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: >-
              Bearer {{studentToken}}
            x-client-type: web
          expect:
            - statusCode: 200
            - hasProperty: data.student_detail
            - hasProperty: data.parent_detail
            - hasProperty: data.student_batch_detail

  - name: Verify student login flow with invalid and exhaust otp's
    flow:
      # Step 1: Send OTP for phone number
      - post:
          name: Verify student can send the OTP using mobile number to login
          url: "{{bffURL}}/api/v1/auth/sendOtp"
          json:
            country_code: "91"
            phone_number: "{{testData.student.phone}}"
            persona_type: STUDENT
          capture:
            - json: $.data.transaction_id
              as: captured_data_transaction_id
          expect:
            - statusCode: 200
            - hasProperty: "data.transaction_id"

      # Step 2: Verify OTP with invalid otp 1st attempt
      - post:
          name: Verify student tries to login with invalid otp in 1st attempt
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 1234
            transaction_id: "{{captured_data_transaction_id}}"
          expect:
            - statusCode: 400

      # Step 3: Verify OTP with invalid otp 2nd attempt
      - post:
          name: "Verify student tries to login with invalid otp at 2nd attempt"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 4356
            transaction_id: "{{captured_data_transaction_id}}"
          expect:
            - statusCode: 400

      # Step 4: Verify OTP with invalid otp 3rd attempt
      - post:
          name: "Verify student tries to login with invalid otp at 3rd attempt"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 5645
            transaction_id: "{{captured_data_transaction_id}}"
          expect:
            - statusCode: 400
      # Step 5: Verify OTP with invalid otp 4th attempt
      - post:
          name: "Verify student tries to login with invalid otp at 4th attempt"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 3243
            transaction_id: "{{captured_data_transaction_id}}"
          expect:
            - statusCode: 400
      # Step 6: Verify OTP blocked due to many attempts
      - post:
          name: "Verify student OTP is blocked due to too many attempts"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 1111
            transaction_id: "{{captured_data_transaction_id}}"
          capture:
            - json: $.reason
              as: login_response
          expect:
            - statusCode: 400
            - equals:
              - "{{login_response}}"
              - "Too many wrong attempts. Please try after some time."



