# # talk-to-us.yml
# # Flow - Submits the Details to Talk to us form
# config:
#   target: https://bff.allen-stage.in
#   variables:
#     page_url_schedule: /schedule-a-call-back
#     page_url_thank_you: /thank-you
#     class: Class 6
#     target_exam: NEET
#     learning_preference: Online
#     billing_state: Andhra Pradesh
#     form_id: marketing_form_1
#     form_url: /schedule-a-call-back
#     lead_source: Marketing - Search Engine
#     sub_lead_source: Landing Page
#     tickTnC: true
#     tickPromoTnC: true
#     studentPhone: 6300000000
#   defaults:
#     headers:
#       content-type: application/json
#       x-client-type: web
#   fixtures:
#     - bffURL
#     - testData

# scenarios:
#   - name: Talk to us form submission
#     skipInProd: true
#     flow:
#       # Step 1: Get page for schedule-a-call-back
#       - post:
#           name: Get Page for Schedule a Call Back
#           url: "{{bffURL}}/pages/getPage"
#           json:
#             page_url: "{{page_url_schedule}}"
#           expect:
#             - statusCode: 200 # Expecting a 200 status code
#             - hasProperty: "status"
#       - function:
#           name: generateRandomString
#           args:
#             - 6
#           capture: random_name

#       # Step 2: Submit marketing enquiry with random values for name, email, and phone
#       - post:
#           name: Submit Marketing Enquiry
#           url: "{{bffURL}}/marketing/enquiry"
#           json:
#             FirstName: "{{ random_name }}" # Generates another random 7-character string for last name
#             LastName: "{{ random_name }}" # Generates another random 7-character string for last name
#             MobilePhone: "{{ studentPhone }}" # Generates a random 10-digit mobile number
#             Email: "{{ testData.student.email }}" # Generates a random email address
#             Class__c: "{{class}}"
#             Target_Exams__c: "{{target_exam}}"
#             Learning_Preference__c: "{{learning_preference}}"
#             Billing_State__c: "{{billing_state}}"
#             Tick_Agree_to_T_C__c: "{{tickTnC}}"
#             Tick_Agree_to_receive_promo: "{{tickPromoTnC}}"
#             Form_Id__c: "{{form_id}}"
#             Form_Url__c: "{{form_url}}"
#             LeadSource: "{{lead_source}}"
#             Sub_Lead_Source__c: "{{sub_lead_source}}"
#             Utm_Medium__c: ""
#             utm_device__c: ""
#             utm_Campaign__c: ""
#             utm_source__c: ""
#             UTM_Adgroup__c: ""
#             fbclid__c: ""
#             GCLID__c: ""
#           expect:
#             - statusCode: 200 # Expecting a 200 status code
#             - hasProperty: "status" # Expecting the response body to contain this text

#       # Step 3: Get page for thank-you, expect 401 error
#       - post:
#           name: Get Page for Thank You
#           url: "{{bffURL}}/pages/getPage"
#           json:
#             page_url: "{{page_url_thank_you}}"
#           expect:
#             - statusCode: 200
