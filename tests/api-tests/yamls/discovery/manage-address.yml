# manage-address.yml
# Flow - Saves an address , updates the address and then deletes the address of the user

config:
  target: https://bff.allen-stage.in
  variables:
    token: Any random token here
    pincode: "322241"
    add1: "bengaluru"
    add3: "srkit house"
    city: "KARAULI"
    state: "Rajasthan"
    deletedSuccessfully: "Address Deleted Successfully"

  defaults:
    headers:
      accept: application/json
      x-client-type: web
      x-device-id: f0a5f3c3-fabd-482a-b0a1-72fb43307010

  fixtures:
    - bffURL
    - testData
    - login

  
scenarios:
  - name: "manage address from your profile" 
    flow:
      # Step 1: Verify pincode is valid or not
      - get:
          name: "get pincode"
          url: "{{bffURL}}/api/v1/user/locations/{{pincode}}"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          expect:
            - statusCode: 200
            - hasProperty: "data.pincode"
            - hasProperty: "data.district"
            - hasProperty: "data.state"

      # Step 2: Add new address
      - post:
          name: "add addresses"
          url: "{{bffURL}}/api/v1/user/addresses"
          headers:
            # x-device-id: 3b93b774-b174-4e75-a741-36e3ab380066
            authorization: "Bearer {{login.studentToken}}"
            # x-client-type: web
          json:
            pin_code: "{{pincode}}"
            address_line1: "{{add1}}"
            address_line2: ""
            address_line3: "{{add3}}"
            city: "{{city}}"
            district: ""
            state: "{{state}}"
            latitude: ""
            longitude: ""
          capture:
            - json: $.data
              as: response
          expect:
            - statusCode: 200
            - equals:
                - "{{response.pin_code}}"
                - "{{pincode}}"
            - equals:
                - "{{response.address_line1}}"
                - "{{add1}}"
            - equals:
                - "{{response.address_line3}}"
                - "{{add3}}"
            - equals:
                - "{{response.city}}"
                - "{{city}}"
            - equals:
                - "{{response.state}}"
                - "{{state}}"
      - log: "{{ response }}"

      # Step 3: Verify added address 
      - get:
          name: "verify added address"
          url: "{{bffURL}}/api/v1/user/addresses"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data[0].id
              as: captured_data_0_id
          expect:
            - statusCode: 200

      # Step 4: adding one more address 
      - post:
          name: "adding one more address"
          url: "{{bffURL}}/api/v1/user/addresses"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            pin_code: "{{pincode}}"
            address_line1: "{{add1}}"
            address_line2: ""
            address_line3: "{{add3}}"
            city: "{{city}}"
            district: ""
            state: "{{state}}"
            latitude: ""
            longitude: ""
          capture:
            - json: $.data
              as: response
          expect:
            - statusCode: 200
            - equals:
                - "{{response.pin_code}}"
                - "{{pincode}}"
            - equals:
                - "{{response.address_line1}}"
                - "{{add1}}"
            - equals:
                - "{{response.address_line3}}"
                - "{{add3}}"
            - equals:
                - "{{response.city}}"
                - "{{city}}"
            - equals:
                - "{{response.state}}"
                - "{{state}}"

      # Step 5: Verify added address 
      - get:
          name: "verify added secong address"
          url: "{{bffURL}}/api/v1/user/addresses"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data[0].id
              as: captured_data_0_id
            - json: $.data[1].id
              as: captured_data_1_id
          expect:
            - statusCode: 200

      # Step 6: update added address 
      - put:
          name: "update added address"
          url: "{{bffURL}}/api/v1/user/addresses/{{captured_data_1_id}}"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            pin_code: "{{pincode}}"
            address_line1: "{{add1}}"
            address_line2: ""
            address_line3: "{{add3}}"
            city: "{{city}}"
            district: ""
            state: "{{state}}"
            latitude: ""
            longitude: ""
          capture:
            - json: $.data
              as: response
          expect:
            - statusCode: 200
            - equals:
                - "{{response.pin_code}}"
                - "{{pincode}}"
            - equals:
                - "{{response.address_line1}}"
                - "{{add1}}"
            - equals:
                - "{{response.address_line3}}"
                - "{{add3}}"
            - equals:
                - "{{response.city}}"
                - "{{city}}"
            - equals:
                - "{{response.state}}"
                - "{{state}}"

      # Step 7: verify updated address 
      - get:
          name: "verify updated address"
          url: "{{bffURL}}/api/v1/user/addresses"
          headers:
            # x-device-id: 3b93b774-b174-4e75-a741-36e3ab380066
            authorization: "Bearer {{login.studentToken}}"
            # x-client-type: web
          capture:
            - json: $.data[0].id
              as: captured_data_0_id
            - json: $.data[1].id
              as: captured_data_1_id
          expect:
            - statusCode: 200

      # Step 8: delete the address
      - delete:
          name: "delete address"
          url: "{{bffURL}}/api/v1/user/addresses/{{captured_data_1_id}}"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data
              as: response
          expect:
            - statusCode: 200
            - equals:
                - "{{response}}"
                - "{{deletedSuccessfully}}"
