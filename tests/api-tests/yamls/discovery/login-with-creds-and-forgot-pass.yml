#login-with-creds-and-forgot-pass.yml
#Flow description - User Logs In with Credentials, Checks with Wrong Pass and Wrong username , User also performs reset password
config:
  target: https://bff.allen-stage.in
  variables:
    explorePageURL: /
    wrongPass: klsaxnln
    wrongUsername: kjldsnlns

  defaults:
    headers:
      accept: application/json
      x-client-type: web
      x-device-id: f0a5f3c3-fabd-482a-b0a1-72fb43307010

  fixtures:
    - bffURL
    - testData

scenarios:
  - name: Verify student should be able to reset the password
    skipInProd: true
    flow:
      - sleep: 1000
      - post:
          name: "Verify student should successfully login with valid email and password"
          url: "{{bffURL}}/api/v1/auth/username"
          json:
            username: "{{testData.student.email}}"
            password: "{{testData.student.phone}}"
            persona_type: STUDENT
          capture:
            - header: x-access-token
              as: studentToken
            - json: $.data.user_data.id
              as: user_details_id
          expect:
            - statusCode: 200
            - equals:
                - "{{user_details_id}}"
                - "{{testData.student.id}}"

      - get:
          name: "Verify student details after login"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: >-
              Bearer {{studentToken}}
          expect:
            - statusCode: 200
            - hasProperty: data.student_detail
            - hasProperty: data.parent_detail
            - hasProperty: data.student_batch_detail

      - post:
          name: "Verify student is able to Logout successfully"
          url: "{{bffURL}}/api/v1/auth/logout"
          headers:
            authorization: >-
              Bearer {{studentToken}}
          json: null
          expect:
            - statusCode: 200

      - get:
          name: "Identity API to collect the student info to reset the password"
          url: "{{bffURL}}/api/v1/user/identities/{{testData.student.phone}}?communicable=true"
          capture:
            - json: $.data.identities[0].identity_type
              as: identity_type
            - json: $.data.identities[0].identity_value
              as: identity_value
            - json: $.data.identities[0].identity_id
              as: identity_id
          expect:
            - statusCode: 200
            - hasProperty: data.identities[0].identity_type
            - hasProperty: data.identities[0].identity_value
            - hasProperty: data.identities[0].identity_id

      - post:
          name: "Verify student can use send otp for password reset"
          url: "{{bffURL}}/api/v1/auth/sendOtp"
          json:
            identity_type: "{{identity_type}}"
            identity_id: "{{identity_id}}"
            identity_value: "{{identity_value}}"
            persona_type: STUDENT
            otp_type: RESET_PASSWORD
          capture:
            - json: $.data.transaction_id
              as: captured_data_transaction_id
          expect:
            - statusCode: 200
            - hasProperty: "data.transaction_id"

      - post:
          name: "Verify student can reset the password using verify otp"
          url: "{{bffURL}}/api/v1/auth/verifyOtp"
          json:
            otp: 1111
            transaction_id: "{{captured_data_transaction_id}}"
            otp_type: RESET_PASSWORD
          expect:
            - statusCode: 200

      - post:
          name: "Verify student can provide the new password to reset"
          url: "{{bffURL}}/api/v1/user/password/reset"
          json:
            transaction_id: "{{captured_data_transaction_id}}"
            password: "{{testData.student.phone}}"
          expect:
            - statusCode: 200

      - post:
          name: "Verify student is able to login with new password"
          url: "{{bffURL}}/api/v1/auth/username"
          json:
            username: "{{testData.student.email}}"
            password: "{{testData.student.phone}}"
            persona_type: STUDENT
          capture:
            - header: x-access-token
              as: studentToken
            - json: $.data.user_data.id
              as: user_details_id
          expect:
            - statusCode: 200
            - equals:
                - "{{user_details_id}}"
                - "{{testData.student.id}}"

      - get:
          name: "Verify student details after login with new password"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: >-
              Bearer {{studentToken}}
          expect:
            - statusCode: 200
            - hasProperty: data.student_detail
            - hasProperty: data.parent_detail
            - hasProperty: data.student_batch_detail

      - post:
          name: "Verify student is able to see explore page after login"
          url: "{{bffURL}}/api/v1/pages/getPage"
          headers:
            authorization: >-
              Bearer {{studentToken}}
          json:
            page_url: "{{explorePageURL}}"
          expect:
            - statusCode: 200
            - hasProperty: "data.page_content.widgets"
            - matchesRegexp:
                "": "MY_SUBJECTS_V3"

  - name: Verify student cannot log in with incorrect credentials, but can log in with valid email and password then logout successfully.
    flow:
      - sleep: 1000
      - post:
          name: "Verify student is able to login with valid email and password"
          url: "{{bffURL}}/api/v1/auth/username"
          json:
            username: "{{testData.student.email}}"
            password: "{{testData.student.phone}}"
            persona_type: STUDENT
          capture:
            - header: x-access-token
              as: studentToken
            - json: $.data.user_data.id
              as: user_details_id
          expect:
            - statusCode: 200
            - equals:
                - "{{user_details_id}}"
                - "{{testData.student.id}}"

      - get:
          name: "Verify student details after login"
          url: "{{bffURL}}/api/v1/user/studentInfo"
          headers:
            authorization: >-
              Bearer {{studentToken}}
          expect:
            - statusCode: 200
            - hasProperty: data.student_detail
            - hasProperty: data.parent_detail
            - hasProperty: data.student_batch_detail

      - post:
          name: "Verify student is able to logout successfully"
          url: "{{bffURL}}/api/v1/auth/logout"
          headers:
            authorization: >-
              Bearer {{studentToken}}
          json: null
          expect:
            - statusCode: 200

      - post:
          name: "Verify student cannot login with wrong password"
          url: "{{bffURL}}/api/v1/auth/username"
          json:
            username: "{{testData.student.email}}"
            password: "{{wrongPass}}"
            persona_type: STUDENT
          expect:
            - statusCode: 400

      - post:
          name: "Verify student cannot login with wrong username"
          url: "{{bffURL}}/api/v1/auth/username"
          json:
            username: "{{wrongUsername}}"
            password: "{{testData.student.phone}}"
            persona_type: STUDENT
          expect:
            - statusCode: 400
