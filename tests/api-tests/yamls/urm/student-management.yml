#student-mangement.yml
# Verify student batch details and update student details
config:
  variables:
    link: "https://ap-south-1-staging-test-and-assessment-uploads-staging.s3.ap-south-1.amazonaws.com/LhpWj62UmX-UtLDMJ5rjy.pdf"
    search_by: "phone_number"
    search: "1234503361"
    sort_order: "ASC"
    page_size: 20
    page_no: 1
    phone_number: "1234503361"


  defaults:
    headers:
      content-type: application/json
      x-client-type: web
  fixtures:
    - adminURL
    - testData
    - bffURL
scenarios:
  - name: "Verify student batch details and update student details"
    skipInProd: true
    flow:
      - function:
          name: generateRandomString
          args:
            - 4
            - "api_"
          capture: random_crm_name
      - function:
          name: getDates
          capture: dates
      # Step 1: search student by phone number
      - get:
          name: "verify search student by phone number"
          url: "{{bffURL}}/internal-bff/api/v1/student-dashboard/:id/:section?search_by=phone_number&search={{phone_number}}&sort_order=ASC&page_size=20&page_no=1&phone_number={{phone_number}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: student_details_response
            - json: $.data.students[0].user_id
              as: student_userId
            - json: $.data.students[0].name
              as: student_userName
          expect:
            - statusCode: 200
            - hasProperty: "data.students[0].phone_number"
        # Step 2: verify student profile
      - get:
          name: "verify student profile"
          url: "{{bffURL}}/node-bff/api/v1/sr-management/users/{{student_userId}}/profile"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.basic_details.full_name
              as: student_fullName
            - json: $.data.basic_details.status
              as: status
          expect:
            - statusCode: 200
            - equals:
                - "{{student_fullName}}"
                - "{{student_userName }}"
            - equals:
                - "{{status}}"
                - "ENROLLED"
      # Step 3: verify student address
      - get:
          name: "verify student address"
          url: "{{bffURL}}/node-bff/api/v1/sr-management/users/{{student_userId}}/addresses"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: student_address_response
          expect:
            - statusCode: 200
      # Step 4: verify student update profile
      - post:
          name: "verify student update profile"
          url: "{{bffURL}}/node-bff/api/v1/sr-management/requests"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            userID: "{{student_userId}}"
            requestType: "PII_UPDATE"
            requestDetails:
              studentInfo:
                bloodGroup: "O+"
              dateOfBirth: "30-01-2025"
              persona_type: "STUDENT"
          capture:
            - json: $.data
              as: student_profile
            - json: $.data.status
              as: profile_status
          expect:
            - statusCode: 200
              message: "Expected status code to be 200"
            - hasProperty: "data.user_id"
              message: "Expected response to have property {{data.user_id}}"
            - equals:
                - "{{profile_status}}"
                - "COMPLETED"
        # Step 5: verify student details
      - get:
          name: "verify student details"
          url: "{{bffURL}}/internal-bff/api/v1/student-dashboard/{{student_userId}}/courses"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: student_course_details_response
            - json: $.data[0].phase_id
              as: student_phasId
            - json: $.data[0].course_id
              as: student_courseId
            - json: $.data[0].batch_history[0].batch_id
              as: current_batchId
          expect:
            - statusCode: 200
            - hasProperty: "data[0].phase_id"
            - hasProperty: "data[0].course_id"
            - hasProperty: "data[0].batch_history[0].batch_id"
      # Step 6: verify batch filter
      - post:
          name: "verify batch filter"
          url: "{{bffURL}}/internal-bff/api/v1/resource/batches/filter"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            search: []
            filter:
              courses:
                op: "IN"
                values:
                  - "{{student_courseId}}"
              phases:
                op: "IN"
                values:
                  - "{{student_phasId}}"
              types:
                op: "IN"
                values:
                  - BATCH_REGULAR
                  -  BATCH_DEFAULT
              statuses:
                op: IN
                values:
                  - BATCH_ACTIVE
            pagination:
              page_number: 1
              page_size: 100
              sort:
                by: "updatedAt"
                order: "DESC"
          capture:
            - json: $.data
              as: filter_batch_details
            - json: $.data.results[0].id
              as: filter_batchId
            - json: $.data.results[1].id
              as: new_batchId
          expect:
            - statusCode: 200
              message: "Expected status code to be 200"
            - hasProperty: "data.results"
            - hasProperty: "data.results[0].id"
              message: "Expected response to have property {{data.results}}"
        #  # Step 7: verify student batch movement
        #   - post:
        #       name: "verify student batch movement"
        #       url: "{{internalBffURL}}/resource/students-batch-movement"
        #       headers:
        #         authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
        #       json:
        #         batch_id: "{{current_batchId}}"
        #         mapping_requests:
        #           - student_id: "{{student_userId}}"
        #             new_batch_id: "{{filter_batchId}}"
        #       capture:
        #         - json: $.data
        #           as: batch_movement
        #       expect:
        #         - statusCode: 200
        #           message: "Expected status code to be 200"
        # Step 8: verify purchase details
      - get:
          name: "verify purchase details"
          url: "{{bffURL}}/internal-bff/api/v1/student-dashboard/{{student_userId}}/purchase"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: student_purchase_details
            - json: $.data.orders[0].user_id
              as: userId
          expect:
            - statusCode: 200
            - hasProperty: "data.orders[0].shipping_address"
            - hasProperty: "data.orders[0].billing_address"
            - equals:
                - "{{userId}}"
                - "{{student_userId}}"
      # Step 9: verify add address
      - post:
          name: "verify add address"
          url: "{{bffURL}}/node-bff/api/v1//sr-management/requests"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            userID: "{{student_userId}}"
            requestType: "PII_ADDRESS_CREATE"
            requestDetails:
              address_line1: "New Address"
              address_line2: "BENGALURU"
              address_line3: ""
              city: "BENGALURU"
              country: ""
              created_by: ""
              district: ""
              latitude: ""
              longitude: ""
              pin_code: "560035"
              state: "Karnataka"
              tenant_id: ""
              user_id: "{{student_userId}}"
          capture:
            - json: $.data
              as: batch_movement
            - json: $.data.user_id
              as: userId_address
            - json: $.data.status
              as: added_address_status
          expect:
            - statusCode: 200
            - hasProperty: data.request_details.address_line1
            - equals:
                - "{{userId_address}}"
                - "{{student_userId}}"
            - equals:
                - "{{added_address_status}}"
                - "COMPLETED"
      # Step 10: get address
      - get:
          name: "verify get address"
          url: "{{bffURL}}/node-bff/api/v1/sr-management/users/{{student_userId}}/addresses"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.addressDetails[0].id
              as: added_new_addressId
            - json: $.data.addressDetails[0].userId
              as: userId
          expect:
            - statusCode: 200
            - hasProperty: "data.addressDetails[0].id"
            - hasProperty: "data.addressDetails[0].userId"
            - equals:
                - "{{userId}}"
                - "{{student_userId}}"
      # Step 11: verify delete address
      - delete:
          name: "verify delete address"
          url: "{{bffURL}}/node-bff/api/v1/sr-management/users/{{student_userId}}/addresses/{{added_new_addressId}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.addressDetails[0].id
              as: added_new_addressId
            - json: $.data.addressDetails[0].userId
              as: userId
          expect:
            - statusCode: 200