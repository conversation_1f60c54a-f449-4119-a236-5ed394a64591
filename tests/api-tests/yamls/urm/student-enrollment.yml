config:
  fixtures:
    - testData
    - bffURL
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      origin: https://console.allen-stage.in
      x-device-id: aaee4e79-9461-474b-aa9c-a59d3b607eee
      x-client-type: web
      authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"

scenarios:
  - name: "Search student by phone number"
    skipInProd: true
    flow:
      - get:
          name: "Verify admin is able to search student by phone"
          url: "{{bffURL}}/internal-bff/api/v1/student-dashboard/:id/:section?search_by=phone_number&search={{testData.student.phone}}&sort_order=ASC&page_size=20&page_no=1&phone_number={{testData.student.phone}}"
          capture:
            - json: $.data.students[0].courses_enrolled[0].batch_id
              as: studentBatchId
          expect:
            - statusCode: 200
            - hasProperty: "data"
      
