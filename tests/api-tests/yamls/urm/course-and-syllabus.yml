# course-and-syllabus.yml
# "add/delete/update syllabus, create/filter/delete phase, create/delete batch"
config:
  target: https://bff.allen-stage.in
  variables:
    expiry_date: "30-01-2026"
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web

  fixtures:
    - adminURL
    - bffURL
    - testData

scenarios:
  - name: "Verify admin should be able to create, search, and delete phases/batches"
    skipInProd: true
    flow:
      - function:
          name: generateRandomString
          args:
            - 3
          capture: random_batch_code
      - function: 
          name: getDates
          capture: dates
      - function:
          name: generateRandomNumber
          args:
            - 5  
          capture: phase_number
      - function:
          name: generateRandomNumber
          args:
            - 7  
          capture: random_erp_number
        # Step 1: create a notice board notication as admin using send api
      - post:
          name: "Verify admin is able to create the phase"
          url: "{{bffURL}}/internal-bff/api/v1/resource/phase"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            course_id: "{{testData.env.API_TESTING_COURSE_ID}}"
            facility_type: "FACILITY_TYPE_CENTER"
            facility_id: "{{testData.env.CENTER_ID}}"
            start_date: "{{dates.formatedDate}}"
            end_date: "{{expiry_date}}"
            syllabus_completion_date: "{{expiry_date}}"
            go_live_date: "{{dates.formatedDate}}"
            purchase_expiry_date: "{{expiry_date}}"
            number: "{{phase_number}}"
            erp_course_code: "{{random_erp_number}}"
          capture:
            - json: $.data
              as: phase_response
            - json: $.data.id
              as: phase_id
            - json: $.data.name
              as: phase_name
          expect:
            - statusCode: 200
            - hasProperty: "data.id"
            - hasProperty: "data.name"
            - hasProperty: "data.facility_id"
            - hasProperty: "data.course_id"
            - equals:
                - "{{phase_response.name}}"
                - "{{phase_number}}"
      - log: "Phase created successfully"

      - sleep: 1000 #1 sleep required to update the data

      - post:
          name: "Verify admin is able to filter the list of phases"
          url: "{{bffURL}}/internal-bff/api/v1/resource/phases/listing"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            search: []
            filter:
              academic_session:
                op: "EQ"
                value: "04_2024__03_2025"
              centers:
                op: "IN"
                values:
                  - "{{testData.env.CENTER_ID}}"
              streams:
                op: "IN"
                values:
                  - "{{testData.syllabusConfig.group_node_details.0.group_node.meta_data.stream}}"
              courses:
                op: "IN"
                values:
                  - "{{testData.env.API_TESTING_COURSE_ID}}"
            pagination:
              page_number: 1
              page_size: 10
              sort:
                by: "updatedAt"
                order: "DESC"
          capture:
            - json: $.data.results
              as: phase_listing_response
          expect:
            - statusCode: 200
            - hasProperty: "data.results"
      - log: "Able to see the list of phases"

      - post:
          name: "Verify admin is able to create batch"
          url: "{{bffURL}}/internal-bff/api/v1/resource/batch"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            description: ""
            course_id: "{{testData.env.API_TESTING_COURSE_ID}}"
            phase_id: "{{phase_id}}"
            facility_id: "{{testData.env.BATCH_FACILITY_ID}}"
            type: "BATCH_REGULAR"
            capacity: 1
            timing: "BATCH_TIMING_AFTERNOON"
            gender: "BATCH_GENDER_ALL"
            days_of_week:
              - "WEEK_DAY_MONDAY"
              - "WEEK_DAY_TUESDAY"
              - "WEEK_DAY_WEDNESDAY"
              - "WEEK_DAY_THURSDAY"
              - "WEEK_DAY_FRIDAY"
              - "WEEK_DAY_SATURDAY"
              - "WEEK_DAY_SUNDAY"
            code: "TEST-{{phase_name}}Q-1{{random_batch_code}}"
            start_time: ""
            end_time: ""
            room_code: "12345"
          capture:
            - json: $.data
              as: batch_response
          expect:
            - statusCode: 200
            - hasProperty: "data.id"
      - log: "Batch creation is successful"

      - sleep: 1000 #1 sleep required to update the data

      - post:
          name: "Verify admin is able to retrieve batch listing"
          url: "{{bffURL}}/internal-bff/api/v1/resource/batches/listing"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            search: []
            filter:
              academic_session:
                op: "EQ"
                value: "04_2024__03_2025"
              centers:
                op: "IN"
                values:
                  - "{{testData.env.CENTER_ID}}"
              courses:
                op: "IN"
                values:
                  - "{{testData.env.API_TESTING_COURSE_ID}}"
              types:
                op: "IN"
                values:
                  - "BATCH_REGULAR"
                  - "BATCH_DEFAULT"
                  - "BATCH_FREEMIUM"
            pagination:
              page_number: 1
              page_size: 10
              sort:
                by: "updatedAt"
                order: "DESC"
          capture:
            - json: $.data
              as: batch_listing_response
          expect:
            - statusCode: 200
            - hasProperty: "data.results"
      - log: "Batch listing is successful"

      - delete:
          name: "Verify admin is able to delete the created batch"
          url: "{{bffURL}}/internal-bff/api/v1/resource/batch/{{batch_response.id}}/delete"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: delte_batch_response
          expect:
            - statusCode: 200
            - hasProperty: "data.message"
            - equals:
                - "{{delte_batch_response.message}}"
                - "successfully deleted batch"
      - log: "Batch deleted successfully"

      - sleep: 1000 #1 sleep required to update the data

      - delete:
          name: "Verify admin can delete a phase"
          url: "{{bffURL}}/internal-bff/api/v1/resource/phase/{{phase_id}}/delete"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          expect:
            - statusCode: 200
            - hasProperty: "data"
      - log: "Phase deletion is successful"

  - name: "Verify admin should be able to see the course listing, add syllabus, fetch syllabus and Delete syllabus"
    skipInProd: true
    flow:
      - function:
          name: generateRandomString
          args:
            - 3
          capture: random_batch_code
      - function: 
          name: getDates
          capture: dates
      - function:
          name: generateRandomNumber
          args:
            - 5  
          capture: phase_number
      - function:
          name: generateRandomNumber
          args:
            - 7  
          capture: random_erp_number

      - post:
          name: "Verify admin is able to fetch the course listing"
          url: "{{bffURL}}/internal-bff/api/v1/resource/courses/listing"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          json:
            search: []
            filter:
              academic_session:
                op: EQ
                value: "04_2024__03_2025"
              facilities:
                op: IN
                values:
                  - "{{testData.env.CENTER_ID}}"
            pagination:
              page_number: 1
              page_size: 10
              sort:
                by: updatedAt
                order: DESC
          capture:
            - json: $.data
              as: course_listing_response
          expect:
            - statusCode: 200
            - hasProperty: "data.results"
      - log: "Fetching course lisiting is successful"

      - post:
          name: "Verify admin is able to apply the filters for course"
          url: "{{bffURL}}/internal-bff/api/v1/resource/courses/listing"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            search: []
            filter:
              academic_session:
                op: "EQ"
                value: "04_2024__03_2025"
              facilities:
                op: "IN"
                values:
                  - "{{testData.env.CENTER_ID}}"
              streams:
                op: IN
                values:
                  - STREAM_JEE_MAIN_ADVANCED
              classes:
                op: IN
                values:
                  - CLASS_11
              types:
                op: IN
                values:
                  - COURSE_TYPE_REGULAR
              languages:
                op: IN
                values:
                  - LANGUAGE_HINGLISH
            pagination:
              page_number: 1
              page_size: 10
              sort:
                by: updatedAt
                order: DESC
          capture:
            - json: $.data
              as: course_filter_response
          expect:
            - statusCode: 200
            - hasProperty: "data"
      - log: "course filter is successful"

      - sleep: 1000 #1 sleep required to update the data

      - post:
          name: "Verify admin is able to fetch the course syllabus listing"
          url: "{{bffURL}}/internal-bff/api/v1/resource/courses-v2syllabus"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            search: []
            filter:
              is_syllabus_tagged:
                op: EQ
                value: "true"
              academic_session:
                op: EQ
                value: "04_2024__03_2025"
              classes:
                op: IN
                values:
                  - CLASS_11
              streams:
                op: IN
                values:
                  - STREAM_JEE_MAIN_ADVANCED
              modes:
                op: IN
                values:
                  - MODE_LIVE_RECORDED
              languages:
                op: IN
                values:
                  - LANGUAGE_HINGLISH
              facilities:
                op: IN
                values:
                  - "{{testData.env.CENTER_ID}}"
            pagination:
              page_number: 1
              page_size: 10
              sort:
                by: updatedAt
                order: DESC
          capture:
            - json: $.data
              as: course_syllabus_listing_response
          expect:
            - statusCode: 200
            - hasProperty: "data"

      - log: "course syllabus listing is successful"

      - post:
          name: "Verify admin is able to create course syllabus"
          url: "{{bffURL}}/internal-bff/api/v1/resource/course-syllabus/create"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            "{{testData.env.API_TESTING_COURSE_SYLLABUS}}"
          capture:
            - json: $.data
              as: create_course_response
          expect:
            - statusCode: 200
            - hasProperty: "data"
      - log: "course created successfully"

      - sleep: 3000 #1 sleep required to update the data

      - post:
          name: "Verify admin is able to fetch syllabus id"
          url: "{{bffURL}}/internal-bff/api/v1/resource/courses/listing"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            search: []
            filter:
              courses:
                op: "IN"
                values:
                  - "cr_lp1OZIhQX2xnlPxUwLrIC"
            pagination:
              page_number: 1
              page_size: 3000
              sort:
                by: "updatedAt"
                order: "DESC"
          capture:
            - json: $.data.results[0].syllabusID
              as: syllabus_id
          expect:
            - statusCode: 200
            - hasProperty: "data.results[0].syllabusID"
      - log: "syllabus id fetched successfully"

            

      - delete:
          name: "Verify admin is able to delete course syllabus"
          url: "{{bffURL}}/internal-bff/api/v1/resource/delete-course-syllabus?syllabus_id={{syllabus_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data
              as: create_course_response
          expect:
            - statusCode: 200
            - hasProperty: "data"
      - log: "created syllabus deleted successfully"

