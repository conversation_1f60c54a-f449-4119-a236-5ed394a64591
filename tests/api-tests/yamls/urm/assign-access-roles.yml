#assign-teacher-roles.yml
#Flow - Assign teacher roles and delete a specific role
config:
  variables:
    employeeId: "1234570054"
    teacherUserId: "oUhRFFInbLVdraDQJI2cZ"
    commsOperatorRoleId: "gXUDxuk4iKLxqmZQevvuL"
  defaults:
    headers:
      accept: application/json
      x-device-id: 969c6d64-33ed-4fd7-a96e-208104d4812f
      x-client-type: web
  fixtures:
    - bffURL
    - testData

scenarios:
  - name: "Verify assign teacher functions from dropdown"
    skipInProd: true
    flow:
      # Step 1: Upload the roles Excel file using a custom function
      - function:
          name: uploadExcelFile
          args:
            - "{{bffURL}}/internal-bff/api/v1/user-role-privilege"
            - "{{testData.env.ADMIN_ACCESS_TOKEN}}"
            - "./test-data/Latest_roles_template.xlsx"
            - "file"
          capture: upload_response
      - log: "Roles Excel file uploaded successfully"
      
      # Step 2: Search for user by employee ID
      - get:
          name: "Search for user by employee ID"
          url: "{{bffURL}}/internal-bff/api/v1/user-management/users?sort_order=desc&page_size=10&page_no=1&search_for={{employeeId}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          capture:
            - json: $.data.users[0].userId
              as: captured_user_id
            - json: $.data.users[0].phone_number
              as: captured_phone_number
          expect:
            - statusCode: 200
            - hasProperty: "data.users[0].userId"
            - equals:
                - "{{captured_user_id}}"
                - "{{teacherUserId}}"
      - log: "User found with ID: {{captured_user_id}}"
      
      # Step 3: Get user details by user ID
      - get:
          name: "Get user details by user ID"
          url: "{{bffURL}}/internal-bff/api/v1/user-management/users?userId={{captured_user_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          capture:
            - json: $.data.user_privileges
              as: user_privileges
          expect:
            - statusCode: 200
            - hasProperty: "data.user_privileges"
      - log: "Retrieved user privileges"
      
      # Step 4: Get user skill mapping
      - post:
          name: "Get user skill mapping"
          url: "{{bffURL}}/internal-bff/api/v1/resource/user-skill-mapping/"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          json:
            filter:
              user_id:
                op: "EQ"
                value: "{{captured_user_id}}"
            pagination:
              sort:
                by: "_id"
                order: "DESC"
          expect:
            - statusCode: 200
            - hasProperty: "data.data"
      - log: "Retrieved user skill mapping"
      
      # Step 5: Delete the Comms Operator role
      - delete:
          name: "Delete Comms Operator role from user"
          url: "{{bffURL}}/internal-bff/api/v1/authorization-service/user-role-privilege?role_id={{commsOperatorRoleId}}&user_id={{captured_user_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            content-type: application/json
          capture:
            - json: $.data.result
              as: delete_result
          expect:
            - statusCode: 200
            - hasProperty: "data.result"
            - equals:
                - "{{delete_result}}"
                - true
      - log: "Deleted Comms Operator role successfully"
