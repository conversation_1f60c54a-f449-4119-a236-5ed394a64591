config:
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      origin: https://console.allen-stage.in
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web
      authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
  fixtures:
    - testData
    - bffURL
    - login

scenarios:
  - name: "Verify admin is able to create, delete facility"
    skipInProd: true
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiCenter_" 
          capture: random_center_name
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiCampus_" 
          capture: random_campus_name
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiBuilding_" 
          capture: random_building_name
      - function:
          name: generateRandomNumber
          args:
            - 5
          capture: random_center_code
      - function:
          name: generateRandomNumber
          args:
            - 5
          capture: random_campus_code
      - function:
          name: generateRandomNumber
          args:
            - 5
          capture: random_building_code
      - post:
          name: "Verify admin is able to create center"
          url: "{{bffURL}}/api/v1/resource/facility"
          json:
            name: "{{random_center_name}}"
            code: "{{random_center_code}}"
            type: "FACILITY_TYPE_CENTER"
            prefix_code: "47"
            parent_id: ""
          capture:
            - json: $.data.id
              as: centerId
      - log: "Center created successfully {{centerId}}"
      - post:
          name: "Verify admin is able to create campus"
          url: "{{bffURL}}/api/v1/resource/facility"
          json:
            name: "{{random_campus_name}}"
            code: "{{random_campus_code}}"
            type: "FACILITY_TYPE_CAMPUS"
            prefix_code: "47"
            parent_id: "{{centerId}}"
          capture:
            - json: $.data.id
              as: campusId
      - log: "Campus created successfully {{campusId}}"
      - post:
          name: "Verify admin is able to create building"
          url: "{{bffURL}}/api/v1/resource/facility"
          json:
            name: "{{random_building_name}}"
            code: "{{random_building_code}}"
            type: "FACILITY_TYPE_BUILDING"
            prefix_code: "47"
            parent_id: "{{campusId}}"
          capture:
            - json: $.data.id
              as: buildingId
      - log: "Building created successfully {{buildingId}}"
      - delete:
          name: "Verify admin is able to delete building"
          url: "{{bffURL}}/api/v1/resource/facility/delete/{{buildingId}}"
      - log: "Building deleted successfully"
      - delete:
          name: "Verify admin is able to delete campus"
          url: "{{bffURL}}/api/v1/resource/facility/delete/{{campusId}}"
      - log: "Campus deleted successfully"
      - delete:
          name: "Verify admin is able to delete center"
          url: "{{bffURL}}/api/v1/resource/facility/delete/{{centerId}}"
      - log: "Center deleted successfully"
