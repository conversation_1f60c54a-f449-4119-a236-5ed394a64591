#online-v2-class-scheduling.yml
#Flow - Admin Schedules Meeting, Publishes from Draft to Published state, 
config:
  variables:
    subjectId: 2
    taxonomyId: "1701181887VZ"
    tenant_id: "aUSsW8GI03dyQ0AIFVn92"
    schedule_type: "SCHEDULE_TYPE_CLASS"
    summarize_by: "status"
    academic_session: "04_2024__03_2025"
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      origin: https://console.allen-stage.in
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web
      authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
  fixtures:
    - testData
    - bffURL

scenarios:
  - name: "Verfiy admin is able to create online class and publish"
    skipInProd: true
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiClass_"
          capture: random_class_name
      - function: 
          name: getDates
          capture: dates
      - log: "Start Epoch: {{dates.start_epoch}}"
      - post:
          name: "Verifty admin is able to fetch the schedule summary"
          url: "{{bffURL}}/internal-bff/api/v1/planning-and-scheduling/schedules/summary"
          json:
            tenant_id: "{{tenant_id}}"
            type: "{{schedule_type}}"
            summarize_by: "{{summarize_by}}"
            filter:
              schedule_filter:
                schedule_time:
                  from: "{{dates.startTime}}"
                  to: "{{dates.endTime}}"
              batches_filter:
                academic_session:
                  op: "EQ"
                  value: "{{academic_session}}"
                types:
                  op: "IN"
                  values: ["BATCH_REGULAR"]
            pagination:
              page_number: 1
              page_size: 10
              sort:
                by: "updatedAt"
                order: "DESC"
          capture:
            - json: $.data.results
              as: schedule_id
          expect:
            - statusCode: 200
            - hasProperty: "data.results"

      - log: "Fetched schedule successfully"

      - post:
          name: "Verify admin is able to create a class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          json:
            tenant_id: "{{tenant_id}}"
            data:
              type: "{{schedule_type}}"
              schedules:
                - external_identifier: "0"
                  title: "testingabc"
                  start_time: "{{dates.meeting_start_epoch}}"
                  end_time: "{{dates.meeting_end_epoch}}"
                  participants:
                    - id: "{{testData.teacher.id}}"
                      type: "PARTICIPANT_TYPE_TEACHER"
                      role: "PARTICIPANT_ROLE_ORGANIZER"
                    - id: "{{testData.batch.id}}"
                      type: "PARTICIPANT_TYPE_BATCH"
                      role: "PARTICIPANT_ROLE_VIEWER"
                  type: "{{schedule_type}}"
                  facility_id: ""
                  visibility: "SCHEDULE_VISIBILITY_PUBLIC"
                  class_schedule_metadata:
                    class_type: "LIVE_LECTURE"
                    mode: "SCHEDULE_MODE_ONLINE"
                    scheduled_subjects:
                      - id: "2"
                        subject_id: "{{subjectId}}"
                        taxonomy_id: "{{taxonomyId}}"
                        nodes: 
                          - id: "86"
                            type: "TOPIC"
                    pre_class_materials: []
          capture:
            - json: $.schedules[0]
              as: schedule_data
          expect:
            - statusCode: 200
      - log: "Class scheduled successfully"
      
      - sleep: 5000 # wait is required to generate meeting

      - put:
          name: "Verify admin is able to publish the created schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          json:
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_DRAFT"
                  to_status: "SCHEDULE_STATUS_PUBLISHED"
                  schedule_time:
                    from: "{{dates.meeting_start_epoch}}"
                    to: "{{dates.meeting_end_epoch}}"
                  filter:
                    participants_ids:
                      op: "IN"
                      values:
                        - "{{testData.batch.id}}"
            type: "{{schedule_type}}"
            tenant_id: "{{tenant_id}}"
          expect:
            - statusCode: 200
      - log: "Class status changed from draft to published"

      - sleep: 3000 # added wait to change status to deleted

      - put:
          name: "Verify admin is able to delete the created schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          json:
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_PUBLISHED"
                  to_status: "SCHEDULE_STATUS_DELETED"
                  schedule_time:
                    from: "{{dates.meeting_start_epoch}}"
                    to: "{{dates.meeting_end_epoch}}"
                  filter:
                    participants_ids:
                      op: "IN"
                      values:
                        - "{{testData.batch.id}}"
            type: "{{schedule_type}}"
            tenant_id: "{{tenant_id}}" 
          expect:
            - statusCode: 200

