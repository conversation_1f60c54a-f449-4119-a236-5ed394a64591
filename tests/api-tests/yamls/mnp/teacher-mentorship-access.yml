#teacher-mentorship-access.yml
#Flow - <PERSON>min creates mentorship class, teacher joins the meeting, admin cancels the meeting
config:
 variables:
   classType: "MENTORSHIP_SESSION"
   classMode: "SCHEDULE_MODE_ONLINE"

 defaults:
   headers:
     accept: application/json
     content-type: application/json
     x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
     x-client-type: web
 fixtures:
   - bffURL
   - testData

scenarios:
 - name: "Verify teacher accessible to mentorship class"
   flow:
     - function:
         name: generateRandomString
         args:
           - 6
           - "apiMentorship_"
         capture: random_class_name
     - function:
         name: getDates
         capture: dates

     # Step 1: Create mentorship class
     - post:
         name: "Create mentorship class"
         url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
         headers:
           authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
         json:
           tenant_id: "{{testData.env.TENANT_ID}}"
           data:
             type: "SCHEDULE_TYPE_CLASS"
             schedules:
               - external_identifier: "0"
                 title: "{{random_class_name}}"
                 start_time: "{{dates.meeting_start_epoch}}"
                 end_time: "{{dates.meeting_end_epoch}}"
                 participants:
                   - id: "{{testData.teacher.id}}"
                     type: "PARTICIPANT_TYPE_TEACHER"
                     role: "PARTICIPANT_ROLE_ORGANIZER"
                   - id: "{{testData.batch.id}}"
                     type: "PARTICIPANT_TYPE_BATCH"
                     role: "PARTICIPANT_ROLE_VIEWER"
                 type: "SCHEDULE_TYPE_CLASS"
                 facility_id: "{{testData.room.id}}"
                 visibility: "SCHEDULE_VISIBILITY_PUBLIC"
                 class_schedule_metadata:
                   class_type: "{{classType}}"
                   mode: "{{classMode}}"
                   description: "E2E Test"
         capture:
           - json: $.schedules[0].id
             as: schedule_id
           - json: $.schedules[0].status
             as: schedule_status
         expect:
           - statusCode: 200
           - hasProperty: "schedules[0].id"
     - log: "Created mentorship class with ID: {{schedule_id}} and status: {{schedule_status}}"

     # Step 2: Publish the meeting
     - put:
         name: "Publish Meeting"
         url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
         headers:
           authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
         json: |
           {
             "type": "SCHEDULE_TYPE_CLASS",
             "tenant_id": "{{testData.env.TENANT_ID}}",
             "class_schedule": {
               "request": [
                 {
                   "from_status": "SCHEDULE_STATUS_DRAFT",
                   "to_status": "SCHEDULE_STATUS_PUBLISHED",
                   "schedule_time": {
                     "from": "",
                     "to": ""
                   },
                   "filter": {
                     "schedules": {
                       "values": [
                         "{{schedule_id}}"
                       ],
                       "op": "IN"
                     }
                   }
                 }
               ]
             }
           }
         capture:
           - json: $.matched
             as: matched_count
           - json: $.successCount
             as: success_count
         expect:
           - statusCode: 200
           - hasProperty: "matched"
           - hasProperty: "successCount"

     - log: "Published mentorship class with ID: {{schedule_id}}"

     - sleep: 10000 # Wait for meeting ID to be generated after publish

     # Step 3: Fetch meeting details (teacher view)
     - post:
         name: "Fetch Meeting Details"
         url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
         headers:
           authorization: "Bearer {{testData.teacher.at}}"
         json: |
           {
             "tenant_id": "{{testData.env.TENANT_ID}}",
             "filter": {
               "schedules": {
                 "op": "IN",
                 "values": ["{{schedule_id}}"]
               },
               "schedule_time": {
                 "from": {{dates.meeting_start_epoch}},
                 "to": {{dates.meeting_end_epoch}}
               }
             },
             "pagination": {
               "page_size": 10,
               "page_number": 1,
               "sort": {
                 "by": "createdAt",
                 "order": "DESC"
               }
             }
           }
         capture:
           - json: $.data.schedules[0].meeting_id
             as: meeting_id
         expect:
           - statusCode: 200
           - hasProperty: "data.schedules[0].meeting_id"

     - log: "Meeting ID: {{meeting_id}}"

     # Step 4: Teacher joins the meeting
     - post:
         name: "Teacher Joins Meeting"
         url: "{{bffURL}}/internal-bff/api/v1/meetings/{{meeting_id}}/join"
         headers:
           authorization: "Bearer {{testData.teacher.at}}"
         json: null
         capture:
           - json: $.data
             as: teacher_join_response
         expect:
           - statusCode: 200
           - hasProperty: "data.room_id"
           - hasProperty: "data.role"

     - log: "Teacher successfully joined meeting: {{teacher_join_response.room_id}} with role: {{teacher_join_response.role}}"

     # Step 5: Teacher leaves the meeting
     - post:
         name: "Teacher leaves the meeting"
         url: "{{bffURL}}/internal-bff/api/v1/meetings/{{meeting_id}}/leave"
         headers:
           authorization: "Bearer {{testData.teacher.at}}"
         json: null
         expect:
           - statusCode: 200

     - log: "Teacher successfully left the meeting"

     # Step 6: Admin deletes the meeting
     - put:
         name: "Admin deletes the meeting"
         url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
         headers:
           authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
         json: |
           {
             "type": "SCHEDULE_TYPE_CLASS",
             "tenant_id": "{{testData.env.TENANT_ID}}",
             "class_schedule": {
               "request": [
                 {
                   "from_status": "SCHEDULE_STATUS_PUBLISHED",
                   "to_status": "SCHEDULE_STATUS_DELETED",
                   "schedule_time": {
                     "from": "",
                     "to": ""
                   },
                   "filter": {
                     "schedules": {
                       "values": [
                         "{{schedule_id}}"
                       ],
                       "op": "IN"
                     }
                   }
                 }
               ]
             }
           }
         capture:
           - json: $.matched
             as: delete_matched_count
           - json: $.successCount
             as: delete_success_count
         expect:
           - statusCode: 200
           - hasProperty: "matched"
           - hasProperty: "successCount"

     - log: "Admin successfully deleted the meeting with schedule ID: {{schedule_id}}"
