# create-cancel-schedule.yml
# Flow - Create and Cancel MNP Schedule
config:
  variables:
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web
      x-device-id: ee7af70f-c38e-4912-af76-54f093fe96a4
  fixtures:
    - testData
    - bffURL

scenarios:
  - name: "Create and cancel personal mentorship schedule"
    flow:
      - function:
          name: getDates
          capture: dates
          
      - function:
          name: getTimeAfterMinutes
          args: [5]
          capture: startTime
          
      - function:
          name: getTimeAfterMinutes
          args: [10]
          capture: endTime
            
      # Step 1: Fetch student data
      - post:
          name: "Fetch student data for mentorship"
          url: "{{bffURL}}/internal-bff/api/v1/carecorner/personal-mentorship/fetch-student-data"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            filters:
              mentor_id: ["{{testData.env.ADMIN_ID}}"]
            page: 1
            size: 10
            sortData: {}
          capture:
            - json: $.data.results[0].student_profile.student_id
              as: student_id
            - json: $.data.results[0].student_profile.batch_id
              as: batch_id
          expect:
            - statusCode: 200
            - hasProperty: "data.results"
      
      - log: "Student ID for mentorship: {{student_id}}"
      
      # Step 2: Create mentorship schedule
      - post:
          name: "Create mentorship schedule"
          url: "{{bffURL}}/internal-bff/api/v1/carecorner/personal-mentorship/schedule"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            data:
              student_id: "{{student_id}}"
              scheduled_date: "{{dates.startDate}}"
              scheduled_start_time: "{{startTime}}"
              scheduled_end_time: "{{endTime}}"
              guests: []
              mode: "ONLINE_MODE"
              mentor_id: "{{testData.env.ADMIN_ID}}"
              schedule_type: "UPCOMING_SCHEDULE"
          capture:
            - json: $.data.schedule.id
              as: schedule_id
            - json: $.data.schedule.status
              as: schedule_status
          expect:
            - statusCode: 200
            - hasProperty: "data.schedule.id"
      
      - log: "Created schedule with ID: {{schedule_id}} and status: {{schedule_status}}"
      
      # Step 3: Verify schedule details in slider data
      - post:
          name: "Verify schedule details in slider data"
          url: "{{bffURL}}/internal-bff/api/v1/carecorner/personal-mentorship/fetch-student-slider-data"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            student_id: "{{student_id}}"
          capture:
            - json: $.data.meeting.id
              as: meeting_id
            - json: $.data.meeting.status
              as: meeting_status
          expect:
            - statusCode: 200
            - hasProperty: "data.meeting"
            - hasProperty: "data.meeting.id"
      
      - log: "Meeting ID from slider: {{meeting_id}} with status: {{meeting_status}}"
      
      # Step 4: Cancel/Close the schedule
      - put:
          name: "Cancel the mentorship schedule"
          url: "{{bffURL}}/internal-bff/api/v1/carecorner/personal-mentorship/schedule"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            schedule_id: "{{meeting_id}}"
            data:
              status: "CLOSED"
              mentor_notes: "Test notes"
              student_notes: "Test student notes"
              category: "scheduled"
          capture:
            - json: $.data.schedule.status
              as: closed_status
          expect:
            - statusCode: 200
      
      - log: "Schedule closed with status: {{closed_status}}"
      
      # Step 5: Verify schedule is closed
      - post:
          name: "Verify schedule is closed"
          url: "{{bffURL}}/internal-bff/api/v1/carecorner/personal-mentorship/fetch-student-data"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            filters:
              mentor_id: ["{{testData.env.ADMIN_ID}}"]
            page: 1
            size: 10
            sortData: {}
          expect:
            - statusCode: 200
      
      - log: "Mentorship schedule test completed successfully"
