#homework-creation.yml
#Flow - Teacher Assigns a Homework to the Meeting Students
config:
  target: https://bff.allen-stage.in
  variables:
    urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"
    subjectId: 2

  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web
      x-device-id: ef8057c0-4679-4cf4-8d94-9b86ffd5363a
  fixtures:
    - adminURL
    - bffURL
    - testData
    - login

scenarios:
  - name: "Creating Homework from teacher side"
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiClass_"
          capture: random_class_name
      - function: 
          name: getDates
          capture: dates
      
      # Step 1: Create class schedule
      - post:
          name: "Create class schedule for homework"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            tenant_id: "{{testData.env.TENANT_ID}}"
            data:
              type: "SCHEDULE_TYPE_CLASS"
              schedules:
                - external_identifier: "0"
                  title: "{{random_class_name}}"
                  start_time: "{{dates.meeting_start_epoch}}"
                  end_time: "{{dates.meeting_end_epoch}}"
                  participants:
                    - id: "{{ testData.teacher.id }}"
                      type: "PARTICIPANT_TYPE_TEACHER"
                      role: "PARTICIPANT_ROLE_ORGANIZER"
                    - id: "{{testData.batch.id}}"
                      type: "PARTICIPANT_TYPE_BATCH"
                      role: "PARTICIPANT_ROLE_VIEWER"
                  type: "SCHEDULE_TYPE_CLASS"
                  facility_id: ""
                  visibility: "SCHEDULE_VISIBILITY_PUBLIC"
                  class_schedule_metadata:
                    class_type: "LIVE_LECTURE"
                    mode: "SCHEDULE_MODE_ONLINE"
                    scheduled_subjects:
                      - id: "{{subjectId}}"
                        subject_id: "{{subjectId}}"
                        taxonomy_id: "{{testData.env.TAXONAMY_ID_LIVE}}"
                        nodes:
                          - id: "86"
                            type: "TOPIC"
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"

      - log: "Status of scheduled class : - {{captured_data_schedules_0_status}}"

      # Step 2: Publish the class
      - put:
          name: "Publish class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_DRAFT"
                  to_status: "SCHEDULE_STATUS_PUBLISHED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 #sleep required to generate the meetingId post publish
      
      # Step 3: Verify the class is published and get meeting ID
      - post:
          name: "Verify class is published and get meeting ID"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_meeting_id
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"
      
      - log: "meeting link : - {{captured_data_meeting_id}}"
      - sleep: 3000

      # Step 4: Create homework
      - post:
          name: "Open the create homework button"
          url: "{{bffURL}}/internal-bff/api/v1/homework"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            context_id: "{{captured_data_meeting_id}}"
            context_type: CONTEXT_TYPE_LIVE_LECTURE
            subject_id: "2"
            course_id: "{{testData.batch.course}}"
            activities:
              - activity_type: ACTIVITY_TYPE_ASSESSMENT
                status: ACTIVITY_DRAFT
          capture:
            - json: $.data.activities[0].activity_id
              as: captured_data_activities_0_activity_id
            - json: $.data.activities[0].status
              as: captured_data_activities_0_status
          expect:
            - statusCode: 200
            - hasProperty: "data.activities[0].activity_id"
            - hasProperty: "data.activities[0].status"
      - log: "Activity id : - {{captured_data_activities_0_activity_id}}"

      - get:
          name: "Preview the homework"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_activities_0_activity_id}}/preview"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          capture:
            - json: $.data.paper_id
              as: captured_data_paper_id
            - json: $.data.status
              as: captured_data_status
          expect:
            - statusCode: 200
            - hasProperty: "data.paper_id"
      - log: "staus of homework : - {{captured_data_status}}"

      - get:
          name: "verify homework topics"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/topics/{{captured_data_paper_id}}/type/HOMEWORK"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          expect:
            - statusCode: 200

      - post:
          name: "homework nesting"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/getNestedChildren"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
            node_ids:
              - "86"
          expect:
            - statusCode: 200

      - put:
          name: "homework sections"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_paper_id}}/sections"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            paper_id: "{{captured_data_paper_id}}"
            topics:
              - name: Atomic Structure
                id: "86"
                node_type: TOPIC
                taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
                sub_nodes: []
          capture:
            - json: $.data.status
              as: captured_data_status
          expect:
            - statusCode: 200
            - hasProperty: "data.status"

      - post:
          name: "homework merged view"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_paper_id}}/get-merged-view"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            section_raw_namespace: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}#86"
            batch_details:
              - batch_code: "{{testData.batch.name}}"
                batch_id: "{{testData.batch.id}}"
          expect:
            - statusCode: 200

      - post:
          name: "homework merged view"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_paper_id}}/get-merged-view"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            section_raw_namespace: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}#86"
            batch_details:
              - batch_code: "{{testData.batch.name}}"
                batch_id: "{{testData.batch.id}}"
          capture:
            - json: $.data.status
              as: captured_data_status
          expect:
            - statusCode: 200
            - hasProperty: "data.status"

      - put:
          name: "questions of homework"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_paper_id}}/questions"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            paper_id: "{{captured_data_paper_id}}"
            questions:
              - action_type: ADD
                question_id: "{{testData.env.API_HOMEWORK_QUESTIONID}}"
                question_version: "{{testData.env.API_HOMEWORK_QUESTION_VERSION}}"
                raw_namespace: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}#86${{testData.env.API_HOMEWORK_SECTION_NAME}}"
                metadata:
                  metadata_kvs:
                    - key: DIGITAL_MODULE_SEQUENCE_NO
                      value: "{{testData.env.API_HOMEWORK_SEQUENCE_VALUE}}"
                    - key: DIGITAL_MODULE_SECTION_NAME
                      value: "{{testData.env.API_HOMEWORK_SECTION_NAME}}"
          expect:
            - statusCode: 200

      - get:
          name: "preview homework"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_paper_id}}/preview"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          capture:
            - json: $.data.status
              as: captured_data_status
          expect:
            - statusCode: 200
      - log: "Preview homework Status : - {{captured_data_status}}"

      - put:
          name: "publish homework"
          url: "{{bffURL}}/internal-bff/api/v1/question-collection/{{captured_data_paper_id}}/status?type=HOMEWORK"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            status: PUBLISHED
            paper_id: "{{captured_data_paper_id}}"
            meeting_id: "{{captured_data_meeting_id}}"
          capture:
            - json: $.data.action.homework_id
              as: captured_data_action_homework_id
          expect:
            - statusCode: 200
            - hasProperty: "data.action.homework_id"
      - log: "Homework id : - {{captured_data_action_homework_id}}"

      - post:
          url: "{{bffURL}}/internal-bff/api/v1/homework/bulk_upsert"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            paper_id: "{{captured_data_paper_id}}"
            source_meeting_id: "{{captured_data_meeting_id}}"
            homeworks:
              - id: "{{captured_data_meeting_id}}"
                schedule:
                  start_time: "{{dates.today_epoch_date}}"
                  deadline: "{{dates.end_of_today}}"
                  duration_in_mins: 0
                recipients:
                  - recipient_id: "{{testData.batch.id}}"
                    recipient_type: RECIPIENT_TYPE_BATCH
                    recipient_code: "{{testData.batch.name}}"
                homework_id: "{{captured_data_action_homework_id}}"
                activities:
                  - activity_type: ACTIVITY_TYPE_ASSESSMENT
                    activity_name: >-
                      "Atomic Structure, Heisenberg Uncertainty Principle, Introduction to Schrodinger Wave Equation, Miscellaneous/Mixed, Quantum Model . Advanced level Illustration, Quantum Model, Electronic Configuration Rules, Heisenberg Uncertainty Principle, Introduction to Schrodinger Wave Equation , Schrodinger Wave Equation, Probabilty Distribution Functions, Quantum Numbers, Shapes of Orbitals, Nodes, Bohr's Atomic Model, Quantum Model, Discovery of Sub-Atomic Particles , Thomson's Model, Anode Rays, Hydrogen Spectrum, Historical Significance of Atom, Planck's Quantum Theory, Photo Electric Effect"
                    activity_id: "{{captured_data_paper_id}}"
                    status: ACTIVITY_SCHEDULED
          capture:
            - json: $.data.status
              as: captured_data_status
          expect:
            - statusCode: 200
            - hasProperty: "data.status"
      - log: "Homework status : - {{captured_data_status}}"
      
      # Step 9: Delete the class schedule
      - put:
          name: "Delete the class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_PUBLISHED",
                    "to_status": "SCHEDULE_STATUS_DELETED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: delete_matched_count
            - json: $.successCount
              as: delete_success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
      - log: "Class deleted successfully"
