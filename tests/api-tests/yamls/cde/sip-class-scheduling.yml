#sip-class-scheduling.yml
#Flow - Admin Schedules Meeting with SIP mode, Publishes from Draft to Published state, teacher and student join and leave
config:
  variables:
    subjectId: 2
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web

  fixtures:
    - bffURL
    - testData
    - login
          
scenarios:
  - name: "Creating SIP mode class scheduling"
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "SIP_"
          capture: random_class_name
      - function: 
          name: getDates
          capture: dates
            
      - post:
          name: "Create SIP class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "data": {
                "type": "SCHEDULE_TYPE_CLASS",
                "schedules": [
                  {
                    "external_identifier": "0",
                    "title": "{{random_class_name}}",
                    "start_time": {{dates.meeting_start_epoch}},
                    "end_time": {{dates.meeting_end_epoch}},
                    "participants": [
                      {
                        "id": "{{ testData.teacher.id }}",
                        "type": "PARTICIPANT_TYPE_TEACHER",
                        "role": "PARTICIPANT_ROLE_ORGANIZER"
                      },
                      {
                        "id": "{{testData.batch.id}}",
                        "type": "PARTICIPANT_TYPE_BATCH",
                        "role": "PARTICIPANT_ROLE_VIEWER"
                      }
                    ],
                    "type": "SCHEDULE_TYPE_CLASS",
                    "facility_id": "{{testData.room.id}}",
                    "visibility": "SCHEDULE_VISIBILITY_PUBLIC",
                    "class_schedule_metadata": {
                      "class_type": "LIVE_LECTURE",
                      "mode": "SCHEDULE_MODE_SIP",
                      "scheduled_subjects": [
                        {
                          "id": "{{subjectId}}",
                          "subject_id": "{{subjectId}}",
                          "taxonomy_id": "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}",
                          "nodes": [
                            {
                              "id": "86",
                              "type": "TOPIC"
                            }
                          ]
                        }
                      ]
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
            - json: $.schedules[0].classScheduleMetadata.mode
              as: captured_class_mode
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"
            - hasProperty: "schedules[0].classScheduleMetadata.mode"
            - equals:
                - "{{captured_class_mode}}"
                - "SCHEDULE_MODE_SIP"

      - log: "Status of scheduled SIP class: {{captured_data_schedules_0_status}}"

      - put:
          name: "Publish SIP class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_DRAFT",
                    "to_status": "SCHEDULE_STATUS_PUBLISHED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 #sleep required to generate the meetingId post publish
      
      - post:
          name: "Verify SIP class is published"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_schedules_0_details_class_id
            - json: $.data.schedules[0].status
              as: published_status
            - json: $.data.schedules[0].mode
              as: class_mode
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"
            - equals:
                - "{{published_status}}"
                - "SCHEDULE_STATUS_PUBLISHED"
            - equals:
                - "{{class_mode}}"
                - "SCHEDULE_MODE_SIP"
      
      - log: "SIP class meeting ID: {{captured_data_schedules_0_details_class_id}}"
      - sleep: 10000 # Wait for meeting to start
      - post:
          name: "Teacher joins the SIP class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_teacher_join
            - json: $.data.room_id
              as: teacher_room_id
            - json: $.data.role
              as: teacher_role
            - json: $.data.type
              as: teacher_type
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - equals:
                - "{{teacher_room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"
            - hasProperty: "data.role"
            - equals:
                - "{{teacher_role}}"
                - "TEACHER"
            - hasProperty: "data.type"
            - equals:
                - "{{teacher_type}}"
                - "ORGANISER"
      
      - log: "Teacher join response: {{captured_data_teacher_join}}"

      - post:
          name: "Student joins the SIP class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_student_join
            - json: $.data.room_id
              as: student_room_id
            - json: $.data.role
              as: student_role
            - json: $.data.type
              as: student_type
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - equals:
                - "{{student_room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"
            - hasProperty: "data.role"
            - equals:
                - "{{student_role}}"
                - "STUDENT"
            - hasProperty: "data.type"
            - equals:
                - "{{student_type}}"
                - "AUDIENCE"
      
      - log: "Student join response: {{captured_data_student_join}}"

      - post:
          name: "Student leaves the SIP class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          expect:
            - statusCode: 200

      - post:
          name: "Teacher leaves the SIP class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          expect:
            - statusCode: 200

      - put:
          name: "Delete the SIP class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_PUBLISHED",
                    "to_status": "SCHEDULE_STATUS_DELETED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
