# #scheduling.yml
# #Flow - Admin Schedules Meeting , Publishes from Draft to Published state ,
# config:
#   target: https://bff.allen-stage.in
#   variables:
#     urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"
#     subjectId: 2
#     taxonomyId: "1701181887VZ"

#   defaults:
#     headers:
#       accept: application/json
#       accept-language: en-GB,en-US;q=0.9,en;q=0.8
#       content-type: application/json
#       # origin: https://console.allen-stage.in
#       x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
#       # user-agent: >-
#       #   Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
#       #   (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36
#       x-client-type: web

#   fixtures:
#     - adminURL
#     - bffURL
#     - testData
#     - login
      
# scenarios:
#   - name: "verify polls"
#     flow:
#       - function:
#           name: generateRandomString
#           args:
#             - 6
#             - "apiClass_"
#           capture: random_class_name
#       - function:
#           name: generateRandomString
#           args:
#             - 4
#           capture: random_UUID
#       - function:
#           name: generateRandomString
#           args:
#             - 4
#           capture: random_quesID
#       - function: 
#           name: getDates
#           capture: dates
#       - post:
#           name: "Create meeting id Api"
#           url: "{{urlIc}}/resource/class-schedules:create"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             data:
#               - display_name: "{{random_class_name}}"
#                 course_id: "{{testData.batch.course}}"
#                 batches:
#                   - id: "{{testData.batch.id}}"
#                     name: "{{testData.batch.name}}"
#                 facility_id: ""
#                 status: SCHEDULE_STATUS_UNSPECIFIED
#                 class_mode: SCHEDULE_MODE_LIVE_TELECAST
#                 scheduled_date: "{{dates.today_epoch_date}}"
#                 scheduled_start_time: "{{dates.startTime}}"
#                 scheduled_end_time: "{{dates.endTime}}"
#                 details:
#                   class_type: LIVE_LECTURE
#                   contents:
#                     - scheduled_topics:
#                         - topic_id: "86"
#                           topic_name: "Atomic Structure"
#                   resources: []
#                   teacher_id: "{{ testData.teacher.id }}"
#                   teacher_name: "{{ testData.teacher.name }} ({{testData.teacher.phone}})"
#                   teacher_in_time: 0
#                   teacher_out_time: 0
#                 subject_id: "{{subjectId}}"
#                 taxonomy_id: "1701181887VZ"
#                 subject_name: "Chemistry (STREAM_JEE_MAIN_ADVANCED-CLASS_11)"
#           capture:
#             - json: $.data.schedules[0].id
#               as: captured_data_schedules_0_id
#             - json: $.data.schedules[0].status
#               as: captured_data_schedules_0_status
#           expect:
#             - statusCode: 200
#             - hasProperty: "data.schedules[0].id"
#             - hasProperty: "data.schedules[0].status"

#       - log: "Status of scheduled class : - {{captured_data_schedules_0_status}}"

#       - put:
#           name: "Publish Meeting Api"
#           url: "{{urlIc}}/resource/class-schedules/status"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             filter:
#               statuses:
#                 op: IN
#                 values:
#                   - "{{captured_data_schedules_0_status}}"
#               schedule_time:
#                 from: "{{dates.start_of_week}}"
#                 to: "{{dates.end_of_week}}"
#               schedules:
#                 op: IN
#                 values:
#                   - "{{captured_data_schedules_0_id}}"
#             target_status: SCHEDULE_STATUS_PUBLISHED
#           expect:
#             - statusCode: 200

#       - sleep: 3000 #sleep required to generate the meetingId post publish
#       - get:
#           name: "Fetch the Meeting Details"
#           url: "{{urlIc}}/resource/class-schedules/teacher-view?scheduled_start_time={{dates.startTime}}&scheduled_end_time={{dates.endTime}}"
#           headers:
#             authorization: "Bearer {{testData.teacher.at}}"
#           capture:
#             - json: $.data.schedules[0].details.class_id
#               as: captured_data_schedules_0_details_class_id
#           expect:
#             - statusCode: 200
#             - hasProperty: "data.schedules[0].details.class_id"
#       - log: "meeting link : - {{captured_data_schedules_0_details_class_id}}"

#       - post:
#           name: "Teacher joins the Meeting Api"
#           url: "{{urlIc}}/meetings/{{captured_data_schedules_0_details_class_id}}/join"
#           headers:
#             authorization: "Bearer {{testData.teacher.at}}"
#           json: null
#           expect:
#             - statusCode: 200

#       - post:
#           name: "Student joins the Meeting Api"
#           url: "{{bffURL}}/meetings/{{captured_data_schedules_0_details_class_id}}/join"
#           headers:
#             authorization: "Bearer {{login.studentToken}}"
#           json: null
#           expect:
#             - statusCode: 200
#       - sleep: 4000
#       - post:
#           name: "Create New Poll"
#           url: "https://bff.allen-stage.in/internal-bff/v1/polls"
#           headers:
#             authorization: "Bearer {{testData.teacher.at}}"
#           json:
#             creator_id: "{{testData.student.id}}"
#             meeting_id: "{{captured_data_schedules_0_details_class_id}}"
#             request_id: "XCVFf195-799e-4d82-84a4-47e7df17a33f"
#             start_time: 1733151611
#             duration_in_secs: 0
#             questions:
#               - id: "ABCDbee7-5da0-4cb8-9240-70b863586c77"
#                 text: ""
#                 type: "SINGLE_SELECT"
#                 options:
#                   - id: "0"
#                     text: "Yes"
#                   - id: "1"
#                     text: "No"
#           expect:
#             - statusCode: 200
#             - hasProperty: pollId
#           capture:
#             - json: $.pollId
#               as: pollId

#       - log: "New Poll created with ID: {{ pollId }}"

#       - post:
#           name: "Submit Vote for Poll from Student Side"
#           url: "{{bffURL}}/polls/{{ pollId }}/votes"
#           headers:
#             authorization: "Bearer {{login.studentToken}}"
#           json:
#             voter_id: "{{testData.student.id}}"
#             request_id: "{{ random_UUID }}f195-799e-4d82-84a4-47e7df17a33f"
#             submissions:
#               - question_id: "{{random_quesID}}bee7-5da0-4cb8-9240-70b863586c77"
#                 options: ["0"]
#           expect:
#             - statusCode: 200
#           capture:
#             - json: $
#               as: voteResponse
#       - log: "{{voteResponse}}"
#       - post:
#           name: "Student leaves the meeting Api"
#           url: "{{bffURL}}/meetings/{{classId}}/leave"
#           headers:
#             authorization: >-
#               "Bearer {{login.studentToken}}"
#           json: null
#           expect:
#             - statusCode: 200

#       - post:
#           name: "Teacher leaves the meeting Api"
#           url: "{{urlIc}}/meetings/{{classId}}/leave"
#           headers:
#             authorization: "Bearer {{testData.teacher.at}}"
#           json: null
#           expect:
#             - statusCode: 200

#       - put:
#           name: "Meeting id status changes to Deleted"
#           url: "{{urlIc}}/resource/class-schedules/status"
#           headers:
#             authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
#           json:
#             filter:
#               statuses:
#                 op: IN
#                 values:
#                   - SCHEDULE_STATUS_PUBLISHED
#               schedule_time:
#                 from: "{{dates.start_of_week}}"
#                 to: "{{dates.end_of_week}}"
#               schedules:
#                 op: IN
#                 values:
#                   - "{{captured_data_schedules_0_id}}"
#             target_status: SCHEDULE_STATUS_DELETED
#           expect:
#             - statusCode: 200
