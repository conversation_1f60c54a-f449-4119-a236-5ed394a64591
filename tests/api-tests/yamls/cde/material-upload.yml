#material-upload.yml
#Flow - Upload Content to Scheduled Meeting
config:
  target: https://bff.allen-stage.in
  variables:
    urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"
    subjectId: 2
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web
      x-device-id: ef8057c0-4679-4cf4-8d94-9b86ffd5363a
  fixtures:
    - adminURL
    - bffURL
    - testData
    - login

scenarios:
  - name: "Upload materials to the scheduled class"
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiClass_"
          capture: random_class_name
      - function: 
          name: getDates
          capture: dates
          
      # Step 1: Create online class schedule
      - post:
          name: "Create online class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            tenant_id: "{{testData.env.TENANT_ID}}"
            data:
              type: "SCHEDULE_TYPE_CLASS"
              schedules:
                - external_identifier: "0"
                  title: "{{random_class_name}}"
                  start_time: "{{dates.meeting_start_epoch}}"
                  end_time: "{{dates.meeting_end_epoch}}"
                  participants:
                    - id: "{{ testData.teacher.id }}"
                      type: "PARTICIPANT_TYPE_TEACHER"
                      role: "PARTICIPANT_ROLE_ORGANIZER"
                    - id: "{{testData.batch.id}}"
                      type: "PARTICIPANT_TYPE_BATCH"
                      role: "PARTICIPANT_ROLE_VIEWER"
                  type: "SCHEDULE_TYPE_CLASS"
                  facility_id: ""
                  visibility: "SCHEDULE_VISIBILITY_PUBLIC"
                  class_schedule_metadata:
                    class_type: "LIVE_LECTURE"
                    mode: "SCHEDULE_MODE_ONLINE"
                    scheduled_subjects:
                      - id: "{{subjectId}}"
                        subject_id: "{{subjectId}}"
                        taxonomy_id: "{{testData.env.TAXONAMY_ID_LIVE}}"
                        nodes:
                          - id: "86"
                            type: "TOPIC"
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"
      - log: "Status of scheduled class: {{captured_data_schedules_0_status}}"

      # Step 2: Publish the online class
      - put:
          name: "Publish online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_DRAFT"
                  to_status: "SCHEDULE_STATUS_PUBLISHED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 #sleep required to generate the meetingId post publish
      
      # Step 3: Verify the online class is published and get meeting ID
      - post:
          name: "Verify online class is published"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_schedules_0_details_class_id
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"
            
      - log: "Meeting ID: {{captured_data_schedules_0_details_class_id}}"

      # material upload
      - post:
          name: "create content"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/createContent"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            meeting_id: "{{captured_data_schedules_0_details_class_id}}"
            resource_type: ANNOTATED_PPT
            status: VISIBLE_TO_HOST
          capture:
            - json: $.data.material.id
              as: captured_data_material_id
          expect:
            - statusCode: 200
            - hasProperty: "data.material.id"

      - post:
          name: "init multipart upload"
          url: "{{bffURL}}/internal-bff/api/v1/learningMaterials/{{captured_data_material_id}}/init_multipart_upload"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            file_format: pdf
          capture:
            - json: $.data.upload_data.upload_id
              as: captured_data_upload_data_upload_id
          expect:
            - statusCode: 200
            - hasProperty: "data.upload_data.upload_id"

      - post:
          name: "materials upload part"
          url: "{{bffURL}}/internal-bff/api/v1/learningMaterials/{{captured_data_material_id}}/upload_part"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            id: "{{captured_data_schedules_0_details_class_id}}"
            part_number: 1
            upload_id: "{{captured_data_upload_data_upload_id}}"
          expect:
            - statusCode: 200

      - get:
          name: "pre-class teacher join api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/preClassTeacher?meeting_id={{captured_data_schedules_0_details_class_id}}"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          capture:
            - json: $.data
              as: captured_pre_class_response
          expect:
            - statusCode: 200
      - log: "pre-class api response: {{captured_pre_class_response}}"

      # Step 8: Delete the online class
      - put:
          name: "Delete the online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_PUBLISHED"
                  to_status: "SCHEDULE_STATUS_DELETED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: delete_matched_count
            - json: $.successCount
              as: delete_success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
            
      - log: "Online class deleted successfully"
