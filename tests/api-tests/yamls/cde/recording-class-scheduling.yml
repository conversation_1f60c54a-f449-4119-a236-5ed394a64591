#recording-class-scheduling.yml
#Flow - Admin Schedules Recording-Only Meeting, Publishes from Draft to Published state, only teacher can join,
config:
  variables:
    subjectId: "2"  # Chemistry
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web
  fixtures:
    - bffURL
    - testData
    - login
          
scenarios:
  - name: "Creating recording-only class scheduling"
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "REC_"
          capture: random_class_name
      
      # Get current time and calculate start/end times that are close enough to join
      - function: 
          name: getDates
          capture: dates
      
      - log: "Meeting start time: {{dates.meeting_start_epoch}}, Meeting end time: {{dates.meeting_end_epoch}}"
      
      # Step 1: Admin creates a new recording-only class schedule
      - post:
          name: "Create recording-only class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            tenant_id: "{{testData.env.TENANT_ID}}"
            data:
              type: "SCHEDULE_TYPE_CLASS"
              schedules:
                - external_identifier: "0"
                  title: "{{random_class_name}}"
                  start_time: "{{dates.meeting_start_epoch}}"
                  end_time: "{{dates.meeting_end_epoch}}"
                  participants:
                    - id: "{{ testData.teacher.id }}"
                      type: "PARTICIPANT_TYPE_TEACHER"
                      role: "PARTICIPANT_ROLE_ORGANIZER"
                    - id: "{{testData.batch.id}}"
                      type: "PARTICIPANT_TYPE_BATCH"
                      role: "PARTICIPANT_ROLE_VIEWER"
                  type: "SCHEDULE_TYPE_CLASS"
                  facility_id: ""
                  visibility: "SCHEDULE_VISIBILITY_PUBLIC"
                  class_schedule_metadata:
                    class_type: "LIVE_LECTURE"
                    mode: "SCHEDULE_MODE_RECORDING_ONLY"
                    scheduled_subjects:
                      - id: "{{subjectId}}"
                        subject_id: "{{subjectId}}"
                        taxonomy_id: "{{testData.env.TAXONAMY_ID_LIVE}}"
                        nodes:
                          - id: "86"
                            type: "TOPIC"
                    pre_class_materials: []
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
            - json: $.schedules[0].classScheduleMetadata.mode
              as: captured_class_mode
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"
            - hasProperty: "schedules[0].classScheduleMetadata.mode"
            - equals:
                - "{{captured_class_mode}}"
                - "SCHEDULE_MODE_RECORDING_ONLY"

      - log: "Status of scheduled recording-only class: {{captured_data_schedules_0_status}}"

      # Step 2: Admin publishes the recording-only class
      - put:
          name: "Publish recording-only class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_DRAFT"
                  to_status: "SCHEDULE_STATUS_PUBLISHED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 #sleep required to generate the meetingId post publish
      
      # Step 3: Verify the recording-only class is published and get meeting ID
      - post:
          name: "Verify recording-only class is published"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_schedules_0_details_class_id
            - json: $.data.schedules[0].status
              as: published_status
            - json: $.data.schedules[0].mode
              as: class_mode
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"
            - equals:
                - "{{published_status}}"
                - "SCHEDULE_STATUS_PUBLISHED"
            - equals:
                - "{{class_mode}}"
                - "SCHEDULE_MODE_RECORDING_ONLY"
      
      - log: "Recording-only class meeting ID: {{captured_data_schedules_0_details_class_id}}"

      # Step 4: Teacher joins the recording-only class
      - post:
          name: "Teacher joins the recording-only class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_Teacher_join
            - json: $.data.room_id
              as: teacher_room_id
            - json: $.data.role
              as: teacher_role
            - json: $.data.type
              as: teacher_type
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - equals:
                - "{{teacher_room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"
            - hasProperty: "data.role"
            - equals:
                - "{{teacher_role}}"
                - "TEACHER"
            - hasProperty: "data.type"
            - equals:
                - "{{teacher_type}}"
                - "ORGANISER"
      
      - log: "Teacher join response: {{captured_data_Teacher_join}}"

      # Step 7: Teacher leaves the recording-only class
      - post:
          name: "Teacher leaves the recording-only class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          expect:
            - statusCode: 200

      # Step 8: Delete the recording-only class
      - put:
          name: "Delete the recording-only class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_PUBLISHED"
                  to_status: "SCHEDULE_STATUS_DELETED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
