#online-class-scheduling.yml
#Flow - Admin Schedules Meeting , Publishes from Draft to Published state ,
config:
  target: https://bff.allen-stage.in
  variables:
    urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"
    subjectId: 2
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      origin: https://console.allen-stage.in
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web
  fixtures:
    - adminURL
    - bffURL
    - testData
    - login

scenarios:
  - name: "Creating online class scheduling, join as teacher, join as student, leave class, delete class"
    flow:
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiClass_"
          capture: random_class_name
      - function: 
          name: getDates
          capture: dates
      
      # Step 1: Create online class schedule
      - post:
          name: "Create online class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            tenant_id: "{{testData.env.TENANT_ID}}"
            data:
              type: "SCHEDULE_TYPE_CLASS"
              schedules:
                - external_identifier: "0"
                  title: "{{random_class_name}}"
                  start_time: "{{dates.meeting_start_epoch}}"
                  end_time: "{{dates.meeting_end_epoch}}"
                  participants:
                    - id: "{{ testData.teacher.id }}"
                      type: "PARTICIPANT_TYPE_TEACHER"
                      role: "PARTICIPANT_ROLE_ORGANIZER"
                    - id: "{{testData.batch.id}}"
                      type: "PARTICIPANT_TYPE_BATCH"
                      role: "PARTICIPANT_ROLE_VIEWER"
                  type: "SCHEDULE_TYPE_CLASS"
                  facility_id: ""
                  visibility: "SCHEDULE_VISIBILITY_PUBLIC"
                  class_schedule_metadata:
                    class_type: "LIVE_LECTURE"
                    mode: "SCHEDULE_MODE_ONLINE"
                    scheduled_subjects:
                      - id: "{{subjectId}}"
                        subject_id: "{{subjectId}}"
                        taxonomy_id: "{{testData.env.TAXONAMY_ID_LIVE}}"
                        nodes:
                          - id: "86"
                            type: "TOPIC"
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"
      - log: "Status of scheduled class: {{captured_data_schedules_0_status}}"

      # Step 2: Publish the online class
      - put:
          name: "Publish online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_DRAFT"
                  to_status: "SCHEDULE_STATUS_PUBLISHED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 #sleep required to generate the meetingId post publish
      
      # Step 3: Verify the online class is published and get meeting ID
      - post:
          name: "Verify online class is published"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_schedules_0_details_class_id
            - json: $.data.schedules[0].status
              as: published_status
            - json: $.data.schedules[0].mode
              as: class_mode
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"
            - equals:
                - "{{published_status}}"
                - "SCHEDULE_STATUS_PUBLISHED"
            - equals:
                - "{{class_mode}}"
                - "SCHEDULE_MODE_ONLINE"
      
      - log: "Online class meeting ID: {{captured_data_schedules_0_details_class_id}}"
      - sleep: 10000 # Wait for meeting to start

      # Step 4: Teacher joins the online class
      - post:
          name: "Teacher joins the online class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_Teacher_join
            - json: $.data.room_id
              as: teacher_room_id
            - json: $.data.role
              as: teacher_role
            - json: $.data.type
              as: teacher_type
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - equals:
                - "{{teacher_room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"
            - hasProperty: "data.role"
            - equals:
                - "{{teacher_role}}"
                - "TEACHER"
            - hasProperty: "data.type"
            - equals:
                - "{{teacher_type}}"
                - "ORGANISER"
      
      - log: "Teacher join response: {{captured_data_Teacher_join}}"

      # Step 5: Student joins the online class
      - post:
          name: "Student joins the online class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_student_join
            - json: $.data.room_id
              as: student_room_id
            - json: $.data.role
              as: student_role
            - json: $.data.type
              as: student_type
          expect:
            - statusCode: 200
            - hasProperty: "data.room_id"
            - equals:
                - "{{student_room_id}}"
                - "{{captured_data_schedules_0_details_class_id}}"
            - hasProperty: "data.role"
            - equals:
                - "{{student_role}}"
                - "STUDENT"
            - hasProperty: "data.type"
            - equals:
                - "{{student_type}}"
                - "AUDIENCE"
      
      - log: "Student join response: {{captured_data_student_join}}"

      # Step 6: Student leaves the online class
      - post:
          name: "Student leaves the online class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          expect:
            - statusCode: 200

      # Step 7: Teacher leaves the online class
      - post:
          name: "Teacher leaves the online class"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          expect:
            - statusCode: 200

      # Step 8: Delete the online class
      - put:
          name: "Delete the online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            type: "SCHEDULE_TYPE_CLASS"
            tenant_id: "{{testData.env.TENANT_ID}}"
            class_schedule:
              request:
                - from_status: "SCHEDULE_STATUS_PUBLISHED"
                  to_status: "SCHEDULE_STATUS_DELETED"
                  schedule_time:
                    from: ""
                    to: ""
                  filter:
                    schedules:
                      values:
                        - "{{captured_data_schedules_0_id}}"
                      op: "IN"
          capture:
            - json: $.matched
              as: delete_matched_count
            - json: $.successCount
              as: delete_success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
