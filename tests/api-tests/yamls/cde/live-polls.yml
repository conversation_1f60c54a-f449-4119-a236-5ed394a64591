# Flow: <PERSON><PERSON> schedules a meeting and publishes,then the teacher and student join,
#  teacher create a poll, student submits a vote, leave the meeting, and the class is deleted.

config:
  variables:
    subjectId: 2
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-device-id: 89f725e4-e05e-4c8b-9c2f-4716726c4331
      x-client-type: web
  fixtures:
    - bffURL
    - testData
    - login

scenarios:
  - name: "Creating online class scheduling, join as teacher & student, participate in a poll, leave class, delete class"
    flow:
      # Generate all required IDs upfront
      - function:
          name: generateRandomString
          args: [8, "poll_"]
          capture: question_id
      
      - function:
          name: generateRandomString
          args: [8, "req_"]
          capture: request_id

      - function:
          name: generateRandomString
          args: [8, "req_"]
          capture: vote_request_id

      # Get all required dates
      - function: 
          name: getDates
          capture: dates
      # Generate random class name
      - function:
          name: generateRandomString
          args:
            - 6
            - "apiClass_"
          capture: random_class_name

      # Step 1: <PERSON><PERSON> creates a new class schedule
      - post:
          name: "Create online class schedule"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "data": {
                "type": "SCHEDULE_TYPE_CLASS",
                "schedules": [
                  {
                    "external_identifier": "0",
                    "title": "{{random_class_name}}",
                    "start_time": {{dates.meeting_start_epoch}},
                    "end_time": {{dates.meeting_end_epoch}},
                    "participants": [
                      {
                        "id": "{{ testData.teacher.id }}",
                        "type": "PARTICIPANT_TYPE_TEACHER",
                        "role": "PARTICIPANT_ROLE_ORGANIZER"
                      },
                      {
                        "id": "{{testData.batch.id}}",
                        "type": "PARTICIPANT_TYPE_BATCH",
                        "role": "PARTICIPANT_ROLE_VIEWER"
                      }
                    ],
                    "type": "SCHEDULE_TYPE_CLASS",
                    "facility_id": "",
                    "visibility": "SCHEDULE_VISIBILITY_PUBLIC",
                    "class_schedule_metadata": {
                      "class_type": "LIVE_LECTURE",
                      "mode": "SCHEDULE_MODE_ONLINE",
                      "scheduled_subjects": [
                        {
                          "id": "{{subjectId}}",
                          "subject_id": "{{subjectId}}",
                          "taxonomy_id": "{{testData.env.TAXONAMY_ID_LIVE}}",
                          "nodes": [
                            {
                              "id": "86",
                              "type": "TOPIC"
                            }
                          ]
                        }
                      ]
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.schedules[0].id
              as: captured_data_schedules_0_id
            - json: $.schedules[0].status
              as: captured_data_schedules_0_status
          expect:
            - statusCode: 200
            - hasProperty: "schedules[0].id"
      - log: "Status of scheduled class : - {{captured_data_schedules_0_status}}"

      # Step 2: Admin publishes the meeting
      - put:
          name: "Publish online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_DRAFT",
                    "to_status": "SCHEDULE_STATUS_PUBLISHED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: matched_count
            - json: $.successCount
              as: success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"

      - sleep: 10000 # Wait for meeting ID to be generated after publish

      # Step 3: Verify the class is published and get meeting ID
      - post:
          name: "Verify class is published"
          url: "{{bffURL}}/urm-bff/api/v1/planning-and-scheduling/schedules"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "filter": {
                "batches": {
                  "op": "IN",
                  "values": ["{{testData.batch.id}}"]
                },
                "schedule_time": {
                  "from": {{dates.meeting_start_epoch}},
                  "to": {{dates.meeting_end_epoch}}
                }
              },
              "pagination": {
                "page_size": 10,
                "page_number": 1,
                "sort": {
                  "by": "createdAt",
                  "order": "DESC"
                }
              }
            }
          capture:
            - json: $.data.schedules[0].meeting_id
              as: captured_data_schedules_0_details_class_id
          expect:
            - statusCode: 200
            - hasProperty: "data.schedules[0].meeting_id"

      - log: "Meeting link : - {{captured_data_schedules_0_details_class_id}}"
      - sleep: 10000 # Wait for meeting to start

      # Step 4: Teacher joins the meeting
      - post:
          name: "Teacher joins the Meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_Teacher_join
          expect:
            - statusCode: 200

      - log: "Teacher join response: {{captured_data_Teacher_join}}"

      # Step 5: Student joins the meeting
      - post:
          name: "Student joins the Meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/join"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          capture:
            - json: $.data
              as: captured_data_student_join
          expect:
            - statusCode: 200

      - log: "Student join response: {{captured_data_student_join}}"

      # Step 6: Teacher Create poll with separate IDs
      - post:
          name: "Create New Poll from Teacher Side"
          url: "{{bffURL}}/internal-bff/v1/polls"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json:
            creator_id: "{{testData.teacher.id}}"
            meeting_id: "{{captured_data_schedules_0_details_class_id}}"
            request_id: "{{request_id}}"  # Unique request ID
            start_time: "{{dates.currentTime_in_secs}}"
            duration_in_secs: 30
            questions:
              - id: "{{question_id}}"  # Unique question ID
                text: ""
                type: "SINGLE_SELECT"
                options:
                  - id: "0"
                    text: "1"
                  - id: "1"
                    text: "2"
                  - id: "2"
                    text: "3"
                  - id: "3"
                    text: "4"
          capture:
            - json: $.pollId
              as: pollId
          expect:
            - statusCode: 200
            - hasProperty: pollId

      - log: "New Poll created with ID: {{pollId}}"

      # Step 7: Student submits a vote
      - post:
          name: "Submit Vote for Poll from Student Side"
          url: "{{bffURL}}/internal-bff/v1/polls/{{pollId}}/votes"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            voter_id: "{{testData.student.id}}"
            request_id: "{{vote_request_id}}"  # New unique request ID for vote
            submissions:
              - question_id: "{{question_id}}"  # Reference to poll question
                options: ["0"]
          expect:
            - statusCode: 200
          capture:
            - json: $
              as: voteResponse

      - log: "{{voteResponse}}"

      # Step 8: Student leaves the meeting
      - post:
          name: "Student leaves the meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json: null
          expect:
            - statusCode: 200

      # Step 9: Teacher leaves the meeting
      - post:
          name: "Teacher leaves the meeting Api"
          url: "{{bffURL}}/internal-bff/api/v1/meetings/{{captured_data_schedules_0_details_class_id}}/leave"
          headers:
            authorization: "Bearer {{testData.teacher.at}}"
          json: null
          expect:
            - statusCode: 200

      # Step 10: Admin deletes the meeting
      - put:
          name: "Delete the online class"
          url: "{{bffURL}}/internal-bff/v1/planning-and-scheduling/schedules/status"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json: |
            {
              "type": "SCHEDULE_TYPE_CLASS",
              "tenant_id": "{{testData.env.TENANT_ID}}",
              "class_schedule": {
                "request": [
                  {
                    "from_status": "SCHEDULE_STATUS_PUBLISHED",
                    "to_status": "SCHEDULE_STATUS_DELETED",
                    "schedule_time": {
                      "from": "",
                      "to": ""
                    },
                    "filter": {
                      "schedules": {
                        "values": [
                          "{{captured_data_schedules_0_id}}"
                        ],
                        "op": "IN"
                      }
                    }
                  }
                ]
              }
            }
          capture:
            - json: $.matched
              as: delete_matched_count
            - json: $.successCount
              as: delete_success_count
          expect:
            - statusCode: 200
            - hasProperty: "matched"
            - hasProperty: "successCount"
