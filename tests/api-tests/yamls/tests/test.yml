# config:
#   target: https://bff.allen-stage.in
#   variables:
#     page_url_schedule: /schedule-a-call-back
#     page_url_thank_you: /thank-you
#     class: Class 11
#     target_exam: JEE MAIN
#     learning_preference: Online
#     billing_state: Rajasthan
#     form_id: PNCF marketing_form_1
#     form_url: /schedule-a-call-back
#     lead_source: Marketing - Search Engine
#     sub_lead_source: Landing Page
#     tickTnC: true
#     tickPromoTnC: true
#     studentAccount:
#       phone: **********
#       name: Test Student
#       email: <EMAIL>
#       id: 123456

#   defaults:
#     headers:
#       content-type: application/json
#       x-client-type: web

#   fixtures:
#     - adminURL
#     - bffURL
#     - testData
#     - testTakingData

# scenarios:
#   - name: Sample test for illustrating the use of fixtures and api request with json body and assertions
#     flow:
#       - post:
#           name: Get Page for Schedule a Call Back
#           url: '{{bffURL}}/pages/getPage'
#           json:
#             page_url: '{{page_url_schedule}}'
#           expect:
#             - statusCode: 200
#             - hasProperty: 'status'

#       # - log: "File Level Test Taking Data: {{ testTakingData }}"
#       - log: 'seco Level Test Taking Data: {{adminURL}}'
#       - log: 'third Level Test Taking Data: {{testData}}'
#       - log: 'Taxonomy ID: {{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}'
