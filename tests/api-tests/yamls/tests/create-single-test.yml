#create-single-test.yml
# scheduling a test by admin and verifying it is showing in student portal or not
config:
  target: https://bff.allen-stage.in
  variables:
    link: "https://ap-south-1-staging-test-and-assessment-uploads-staging.s3.ap-south-1.amazonaws.com/LhpWj62UmX-UtLDMJ5rjy.pdf"
    paperCode: "9610WJAPRANUR24001"
    test_name: "ALLEN Test"
    test_category: "CLASSROOM"
    test_mode: "TEST_TAKING_MODE_OMR"
    urlIc: "https://bff.allen-stage.in/internal-bff/api/v1"


  defaults:
    headers:
      content-type: application/json
      x-client-type: web  
  fixtures:
    - adminURL
    - bffURL
    - testData
    - login

scenarios:
  - name: "create test as objective type, verify test status and cancel test" 
    flow:
      - function: 
          name: getDates
          capture: dates
      # Step 1: create the test as objective using admin
      - post:
          name: "create test"
          url: "{{bffURL}}/internal-bff/api/v1/tests"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            display_name: "{{test_name}}"
            category: "{{test_category}}"
            syllabus_pdf_link: "{{testData.env.TEST_SYLLABUS_LINK}}"
            assignment:
              batches:
                - batch_code: "{{testData.batch.name}}"
                  test_taking_mode: "{{test_mode}}"
            schedule:
              start_time: "{{dates.formattedNowDate}}"
              login_window_in_minutes: 5
              duration_in_minutes: 5
            student_test_display_details:
              name: "ACT"
              number: '156'
              paper_number: ''
            test_setting:
              is_reattempt_allowed: true
            type: "OBJECTIVE"
            ey_category: "OTHER"
            generate_ranks: ["TEST_LEVEL"]
            report_header_name: ''
          capture:
            - json: $.data
              as: create_test_response
          expect:
            - statusCode: 200
              message: "Expected status code to be 200"
            - hasProperty: "data.test_id"
              message: "Expected response to have property {{data.test_id}}"

      - log : "create test response : - {{create_test_response}}"

      # Step 2: publish the test post test creation
      - put:
          name: "publish test"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}/attachQuestionPaper"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            paper_code: "{{paperCode}}"
          capture:
            - json: $.data
              as: captured_data_status
          expect:
            - statusCode: 200
      - log : "publish test response : - {{captured_data_status}}"   

      # Step 3: verify test is published from admin side
      - get:
          name: "verify test is published"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}"
          headers:
            Authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.status
              as: test_status
          expect:
            - statusCode: 200
            - equals:
                - "{{test_status}}"
                - "PUBLISHED"
      - log : "test status : - {{test_status}}"

      # Step 4: cancel the created test
      - delete:
          name: "cancel created test"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.cancelled
              as: status
          expect:
            - statusCode: 200
            - equals:
                - "{{status}}"
                - true
      - log : "status of test is this cancelled : - {{status}}"

  - name: "Verify admin can create the test as subjective type and able to cancel at publish state" 
    flow:
      - function: 
          name: getDates
          capture: dates
      # Step 1: create the test as objective using admin
      - post:
          name: "create the test with valid subjective details"
          url: "{{bffURL}}/internal-bff/api/v1/tests"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            display_name: "{{test_name}}"
            category: "{{test_category}}"
            syllabus_pdf_link: "{{testData.env.TEST_SYLLABUS_LINK}}"
            assignment:
              batches:
                - batch_code: "{{testData.batch.name}}"
                  test_taking_mode: "{{test_mode}}" 
            schedule:
              start_time: "{{dates.formattedNowDate}}"
              login_window_in_minutes: 2
              duration_in_minutes: 2
            student_test_display_details:
              name: "ACT"
              number: '151'
              paper_number: ''
            test_setting:
              is_reattempt_allowed: false
            type: "SUBJECTIVE"
            generate_ranks: ["TEST_LEVEL"]
            report_header_name: ''
          capture:
            - json: $.data
              as: create_test_response
          expect:
            - statusCode: 200
              message: "Expected status code to be 200"
            - hasProperty: "data.test_id"
              message: "Expected response to have property {{data.test_id}}"

      - log : "create test response : - {{create_test_response}}"

      # Step 2: publish the test post test creation
      - put:
          name: "publish the test with papercode"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}/attachQuestionPaper"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            paper_code: "{{paperCode}}"
          capture:
            - json: $.data
              as: captured_data_status
          expect:
            - statusCode: 200
      - log : "publish test response : - {{captured_data_status}}"   

      # Step 3: verify test is published from admin side
      - get:
          name: "verify test is published"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}"
          headers:
            Authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.status
              as: test_status
          expect:
            - statusCode: 200
            - equals:
                - "{{test_status}}"
                - "PUBLISHED"
      - log : "test status : - {{test_status}}"

      # Step 4: cancel the created test
      - delete:
          name: "cancel created test when in publish state"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.cancelled
              as: status
          expect:
            - statusCode: 200
            - equals:
                - "{{status}}"
                - true
      - log : "status of test is this cancelled : - {{status}}"

  - name: "Verify test manager in IC" 
    flow:
      # Step : Get the info from test manager in admin ic
      - get:
          name: "Get the test manager details info"
          url: "{{bffURL}}/internal-bff/api/v1/tests/filterAdminTests?page_size=25&page_no=1&category=CLASSROOM,OLTS"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
            x-client-type: 'web'
            x-device-id: 'b8637e28-25d7-4cd9-9a22-1efb2070745b'
          capture:
            - json: $.data
              as: test_manager_response
          expect:
            - statusCode: 200
            - hasProperty: "data.total_results"
            - hasProperty: "data.test_info"
            - hasProperty: "data.test_details"
            - greaterThanZero: "{{test_manager_response.total_results}}"

      - log : "Test manager page response : - {{test_manager_response.total_results}}"
