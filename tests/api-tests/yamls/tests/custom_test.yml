#custom_test.yml
# generate custom test by student itself 
config:
  target: https://bff.allen-stage.in
  variables:

  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web
  
  fixtures:
    - bffURL
    - testData
    - login

scenarios:
  - name: "create custom test and verify" 
    flow:
      # Step 1:create custom test and verify test
      - post:
          name: "create custom test"
          url: "{{bffURL}}/api/v1/custom-test"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            topics:
              - id: "2"
                node_type: 2
                sub_nodes:
                  - id: "86"
                    node_type: 4
                    taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
                taxonomy_id: "{{testData.syllabusConfig.group_node_details.0.group_node.taxonomy_id}}"
            filters:
              difficulty:
                - "MEDIUM"
              source:
                - "ADPL-PYQ"
                - "ADPL-HW-JA"
                - "ADPL-Papers-23-24"
              question_count:
                - 10
              duration_in_minutes:
                - 40
          capture:
            - json: $.data
              as: custom_test_response
          expect:
            - statusCode: 200
            - hasProperty: "data.action.data.query.test_id"
      - log : "custom test response : - {{custom_test_response}}"  

      # Step 2: submit the crteated test
      - post:
          url: "{{bffURL}}/api/v1/custom-test/submit?test_id={{custom_test_response.action.data.query.test_id}}"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            test_id: "{{custom_test_response.action.data.query.test_id}}"
          capture:
            - json: $.data
              as: submit_test_response
          expect:
            - statusCode: 200
      - log : "submit custom test response : - {{submit_test_response}}"


      # - get:
      #     url: /api/v1/custom-test/questions?mode=TEST&test_id={{captured_data_action_data_query_test_id}}&slug[]=questions
      #     headers:
      #       authorization: "Bearer {{studentToken}}"
      #     expect:
      #       - statusCode: 200

      # - post:
      #     url: /v1/events/app_events/publish/batch
      #     headers:
      #       authorization: "Bearer {{studentToken}}"
      #     expect:
      #       - statusCode: 200
      
