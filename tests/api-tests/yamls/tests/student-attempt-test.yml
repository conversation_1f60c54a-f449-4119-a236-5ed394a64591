# student-attempt-test.yml
# Ad<PERSON> creates an objective test and student attempts it

config:
  variables:
    link: "https://ap-south-1-staging-test-and-assessment-uploads-staging.s3.ap-south-1.amazonaws.com/LhpWj62UmX-UtLDMJ5rjy.pdf"
    paperCode: "9610WJAPRANUR24001"
    test_name: "ALLEN API Test"
    test_category: "CLASSROOM"
    test_mode: "TEST_TAKING_MODE_ONLINE"
  defaults:
    headers:
      content-type: application/json
      x-client-type: web
      x-device-id: "633c007c-5f54-477d-a0f0-c77a8fdfa22c"
  fixtures:
    - bffURL
    - testData
    - login

scenarios:
  - name: "<PERSON><PERSON> creates objective test and student attempts it"
    flow:
      - function:
          name: getDates
          capture: dates

      # Step 1: <PERSON><PERSON> creates the test
      - post:
          name: "Create objective test"
          url: "{{bffURL}}/internal-bff/api/v1/tests"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            display_name: "{{test_name}}"
            category: "{{test_category}}"
            syllabus_pdf_link: "{{testData.env.TEST_SYLLABUS_LINK}}"
            assignment:
              batches:
                - batch_code: "{{testData.batch.name}}"
                  test_taking_mode: "{{test_mode}}"
            schedule:
              start_time: "{{dates.FormattedNowDateTime}}"
              login_window_in_minutes: 30
              duration_in_minutes: 30
            student_test_display_details:
              name: "ACT"
              number: "156"
              paper_number: ""
            test_setting:
              is_reattempt_allowed: true
            type: "OBJECTIVE"
            ey_category: "OTHER"
            generate_ranks: ["TEST_LEVEL"]
            report_header_name: ""
          capture:
            - json: $.data
              as: create_test_response
          expect:
            - statusCode: 200
            - hasProperty: "data.test_id"
      - log: "Test created with ID: {{create_test_response.test_id}}"

      # Step 2: Admin publishes the test
      - put:
          name: "Publish test"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}/attachQuestionPaper"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            paper_code: "{{paperCode}}"
          expect:
            - statusCode: 200
      - log: "Test published successfully"
      # Step 3: Verify test is published
      - get:
          name: "Verify test is published"
          url: "{{bffURL}}/internal-bff/api/v1/tests/{{create_test_response.test_id}}"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          capture:
            - json: $.data.status
              as: test_status
          expect:
            - statusCode: 200
            - equals:
                - "{{test_status}}"
                - "PUBLISHED"
      - log: "Test status: {{test_status}}"

      - sleep: 10000 # 10 seconds for test to be available

      # Step 4: Student checks test schedule
      - get:
          name: "Student checks test schedule"
          url: "{{bffURL}}/v1/tests/{{create_test_response.test_id}}/testSchedule"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data
              as: test_schedule
            - json: $.data.test_status
              as: test_status
          expect:
            - statusCode: 200
            - hasProperty: "data.test_id"
      - log: "Test schedule: {{test_status}}"

      # Step 5: Student starts the test
      - post:
          name: "Student starts the test"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/start?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            test_id: "{{create_test_response.test_id}}"
            attempt: 0
          capture:
            - json: $.data
              as: test_start_response
            - json: $.data.student_test_details.remaining_duration_in_sec
              as: remaining_duration
          expect:
            - statusCode: 200
            - hasProperty: "data.student_test_details"
            - hasProperty: "data.test_config"
      - log: "Student started test with remaining duration: {{remaining_duration}} seconds"

      # Step 6: Student starts the test timer
      - put:
          name: "Student starts the test timer"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/start_test_timer?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            test_id: "{{create_test_response.test_id}}"
            attempt: 0
          capture:
            - json: $.data.student_test_details
              as: student_test_details
          expect:
            - statusCode: 200
            - hasProperty: "data.student_test_details.test_status"
            - equals:
                - "{{student_test_details.test_status}}"
                - "STS_IN_PROGRESS"
      - log: "Test timer started successfully"

      # Step 7: Student fetches test instructions
      - get:
          name: "Student fetches test instructions"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/question_paper_instructions?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data
              as: test_instructions
          expect:
            - statusCode: 200
            - hasProperty: "data.instructions"
      - log: "Student fetched test instructions successfully"

      # Step 8: Student fetches question paper
      - get:
          name: "Student fetches question paper"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/question_paper?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data.question_paper
              as: question_paper
            - json: $.data.question_paper.sections[0].questions[0].question.question_id
              as: first_question_id
            - json: $.data.question_paper.sections[0].questions[1].question.question_id
              as: second_question_id
            - json: $.data.question_paper.sections[0].questions[2].question.question_id
              as: third_question_id
          expect:
            - statusCode: 200
            - hasProperty: "data.question_paper.sections"
      - log: "Student fetched question paper with {{question_paper.sections.0.questions.length}} questions"

      # Step 9: Student answers first question
      - post:
          name: "Student answers first question"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/mark_response?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            responses:
              - question_id: "{{first_question_id}}"
                section_name: "PHYSICS(SECTION-I)"
                namespace: "1.1"
                marked_response: "[2]"
                action_type: "ANSWERED"
                content_language: "ENGLISH"
                question_sequence_no: 1
                question_version: 1715677348211
            set_id: "Set-A"
            test_id: "{{create_test_response.test_id}}"
            attempt: "0"
          capture:
            - json: $.data.student_test_details
              as: updated_test_details
          expect:
            - statusCode: 200
            - hasProperty: "data.student_test_details.remaining_duration_in_sec"
      - log: "Student answered first question, remaining time: {{updated_test_details.remaining_duration_in_sec}} seconds"

      # Step 10: Student marks second question for review
      - post:
          name: "Student marks second question for review"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/mark_response?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            responses:
              - question_id: "{{second_question_id}}"
                section_name: "PHYSICS(SECTION-I)"
                namespace: "1.1"
                marked_response: ""
                action_type: "MARKED_FOR_REVIEW"
                content_language: "ENGLISH"
                question_sequence_no: 2
                question_version: 1715677386467
            set_id: "Set-A"
            test_id: "{{create_test_response.test_id}}"
            attempt: "0"
          capture:
            - json: $.data.student_test_details
              as: updated_test_details
          expect:
            - statusCode: 200
      - log: "Student marked second question for review"

      # Step 11: Student checks test answers summary
      - get:
          name: "Student checks test answers summary"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/answers?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          capture:
            - json: $.data.student_test_details.response_counters.total
              as: response_summary
          expect:
            - statusCode: 200
            - hasProperty: "data.student_test_details.response_counters"
      - log: "Test answer summary - Answered: {{response_summary.answered}}, Marked for review: {{response_summary.marked_for_review}}, Not answered: {{response_summary.not_answered}}"

      # Step 12: Student submits the test
      - put:
          name: "Student submits the test"
          url: "{{bffURL}}/api/v1/tests/{{create_test_response.test_id}}/submit?attempt=0"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            test_id: "{{create_test_response.test_id}}"
            attempt: 0
          capture:
            - json: $.data.student_test_details.test_status
              as: final_test_status
          expect:
            - statusCode: 200
            - equals:
                - "{{final_test_status}}"
                - "STS_SUBMITTED"
      - log: "Student successfully submitted the test with status: {{final_test_status}}"