# neet-vtag-flow.yml
# Flow - NEET student asks a doubt using vtag and verifies bot response
config:
  variables:
      vtag: "BC0908"
  defaults:
    headers:
      x-device-id: 649baf75-e974-48ab-90e7-eb418a91a7b0
      x-client-type: web
  fixtures:
    - bffURL
    - neetTestData
    - neetLogin

scenarios:
  - name: "NEET student asks a doubt using vtag and verifies bot response"
    flow:
      
      # Step 1: Submit a doubt
      - post:
          name: "submit vtag doubt"
          url: "{{bffURL}}/api/v1/v6/doubts/submit"
          headers:
            authorization: "Bearer {{neetLogin.studentToken}}"
          json:
            v_tag: "{{vtag}}"
            doubt: {}
          capture:
            - json: $.data.action.data.query.did
              as: doubt_id
          expect:
            - statusCode: 200
            - hasProperty: "data.action.data.query.did"
            - hasProperty: "data.action.type"

            - log: "Captured doubt ID: {{doubt_id}}"

      - log: "<PERSON><PERSON><PERSON> submitted successfully with ID: {{doubt_id}}"
      # Step 2: Fetch conversation to verify bot's response
      - get:
          name: "verify bot response in conversation"
          url: "{{bffURL}}/api/v1/v4/doubts/{{doubt_id}}/conversation"
          headers:
            authorization: "Bearer {{neetLogin.studentToken}}"
          query:
            spotlight: "false"
          capture:
            - json: $.data.data
              as: conversation_response
          expect:
            - statusCode: 200
            - hasProperty: "data.data.conversations"
            - hasProperty: "data.data.header"

      - log: "Conversation Response: {{conversation_response}}"

