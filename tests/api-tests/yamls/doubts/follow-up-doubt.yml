# follow-up-doubt.yml
# Flow - User Follows up with a doubt
config:
  target: https://bff.allen-stage.in

  variables:
    question: "what is dc voltage"
    subjectId: "1701181887VZ_354"
    subjectName: "Physics"
    topicId: "1701181887VZ_408"
    topicName: "Heat and Thermodynamics"
    Solved_Text: ✅ Yes, got it
  defaults:
    headers:
      accept: application/json
      origin: https://console.allen-stage.in
      x-device-id: 649baf75-e974-48ab-90e7-eb418a91a7b0
      x-client-type: web
  fixtures:
    - adminURL
    - bffURL
    - testData
    - login

scenarios:
  - name: "create doubt and get solutions and close the doubt" 
    flow:
      - function: 
          name: getDates
          capture: dates
      # Step 1: create the draft doubt 
#      - post:
#          name: "draft the doubt"
#          url: "{{bffURL}}/api/v1/v3/doubts/draft"
#          headers:
##            authorization: "Bearer {{login.studentToken}}"
#          json:
#            doubt:
#              images: []
#              override: false
#              text: "{{question}}"
#          capture:
#            - json: $.data.doubt_id
#              as: draft_doubt_id
#          expect:
#            - statusCode: 200
#            - hasProperty: "data.doubt_id"

      # Step 2: submit the doubt 
      - post:
          name: "submit the doubt"
          url: "{{bffURL}}/api/v1/v6/doubts/submit"
          headers:
            authorization: "Bearer {{login.studentToken}}"
          json:
            doubt:
              images: []
              override: "false"
              text: "{{question}} {{dates.currentTime_in_secs}}"
          capture:
            - json: $.data
              as: submit_doubt_response
          expect:
            - statusCode: 200

      # # Step 3: conversations of the doubt
      # - get:
      #     name: "converstion of the doubt"
      #     url: "{{bffURL}}/v4/doubts/{{draft_doubt_id}}/conversation?spotlight=false"
      #     headers:
      #       authorization: "Bearer {{login.studentToken}}"
      #     capture:
      #       - json: $.data
      #         as: conversation_doubt_response
      #     expect:
      #       - statusCode: 200
      #       - hasProperty: "data.data.conversations[2].message_type"
      #       - hasProperty: "data.data.conversations[2].message.expand_text"
      # - log: "converstation doubt response: {{conversation_doubt_response}}"

      # # Step 4: reply to the doubt 
      # - post:
      #     name: "reply to the doubt"
      #     url: "{{bffURL}}/v4/doubts/reply"
      #     headers:
      #       authorization: "Bearer {{login.studentToken}}"
      #     json:
      #       doubt_id: "{{draft_doubt_id}}"
      #       doubt_reply:
      #         text: "{{Solved_Text}}"
      #         images: []
      #         audios: []
      #       is_chip_selected: true
      #     capture:
      #       - json: $.data.status
      #         as: doubt_status
      #       - json: $.data.reply_id
      #         as: doubt_reply_id
      #     expect:
      #       - statusCode: 200
      #       - hasProperty: "data.status"
      #       - hasProperty: "data.reply_id"

      # # Step 3: conversations of the doubt post resolved
      # - get:
      #     name: "converstion of the doubt post resolved"
      #     url: "{{bffURL}}/v4/doubts/{{draft_doubt_id}}/conversation?spotlight=false"
      #     headers:
      #       authorization: "Bearer {{login.studentToken}}"
      #     capture:
      #       - json: $.data
      #         as: conversation_doubt_response_closed
      #     expect:
      #       - statusCode: 200
      #       - hasProperty: "data.data.conversations[5].message_subtype"
      #       - hasProperty: "data.data.conversations[5].message.title"
      # - log: "converstation doubt response post closed: {{conversation_doubt_response_closed}}"

