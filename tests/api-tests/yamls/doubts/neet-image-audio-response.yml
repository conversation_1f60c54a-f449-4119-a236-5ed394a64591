# neet-image-audio-response.yml
# Flow - NEET user asks a doubt about malphigian body and verifies image in bot response
config:
  variables:
    question: "what is malphigian body?"
    confirmText: "Confirm"
    streamName: "NEET"
    subjectName: "Biology"
  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web

  fixtures:
    - bffURL
    - neetLogin
    - neetTestData

scenarios:
  - name: "NEET user asks doubt about malphigian body and verifies image & audio response"
    flow:
      - function:
          name: getFormattedDateTime
          args:
            - "HH:mm:ss"
          capture: current_time

      # Step 1: Submit the doubt with timestamp to avoid duplicate error
      - post:
          name: "submit the doubt"
          url: "{{bffURL}}/api/v1/v6/doubts/submit"
          headers:
            authorization: "Bearer {{neetLogin.studentToken}}"
          json:
            doubt:
              images: []
              override: "false"
              text: "{{question}} {{current_time}}"
              subject: "{{subjectName}}"
              stream: "{{streamName}}"
          capture:
            - json: $.data.action.data.query.did
              as: doubt_id
          expect:
            - statusCode: 200
            - hasProperty: "data.action.data.query.did"

      - log: "Captured doubt ID: {{doubt_id}}"

      # Step 2: Confirm the doubt with a reply
      - post:
          name: "confirm the doubt"
          url: "{{bffURL}}/api/v1/v4/doubts/reply"
          headers:
            authorization: "Bearer {{neetLogin.studentToken}}"
          json:
            doubt_id: "{{doubt_id}}"
            doubt_reply:
              text: "{{confirmText}}"
              images: []
              audios: []
            meta:
              submission: true
            is_chip_selected: true
          expect:
            - statusCode: 200
            - hasProperty: "data.reply_id"
          capture:
            - json: $.data.reply_id
              as: reply_id

      # Step 3: Wait for bot to process and respond 
      - sleep: 20000

      # Step 4: Fetch conversation to verify bot's response with image & audio
      - get:
          name: "fetch bot response"
          url: "{{bffURL}}/api/v1/v4/doubts/{{doubt_id}}/conversation"
          headers:
            authorization: "Bearer {{neetLogin.studentToken}}"
          query:
            spotlight: "false"
          capture:
            - json: $.data.data
              as: conversation_data
            - json: $.data.data.conversations[3].message.media_list.images
              as: bot_images
            - json: $.data.data.conversations[3].message.meta.audio
              as: bot_audio
          expect:
            - statusCode: 200
            - hasProperty: "data.data.conversations"
            - hasProperty: "data.data.conversations[3].message.media_list.images"
            - hasProperty: "data.data.conversations[3].message.meta.audio"
            - greaterThanZero: "{{bot_images.length}}"
