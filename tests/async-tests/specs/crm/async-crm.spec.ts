import { test, expect, slowExpect } from '../../../ui-tests/fixtures/ui-fixtures';
import { StringUtils, PlaywrightUtils } from '../../../commons/common-functions';
import { EnvUtils } from '../../../commons/env-utilities';

const currentEnv = EnvUtils.getInstance();
if (currentEnv.isStage()) {
    test.describe('CRM UI Tests', {
        tag: ['@crm', '@ui']
    }, () => {
        // Verify broadcast creation as admin/teacher with live/publish options, validate from student side
        /* CRM Push Notification service is not applicable for WEB currently and this will be on APP testing */
        // test('Verify notification under bell icon from student side', async ({ adminPage, studentPage, testData }) => {
        //     test.setTimeout(2400000);
        //     const title = "automation_" + StringUtils.generateRandomString(3);
        //     const type = "admin";
        //     const crmDescription = "https://www.google.com/";
        //     const broadcastCategory = "General";

        //     // Create the first broadcast using API
        //     await test.step(`Create ${type} broadcast with live option`, async () => {
        //         const tomorrowDateEpoch = StringUtils.getTomorrowDate(true);
        //         await adminPage.apis.resourceManagement.sendNotice(
        //             title,
        //             crmDescription,
        //             broadcastCategory,
        //             parseInt(tomorrowDateEpoch),
        //             process.env.CENTER_ID ? process.env.CENTER_ID : "",
        //             testData.batch.course,
        //             testData.batch.id,
        //             testData.batch.phase,
        //             process.env.CENTER_ID ? process.env.CENTER_ID : "",
        //             "STREAM_JEE_MAIN_ADVANCED",
        //             "CLASS_11",
        //             type === "admin" ? process.env.ADMIN_ID ? process.env.ADMIN_ID : "" : process.env.ADMIN_ID ? process.env.ADMIN_ID : "",
        //             type === "admin" ? "NOTICE_SENDER_ADMIN" : "NOTICE_SENDER_TEACHER"
        //         );
        //     });

        //     // 3 mins required to wait for the notification appear in stage 
        //     await studentPage.waitForTimeout(180000);

        //     // Validate admin/teacher with live/publish broadcasts from student side
        //     await test.step('Validate admin/teacher created broadcasts with live/publish options is visible from student side', async () => {
        //         await test.step('Navigate to noticeboard by login as student', async () => {
        //             await studentPage.login();
        //             await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text visibility').toBeVisible();
        //             await expect(studentPage.pos.homePage.userNameMenu, 'Check user name menu is visible').toBeVisible();
        //             await expect(studentPage.pos.homePage.notificationButton, 'Check notification button is visible').toBeVisible();
        //             await studentPage.pos.homePage.notificationButton.click();
        //             await slowExpect(studentPage.pos.homePage.notificationText, 'Check notification header is visible').toBeVisible();
        //             await slowExpect(studentPage.pos.homePage.broadcastText(title), 'Check broadcast text is visible').toBeVisible();
        //         });
        //     });

        //     // Add deletion for live broadcasts after student validation
        //     await test.step('Delete created broadcast', async () => {
        //         await adminPage.goto(adminPage.pos.broadcastPage.url);
        //         await adminPage.waitForLoadState('networkidle');
        //         await slowExpect(adminPage).toHaveURL(/.*broadcast-crm/);
        //         await adminPage.waitForLoadState('networkidle');
        //         await adminPage.pos.broadcastPage.validateBroadcastPage();
        //         await slowExpect(adminPage.pos.broadcastPage.createdBroadcastName(title), 'Verifying created broadcast title text should be visible').toBeVisible();
        //         await adminPage.pos.broadcastPage.createdBroadcastName(title).click();
        //         await slowExpect(adminPage.pos.broadcastPage.updateBroadcastTitle, 'Verifying update created broadcast title should be visible').toBeVisible();
        //         expect(adminPage.url()).toBeTruthy();
        //         const full_url = adminPage.url();
        //         const crm_id = full_url.split('/').pop();
        //         await adminPage.goBack();
        //         await adminPage.pos.broadcastPage.validateandDeleteCreatedCRM(title, crm_id);

        //     });
        // });
    });
}