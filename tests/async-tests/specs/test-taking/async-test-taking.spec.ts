import { test, expect, slowExpect } from '../../../ui-tests/fixtures/ui-fixtures';
import { StringUtils, PlaywrightUtils } from '../../../commons/common-functions';
import { EnvUtils } from '../../../commons/env-utilities';

const currentEnv = EnvUtils.getInstance();
if (currentEnv.isStage()) {
    test.describe('Test Taking UI Tests', {
        tag: ['@test-taking', '@ui']
    }, () => {
        // Async flow for bulk upload will work once work around came to this
        test('Verify bulk upload test creation/cancel as admin and validate from student side', async ({ adminPage, testData, studentPage }) => {
            test.setTimeout(180000);

            const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
            const currentDateAndTime = StringUtils.getCurrentTimeWithOffset("YYYY-MM-DD hh:mm:ss tt", 120, false, timezone);

            //data for creating the test 
            const SyllabusPublicURL = process.env.TEST_BULK_SYLLABUS ? process.env.TEST_BULK_SYLLABUS : "";;
            const batchId = testData.batch.name //Need to check if test paper code is mapped to batch or course or center
            const testDisplayFirstName = "FULL TEST";
            const randomFourDigit = StringUtils.generateRandomFourDigits();
            const objectiveTestStudentDisplayName = testDisplayFirstName + " " + randomFourDigit;
            const FourDigit = StringUtils.generateRandomFourDigits();
            const subjectiveTestStudentDisplayName = testDisplayFirstName + " " + FourDigit;
            const icObjectiveTestName = "Automation_" + StringUtils.generateRandomString(5);
            const icSubjectiveTestName = "Automation_" + StringUtils.generateRandomString(6);


            const filePath = 'test-data/Test_Creation_Bulk.xlsx';
            const sheetName = 'Sheet1';
            const cellNames = ['A2', 'A3', 'B3', 'C2', 'I2', 'I3', 'M2', 'M3', 'P2', 'P3']; // Example cell addresses
            const cellValues = [icSubjectiveTestName, icObjectiveTestName, batchId, batchId, randomFourDigit, FourDigit, currentDateAndTime, currentDateAndTime, SyllabusPublicURL, SyllabusPublicURL];
            const newColumnIndexValue = 23;
            StringUtils.updatingExcelData(filePath, sheetName, cellNames, cellValues, newColumnIndexValue);
            let adminTests;
            //navigate to test home home page
            await test.step(`Verify and create objective and subjective test using as a bulk by admin from test home page`, async () => {
                await adminPage.pos.createTestPage.goto();
                await expect(adminPage).toHaveURL(/.*student-tests/);
                await adminPage.waitForLoadState('networkidle'); // To handle network calls to load
                await adminPage.pos.createTestPage.verifyTestManagementPage();
                await adminPage.pos.createTestPage.clickAndVerifyBulkTestCreationPopup();
                await adminPage.pos.createTestPage.bulkFileInput.setInputFiles(filePath);
                await slowExpect(adminPage.pos.createTestPage.bulkUploadTestTost, "Verify bulk upload test tost is visible").toBeVisible();
                await slowExpect(adminPage.pos.createTestPage.apiLoader, "Verify loading to fetch data is not visible").not.toBeVisible();
                await adminPage.waitForTimeout(10000); //Wait required to update the test in test management since the bulk upload will create ema
                await adminPage.reload();
                await slowExpect(adminPage.pos.createTestPage.apiLoader, "Verify loading to fetch data is not visible").not.toBeVisible();
                adminTests = await adminPage.apis.resourceManagement.filterAdminTests(icObjectiveTestName, icSubjectiveTestName);
            });

            /* login for the student page*/
            await test.step(`Verify student able to login and see the created tests`, async () => {
                await studentPage.login();
                await studentPage.waitForLoadState('networkidle');
                await studentPage.waitForLoadState('domcontentloaded');
                await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'Verifying "explore Study Materials" text should be visible').toBeVisible();
                await slowExpect(studentPage.pos.homePage.createdTestId(objectiveTestStudentDisplayName), "Created objective test id is visible in student home page").toBeVisible();
                await slowExpect(studentPage.pos.homePage.createdTestId(subjectiveTestStudentDisplayName), "Created subjective test id is visible in student home page").toBeVisible();
            });

            /* The below commented script will delete in further observation runs */
            await test.step(`Verify and cancel the created tests in test listing page`, async () => {
                await slowExpect(adminPage.pos.createTestPage.createdTestId(adminTests.testValue1!), "Created test id 1 is visible in listing page").toBeVisible();
                await slowExpect(adminPage.pos.createTestPage.createdTestId(adminTests.testValue2!), "Created test id 2 is visible in listing page").toBeVisible();
                await expect(adminPage.pos.createTestPage.testActionButton(adminTests.testValue1!)).toBeVisible();
                await adminPage.pos.createTestPage.testActionButton(adminTests.testValue1!).click();
                await expect(adminPage.pos.createTestPage.cancelTestText, "Verify cancel test is visible").toBeVisible();
                await adminPage.pos.createTestPage.cancelTestText.click();
                await expect(adminPage.pos.createTestPage.confirmText, "Verify confirm test is visible").toBeVisible();
                await adminPage.pos.createTestPage.confirmText.click();
                await slowExpect(adminPage.pos.createTestPage.apiLoader, "Verify loading to fetch data is not visible").not.toBeVisible();
                await adminPage.waitForTimeout(2000); // wait required to handle the dom load
                await expect(adminPage.pos.createTestPage.testActionButton(adminTests.testValue2!)).toBeVisible();
                await adminPage.pos.createTestPage.testActionButton(adminTests.testValue2!).click();
                await expect(adminPage.pos.createTestPage.cancelTestButton, "Verify cancel test is visible").toBeVisible();
                await adminPage.pos.createTestPage.cancelTestButton.click();
                await expect(adminPage.pos.createTestPage.confirmText, "Verify confirm test is visible").toBeVisible();
                await adminPage.pos.createTestPage.confirmText.click();
                await adminPage.waitForLoadState('networkidle');
                await adminPage.waitForLoadState('domcontentloaded');
            });



        });

        test('Verify provisonal and final result for the submitted test by the student', async ({ adminPage, testData, studentPage }) => {
            test.setTimeout(4900000);

            const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage);
            const currentDateAndTime = StringUtils.getCurrentTimeWithOffset("YYYY-MM-DD hh:mm:ss tt", 40, true, timezone);

            //data for creating the test 
            const testName = "ALLEN DIGITAL";
            const SyllabusPublicURL = process.env.TEST_SYLLABUS_LINK ? process.env.TEST_SYLLABUS_LINK : "";;
            const batchId = testData.batch.name
            const testDuration = "1"
            const windowLoginDuration = "2"
            const paperCode = "9610WJAPRANUR24001"
            const testDisplayFirstName = "FULL TEST";
            const randomFourDigit = StringUtils.generateRandomFourDigits();
            const testDisplayNamePlusNumber = testDisplayFirstName + " " + randomFourDigit;
            const qustionNumber1 = "1";
            const qustionNumber2 = "2";
            const qustionNumber3 = "3";
            const qustionNumber4 = "4";
            const qustionNumber5 = "5";
            //     const uploadSyllabusInputFilePath = "test-data/stage/test-taking-syllabus.pdf";
            const studentMobileNumber = "9513199542";


            //navigate to internal user home page
            await test.step(`Navigate from teacher home page to internal user home page`, async () => {
                await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
                // await adminPage.pos.teacherHomePage.navigateToInternalUser();
            });

            //create the single test
            await test.step(`Create the single test`, async () => {
                // await adminPage.pos.createTestPage.createSingleTest();
                await adminPage.goto(adminPage.pos.createTestPage.url + '/create?type=e2e-automation'); //this end point for automation purpose
                await expect(adminPage).toHaveURL(/.*e2e-automation/);
                await adminPage.waitForLoadState('networkidle');
            });

            //enter the vaild data for creation of test
            await test.step(`Enter the basic details for creation of test`, async () => {
                await adminPage.pos.basicDetailsPage.enterBasicDetailsForTestCreation(testName, testDisplayFirstName, randomFourDigit, SyllabusPublicURL, batchId, currentDateAndTime, testDuration, windowLoginDuration, "objective", "onlineBatch");
                await expect(adminPage.pos.basicDetailsPage.createAndNextButton, "Verify create and next button is visible").toBeVisible();
                await adminPage.pos.basicDetailsPage.createAndNextButton.click();
            });

            //import the paper code and published the test
            await test.step(`Enter the paper code and publish the test`, async () => {
                await adminPage.pos.paperReviewPage.enterPaperCodeAndPublishTestWithReviewQuestionPaper(paperCode);
            });

            /** 
            * @info Student side login and submit the test
           */

            //login for the student page
            await test.step(`Verify login to the student side`, async () => {
                await studentPage.waitForTimeout(15000);
                await studentPage.login();
                // await studentPage.pos.preHomePage.loginWithMobileNumber(studentMobileNumber, "1111");
            });

            //Validate test card widgets and start the test
            await test.step(`Validate test card widgets and start the test`, async () => {
                await studentPage.pos.homePage.validateUpcomingTestCard(testDisplayNamePlusNumber);
                await studentPage.waitForLoadState('networkidle');
                await studentPage.goBack();
                await studentPage.waitForTimeout(20000); //hard wait is required according to business logic for the test taking module
                await studentPage.reload(); // reload of the page is requried because to get the start button to start test
                await studentPage.pos.homePage.startTest(testDisplayNamePlusNumber);
            });

            // verify user is accept the instructions that terms and conditions
            await test.step(`Verify user accept of terms and conditions in the instructions and click on proceed test`, async () => {
                await studentPage.pos.instructionPage.verifyTheInstructionPageAndProceedTest();
            });

            // verify after test started student can see the countdown of the timer
            await test.step(`Verify test taking countdown timer`, async () => {
                await studentPage.pos.examPage.validatingTestTimerCountDown();
            });

            // verify before and after marking the question student can see it is answered and not visited
            await test.step(`Verify student can see the question is answered once it marked`, async () => {
                await studentPage.pos.examPage.rightArrowButton.click();
                await studentPage.pos.examPage.validatingQuestionIsAnswered(qustionNumber1);
            });

            // verify the question is not answered by clicking save and next
            await test.step(`Verify student can see the question is not answered by clicking save and next`, async () => {
                await studentPage.pos.examPage.validatingQuestionIsNotAnswered(qustionNumber2);
            });

            // verify the question is answered but marked for review
            await test.step(`Verify student can see the question is answered but marked for review`, async () => {
                await studentPage.pos.examPage.validatingQuestionIsAnsweredAndMarkedForReview(qustionNumber3);
            });

            // verify the question is not answered but marked for review
            await test.step(`Verify student can see the question is not answered but marked for review`, async () => {
                await studentPage.pos.examPage.validatingQuestionIsNotAnsweredAndMarkedForReview(qustionNumber4);
            });

            // verify the user is able to take the test
            await test.step(`Verify clear response and back functionality`, async () => {
                await studentPage.pos.examPage.clearResponseAndAgainAnswerTheQuestion(qustionNumber5);
                await studentPage.pos.examPage.verifyTheBackButtonFunctionality(qustionNumber4);
            });
            // verify user is able to submit the test
            await test.step(`Verify user is able to submit the test`, async () => {
                await expect(studentPage.pos.examPage.submitButton, "verify submit button is visible").toBeVisible();
                await studentPage.waitForTimeout(2000); //wait is required to load the answers
                await studentPage.pos.examPage.submitButton.click();
                await studentPage.waitForTimeout(2000); //wait is required to load the answers
            });
            // verify user is successfully submitted the test with toast message
            await test.step(`Verify user is successfully submitted the test with toast message`, async () => {
                await studentPage.pos.examSummaryPage.verifySuccessToasMessageAfterTestSubmission();
            });

            /** 
            * @info Validate view provisional results flow after 45 mins post test submission
            */

            // 45 minutes required to wait for provisional result generation (this wait time may extend in some cases, its not constant)
            await studentPage.waitForTimeout(2700000);

            // Verify and validate user is able to navigate to your past tests section
            await test.step(`Verify and validate user is able to navigate to your past tests section`, async () => {
                // await studentPage.pos.preHomePage.loginWithMobileNumber(studentMobileNumber, "1111");
                await studentPage.goto("/");
                await studentPage.waitForLoadState('networkidle');
                await slowExpect(studentPage.pos.homePage.testsMenu, 'verifying "test menu" sidebar option is visible').toBeVisible();
                await studentPage.pos.homePage.testsMenu.click();
                await studentPage.pos.testTabPage.navigateToPastTestsResults();
            });
            // Verify and validate provisional results section
            await test.step(`Verify and validate provisional results section`, async () => {
                await studentPage.pos.previousTestsResultPage.validateProvisonalResultForTest(testDisplayNamePlusNumber);
            });

            /** 
            * @info Validate view final results flow after 1 hour post provisional result generation
            */

            // // 1 hour required to wait for final result generation (this wait time may extend in some cases, its not constant)
            // await studentPage.waitForTimeout(3600000);

            // // Verify and validate final results section
            // await test.step(`Verify and validate final results section`, async () => {
            //     await expect(studentPage.pos.previousTestsResultPage.backButtonToTestResults, "Verify back button is visible").toBeVisible();
            //     await studentPage.pos.previousTestsResultPage.backButtonToTestResults.click();
            //     await studentPage.waitForLoadState("networkidle");
            //     await expect(studentPage).toHaveURL(/.*previous-tests/);
            //     await studentPage.pos.previousTestsResultPage.validateFinalResultForTest(testDisplayNamePlusNumber);
            // });

        });

    });
}