import fs from 'fs'
import path from 'path'

// Severity levels
export type LogSeverity = 'debug' | 'info' | 'warn' | 'error'

// Logger configuration interface
export interface LoggerConfig {
  logToFile: boolean
  logLevel: LogSeverity
  logFilePath: string
  logToConsole: boolean
}

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs')
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true })
}

// Default configuration
const defaultConfig: LoggerConfig = {
  logToFile: process.env.LOG_TO_FILE === '1' || false,
  logLevel: (process.env.LOG_LEVEL as LogSeverity) || 'info',
  logFilePath: path.join(logsDir, `playwright-${new Date().toISOString().split('T')[0]}.log`),
  logToConsole: process.env.LOG_TO_CONSOLE !== '0',
}

// Severity level mapping (for filtering)
const severityMap: Record<LogSeverity, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
}

export class Logger {
  private config: LoggerConfig

  constructor(customConfig?: Partial<LoggerConfig>) {
    this.config = { ...defaultConfig, ...customConfig }

    // Create directory for log file if it doesn't exist
    const logFileDir = path.dirname(this.config.logFilePath)
    if (!fs.existsSync(logFileDir)) {
      fs.mkdirSync(logFileDir, { recursive: true })
    }

    // Log initialization
    this.info('Logger', 'Logger initialized with config', this.config)
  }

  /**
   * Check if a log with the given severity should be processed
   */
  private isEnabled(severity: LogSeverity): boolean {
    return severityMap[severity] >= severityMap[this.config.logLevel]
  }

  /**
   * Format a log message
   */
  private formatMessage(module: string, severity: LogSeverity, message: string, ...args: any[]): string {
    const timestamp = new Date().toISOString()
    let formattedArgs = ''

    if (args.length) {
      formattedArgs = args.map((arg) => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg))).join(' ')
    }

    return `${timestamp} [${severity.toUpperCase()}] ${module} - ${message} ${formattedArgs}`.trim()
  }

  /**
   * Write a log message to file
   */
  private writeToFile(formattedMessage: string): void {
    if (this.config.logToFile) {
      try {
        fs.appendFileSync(this.config.logFilePath, formattedMessage + '\n')
      } catch (error) {
        console.error(`Failed to write to log file: ${error}`)
      }
    }
  }

  /**
   * Log a message with the specified severity
   */
  log(module: string, severity: LogSeverity, message: string, ...args: any[]): void {
    if (!this.isEnabled(severity)) {
      return
    }

    const formattedMessage = this.formatMessage(module, severity, message, ...args)

    // Log to console based on severity
    if (this.config.logToConsole) {
      switch (severity) {
        case 'debug':
        case 'info':
          console.log(formattedMessage)
          break
        case 'warn':
          console.warn(formattedMessage)
          break
        case 'error':
          console.error(formattedMessage)
          break
      }
    }

    // Also write to log file if enabled
    this.writeToFile(formattedMessage)
  }

  /**
   * Log a debug message
   */
  debug(module: string, message: string, ...args: any[]): void {
    this.log(module, 'debug', message, ...args)
  }

  /**
   * Log an info message
   */
  info(module: string, message: string, ...args: any[]): void {
    this.log(module, 'info', message, ...args)
  }

  /**
   * Log a warning message
   */
  warn(module: string, message: string, ...args: any[]): void {
    this.log(module, 'warn', message, ...args)
  }

  /**
   * Log an error message
   */
  error(module: string, message: string, ...args: any[]): void {
    this.log(module, 'error', message, ...args)
  }

  /**
   * Create a child logger with a fixed module name
   */
  getChildLogger(module: string) {
    return {
      debug: (message: string, ...args: any[]) => this.debug(module, message, ...args),
      info: (message: string, ...args: any[]) => this.info(module, message, ...args),
      warn: (message: string, ...args: any[]) => this.warn(module, message, ...args),
      error: (message: string, ...args: any[]) => this.error(module, message, ...args),
    }
  }
}

// Export a singleton instance with default config
export const logger = new Logger();

// Factory function to create new loggers with custom config
export const createLogger = (customConfig?: Partial<LoggerConfig>) => new Logger(customConfig)

export default logger
