{"name": "allen-web-automation", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"lint": "eslint --ignore-path .gitignore --ext .js,.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.44.0", "@reportportal/agent-js-playwright": "^5.1.8", "@types/node": "^20.11.22", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-playwright": "^1.5.1", "prettier": "^3.2.5", "@allen-career-institute/torch-playwright-reporter": "^1.2.28", "node-fetch": "2.6.9", "strip-ansi": "^7.1.0"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.787.0", "@aws-sdk/client-sts": "^3.796.0", "jsonpath": "^1.1.1", "mysql2": "^3.11.3", "xlsx": "^0.18.5"}}