import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';
import { Page } from 'playwright';

const xlsx = require('xlsx');

// From file-utils.ts
interface CSVRow {
  [key: string]: string;
}

function readCSV(filePath: string): Promise<CSVRow[]> {
  return new Promise((resolve, reject) => {
    const results: CSVRow[] = [];
    let headers: string[] = [];

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity
    });

    rl.on('line', (line) => {
      if (headers.length === 0) {
        headers = line.split(',').map(header => header.trim());
      } else {
        const values = line.split(',').map(value => value.trim());
        const row: CSVRow = {};
        headers.forEach((header, index) => {
          row[header] = values[index];
        });
        results.push(row);
      }
    });

    rl.on('close', () => {
      resolve(results);
    });

    rl.on('error', (error) => {
      reject(error);
    });
  });
}

export async function updateExcelFile(inputPath, newBatch, newEmployeeId, stream = "JEE") {
  // Read the file
  const workbook = xlsx.readFile(inputPath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  // Convert sheet to JSON for easy manipulation
  const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

  // Find and update batch and employeeId columns
  const headers = data[0];
  const batchIndex = headers.indexOf("BatchCode");
  const employeeIdIndex = headers.indexOf("EmployeeId1");

  if (batchIndex === -1 || employeeIdIndex === -1) {
    console.log("batch or employeeId column not found.");
    return;
  }

  // Loop through each row starting from the second row to update the values
  for (let i = 1; i < data.length; i++) {
    data[i][batchIndex] = newBatch;
    data[i][employeeIdIndex] = newEmployeeId;
  }

  // Convert JSON back to worksheet
  const newWorksheet = xlsx.utils.aoa_to_sheet(data);
  workbook.Sheets[sheetName] = newWorksheet;

  // Convert the workbook to a buffer
  const excelBuffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  if (stream == "JEE") {
    await fs.promises.writeFile(`${__dirname}/../../test-data/doubt_template.xlsx`, excelBuffer);
  }
  else {
    await fs.promises.writeFile(`${__dirname}/../../test-data/doubt_template_neet.xlsx`, excelBuffer);
  }
  return excelBuffer;
}

export async function readCSVFile(filePath: string) {
  let data: CSVRow[] = [];
  try {
    data = await readCSV(filePath);
  } catch (error) {
    console.error('Error reading CSV file:', error);
  }
  return data;
}

// From playwright-utils.ts
export class PlaywrightUtils {
  static async getBrowserTimezone(page: Page): Promise<string> {
    return await page.evaluate(() => Intl.DateTimeFormat().resolvedOptions().timeZone);
  }

  static async vercelLogin(page, vercelpwd) {
    await page.getByLabel('VISITOR PASSWORD').click();
    await page.getByLabel('VISITOR PASSWORD').fill(vercelpwd);
    await page.getByRole('button', { name: 'Log in', exact: true }).click();
  }

  static async listenWebsockets(page: Page, name: string, sockets: string[] = ['wss://sig.allen-stage.in', 'wss://chat.allen-stage.in', 'wss://sig.allen-live.in', 'wss://chat.allen-live.in']) {

    const getTimestamp = () => new Date().toLocaleTimeString('en-US', { hour12: false });
    page.on('websocket', ws => {
      if (!sockets.some(socket => ws.url().startsWith(socket))) {
        return;
      }
      const baseSocket = sockets.find(socket => ws.url().startsWith(socket));

      console.log(`${getTimestamp()} [${baseSocket}]: ${name} ws opened:`, ws.url());

      ws.on('framesent', event => {
        console.log(`${getTimestamp()} [${baseSocket}]: ${name} sent:`, event.payload);
      });

      ws.on('framereceived', event => {
        console.log(`${getTimestamp()} [${baseSocket}]: ${name} received:`, event.payload);
      });

      ws.on('close', () => {
        console.log(`${getTimestamp()} [${baseSocket}]: ${name} ws closed:`, ws.url());
      });
    });
  }
}
// From string-utils.ts
export class StringUtils {
  static getRandomChars(len: number) {
    const randomLetter = () => String.fromCharCode(0 | Math.random() * 26 + 65);
    return Array(len).fill(undefined).map(randomLetter).join('');
  }

  static generateRandomMobileNumber() {
    const randomNumber1 = '6' + Math.floor(100000000 + Math.random() * 900000000);
    return randomNumber1.toString().substring(0, 10);
  }

  static generateXLetterCode(length) {
    return Array.from({ length: length }, () =>
      String.fromCharCode(65 + Math.floor(Math.random() * 26)),
    ).join('')
  }

  static generateRandomNumString(length, characters, minRange, maxRange) {
    let result = '';
    // Generate a random number within the specified range
    const randomNum = Math.floor(Math.random() * (maxRange - minRange + 1)) + minRange;
    // Convert to string and pad with leading zeros
    result = randomNum.toString().padStart(length, '0');
    return result;
  }

  static generateRandomString(length: number) {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let randomString = '';
    for (let i = 0; i < length; i++) {
      const index = Math.floor(Math.random() * characters.length);
      const randomChar = characters.charAt(index);
      randomString += randomChar;
    }
    return randomString;
  }

  static generateRandomFourDigits() {
    const randomNumber1 = '1' + Math.floor(1000 + Math.random() * 9000);
    return randomNumber1.toString().substring(0, 4);
  }

  static getCurrentTimeWithOffset(
    format: string,
    offsetSeconds: number = 0,
    is12HourFormat: boolean = false,
    timeZone: string = 'Asia/Kolkata'
  ): string {
    const now = new Date();
    const offsetTime = new Date(now.getTime() + offsetSeconds * 1000);
    const options: Intl.DateTimeFormatOptions = {
      timeZone: timeZone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: is12HourFormat,
    };

    const formatter = new Intl.DateTimeFormat([], options);
    const parts = formatter.formatToParts(offsetTime);
    const dateParts: { [key: string]: string } = {};

    parts.forEach(({ type, value }) => {
      dateParts[type] = value;
    });

    if (dateParts.hour === '24') {
      dateParts.hour = '00';
    }

    let period = '';
    if (is12HourFormat) {
      period = dateParts.dayPeriod || '';
    }

    return format
      .replace('YYYY', dateParts.year)
      .replace('MM', dateParts.month)
      .replace('DD', dateParts.day)
      .replace('hh', dateParts.hour)
      .replace('mm', dateParts.minute)
      .replace('ss', dateParts.second)
      .replace('tt', period);
  }

  static escapeRegExp(string: string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  static getTomorrowDate(epoch: boolean = false): string {
    const tomorrow = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
    tomorrow.setDate(tomorrow.getDate() + 1);

    const day = tomorrow.getDate().toString().padStart(2, '0');
    const month = (tomorrow.getMonth() + 1).toString().padStart(2, '0');
    const year = tomorrow.getFullYear();

    if (epoch) {
      return Math.floor(tomorrow.getTime() / 1000).toString(); // Return epoch time in seconds if requested
    }

    return `${day}-${month}-${year} : 12:00 AM`;
  }
  static updatingExcelData(file_path: string, sheet_Name: string, cellNames: string[], cellValues: any[], newColumnIndexValue) {
    console.log("This method is from dataUpdate");

    // Read the Excel file
    const workbook = xlsx.readFile(file_path);
    const worksheet = workbook.Sheets[sheet_Name];

    if (!worksheet) {
      console.log(`Error: Sheet "${sheet_Name}" not found in the workbook.`);
      return;
    }

    // Determine the last used column
    const lastColumn = Object.keys(worksheet).reduce((max, cell) => {
      const col = cell.charCodeAt(0) - 'A'.charCodeAt(0);
      return col > max ? col : max;
    }, -1);

    // Create one new column after specified index
    const newColumnIndex = newColumnIndexValue;
    const newColumnAddress = String.fromCharCode('A'.charCodeAt(0) + newColumnIndex); // Column added at mentioned index

    // Set the header for the new column T to null
    worksheet[newColumnAddress + '1'] = { v: null }; // Set header of column T to null

    // Update each specified cell
    if (Array.isArray(cellNames) && Array.isArray(cellValues) && cellNames.length === cellValues.length) {
      for (let i = 0; i < cellNames.length; i++) {
        const cellAddress = cellNames[i];
        const cellValue = cellValues[i];

        // Check if the cell address is within the range A1:Z20
        const col = cellAddress.charCodeAt(0) - 'A'.charCodeAt(0);
        const row = parseInt(cellAddress.slice(1)) - 1; // Convert to zero-based index

        if (col >= 0 && col < 26 && row >= 0 && row < 20) { // A-Z and 1-20
          worksheet[cellAddress].v = cellValue !== undefined && cellValue !== null ? cellValue : null; // Update or set to null
        } else {
          console.log(`Warning: Cell ${cellAddress} is out of the allowed range (A1:Z20).`);
        }
      }
    } else {
      console.log("Error: cellNames and cellValues must be arrays of the same length.");
      return;
    }

    // Write back to the file without altering the structure
    xlsx.writeFile(workbook, file_path);

    // // How use this method
    // const filePath = "path/location";
    // const sheetName = 'SheetName';
    // const cell_Names = ['A2', 'A3']; // Example cell addresses
    // const cell_Values = ['A2Value','A3Value'];
    // StringUtils.updatingExcelData(filePath, sheetName, cell_Names, cell_Values);
  }

}

export function getStartAndEndTimestamps(): { startTimestamp: number; endTimestamp: number } {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setMinutes(yesterday.getMinutes() + 180);
  const startTimestamp = yesterday.getTime();
  const startDate = new Date(startTimestamp);
  yesterday.setMinutes(startDate.getMinutes() + 300);
  const endTimestamp = yesterday.getTime();

  return { startTimestamp, endTimestamp };
}

export function getDateStringAfterDays(days: number): string {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0]; // Returns 'YYYY-MM-DD'
}

export function getDateStringAfterMonths(months: number): string {
  const date = new Date();
  date.setMonth(date.getMonth() + months);
  return date.toISOString().split('T')[0]; // Returns 'YYYY-MM-DD'
}

export function getDateStringAfterYears(years: number): string {
  const date = new Date();
  date.setFullYear(date.getFullYear() + years);
  return date.toISOString().split('T')[0]; // Returns 'YYYY-MM-DD'
}



