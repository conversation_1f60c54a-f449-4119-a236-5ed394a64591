import { expect, type Locator, type Page } from '@playwright/test';
import { WebPage } from './web-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { EnvUtils } from '../../../commons/env-utilities';



const homeworkPageUrl = '/homework-listing';
const currentEnv = EnvUtils.getInstance();

export class HomeworkPage extends WebPage {
    readonly exploreHomework: Locator;
    readonly todayHeading: Locator;
    readonly pendingHeading: Locator;
    readonly completedHeading: Locator;
    readonly atomaticStructureHomework: Locator;
    readonly instructionsTitle: Locator;
    readonly readInstructions: Locator;
    readonly generalInstructionsTitle: Locator;
    readonly englishLanguage: Locator;
    readonly iHaveReadInstructionsText: Locator;
    readonly proceedTestButton: Locator;
    readonly questionPaperSubtitle: Locator;
    readonly studentName: Locator;
    readonly questionPaperType: Locator;
    readonly question1: Locator;
    readonly question2: Locator;
    readonly question3: Locator;
    readonly markForReviewButton: Locator;
    readonly clearResponseButton: Locator;
    readonly saveAndNextButton: Locator;
    readonly testSideMenuDrawer: Locator;
    readonly legendText: Locator;
    readonly questionPalette: Locator;
    readonly answeredTag: Locator;
    readonly sideMenuSubmitTestButton: Locator;
    readonly option1RadioButton: Locator;
    readonly option2RadioButton: Locator;
    readonly question1BrickAnsweredMarkedForReview: Locator;
    readonly question1BrickAnswered: Locator;
    readonly questionBackButton: Locator;
    readonly question2BrickMarkedForReview: Locator;
    readonly question2BrickNotAnswered: Locator;
    readonly question3BrickMarkedForReview: Locator;
    readonly hwprodOption1: Locator;
    readonly integerQuestionType: Locator;
    readonly question1BrickMarkedForReview: Locator;
    readonly question2BrickAnswered: Locator;
    readonly basicMoleConceptHomeWork: Locator;
    readonly okayGotItButton: Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, homeworkPageUrl, isMobile);
        this.exploreHomework = page.getByText('ExploreHomework');
        this.todayHeading = page.getByText('Today', { exact: true });
        this.completedHeading = page.getByText('Completed', { exact: true });
        this.pendingHeading = page.getByText('Pending', { exact: true }).first();
        this.atomaticStructureHomework = page.getByText('3 Exclusive qs.MiscAtomic').first();
        this.instructionsTitle = page.getByText('Instructions:', { exact: true });
        this.readInstructions = page.getByText('Please read the instructions');
        this.generalInstructionsTitle = page.getByText('General Instructions:');
        this.englishLanguage = page.getByText('ENGLISH').locator('visible=true');
        this.iHaveReadInstructionsText = page.getByText('I have read and understood').locator('visible=true');
        this.proceedTestButton = page.getByTestId('proceed-test-web');
        this.questionPaperSubtitle = page.getByText('Question Paper');
        this.studentName = page.getByTestId('student-name');
        this.questionPaperType = page.getByText("Question Type :").first();
        this.question1 = page.getByTestId('question_number').getByText('Question No 1');
        this.question2 = page.getByTestId('question_number').getByText('Question No 2');
        this.question3 = page.getByTestId('question_number').getByText('Question No 3');
        this.markForReviewButton = page.getByTestId('Mark for Review & Next');
        this.clearResponseButton = page.getByTestId('Clear Response');
        this.saveAndNextButton = page.getByTestId('Save & Next');
        // this.submitHomeworkButton = page.getByRole('button', { name: 'Submit Homework' });
        this.testSideMenuDrawer = page.locator("//*[@data-testid='test-drawer']").locator('visible=true');
        this.legendText = page.getByText('Legend');
        this.answeredTag = page.getByText('Answered', { exact: true });
        this.questionPalette = page.getByText('Question Palette');
        this.sideMenuSubmitTestButton = page.getByTestId('submit-test');
        this.option1RadioButton = page.getByTestId('option_1');
        this.option2RadioButton = page.getByTestId('option_2');
        this.question1BrickAnsweredMarkedForReview = page.locator("//*[@data-testid='bricks_1']//*[@alt='answered and marked for review']").locator('visible=true').or(page.locator("//*[@data-testid='bricks_1']//*[@alt='answered_and_marked_for_review']").locator('visible=true'));
        this.questionBackButton = page.getByTestId('Back');
        this.question1BrickAnswered = page.locator("//*[@data-testid='bricks_1']//*[@alt='answered']").locator('visible=true');
        this.question2BrickMarkedForReview = page.locator("//*[@data-testid='bricks_2']//*[@alt='marked for review']").locator('visible=true').or(page.locator("//*[@data-testid='bricks_2']//*[@alt='marked_for_review']").locator('visible=true'));
        this.question2BrickNotAnswered = page.locator("//*[@data-testid='bricks_2']//*[@alt='not answered']").locator('visible=true').or(page.locator("//*[@data-testid='bricks_2']//*[@alt='not_answered']").locator('visible=true'));
        this.question3BrickMarkedForReview = page.locator("//*[@data-testid='bricks_3']//*[@alt='marked for review']").locator('visible=true').or(page.locator("//*[@data-testid='bricks_3']//*[@alt='not_visited']").locator('visible=true'));
        this.hwprodOption1 = page.getByTestId('submit-response-view').locator('span').nth(1);
        this.integerQuestionType = page.getByText('Question Type : Integer').first();
        this.question1BrickMarkedForReview = page.locator("//*[@data-testid='bricks_1']//*[@alt='marked for review']").locator('visible=true');
        this.question2BrickAnswered = page.locator("//*[@data-testid='bricks_2']//*[@alt='answered']").locator('visible=true');
        this.basicMoleConceptHomeWork = page.getByText('MiscBasic Mole Concept3 Qs•').first();
        this.okayGotItButton = page.getByRole('button', { name: 'Okay, Got it' });

    }

    async validateHomeworkPage() {
        await slowExpect(this.page, "Verify after clicking on homework navigated to homework url").toHaveURL(/.*homework-listing/);
        await this.page.waitForTimeout(10000);

        if (await this.okayGotItButton.isVisible({ timeout: 5000 })) {
            await this.okayGotItButton.click();
        }

        await slowExpect(this.exploreHomework, "Verify explore homework is visible").toBeVisible();
        await expect(this.todayHeading, "Verify today heading is visible").toBeVisible();
        await expect(this.pendingHeading, "Verify pending heading is visible").toBeVisible();
        await expect(this.completedHeading, "Verify completed heading is visible").toBeVisible();
    }

    async startHomeworkWithInstructionsPage() {
        if (currentEnv.isProd()) {
            await expect(this.atomaticStructureHomework, "Verify atomatic Structure Homework is visible").toBeVisible();
            await this.atomaticStructureHomework.click();
        } else {
            await expect(this.basicMoleConceptHomeWork, "Verify basic mole concept Homework is visible").toBeVisible();
            await this.basicMoleConceptHomeWork.click();
        }
        await slowExpect(this.page, "Verify after clicking on start homework page url").toHaveURL(/.*homework*/);
        await expect(this.instructionsTitle, "Verify instructions title is visible").toBeVisible();
        await expect(this.readInstructions, "Verify reading instruction is visible").toBeVisible();
        await expect(this.generalInstructionsTitle, "Verify general instructions is visible").toBeVisible();
        await expect(this.englishLanguage, "Verify language english is visible").toBeVisible();
        await expect(this.iHaveReadInstructionsText, "Verify i have read the instructions text is visible").toBeVisible();
        await this.iHaveReadInstructionsText.click();
        await expect(this.proceedTestButton, "Verify proceed test button is visible").toBeVisible();
        await this.proceedTestButton.click();
    }

    async VerifyQuestionsPage() {
        await slowExpect(this.questionPaperSubtitle, "Verify question Paper Subtitle is visible").toBeVisible();
        await expect(this.studentName, "Verify student name is visible").toBeVisible();
        await expect(this.questionPaperType, "Verify question paper type is visible").toBeVisible();
        await expect(this.question1, "Verify question 1 is visible").toBeVisible();
        await expect(this.markForReviewButton, "Verify mark For Review Button is visible").toBeVisible();
        await expect(this.clearResponseButton, "Verify clear Response Button is visible").toBeVisible();
        await expect(this.saveAndNextButton, "Verify save And Next Button is visible").toBeVisible();
        await expect(this.testSideMenuDrawer, "Verify test side menu drawer is visible").toBeVisible();
        await expect(this.testSideMenuDrawer, "Verify test side menu drawer is visible").not.toBeDisabled();
        // await this.page.waitForTimeout(1000);
        await this.testSideMenuDrawer.click();
        await this.page.waitForLoadState('networkidle'); // wait to load the network calls to complete
        await this.page.waitForLoadState('domcontentloaded');
        // await expect(this.submitHomeworkButton, "Verify submit Homework Button is visible").toBeVisible();
        await expect(this.legendText, "Verify legend text is visible").toBeVisible();
        await expect(this.answeredTag, "Verify answered tag is visible").toBeVisible();
        await expect(this.questionPalette, "Verify question palette is visible").toBeVisible();
        await expect(this.sideMenuSubmitTestButton, "Verify sideMenu Submit Test Button is visible").toBeVisible();
    }

}