import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { slowExpect } from '../../fixtures/ui-fixtures';

const CourseListingManagementPage = '/listing-management/course';

export class CourseListingManagement extends ICPage {
    readonly filterDropDown: Locator;
    readonly applyFilterButton: Locator;
    readonly successMessage: Locator;
    readonly platformDropDown: Locator;
    readonly nameInput: Locator;
    readonly descriptionInput: Locator;
    readonly priceInput: Locator;
    readonly durationInput: Locator;
    readonly startDateInput: Locator;
    readonly endDateInput: Locator;
    readonly createButton: Locator;
    readonly submitButton: Locator;
    readonly apiLoader: Locator;
    readonly listingSubmittedMessage: Locator;
    readonly listingApprovedMessage: Locator;
    readonly searchButton: Locator;
    readonly academicSessionDropdown: Locator;
    readonly classDropdown: Locator;
    readonly streamDropdown: Locator;
    readonly courseDropdown: Locator;
    readonly Dropdownsearch: Locator;
    readonly phaseDropdown: Locator;
    readonly centerDropdown: Locator;
    readonly durationTimeInput: Locator;
    readonly timeUnitDropdown: Locator;
    readonly tcInput: Locator;
    readonly mrpInput: Locator;
    readonly mopInput: Locator;
    readonly otherDetailsDropdown: Locator;
    readonly saveAsDraftButton: Locator;
    readonly sendForApprovalButton: Locator;
    readonly listingRow: (listingName: string) => Locator;
    // readonly listingRowImage: Locator;
    readonly hideOnWebsiteDropdown: Locator;
    readonly otherDetailsOption: (details: string) => Locator;
   readonly searchtext: (searchtext: string) => Locator;
    constructor(page: Page, isMobile: boolean) {
        super(page, CourseListingManagementPage, isMobile);
        
        this.filterDropDown = page.getByRole('combobox', { name: 'Filter' });
        this.applyFilterButton = page.getByRole('button', { name: 'Apply Filter' });
        this.successMessage = page.getByText('Course listing created successfully');
        this.platformDropDown = page.locator('div').filter({ hasText: /^Select Platform$/ }).nth(1);
        this.nameInput = page.getByPlaceholder('Name');
        this.descriptionInput = page.getByLabel('Description');
        this.priceInput = page.getByLabel('Price');
        this.durationInput = page.getByLabel('Duration');
        this.startDateInput = page.locator("//*[@col-id='startDate']//input[@type='date']");
        this.endDateInput = page.locator("//*[@col-id='endDate']//input[@type='date']");
        this.createButton = page.getByRole('button', { name: 'Create' });
        this.submitButton = page.getByRole('button', { name: 'Submit' });
        this.apiLoader = page.getByTestId('api-loader');
        this.listingSubmittedMessage = page.getByText('Course listing edited and sent for approval successfully!');
        this.listingApprovedMessage = page.getByText('Course listing approved successfully');
        this.searchButton = page.getByTestId('sort-search-primary-button');
        this.academicSessionDropdown = page.getByText('Select Academic Session');
        this.classDropdown = page.getByText('Class Select Class');
        this.streamDropdown = page.getByText('Stream *Select a Value');
        this.courseDropdown = page.getByText('Select Course *Select a Value');
        this.Dropdownsearch = page.getByPlaceholder('Search...');
        this.phaseDropdown = page.getByText('Phase Select a Value');
        this.centerDropdown = page.getByText('Center *Select a Value');
        this.durationTimeInput = page.getByPlaceholder('Duration time');
        this.timeUnitDropdown = page.getByText('Select Time Unit');
        this.tcInput = page.getByPlaceholder('T&C');
        this.mrpInput = page.getByPlaceholder('MRP');
        this.mopInput = page.getByPlaceholder('MOP');
        this.otherDetailsDropdown = page.locator('div').filter({ hasText: /^Other DetailsAdditional attributes for grouping mechanism \*Select Attributes$/ }).getByTestId('dropdown');
        this.saveAsDraftButton = page.getByRole('button', { name: 'Save as Draft' });
        this.sendForApprovalButton = page.getByRole('button', { name: 'Send for Approval' });
        this.listingRow = (listingName: string) => page.getByRole('row', { name: listingName}).first();
        // this.listingRowImage = this.listingRow(listingName).getByRole('img').first();
        this.hideOnWebsiteDropdown = page.getByText('Should hide on website *Select');
        this.otherDetailsOption = (details: string) => page.getByTestId(`dropdown-option-${details}`);
        this.searchtext = (searchtext: string) => page.getByText(searchtext);
    }

    async createCourseListing(listingData: {
        name: string;
        platform: string;
        academicSession: string;
        class: string;
        stream: string;
        course: string;
        phase: string;
        center: string;
        duration: string;
        timeUnit: string;
        tc: string;
        mrp: string;
        mop: string;
        startDate: string;
        endDate: string;
        otherDetails: string;
        hideOnWebsite: string;
    }) {
        // Click search button to ensure page is loaded
        await expect(this.searchButton, 'Verify search button is visible').toBeVisible();
        await this.searchButton.click();
        await this.page.waitForLoadState('networkidle');
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();


        // Select platform
        await slowExpect(this.platformDropDown, 'Verify platform dropdown is visible').toBeVisible();
        await this.platformDropDown.click();
        await expect(this.otherDetailsOption(listingData.platform), 'Verify platform option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.platform).click();
        
        // Fill in listing details
        await expect(this.nameInput, 'Verify name input is visible').toBeVisible();
        await this.nameInput.fill(listingData.name);
        
        // Select center - moved to right after name input
        await expect(this.centerDropdown, 'Verify center dropdown is visible').toBeVisible();
        await this.centerDropdown.click();
        await expect(this.otherDetailsOption(listingData.center), 'Verify center option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.center).click();
        
        // Select academic session
        await expect(this.academicSessionDropdown, 'Verify academic session dropdown is visible').toBeVisible();
        await this.academicSessionDropdown.click();
        await expect(this.otherDetailsOption(listingData.academicSession), 'Verify academic session option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.academicSession).click();
        
        // Select class
        await expect(this.classDropdown, 'Verify class dropdown is visible').toBeVisible();
        await this.classDropdown.click();
        await expect(this.otherDetailsOption(listingData.class), 'Verify class option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.class).click();
        await this.page.waitForLoadState('networkidle');
        
        // Select stream
        await expect(this.streamDropdown, 'Verify stream dropdown is visible').toBeVisible();
        await this.streamDropdown.click();
        await expect(this.otherDetailsOption(listingData.stream), 'Verify stream option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.stream).click();
        
        // Select course 
        await expect(this.courseDropdown, 'Verify course dropdown is visible').toBeVisible();
        await this.courseDropdown.click(); 
        await expect(this.Dropdownsearch, 'Verify dropdown search is visible').toBeVisible();
        await this.Dropdownsearch.fill(listingData.course);
        await expect(this.searchtext(listingData.course), 'Verify course option is visible').toBeVisible();
        await this.searchtext(listingData.course).click();

        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();

        // Select phase
        await slowExpect(this.phaseDropdown, 'Verify phase dropdown is visible').toBeVisible();
        await this.phaseDropdown.click();
        await expect(this.searchtext(listingData.phase), 'Verify phase option is visible').toBeVisible();
        await this.searchtext(listingData.phase).click();
        
        // Fill duration
        await expect(this.durationTimeInput, 'Verify duration time input is visible').toBeVisible();
        await this.durationTimeInput.fill(listingData.duration);
        
        // Select time unit
        await expect(this.timeUnitDropdown, 'Verify time unit dropdown is visible').toBeVisible();
        await this.timeUnitDropdown.click();
        await expect(this.otherDetailsOption(listingData.timeUnit), 'Verify time unit option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.timeUnit).click();
        
        // Fill T&C
        await expect(this.tcInput, 'Verify T&C input is visible').toBeVisible();
        await this.tcInput.fill(listingData.tc);
        
        // Set hide on website - added right after T&C
        await expect(this.hideOnWebsiteDropdown, 'Verify hide on website dropdown is visible').toBeVisible();
        await this.setHideOnWebsite(listingData.hideOnWebsite);
        
        // Fill pricing details
        await expect(this.mrpInput, 'Verify MRP input is visible').toBeVisible();
        await this.mrpInput.fill(listingData.mrp);
        
        await expect(this.mopInput, 'Verify MOP input is visible').toBeVisible();
        await this.mopInput.fill(listingData.mop);
        
        // Set dates
        await expect(this.startDateInput, 'Verify start date input is visible').toBeVisible();
        await this.startDateInput.fill(listingData.startDate);
        
        await expect(this.endDateInput, 'Verify end date input is visible').toBeVisible();
        await this.endDateInput.fill(listingData.endDate);
        
        // Select other details
        await expect(this.otherDetailsDropdown, 'Verify other details dropdown is visible').toBeVisible();
        await this.otherDetailsDropdown.click();
        await expect(this.otherDetailsOption(listingData.otherDetails), 'Verify other details option is visible').toBeVisible();
        await this.otherDetailsOption(listingData.otherDetails).click();
        
        // Save as draft
        await expect(this.saveAsDraftButton, 'Verify save as draft button is visible').toBeVisible();
        await this.saveAsDraftButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

    async submitForApproval(listingName: string) {
        await slowExpect(this.listingRow(listingName), 'Verify listing row is visible').toBeVisible();
        await this.listingRow(listingName).getByRole('img').first().click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
        await slowExpect(this.sendForApprovalButton, 'Verify send for approval button is visible').toBeVisible();
        await this.sendForApprovalButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
        await slowExpect(this.listingSubmittedMessage, 'Verify submission success message is visible').toBeVisible();
    }

    async setHideOnWebsite(value: string) {
        await expect(this.hideOnWebsiteDropdown, 'Verify hide on website dropdown is visible').toBeVisible();
        await this.hideOnWebsiteDropdown.click();
        await this.otherDetailsOption(value).click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();

    }
} 