import { cleanupRegistry } from './tests/commons/cleanup-registry.ts'
import { initializeSecrets } from './scripts/secrets-manager'

export default async function globalSetup() {
  const env = process.env.PROD === '1' ? 'prod' : 'stage';
  await initializeSecrets(env);    // Initialize secrets first

  let isCleaningUp = false

  // Handle cleanup for different process termination signals
  const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT', 'SIGHUP']

  signals.forEach((signal) => {
    process.on(signal, async () => {
      if (isCleaningUp) {
        console.log('Already cleaning up, please wait...')
        return
      }

      isCleaningUp = true
      console.log(`\nForce cleanup initiated by ${signal}...`)

      try {
        console.log('Starting cleanup tasks...')

        const cleanupPromise = cleanupRegistry.runAllCleanups()
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            console.log('\n⚠️ WARNING: Cleanup still running after 120 seconds...')
            reject(new Error('Cleanup timeout'))
          }, 120000)
        })

        await Promise.race([cleanupPromise, timeoutPromise])
        console.log('Cleanup completed successfully')
      } catch (error) {
        console.error('Force cleanup failed:', error)
      } finally {
        isCleaningUp = false
        process.exit(1)
      }
    })
  })

  // Handle uncaught exceptions
  process.on('uncaughtException', async (error) => {
    console.error('Uncaught Exception:', error)
    if (!isCleaningUp) {
      await cleanupRegistry.runAllCleanups().catch(console.error)
    }
    process.exit(1)
  })

  // Handle unhandled promise rejections
  process.on('unhandledRejection', async (reason) => {
    console.error('Unhandled Promise Rejection:', reason)
    if (!isCleaningUp) {
      await cleanupRegistry.runAllCleanups().catch(console.error)
    }
    process.exit(1)
  })
}
