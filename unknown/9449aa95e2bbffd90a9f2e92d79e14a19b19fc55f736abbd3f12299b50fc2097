import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';
import { execPath } from 'process';

const OffersAndDiscountsPageUrl = '/listing-management/offers'


export class OffersAndDiscountsPage extends ICPage {
  readonly apiLoader: Locator;
  readonly createOfferButton: Locator;
  readonly fillFromConsoleButton: Locator;
  readonly userDetailsText: Locator;
  readonly mobileNumberInput: Locator;
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly dropdownSelect: Locator;
  readonly concessionOption: Locator;
  readonly selectCriteriaButton: Locator;
  readonly codeInput: Locator;
  readonly selectClassButton: Locator;
  readonly class11Option: Locator;
  readonly selectCourseModeButton: Locator;
  readonly liveModeOption: Locator;
  readonly selectStreamButton: Locator;
  readonly jeeAdvancedStreamOption: Locator;
  readonly minAmountInput: Locator;
  readonly maxAmountInput: Locator;
  readonly selectTypeButton: Locator;
  readonly flatDiscountOption: Locator;
  readonly maxCapInput: Locator;
  readonly startDateInput: Locator;
  readonly startDateOption: Locator;
  readonly expiryDateInput: Locator;
  readonly expiryDateOption: Locator;
  readonly selectValueButton: Locator;
  readonly tallentexOption: Locator;
  readonly createButton: Locator;
  readonly sidebarSubItemOffersDiscounts: Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, OffersAndDiscountsPageUrl, isMobile);
    this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
    this.createOfferButton = page.getByRole('button', { name: 'Create Offer' });
    this.fillFromConsoleButton = page.getByRole('button', { name: 'Fill From Console' });
    this.userDetailsText = page.getByText('User Details');
    this.mobileNumberInput = page.getByPlaceholder('Mobile Number');
    this.firstNameInput = page.getByPlaceholder('First Name');
    this.lastNameInput = page.getByPlaceholder('Last Name');
    this.dropdownSelect = page.getByTestId('dropdown').getByText('Select...');
    this.concessionOption = page.getByTestId('dropdown-option-concession');
    this.selectCriteriaButton = page.getByRole('button', { name: 'Select Criteria' });
    this.codeInput = page.getByPlaceholder('CODE10');
    this.selectClassButton = page.getByText('Select Class');
    this.class11Option = page.getByTestId('dropdown-option-CLASS_11');
    this.selectCourseModeButton = page.getByText('Select Course Mode');
    this.liveModeOption = page.getByTestId('dropdown-option-MODE_LIVE');
    this.selectStreamButton = page.getByText('Select Stream');
    this.jeeAdvancedStreamOption = page.getByTestId('dropdown-option-STREAM_JEE_MAIN_ADVANCED');
    this.minAmountInput = page.getByPlaceholder('Enter Minimum Amount');
    this.maxAmountInput = page.getByPlaceholder('Enter Maximum Amount');
    this.selectTypeButton = page.getByText('Select Type');
    this.flatDiscountOption = page.getByTestId('dropdown-option-FLAT_DISCOUNT');
    this.maxCapInput = page.getByPlaceholder('Enter Max Cap');
    this.startDateInput = page.getByPlaceholder('Enter Start Date');
    this.startDateOption = page.getByPlaceholder('Enter Start Date');
    this.expiryDateInput = page.getByPlaceholder('Enter Expiry Date');
    this.expiryDateOption = page.getByPlaceholder('Enter Expiry Date');
    this.selectValueButton = page.getByText('Select Value');
    this.tallentexOption = page.getByTestId('dropdown-option-tallentex');
    this.createButton = page.getByRole('button', { name: 'Create' });
    this.sidebarSubItemOffersDiscounts = page.getByTestId('sidebar-subtab-item-Offers/Discounts');
  }
  async createOffer(phoneNumber: string, offercode: string, maximumCap: string,  minAmount: string, maxAmount: string, startDate: string, expiryDate: string) {
    await expect(this.createOfferButton, "Verify create offer button is visible").toBeVisible();
    await this.createOfferButton.click();
    await expect(this.fillFromConsoleButton, "Verify fill from console button is visible").toBeVisible();
    await this.fillFromConsoleButton.click();
    await this.page.waitForLoadState('networkidle');
    await expect(this.userDetailsText, "Verify user details text is visible").toBeVisible();
    await expect(this.mobileNumberInput, "Verify mobile number input is visible").toBeVisible();
    await this.mobileNumberInput.fill(phoneNumber);
    await expect(this.firstNameInput, "Verify first name input is visible").toBeVisible();
    await this.firstNameInput.fill('Test');
    await expect(this.lastNameInput, "Verify last name input is visible").toBeVisible();
    await this.lastNameInput.fill('Student');
    await expect(this.dropdownSelect, "Verify dropdown select is visible").toBeVisible();
    await this.dropdownSelect.click();
    await expect(this.concessionOption, "Verify concession option is visible").toBeVisible();
    await this.concessionOption.click();
    await expect(this.selectCriteriaButton, "Verify select criteria button is visible").toBeVisible();
    await this.selectCriteriaButton.click();
    await this.page.waitForTimeout(1000);
    await expect(this.codeInput, "Verify code input is visible").toBeVisible();
    await this.codeInput.fill(offercode);
    await expect(this.selectClassButton, "Verify select class button is visible").toBeVisible();
    await this.selectClassButton.click();
    await expect(this.class11Option, "Verify class 11 option is visible").toBeVisible();
    await this.class11Option.click();
    await expect(this.selectCourseModeButton, "Verify select course mode button is visible").toBeVisible();
    await this.selectCourseModeButton.click();
    await expect(this.liveModeOption, "Verify live mode option is visible").toBeVisible();
    await this.liveModeOption.click();
    await expect(this.selectStreamButton, "Verify select stream button is visible").toBeVisible();
    await this.selectStreamButton.click();
    await expect(this.jeeAdvancedStreamOption, "Verify jee advanced stream option is visible").toBeVisible();
    await this.jeeAdvancedStreamOption.click();
    await expect(this.minAmountInput, "Verify min amount input is visible").toBeVisible();
    await this.minAmountInput.fill(minAmount);
    await expect(this.maxAmountInput, "Verify max amount input is visible").toBeVisible();
    await this.maxAmountInput.fill(maxAmount);
    await expect(this.selectTypeButton, "Verify select type button is visible").toBeVisible();
    await this.selectTypeButton.click();
    await expect(this.flatDiscountOption, "Verify flat discount option is visible").toBeVisible();
    await this.flatDiscountOption.click();
    await expect(this.maxCapInput, "Verify max cap input is visible").toBeVisible();
    await this.maxCapInput.fill(maximumCap);
    await expect(this.startDateInput, "Verify start date input is visible").toBeVisible();
    await this.startDateInput.click();
    await expect(this.startDateOption, "Verify start date is visible").toBeVisible();
    await this.startDateOption.fill(startDate);
    await expect(this.expiryDateInput, "Verify expiry date input is visible").toBeVisible();
    await this.expiryDateOption.fill(expiryDate);
    await expect(this.selectValueButton, "Verify select value button is visible").toBeVisible();
    await this.selectValueButton.click();
    await expect(this.tallentexOption, "Verify tallentex option is visible").toBeVisible();
    await this.tallentexOption.click();
    await expect(this.createButton, "Verify create button is visible").toBeVisible();
    await this.createButton.click();
      await slowExpect(this.apiLoader, "Verify api loader to fetch data should not visible").toBeHidden();
    }
}