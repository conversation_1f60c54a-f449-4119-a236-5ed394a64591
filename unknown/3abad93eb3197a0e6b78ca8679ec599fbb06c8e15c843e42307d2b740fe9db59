# borrow-taxonomy.yml
# Flow - Borrow taxonomy from one center to another center
config:
  variables:
    source_stream: "STREAM_JEE_MAIN_ADVANCED"
    destination_center: "KOTA"
    destination_stream: "STREAM_JEE_MAIN_ADVANCED"

  defaults:
    headers:
      accept: application/json
      content-type: application/json
      x-client-type: web

  fixtures:
    - bffURL
    - testData

scenarios:
  - name: "Verify borrow taxonomy from one center to another fails when destination taxonomy already exists"
    flow:
          
      # Step 1: verify borrow taxonomy from one center to another center
      - post:
          name: "verify borrow taxonomy from one center to another center"
          url: "{{bffURL}}/internal-bff/api/v1/taxonomy/borrow"
          headers:
            authorization: "Bearer {{testData.env.ADMIN_ACCESS_TOKEN}}"
          json:
            source_center: "{{testData.env.CENTER_NAME}}"
            source_stream: "{{source_stream}}"
            source_session: "{{testData.env.CENTER_SESSION}}"
            destination_center: "{{destination_center}}"
            destination_stream: "{{destination_stream}}"
            destination_session: "{{testData.env.CENTER_SESSION}}"
          capture:
            - json: $.reason
              as: error_reason
          expect:
            - statusCode: 400
            - equals:
                - "{{error_reason}}"
                - "BorrowTaxonomy: Destination Taxonomy already exists"
      
      - log: "Error reason: {{error_reason}}"
