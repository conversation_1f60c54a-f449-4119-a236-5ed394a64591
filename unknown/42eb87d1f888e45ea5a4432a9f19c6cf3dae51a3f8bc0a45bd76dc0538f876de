import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { customExpect, slowExpect } from '../../fixtures/ui-fixtures';

const CreateUrlEditsPageUrl = '/page-management/url'

export class CreateUrlEditsPage extends ICPage {

    readonly apiLoader: Locator;
    readonly urlCreationTitleText: Locator;
    readonly viewUrlsText: Locator;
    readonly createUrlButton: Locator;
    readonly copiedText: Locator;
    readonly urlNameRow: (urlName: string) => Locator;
    readonly urlConstId: (urlName: string) => Locator;
    readonly copyToClipBoardWidgetConstId: (urlConstId: string) => Locator;
    readonly isInternalUserCriteriaText: (urlConstID: string) => Locator;
    readonly pageConstIdFeild: Locator;
    readonly actionsButton: Locator;
    readonly applyFilterButton: Locator;
    readonly resetFilterButton: Locator;
    readonly enableCriteriaButton: Locator;
    readonly areYouSureText: Locator;
    readonly enableButton: Locator;
    readonly enableCriteriaText: Locator;
    // URL Creation locators
    readonly pageUrlInput: Locator;
    readonly applyFiltersButton: Locator;
    readonly urlActionsCell: Locator;
    readonly nextButton: Locator;
    readonly addNewCardButton: Locator;
    readonly titleInput: Locator;
    readonly programModeInput: Locator;
    readonly discountedPriceInput: Locator;
    readonly labelInput: Locator;
    readonly actionDropdown: Locator;
    readonly navigationUrlInput: Locator;
    readonly navigationTypeDropdown: Locator;
    readonly filterDimensionInput: Locator;
    readonly filterValueInput: Locator;
    readonly addFilterValueButton: Locator;
    readonly addFilterDimensionButton: Locator;
    readonly submitForApprovalButton: Locator;
    readonly pageconstId: Locator;
    readonly widgetConstId: Locator;
    readonly CardTemplate: (cardCount) => Locator;
    readonly cards: Locator;
    readonly toastMessageText: Locator;
    readonly urlInput: Locator;
    readonly pageConstIdInput: Locator;
    readonly getButton: Locator;
    readonly metaKeyInput: Locator;
    readonly metaValueInput: Locator;
    readonly addButton: Locator;
    readonly submitButton: Locator;
    readonly discountCodeInput: Locator;
    readonly applyDiscountButton: Locator;
    readonly discountAppliedMessage: Locator;
    readonly errorMessage: Locator;
    readonly urlText: Locator;
    readonly selectNavigationDropdownOption: Locator;
    readonly selectWebviewDropdownOption: Locator;
    readonly addFilterDataButton: Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, CreateUrlEditsPageUrl, isMobile);

        this.apiLoader = page.getByTestId('api-loader').getByTestId('shownImage');
        this.urlCreationTitleText = page.getByText('URL Creation & Edits');
        this.viewUrlsText = page.getByRole('heading', { name: 'View URLs' });
        this.createUrlButton = page.getByRole('button', { name: 'Create URL' });
        this.pageConstIdFeild = page.getByPlaceholder('Page Const Id');
        this.urlNameRow = (urlName: string) => page.locator(`xpath=//*[contains(@id,'cell-url') and text()='${urlName}']/ancestor::div[contains(@class, 'ag-row')]/descendant::*[@row-id="0"]`);
        this.urlConstId = (urlName: string) => (this.urlNameRow(urlName)).locator(`xpath=.//*[contains(@id,"cell-url_id-")]//*[contains(@class, 'underline cursor-pointer text-blue500')]`);
        this.copyToClipBoardWidgetConstId = (urlConstId: string) => page.locator(`//*[text()='${urlConstId}']/..//following-sibling::*//*[@data-testid="CopyAllIcon"]`);
        this.copiedText = page.getByText('Copied!');
        this.isInternalUserCriteriaText = (urlName: string) => (this.urlNameRow(urlName)).locator(`xpath=.//*[contains(@id,"cell-criterias") and text()="isInternalUser=true"]`);
        this.applyFilterButton = page.getByRole('button', { name: 'Apply Filters' });
        this.resetFilterButton = page.getByRole('button', { name: 'Reset Filters' });
        this.actionsButton = page.getByTestId('urlViewActionsCell-0').getByTestId('shownImage');
        this.enableCriteriaButton = page.getByText('Enable Criteria');
        this.areYouSureText = page.getByText('Are you sure, you want to');
        this.enableButton = page.getByRole('button', { name: 'Enable' });
        this.enableCriteriaText = page.getByTestId('page-content').getByText('Enable Criteria');
        
        // Initialize URL Creation locators
        this.pageUrlInput = page.getByPlaceholder('Page URL');
        this.applyFiltersButton = page.getByRole('button', { name: 'Apply Filters' });
        this.urlActionsCell = page.getByTestId('urlViewActionsCell-0');
        this.addNewCardButton = page.getByRole('button', { name: 'Add New Card' }).first();
        this.nextButton = page.getByRole('button', { name: 'Next' });
        this.titleInput = page.locator('div').filter({ hasText: /^Title\*$/ }).getByRole('textbox');
        this.programModeInput = page.locator('input[name="program_mode"]');
        this.discountedPriceInput = page.locator('input[name="discounted_price"]');
        this.labelInput = page.locator('input[name="label"]');
        this.actionDropdown = page.locator('div').filter({ hasText: /^Action\*Select an actionRemove Action$/ }).getByRole('img');
        this.navigationUrlInput = page.getByPlaceholder('Navigation URL');
        this.navigationTypeDropdown = page.locator('div').filter({ hasText: /^Navigation TypeSelect a Type$/ }).getByTestId('dropdown');
        this.filterDimensionInput = page.locator('(//input[@name="filter_dimension"])[last()]');
        this.filterValueInput = page.locator('(//input[@name="filterValue"])[last()]');
        this.addFilterValueButton = page.locator('(//button[div[text()="Add Value"]])[last()]');
        this.addFilterDimensionButton = page.getByRole('button', { name: 'Add Filter Dimenion' }).nth(1);
        this.submitForApprovalButton = page.getByRole('button', { name: 'Submit For Approval' });
        this.pageconstId = page.getByRole('link', { name: 'ikoBPI_As5FdBg80g7wK6' });//for jee/online-coaching-class-11
        this.widgetConstId = page.getByRole('link', { name: 'yIQACvnU0sozZDoZPcPUC' }); //for jee-11
        this.cards = page.locator('[data-rfd-draggable-id^="section-0-card-"]');
        this.CardTemplate = (cardCount) => page.getByRole('button', { name: 'Card ' + cardCount });
        this.toastMessageText = page.getByTestId('toast_message_text');
        this.urlInput = page.getByPlaceholder('Enter URL');
        this.pageConstIdInput = page.getByPlaceholder('Enter page const id');
        this.getButton = page.getByRole('button', { name: 'Get' });
        this.metaKeyInput = page.locator('//button[div[text()="Add"]]/preceding::input[@placeholder][2]');
        this.metaValueInput = page.locator('//button[div[text()="Add"]]/preceding::input[@placeholder][1]');
        this.addButton = page.getByRole('button', { name: 'Add' });
        this.enableButton = page.getByRole('button', { name: 'Enable' });
        this.addNewCardButton = page.getByRole('button', { name: 'Add New Card' }).first(); 
        this.titleInput = page.locator('div').filter({ hasText: /^Title\*$/ }).getByRole('textbox');
        this.programModeInput = page.locator('input[name="program_mode"]');
        this.discountedPriceInput = page.locator('input[name="discounted_price"]');
        this.labelInput = page.locator('input[name="label"]');
        this.actionDropdown = page.locator('div').filter({ hasText: /^Action\*Select an actionRemove Action$/ }).getByRole('img');
        this.selectNavigationDropdownOption = page.getByTestId('dropdown-option-NAVIGATION');
        this.navigationUrlInput = page.getByPlaceholder('Navigation URL');
        this.navigationTypeDropdown = page.locator('div').filter({ hasText: /^Navigation TypeSelect a Type$/ }).getByTestId('dropdown');
        this.selectWebviewDropdownOption = page.getByTestId('dropdown-option-webview');
        this.addFilterDataButton = page.getByRole('button', { name: 'Add Filter Data' });
        this.submitButton = page.getByRole('button', { name: 'Submit' });
        this.discountCodeInput = page.getByPlaceholder('Enter discount code');
        this.applyDiscountButton = page.getByRole('button', { name: 'Apply Discount' });
        this.discountAppliedMessage = page.getByText('Discount applied successfully');
        this.errorMessage = page.getByText('Invalid URL');
        this.apiLoader = page.getByTestId('api-loader');
        this.urlText = page.getByText('URL', { exact: true });

    }

    /* verifying to url creation and edits page */
    async verifyUrlCreationAndEditsPage() {
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
        await expect(this.urlCreationTitleText, "Verify url creation and edits title text is visible").toBeVisible();
        await expect(this.viewUrlsText, "Verify view url's text is visible").toBeVisible();
        await expect(this.createUrlButton, "Verify create url button is visible").toBeVisible();
    }


    /* verifying to url creation and edits page */
    async clickOnCreateUrlButton() {
        await expect(this.createUrlButton, "Verify create url button is visible").toBeVisible();
        await this.createUrlButton.click();
    }



    /* verifying widget created  */
    async verifyCreatedUrl(urlName) {
        await slowExpect(this.urlNameRow(urlName), "Verify created url name is visible").toBeVisible();
        await expect(this.urlConstId(urlName), "Verify create widget const id is visible").toBeVisible();
        const widgetContsIdLocator = await this.urlConstId(urlName);
        const constId = await widgetContsIdLocator.textContent() ?? "";
        return constId;
    }

    /* verifying widget created  */
    async verifyUrlConstIdCopyToClipboard(widgetConstId) {
        await expect(this.copyToClipBoardWidgetConstId(widgetConstId), "Verify copy to clipboard is visible").toBeVisible();
        await this.copyToClipBoardWidgetConstId(widgetConstId).click();
        await expect(this.copiedText, "Verify copied text is visible").toBeVisible();
    }

    /* verifying InternalUserCriteria  */
    async verifyInternalUserCriteria(widgetConstId, pageConstId) {
        await expect(this.isInternalUserCriteriaText(widgetConstId), "Verify is internal user criteria is true is visible").toBeVisible();
        await expect(this.pageConstIdFeild, "Verify page const id feild is visible").toBeVisible();
        await this.pageConstIdFeild.fill(pageConstId);
        await expect(this.applyFilterButton, "Verify apply filter is visible").toBeVisible();
        await this.applyFilterButton.click();
        await customExpect(15000)(this.apiLoader, "Verify page loading").not.toBeVisible();
    }


    /* verifying enable criteria   */
    async verifyEnableCriteria(baseURL) {
        await expect(this.actionsButton, "Verify actions button is visible").toBeVisible();
        await this.actionsButton.click();

        await expect(this.enableCriteriaButton, "Verify enable criteria button is visible").toBeVisible();
        await this.enableCriteriaButton.click();

        await expect(this.enableCriteriaText, "Verify enable criteria text is visible").toBeVisible();
        await expect(this.areYouSureText, "Verify are you sure, you want to enable text is visible").toBeVisible();
        await expect(this.enableButton, "Verify enable button is visible").toBeVisible();

        const responsePromise = this.page.waitForResponse(baseURL + '/urls/enable', { timeout: 10000 });
        await this.enableButton.click();
        await responsePromise;

    }

    /* click to url created */
    async clickOnCreatedUrlId(urlName) {
        await expect(this.urlConstId(urlName), "Verify url created const id is visible").toBeVisible();
        await this.urlConstId(urlName).click();
        await slowExpect(this.apiLoader, "Verify page loading").not.toBeVisible();
    }
    
    async verifyInvalidUrlHandling(url: string) {
        await this.page.goto(url);
        await expect(this.errorMessage, 'Verify error message is visible').toBeVisible();
    }

    async createCheckoutUrl(path: string, id: string) {
        await expect(this.createUrlButton, "Verify create url button is visible").toBeVisible();
        await this.createUrlButton.click();
        await this.page.waitForLoadState('networkidle');
        await expect(this.apiLoader, "Verify page loading").toBeHidden();
        await expect(this.urlText, "Verify url text is visible").toBeVisible();
        await expect(this.urlInput, "Verify url input is visible").toBeVisible();
        await this.urlInput.fill(path);
        await expect(this.pageConstIdInput, "Verify page const id input is visible").toBeVisible();
        await this.pageConstIdInput.fill(id);
        await expect(this.getButton, "Verify get button is visible").toBeVisible();
        await this.getButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

    async addMetadata(key: string, value: string) {
        await expect(this.metaKeyInput, "Verify meta key input is visible").toBeVisible();
        await this.metaKeyInput.fill(key);
        await expect(this.metaValueInput, "Verify meta value input is visible").toBeVisible();
        await this.metaValueInput.fill(value);
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

    async submitForApproval() {
        await expect(this.submitForApprovalButton, "Verify submit for approval button is visible").toBeVisible();
        await this.submitForApprovalButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

    async enableCriteria(Url:string){  //for a URL Created
    await slowExpect(this.pageUrlInput, "Verify page URL input is visible").toBeVisible();
    await this.pageUrlInput.fill(Url);
    await slowExpect(this.applyFiltersButton, "Verify apply filters button is visible").toBeVisible();
    await this.applyFiltersButton.click();
    await slowExpect(this.urlActionsCell, "Verify url actions cell is visible").toBeVisible();
    await this.urlActionsCell.click();
    await expect(this.enableCriteriaButton, "Verify enable criteria button is visible").toBeVisible();
    await this.enableCriteriaButton.click();
    await expect(this.enableButton, "Verify enable button is visible").toBeVisible();
    await this.enableButton.click();
    await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

    //Going to /jee/online-coaching-class-11 page Widgets
    async GoingtoPageWidgets(pageUrl:string) {
        await slowExpect(this.pageUrlInput, "Verify page url input is visible").toBeVisible();
        await this.pageUrlInput.fill(pageUrl);
        await expect(this.applyFiltersButton, "Verify apply filters button is visible").toBeVisible();
        await this.applyFiltersButton.click();
        await expect(this.pageconstId, "Verify page const id is visible").toBeVisible();
        await this.pageconstId.click(); //for /jee/online-coaching-class-11 only
        await expect(this.nextButton, "Verify next button is visible").toBeVisible();
        await this.nextButton.click();
        await expect(this.widgetConstId, "Verify widget const id is visible").toBeVisible();
        await this.widgetConstId.dblclick(); // for course details- yIQACvnU0sozZDoZPcPUC widget-id only
        await this.page.waitForTimeout(1500);
       
    }
    //Intial steps to start Widget card Creation for going to course details page
    async WidgetCardCreation(randomUrl:string, widgetTitle:string, programMode:string, discountedPrice:string, label:string) {
        await expect(this.nextButton, "Verify next button is visible").toBeVisible();
        await this.nextButton.click();
        await expect(this.addNewCardButton, "Verify add new card button is visible").toBeVisible();
        await this.addNewCardButton.click();
        const cards = await this.cards;
        const cardCount = await cards.count();
        await expect(this.CardTemplate(cardCount), "Verify card template is visible").toBeVisible();
        await this.CardTemplate(cardCount).click(); 
        await expect(this.titleInput, "Verify title input is visible").toBeVisible();
        await this.titleInput.click();
        await this.titleInput.fill(widgetTitle);
        await expect(this.programModeInput, "Verify program mode input is visible").toBeVisible();
        await this.programModeInput.click();
        await this.programModeInput.fill(programMode);
        await expect(this.discountedPriceInput, "Verify discounted price input is visible").toBeVisible();
        await this.discountedPriceInput.click();
        await this.discountedPriceInput.fill(discountedPrice);
        await expect(this.labelInput, "Verify label input is visible").toBeVisible();
        await this.labelInput.click();
        await this.labelInput.fill(label);
        await expect(this.actionDropdown, "Verify action dropdown is visible").toBeVisible();
        await this.actionDropdown.click();
        await expect(this.selectNavigationDropdownOption, "Verify select navigation dropdown option is visible").toBeVisible();
        await this.selectNavigationDropdownOption.click();
        await expect(this.navigationUrlInput, "Verify navigation url input is visible").toBeVisible();
        await this.navigationUrlInput.click();
        await this.navigationUrlInput.fill(randomUrl);
        await expect(this.navigationTypeDropdown, "Verify navigation type dropdown is visible").toBeVisible();
        await this.navigationTypeDropdown.click();
        await expect(this.selectWebviewDropdownOption, "Verify select webview dropdown option is visible").toBeVisible();
        await this.selectWebviewDropdownOption.click();
    }

    async addFilterData(filterDimension:string, filterValue:string) {
        await expect(this.filterDimensionInput, "Verify filter dimension input is visible").toBeVisible();
        await this.filterDimensionInput.click();
        await this.filterDimensionInput.fill(filterDimension);
        await expect(this.filterValueInput, "Verify filter value input is visible").toBeVisible();
        await this.filterValueInput.click();
        await this.filterValueInput.fill(filterValue);
        await expect(this.addFilterValueButton, "Verify add filter value button is visible").toBeVisible();
        await this.addFilterValueButton.click();
    }
    
}