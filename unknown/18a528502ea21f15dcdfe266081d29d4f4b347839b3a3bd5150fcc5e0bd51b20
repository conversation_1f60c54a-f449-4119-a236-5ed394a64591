import { expect, type Locator, type Page } from '@playwright/test';
import { ICPage } from './ic-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import { customExpect } from '../../fixtures/ui-fixtures';

const CourseListingApprovalUrl = '/listing-management/listing-approval';

export class CourseListingApproval extends ICPage {
    readonly searchInputField: Locator;
    readonly searchButton: Locator;
    readonly apiLoader: Locator;
    readonly approveButton: Locator;
    readonly approveConfirmationButton: Locator;
    readonly approveSuccessMessage: Locator;
    readonly deleteIcon: Locator;
    readonly listingRow: Locator;
    readonly filterPannelClosedIcon: Locator;
    readonly streamTitle: Locator;
    readonly streamDropdown: Locator;
    readonly streamOption: (streamName: string) => Locator;
    readonly courseTitle: Locator;
    readonly courseDropdown: Locator;
    readonly courseOption: (courseName: string) => Locator;
    readonly applyFilterButton: Locator;
    readonly phaseTitle: Locator;
    readonly phaseDropdown: Locator;
    readonly phaseOption: (phaseName: string) => Locator;
    readonly centerTitle: Locator;
    readonly centerDropdown: Locator;
    readonly centerOption: (centerName: string) => Locator;
    readonly academicSessionTitle: Locator;
    readonly academicSessionDropdown: Locator;
    readonly academicSessionOption: (sessionName: string) => Locator;
    readonly classTitle: Locator;
    readonly classDropdown: Locator;
    readonly classOption: (className: string) => Locator;
    readonly statusTitle: Locator;
    readonly statusDropdown: Locator;
    readonly statusOption: (status: string) => Locator;

    constructor(page: Page, isMobile: boolean) {
        super(page, CourseListingApprovalUrl, isMobile);
        
        this.searchInputField = page.getByPlaceholder('Search');
        this.searchButton = page.getByRole('button', { name: 'Search' });
        this.apiLoader = page.getByTestId('api-loader');
        this.approveButton = page.locator('.cursor-pointer:has-text("Approve")');
        this.approveConfirmationButton = page.getByRole('button', { name: 'Approve' });
        this.approveSuccessMessage = page.getByText('Test listing was approved successfully');
        this.deleteIcon = page.getByRole('button', { name: 'delete_icon' });
        this.listingRow = page.getByRole('row', { name: /Test Product Listing/ });
        this.filterPannelClosedIcon = page.getByTestId('filter-panel-closed').getByTestId('shownImage');
        this.streamTitle = page.getByText('stream', { exact: true });
        this.streamDropdown = page.getByTestId('stream-dropdown');
        this.streamOption = (streamName: string) => page.getByTestId(`stream-dropdown-option-${streamName}`);
        this.courseTitle = page.getByText('course', { exact: true });
        this.courseDropdown = page.getByTestId('type_id-dropdown');
        this.courseOption = (courseName: string) => page.getByText(`${courseName}`);
        this.applyFilterButton = page.getByTestId('apply-filter-panel');
        this.phaseTitle = page.getByText('phase', { exact: true });
        this.phaseDropdown = page.getByTestId('phase_id-dropdown');
        this.phaseOption = (phaseName: string) => page.getByText(`${phaseName}`);
        this.centerTitle = page.getByText('Center');
        this.centerDropdown = page.getByTestId('facility_id-dropdown');
        this.centerOption = (centerName: string) => page.getByTestId(`facility_id-dropdown-option-${centerName}`);
        this.academicSessionTitle = page.getByText('session');
        this.academicSessionDropdown = page.getByTestId('academic_session-dropdown');
        this.academicSessionOption = (sessionName: string) => page.getByTestId(`academic_session-dropdown-option-${sessionName}`);
        this.classTitle = page.getByText('Class');
        this.classDropdown = page.getByTestId('class-dropdown');
        this.classOption = (className: string) => page.getByTestId(`class-dropdown-option-${className}`);
        this.statusTitle = page.getByText('status', { exact: true });
        this.statusDropdown = page.getByTestId('status-dropdown');
        this.statusOption = (status: string) => page.getByTestId(`status-dropdown-option-${status}`);
    }

    async searchForListing(listingName: string) {
        await expect(this.searchInputField, 'Verify search input field is visible').toBeVisible();
        await this.searchInputField.fill(listingName);
        await expect(this.searchButton, 'Verify search button is visible').toBeVisible();
        await this.searchButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

    async approveListing(listingName: string) {
        await slowExpect(this.approveButton, 'Verify approve button is visible').toBeVisible();
        await this.approveButton.dblclick();
        await expect(this.approveConfirmationButton, 'Verify approve confirmation button is visible').toBeVisible();
        await this.approveConfirmationButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
        await slowExpect(this.approveSuccessMessage, 'Verify approval success message is visible').toBeVisible();
        await expect(this.deleteIcon, 'Verify delete icon is visible').toBeVisible();
        await this.deleteIcon.click();
        
    }

    async ListingApprovalFilter(streamName: string, academicSessionName: string, centerName: string, className: string, courseName: string, phaseName: string){
        await expect(this.filterPannelClosedIcon, 'Verify filter pannel closed icon is visible').toBeVisible();
        await this.filterPannelClosedIcon.click();
        await expect(this.streamTitle, 'Verify stream title is visible').toBeVisible();
        await expect(this.streamDropdown, 'Verify stream dropdown is visible').toBeVisible();
        await this.streamDropdown.click();
        await expect(this.streamOption(streamName), 'Verify stream option is visible').toBeVisible();
        await this.streamOption(streamName).click();
        await expect(this.academicSessionTitle, 'Verify academic session title is visible').toBeVisible();
        await expect(this.academicSessionDropdown, 'Verify academic session dropdown is visible').toBeVisible();
        await this.academicSessionDropdown.click();
        await expect(this.academicSessionOption(academicSessionName), 'Verify academic session option is visible').toBeVisible();
        await this.academicSessionOption(academicSessionName).click();
        await expect(this.centerTitle, 'Verify center title is visible').toBeVisible();
        await expect(this.centerDropdown, 'Verify center dropdown is visible').toBeVisible();
        await this.centerDropdown.click();
        await expect(this.centerOption(centerName), 'Verify center option is visible').toBeVisible();
        await this.centerOption(centerName).click();
        await expect(this.classTitle, 'Verify class title is visible').toBeVisible();
        await expect(this.classDropdown, 'Verify class dropdown is visible').toBeVisible();
        await this.classDropdown.click();
        await expect(this.classOption(className), 'Verify class option is visible').toBeVisible();
        await this.classOption(className).click();
        await expect(this.courseTitle, 'Verify course title is visible').toBeVisible();
        await expect(this.courseDropdown, 'Verify course dropdown is visible').toBeVisible();
        await this.courseDropdown.click();
        await expect(this.searchInputField, 'Verify search input field is visible').toBeVisible();
        await this.searchInputField.fill(courseName);
        await expect(this.courseOption(courseName), 'Verify course option is visible').toBeVisible();
        await this.courseOption(courseName).click();
        await expect(this.phaseTitle, 'Verify phase title is visible').toBeVisible(); 
        await expect(this.phaseDropdown, 'Verify phase dropdown is visible').toBeVisible();
        await this.phaseDropdown.click();
        await expect(this.searchInputField, 'Verify search input field is visible').toBeVisible();
        await this.searchInputField.fill(phaseName);
        await expect(this.phaseOption(phaseName), 'Verify phase option is visible').toBeVisible();
        await this.phaseOption(phaseName).click();
        await expect(this.applyFilterButton, 'Verify apply filter button is visible').toBeVisible();
        await this.applyFilterButton.click();
        await slowExpect(this.apiLoader, "Verify api loader to fetch data is not visible").toBeHidden();
    }

} 