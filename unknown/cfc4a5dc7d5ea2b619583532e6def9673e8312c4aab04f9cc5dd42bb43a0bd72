import { chromium } from '@playwright/test';
import API from '../../../commons/api-helpers/api-helper';
import ScheduleManagement from '../../../commons/api-helpers/schedule-management';
import { writeTestData } from './test-data-utils';
import { initializeSecrets } from '../../../../scripts/secrets-manager';

async function globalSetup() {
  const env = process.env.PROD === '1' ? 'prod' : 'stage';
  await initializeSecrets(env);    // Initialize secrets first

  console.log('Starting setup for meeting ID tests...');

  // Create a browser instance to get the context for API calls
  const browser = await chromium.launch({
    headless: true,
    args: [
      '--use-fake-device-for-media-stream',
      '--use-fake-ui-for-media-stream',
      '--disable-web-security',
      '--no-sandbox',  
      '--disable-setuid-sandbox'
    ],
    channel: 'chrome'
  });
  console.log('Browser launched successfully');

  const context = await browser.newContext({
    permissions: ['microphone', 'camera']
  });

  const baseURL = process.env.PROD === '1' ? 'https://api.allen-live.in/v1/' : 'https://bff.allen-stage.in/v1/';

  // Initialize API and set token
  const api = new API(context.request, baseURL);
  api.setToken(process.env.ADMIN_ACCESS_TOKEN || '');
  const scheduleManagement = new ScheduleManagement(api);

  try {
    const now = Math.floor(Date.now() / 1000); 
    const startTimestamp = now + (2 * 60 * 60);
    const endTimestamp = startTimestamp + (1 * 60 * 60);
    const meetingIds = await scheduleManagement.getMeetingIds(startTimestamp, endTimestamp);
    writeTestData(meetingIds);
  } catch (error) {
    console.error('Error creating meetings:', error);
  } finally {
    await browser.close();
  }
}

export default globalSetup; 

function epochToDate(epoch: number) {
  return new Date(epoch * 1000).toLocaleString('en-US', {
    timeZone: 'Asia/Kolkata',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
}