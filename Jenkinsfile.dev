@Library('Allen_Shared_Libraries') _

node {
    checkout scm

    def common = load "${WORKSPACE}/jenkins/common.groovy"
    def utils = load "${WORKSPACE}/jenkins/utils.groovy"

    // Define test modules based on test type
    def webTestModules = [
        [name: 'All_WEB', path: 'tests/ui-tests/specs/*.spec.ts'],
        [name: 'CRM_WEB', path: 'tests/ui-tests/specs/crm.spec.ts'],
        [name: 'Discovery_WEB', path: 'tests/ui-tests/specs/discovery.spec.ts'],
        [name: 'Doub<PERSON>_WEB', path: 'tests/ui-tests/specs/doubts.spec.ts'],
        [name: 'Growth_WEB', path: 'tests/ui-tests/specs/growth.spec.ts'],
        [name: 'Live_WEB', path: 'tests/ui-tests/specs/live.spec.ts'],
        [name: 'LMM_WEB', path: 'tests/ui-tests/specs/lmm.spec.ts'],
        [name: 'MNP_WEB', path: 'tests/ui-tests/specs/mnp.spec.ts'],
        [name: 'Test Taking_WEB', path: 'tests/ui-tests/specs/test-taking.spec.ts'],
        [name: 'URM_WEB', path: 'tests/ui-tests/specs/urm.spec.ts']
    ]
    
    def apiTestModules = [
        [name: 'All_API', path: 'tests/api-tests/specs/*.spec.ts'],
        [name: 'CDE_API', path: 'tests/api-tests/specs/cde.spec.ts'],
        [name: 'CRM_API', path: 'tests/api-tests/specs/crm.spec.ts'],
        [name: 'Discovery_API', path: 'tests/api-tests/specs/discovery.spec.ts'],
        [name: 'Doubts_API', path: 'tests/api-tests/specs/doubts.spec.ts'],
        [name: 'Ecommerce_API', path: 'tests/api-tests/specs/ecommerce.spec.ts'],
        [name: 'Homework_API', path: 'tests/api-tests/specs/homework.spec.ts'],
        [name: 'LMM_API', path: 'tests/api-tests/specs/lmm.spec.ts'],
        [name: 'MNP_API', path: 'tests/api-tests/specs/mnp.spec.ts'],
        [name: 'Tests_API', path: 'tests/api-tests/specs/tests.spec.ts'],
        [name: 'URM_API', path: 'tests/api-tests/specs/urm.spec.ts']
    ]

    // Combine all test modules for the parameter
    def allTestModules = webTestModules + apiTestModules

    properties([
        parameters([
            choice(
                name: 'ENVIRONMENT_TYPE',
                choices: ['stage', 'prod'],
                description: 'Select the environment to run tests against',
                defaultValue: 'stage'
            ),
            choice(
                name: 'TEST_TYPE',
                choices: ['WEB', 'API'],
                description: 'Test Type',
                defaultValue: 'WEB'
            ),
            choice(
                name: 'TEST_MODULE',
                choices: allTestModules.collect { "${it.name} - ${it.path}" },
                description: 'Select specific test module (filter by TEST_TYPE: WEB modules start with tests/ui-tests/, API modules start with tests/api-tests/)',
            ),
            string(
                name: 'PLAYWRIGHT_WORKERS',
                description: 'Number of Playwright workers (default: 1)',
                defaultValue: '1'
            )
        ]),
        pipelineTriggers([
        //     cron('0 */1 * * *')  // Run every 1 hour
        ])
    ])

    // Find the selected module from the tuple structure
    def selectedModule = allTestModules.find { "${it.name} - ${it.path}" == params.TEST_MODULE }
    def selectedModulePath = selectedModule ? selectedModule.path : null
    
    def isValidSelection = (params.TEST_TYPE == 'WEB' && selectedModulePath && selectedModulePath.startsWith('tests/ui-tests/')) ||
                          (params.TEST_TYPE == 'API' && selectedModulePath && selectedModulePath.startsWith('tests/api-tests/'))
    
    if (!isValidSelection) {
        error "Invalid selection: TEST_TYPE is '${params.TEST_TYPE}' but selected module '${params.TEST_MODULE}' doesn't match. Please select an appropriate module for ${params.TEST_TYPE} tests."
    }

    withEnv([
        "PLAYWRIGHT_WORKERS=${params.PLAYWRIGHT_WORKERS}"
    ]) {
        common([
            environment: params.ENVIRONMENT_TYPE,
            slackChannel: 'C086157JNP3', //channel to be changed once meeting testing is done
            isProd: params.ENVIRONMENT_TYPE == 'prod' ? '1' : '0',
            ci: '1',
            testType: params.TEST_TYPE,
            uiTestPath: selectedModulePath,
            apiTestPath: selectedModulePath,
            utils: utils 
        ])
    }
}