@Library('Allen_Shared_Libraries') _

node {
    checkout scm

    def common = load "${WORKSPACE}/jenkins/common.groovy"
    def utils = load "${WORKSPACE}/jenkins/utils.groovy"

    properties([
        parameters([
            choice(
                name: 'ENVIRONMENT_TYPE',
                choices: ['stage', 'prod'],
                description: 'Select the environment to run tests against',
                defaultValue: 'stage'
            )
        ]),
        // pipelineTriggers([
        //     cron('0 */1 * * *')  // Run every 1 hour
        // ])
    ])

    withEnv([
        'MEETING_ID_TEST=1',
        'PLAYWRIGHT_WORKERS=6',
        'PLAYWRIGHT_RETRIES=2',
    ]) {
        common([
            environment: params.ENVIRONMENT_TYPE,
            testType: 'WEB',
            slackChannel: 'C086157JNP3', //channel to be changed once meeting testing is done
            isProd: params.ENVIRONMENT_TYPE == 'prod' ? '1' : '0',
            ci: '0',
            uiTestPath: 'tests/misc-tests/specs/join-meetings/admin-joins-scheduled-meetings.spec.ts',
            utils: utils 
        ])
    }
}