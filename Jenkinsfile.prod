@Library('Allen_Shared_Libraries') _

node {
    checkout scm

    def common = load "${WORKSPACE}/jenkins/common.groovy"
    def utils = load "${WORKSPACE}/jenkins/utils.groovy"

    properties([
        pipelineTriggers([
            cron('30  1-23/3 * * *')
        ])
    ])

    common([
        environment: 'prod',
        slackChannel: 'C082366JJ9J',
        isProd: '1',
        ci: '1',
        apiTestPath: 'tests/api-tests/',
        uiTestPath: 'tests/ui-tests/',
        utils: utils,
        testType: 'WEB'
    ])
}